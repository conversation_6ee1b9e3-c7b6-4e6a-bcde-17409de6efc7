{"permissions": {"allow": ["Bash(rm:*)", "<PERSON><PERSON>(mvn:*)", "Bash(./mvnw:*)", "Bash(gradle build:*)", "<PERSON><PERSON>(gradle:*)", "Bash(rm -f \"/Users/<USER>/工作文档/dev/pricefox-api/src/main/java/ai/pricefox/mallfox/domain/product/CategoryInfo.java\")", "Bash(rm -f \"/Users/<USER>/工作文档/dev/pricefox-api/src/main/java/ai/pricefox/mallfox/mapper/product/CategoryInfoMapper.java\")", "Bash(rm -f \"/Users/<USER>/工作文档/dev/pricefox-api/src/main/java/ai/pricefox/mallfox/service/product/CategoryInfoService.java\")", "Bash(rm -rf target/classes/mapper/CategoryInfoMapper.xml)", "Bash(find:*)", "Bash(rm -rf target)", "Bash(./mvnw clean compile -q)", "Bash(find \"/Users/<USER>/工作文档/dev/pricefox-api/target\" -name \"*CategoryController*\" -type f)", "<PERSON><PERSON>(timeout:*)", "<PERSON><PERSON>(pkill:*)", "Bash(git reset:*)", "Bash(git checkout:*)", "<PERSON><PERSON>(curl:*)", "Bash(lsof:*)", "Bash(kill:*)"], "deny": []}}
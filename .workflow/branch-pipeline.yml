version: '1.0'
name: branch-pipeline
displayName: 测试版本发布
triggers:
  trigger: manual
  push:
    branches:
      include:
        - devlop
      exclude:
        - master
stages:
  - name: compile
    displayName: 编译
    strategy: naturally
    trigger: auto
    steps:
      - step: build@maven
        name: build_maven
        displayName: Maven 构建
        jdkVersion: '21'
        mavenVersion: 3.9.6
        commands:
          - mvn -B clean package -Dmaven.test.skip=true
        artifacts:
          - name: BUILD_ARTIFACT
            path:
              - ./target
        strategy:
          timeout: 3
          retry: '1'
      - step: publish@general_artifacts
        name: publish_general_artifacts
        displayName: 上传制品
        dependArtifact: BUILD_ARTIFACT
        artifactName: output
        strategy: {}
        dependsOn: build_maven
  - name: release
    displayName: 发布
    strategy: naturally
    trigger: auto
    steps:
      - step: publish@release_artifacts
        name: publish_release_artifacts
        displayName: 发布
        dependArtifact: output
        version: *******
        autoIncrement: true
        strategy: {}
strategy:
  blocking: true

// 声明式流水线 (Declarative Pipeline) - 修正版
pipeline {
    // 1. 指定在哪里执行流水线
    agent any

    // 新增/修改部分：添加流水线选项
    options {
        // 这个选项会告诉 Jenkins 不要在一开始就自动检出代码
        skipDefaultCheckout true
    }

    // 2. 定义整个流水线包含的各个阶段
    stages {
        // 阶段一：准备工作区并检出代码
        stage('Prepare & Checkout') {
            steps {
                // 1. 在所有操作开始前，强制清理工作区
                echo "Cleaning workspace before checkout..."
                cleanWs()

                // 2. 手动执行代码检出，确保拉取到的是最新版本
                echo "Checking out latest code from Gitee..."
                checkout scm
            }
        }

        // 阶段二：编译、测试和打包 (内容完全不变)
        stage('Build with Maven') {
            steps {
                echo 'Starting Maven build (compile, test, package)...'
                sh './mvnw clean package -DskipTests'
            }
        }

        // 阶段三：存档构建产物 (内容完全不变)
        stage('Archive Artifact') {
            steps {
                echo 'Archiving the build artifact (JAR file)...'
                archiveArtifacts artifacts: 'target/mallfox-1.0.0.jar', fingerprint: true
            }
        }

        // 阶段四：部署 (内容完全不变)
        stage('Deploy') {
            steps {
                echo 'Starting deployment...'
                sshagent(credentials: ['deploy-key']) {
                    script {
                        def remoteUser = "ubuntu"
                        def remoteHost = "*************"
                        def remote = "${remoteUser}@${remoteHost}"
                        def appDir = "/home/<USER>/app"
                        def logDir = "/home/<USER>/logs"
                        def jarName = "mallfox-1.0.0.jar"
                        def jarPath = "target/${jarName}"

                        // 1. 停止旧的应用
                        echo "Stopping old application on ${remoteHost}..."
                        // 我们将 sh 命令改为带有参数的格式，并捕获它的退出状态码
                        // returnStatus: true 是关键，它告诉 Jenkins 不要因为命令失败而中止流水线
                        def pkill_status = sh(script: "ssh -o StrictHostKeyChecking=no ${remote} 'sudo pkill -f ${jarName}'", returnStatus: true)
                        echo "pkill command finished with status: ${pkill_status}" // 打印状态码，方便调试

                        // 2. 将新的 JAR 包通过 scp 复制到目标服务器
                        echo "Copying ${jarName} to ${remoteHost}..."
                        sh "scp -o StrictHostKeyChecking=no ${jarPath} ${remote}:${appDir}/"

                        // 3. 通过 ssh 远程执行启动命令
                        echo "Starting new application on ${remoteHost}..."
                        sh """
                            ssh -o StrictHostKeyChecking=no ${remote} '''
                                echo "Changing directory to ${appDir} and starting application..."
                                cd ${appDir}
                                nohup java -Xms1536m -Xmx1536m \\
                                -XX:MetaspaceSize=128m -XX:MaxMetaspaceSize=256m \\
                                -XX:+UseG1GC \\
                                -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=${logDir}/heapdump.hprof \\
                                -XX:+DisableExplicitGC -Dfile.encoding=UTF-8 \\
                                -jar ${jarName} > ${logDir}/pricefox-server.log 2>&1 &
                            '''
                        """

                        echo 'Deployment finished successfully!'
                    }
                }
            }
        }
    }

    // 3. 定义流水线结束后要执行的操作 (内容完全不变)
    post {
        always {
            echo 'Build finished.'
            // 这里的 cleanWs 是构建结束后清理，我们新加的是构建前清理
            cleanWs()
        }
    }
}
-- 字段数据来源追踪表（JSON格式）
DROP TABLE IF EXISTS `product_field_source_tracking`;
CREATE TABLE `product_field_source_tracking` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `table_name` varchar(50) NOT NULL COMMENT '表名(product_data_offers/product_data_simplify)',
  `record_id` bigint(20) unsigned NOT NULL COMMENT '记录ID',
  `field_sources` json NOT NULL COMMENT '字段来源JSON: {"fieldName": {"dataSource": 1, "sourcePlatform": "amazon", "lastUpdate": "2025-01-14T10:30:00", "oldValue": "old", "newValue": "new"}}',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_table_record` (`table_name`, `record_id`),
  KEY `idx_table_name` (`table_name`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='字段数据来源追踪表';

-- 价格变化历史记录表
DROP TABLE IF EXISTS `product_price_history`;
CREATE TABLE `product_price_history` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `offer_id` bigint(20) unsigned NOT NULL COMMENT '关联product_data_offers表的ID',
  `sku_id` varchar(50) NOT NULL COMMENT '关联的SKU ID',
  `spu_id` varchar(50) NOT NULL COMMENT '关联的SPU ID',
  `source_platform` varchar(50) NOT NULL COMMENT '数据来源平台',
  `platform_spu_id` varchar(100) DEFAULT NULL COMMENT '来源平台的商品ID',
  `platform_sku_id` varchar(100) DEFAULT NULL COMMENT '来源平台的SKU ID',
  
  -- 价格字段
  `old_price` decimal(10,2) DEFAULT NULL COMMENT '变更前价格',
  `new_price` decimal(10,2) DEFAULT NULL COMMENT '变更后价格',
  `old_list_price` decimal(10,2) DEFAULT NULL COMMENT '变更前零售价',
  `new_list_price` decimal(10,2) DEFAULT NULL COMMENT '变更后零售价',
  `old_discount` decimal(10,2) DEFAULT NULL COMMENT '变更前折扣',
  `new_discount` decimal(10,2) DEFAULT NULL COMMENT '变更后折扣',
  
  -- 变化信息
  `price_change_amount` decimal(10,2) DEFAULT NULL COMMENT '价格变化金额(new-old)',
  `price_change_percent` decimal(5,2) DEFAULT NULL COMMENT '价格变化百分比',
  `change_type` tinyint(1) NOT NULL COMMENT '变化类型: 1-新增, 2-更新, 3-删除',
  
  -- 数据来源信息
  `data_channel` int(11) DEFAULT NULL COMMENT '数据渠道：1-爬虫 2-API',
  `trigger_source` varchar(50) DEFAULT NULL COMMENT '触发来源: batch_process/manual_update/scheduled_job',
  
  -- 时间信息
  `price_update_time` varchar(50) DEFAULT NULL COMMENT '价格更新时间(来源数据时间)',
  `record_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  
  PRIMARY KEY (`id`),
  KEY `idx_offer_id` (`offer_id`),
  KEY `idx_sku_id` (`sku_id`),
  KEY `idx_spu_id` (`spu_id`),
  KEY `idx_platform_change` (`source_platform`, `change_type`),
  KEY `idx_record_time` (`record_time`),
  KEY `idx_price_change` (`price_change_amount`, `price_change_percent`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品价格变化历史记录表';

-- 添加外键约束
ALTER TABLE `product_price_history` 
ADD CONSTRAINT `fk_price_history_offer` 
FOREIGN KEY (`offer_id`) REFERENCES `product_data_offers` (`id`) 
ON DELETE CASCADE ON UPDATE CASCADE;
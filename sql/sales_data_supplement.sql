-- 销量数据补充记录表
-- 用于记录每月补充的product_data_offers记录ID，确保持续更新

CREATE TABLE sales_data_supplement_record (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '自增主键ID',
    offer_id BIGINT NOT NULL COMMENT 'product_data_offers表的id',
    supplement_month VARCHAR(7) NOT NULL COMMENT '补充月份(YYYY-MM格式)',
    supplement_sales VARCHAR(50) COMMENT '补充的销量数据',
    review_number INTEGER COMMENT '用于计算的评论数量',
    calculation_ratio DECIMAL(5,3) DEFAULT 0.030 COMMENT '计算比例(默认3%)',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
    UNIQUE KEY uk_offer_month (offer_id, supplement_month),
    INDEX idx_supplement_month (supplement_month),
    INDEX idx_offer_id (offer_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='销量数据补充记录表';
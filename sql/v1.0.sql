/*
 Navicat Premium Dump SQL

 Source Server         : pricefox_dev
 Source Server Type    : MySQL
 Source Server Version : 50718 (5.7.18-txsql-log)
 Source Host           : sh-cdb-4wbl7hpc.sql.tencentcdb.com:21241
 Source Schema         : pricefox

 Target Server Type    : MySQL
 Target Server Version : 50718 (5.7.18-txsql-log)
 File Encoding         : 65001

 Date: 08/07/2025 18:44:03
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for admin_user
-- ----------------------------
DROP TABLE IF EXISTS `admin_user`;
CREATE TABLE `admin_user` (
                              `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
                              `username` varchar(50) NOT NULL COMMENT '用户名',
                              `password` varchar(255) NOT NULL COMMENT '密码',
                              `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
                              `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
                              `nickname` varchar(50) DEFAULT NULL COMMENT '昵称',
                              `avatar` varchar(255) DEFAULT NULL COMMENT '头像',
                              `sex` tinyint(1) DEFAULT '0' COMMENT '性别(0:未知 1:男 2:女)',
                              `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态(0:禁用 1:启用)',
                              `access_token` varchar(255) DEFAULT NULL COMMENT '访问令牌',
                              `refresh_token` varchar(255) DEFAULT NULL COMMENT '刷新令牌',
                              `token_expire_time` datetime DEFAULT NULL COMMENT '令牌过期时间',
                              `refresh_token_expire_time` datetime DEFAULT NULL COMMENT '刷新令牌过期时间',
                              `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
                              `last_login_ip` varchar(50) DEFAULT NULL COMMENT '最后登录IP',
                              `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                              `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                              `create_by` bigint(20) DEFAULT NULL COMMENT '创建人',
                              `update_by` bigint(20) DEFAULT NULL COMMENT '更新人',
                              `remark` varchar(500) DEFAULT NULL COMMENT '备注',
                              PRIMARY KEY (`id`),
                              UNIQUE KEY `uk_username` (`username`),
                              UNIQUE KEY `uk_email` (`email`),
                              UNIQUE KEY `uk_phone` (`phone`),
                              KEY `idx_status` (`status`),
                              KEY `idx_access_token` (`access_token`),
                              KEY `idx_refresh_token` (`refresh_token`),
                              KEY `idx_token_expire_time` (`token_expire_time`),
                              KEY `idx_last_login_time` (`last_login_time`),
                              KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COMMENT='后台用户表';

-- ----------------------------
-- Table structure for affiliate_configs
-- ----------------------------
DROP TABLE IF EXISTS `affiliate_configs`;
CREATE TABLE `affiliate_configs` (
                                     `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
                                     `source_platform` varchar(50) NOT NULL COMMENT '平台统一标识 (与所有其他表保持一致, Amazon, eBay)',
                                     `generation_mode` tinyint(4) NOT NULL DEFAULT '1' COMMENT '链接生成模式 (1:本地模板拼接, 2:调用外部API)',
                                     `url_template` varchar(1024) DEFAULT NULL COMMENT '用于拼接的URL模板 (https://rover.ebay.com/rover/1/...?url={ITEM_URL}&campid={CAMPAIGN_ID})',
                                     `api_endpoint` varchar(512) DEFAULT NULL COMMENT '第三方联盟平台的API端点',
                                     `api_key` varchar(255) DEFAULT NULL COMMENT '用于API调用的Key',
                                     `api_secret` varchar(255) DEFAULT NULL COMMENT '用于API调用的Secret (建议加密存储)',
                                     `default_campaign_id` varchar(100) DEFAULT NULL COMMENT '默认的活动ID (可被动态参数覆盖)',
                                     `commission_rate_info` varchar(255) DEFAULT NULL COMMENT '佣金率文字描述',
                                     `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '配置状态 (1:启用, 0:禁用)',
                                     `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                     `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                     PRIMARY KEY (`id`),
                                     UNIQUE KEY `uk_source_platform` (`source_platform`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='联盟营销平台配置与规则表';

-- ----------------------------
-- Table structure for alert_notification
-- ----------------------------
DROP TABLE IF EXISTS `alert_notification`;
CREATE TABLE `alert_notification` (
                                      `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
                                      `alert_id` bigint(20) unsigned NOT NULL COMMENT '关联的价格提醒任务ID',
                                      `user_id` bigint(20) unsigned NOT NULL COMMENT '接收通知的用户ID',
                                      `sent_to_email` varchar(255) NOT NULL COMMENT '发送的目标邮箱地址',
                                      `subject` varchar(255) NOT NULL COMMENT '邮件标题',
                                      `body` text NOT NULL COMMENT '邮件内容',
                                      `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '发送状态 (1:Pending, 2:Sent, 3:Failed)',
                                      `sent_at` timestamp NULL DEFAULT NULL COMMENT '成功发送的时间',
                                      `failure_reason` varchar(512) DEFAULT NULL COMMENT '发送失败的原因',
                                      `retry_count` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '重试次数',
                                      `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                      PRIMARY KEY (`id`),
                                      KEY `idx_alert_id` (`alert_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='价格提醒发送日志表';

-- ----------------------------
-- Table structure for attribute_definition
-- ----------------------------
DROP TABLE IF EXISTS `attribute_definition`;
CREATE TABLE `attribute_definition` (
                                        `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '属性ID',
                                        `group_id` bigint(20) unsigned NOT NULL COMMENT '所属属性分组ID',
                                        `attribute_name` varchar(100) NOT NULL COMMENT '属性的显示名称 (例如: 品牌, 型号, 屏幕尺寸)',
                                        `value_type` tinyint(4) NOT NULL COMMENT '值类型 (1:String, 2:Text, 3:Int, 4:Decimal, 5:Datetime, 6:Boolean)',
                                        `unit` varchar(20) DEFAULT NULL COMMENT '单位 (例如: 英寸, Hz, 克, mm)',
                                        `input_type` tinyint(4) NOT NULL DEFAULT '1' COMMENT '后台录入控件 (1:文本框, 2:文本域, 3:下拉框...)',
                                        `options` text COMMENT '可选值列表 (JSON格式，用于SELECT, RADIO等, ["Android", "iOS"])',
                                        `is_required` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否为必填项',
                                        `is_filterable` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否可作为前端列表页的筛选条件',
                                        `sort_order` int(11) NOT NULL DEFAULT '0' COMMENT '属性在分组内的显示排序',
                                        `is_rankable` tinyint(1) NOT NULL DEFAULT '0' COMMENT '此属性是否参与排名',
                                        `ranking_direction` varchar(20) DEFAULT NULL COMMENT '排名方向 DESC ASC ',
                                        `ranking_source_url` varchar(512) DEFAULT NULL COMMENT '排名参考数据源URL (如: nanoreview.net)',
                                        `is_comparable` tinyint(1) NOT NULL DEFAULT '0' COMMENT '此属性是否出现在“商品比较”模块',
                                        `display_priority` int(11) NOT NULL DEFAULT '999' COMMENT '展示优先级 (数字越小越靠前)',
                                        `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                        `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                        PRIMARY KEY (`id`),
                                        KEY `idx_group_id` (`group_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品属性定义表 (Attribute)';

-- ----------------------------
-- Table structure for attribute_group
-- ----------------------------
DROP TABLE IF EXISTS `attribute_group`;
CREATE TABLE `attribute_group` (
                                   `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
                                   `group_name` varchar(100) NOT NULL COMMENT '分组名称 (例如: 主体, 显示, 网络连接)',
                                   `sort_order` int(11) NOT NULL DEFAULT '0' COMMENT '分组显示排序',
                                   `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                   `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                   PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品属性分组表';

-- ----------------------------
-- Table structure for brand
-- ----------------------------
DROP TABLE IF EXISTS `brand`;
CREATE TABLE `brand` (
                         `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '品牌ID',
                         `name` varchar(64) NOT NULL COMMENT '品牌名称',
                         `logo` varchar(255) DEFAULT NULL COMMENT '品牌logoURL',
                         `description` text COMMENT '品牌描述',
                         `first_letter` char(1) DEFAULT NULL COMMENT '品牌首字母',
                         `sort` int(11) DEFAULT '0' COMMENT '排序',
                         `is_display` tinyint(1) DEFAULT '1' COMMENT '是否显示',
                         `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
                         `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                         PRIMARY KEY (`id`),
                         UNIQUE KEY `uk_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='品牌表';

-- ----------------------------
-- Table structure for brand_info
-- ----------------------------
DROP TABLE IF EXISTS `brand_info`;
CREATE TABLE `brand_info` (
                              `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '品牌ID',
                              `name` varchar(100) NOT NULL COMMENT '品牌官方名称 (如: Apple)',
                              `logo_url` varchar(512) DEFAULT NULL COMMENT '品牌Logo图片地址',
                              `website` varchar(255) DEFAULT NULL COMMENT '品牌官网',
                              `sort_order` int(11) DEFAULT '0',
                              `is_active` tinyint(1) NOT NULL DEFAULT '1',
                              `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                              `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                              PRIMARY KEY (`id`),
                              UNIQUE KEY `uk_name` (`name`)
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COMMENT='品牌信息表';

-- ----------------------------
-- Table structure for cashback_tasks
-- ----------------------------
DROP TABLE IF EXISTS `cashback_tasks`;
CREATE TABLE `cashback_tasks` (
                                  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
                                  `task_id` varchar(50) NOT NULL COMMENT '自建的唯一任务ID ( CBT00001)',
                                  `user_id` bigint(20) unsigned NOT NULL COMMENT '参与活动的用户ID',
                                  `sku_id` varchar(50) NOT NULL COMMENT '关联的商品SKU ID',
                                  `offer_id` bigint(20) unsigned NOT NULL COMMENT '关联到channel_offers表的ID，记录用户激活时是哪个报价',
                                  `activated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '任务激活时间',
                                  `cashback_amount` decimal(10,2) NOT NULL COMMENT '承诺给用户的返利金额',
                                  `expected_commission` decimal(10,2) DEFAULT NULL COMMENT '我们期望从这次交易中获得的佣金',
                                  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '任务状态 (1:Activated, 2:Purchase_Verified, 3:Review_Submitted, 4:Approved, 5:Paid, 6:Rejected, 7:Expired)',
                                  `order_id` varchar(100) DEFAULT NULL COMMENT '从联盟营销平台获取的订单ID (用于追踪)',
                                  `user_review_id` varchar(50) DEFAULT NULL COMMENT '用户提交的、用于完成此任务的评论ID',
                                  `notes` text COMMENT '运营备注 (如拒绝原因)',
                                  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                  PRIMARY KEY (`id`),
                                  UNIQUE KEY `uk_task_id` (`task_id`),
                                  UNIQUE KEY `uk_user_sku_active` (`user_id`,`sku_id`,`status`),
                                  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='评论换返利任务追踪表';

-- ----------------------------
-- Table structure for category_info
-- ----------------------------
DROP TABLE IF EXISTS `category_info`;
CREATE TABLE `category_info` (
                                 `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '分类ID',
                                 `parent_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '父分类ID (0代表顶级分类)',
                                 `name` varchar(100) NOT NULL COMMENT '分类名称 (如: 手机, 智能手机)',
                                 `level` int(11) NOT NULL COMMENT '分类层级',
                                 `icon_url` varchar(512) DEFAULT NULL COMMENT '分类图标',
                                 `sort_order` int(11) DEFAULT '0',
                                 `is_active` tinyint(1) NOT NULL DEFAULT '1',
                                 `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                 `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                 PRIMARY KEY (`id`),
                                 KEY `idx_parent_id` (`parent_id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COMMENT='商品分类表';

-- ----------------------------
-- Table structure for channel_offers
-- ----------------------------
DROP TABLE IF EXISTS `channel_offers`;
CREATE TABLE `channel_offers` (
                                  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
                                  `sku_id` varchar(50) NOT NULL COMMENT '关联到product_sku表的sku_id',
                                  `source_platform` varchar(50) NOT NULL COMMENT '渠道/平台名称 (Amazon, ebay)',
                                  `platform_sku_id` varchar(255) DEFAULT NULL COMMENT '商品在渠道平台的唯一ID (用于反查)',
                                  `price` decimal(10,2) NOT NULL COMMENT '当前售价',
                                  `list_price` decimal(10,2) DEFAULT NULL COMMENT '划线价',
                                  `inventory` varchar(50) DEFAULT NULL COMMENT '库存 (In Stock,10)',
                                  `seller_name` varchar(255) DEFAULT NULL COMMENT '卖家名称',
                                  `shipping_time` varchar(255) DEFAULT NULL COMMENT '发货时间',
                                  `merchant_rating` decimal(10,2) DEFAULT NULL COMMENT '卖家评分',
                                  `item_url` text NOT NULL COMMENT '引导用户购买的商品链接',
                                  `sales_last30_days` varchar(50) DEFAULT NULL COMMENT '近30天销量',
                                  `installment_info` varchar(255) DEFAULT '' COMMENT '分期信息',
                                  `last_crawled_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后一次采集/更新时间',
                                  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                  PRIMARY KEY (`id`),
                                  UNIQUE KEY `uk_sku_platform_seller` (`sku_id`,`source_platform`,`seller_name`),
                                  KEY `idx_sku_id_price` (`sku_id`,`price`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='渠道商品报价表 (高频)';

-- ----------------------------
-- Table structure for data_calibration_tags
-- ----------------------------
DROP TABLE IF EXISTS `data_calibration_tags`;
CREATE TABLE `data_calibration_tags` (
                                         `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                         `target_table` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '被标记的表名 (product_data_offers, product_data_simplify)',
                                         `target_id` bigint(20) unsigned NOT NULL COMMENT '被标记的记录ID (offers.id 或 simplify.id)',
                                         `field_name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '被标记的字段名 (title, price)',
                                         `tag_status` tinyint(4) NOT NULL COMMENT '标记状态 (1: 标记缺失, 2: 标记错误)',
                                         `operator_id` bigint(20) NOT NULL COMMENT '操作员ID',
                                         `remark` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注信息',
                                         `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                         `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                         PRIMARY KEY (`id`),
                                         UNIQUE KEY `uk_target_field` (`target_table`,`target_id`,`field_name`),
                                         KEY `idx_target` (`target_table`,`target_id`)
) ENGINE=InnoDB AUTO_INCREMENT=30 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='数据校准标记表';

-- ----------------------------
-- Table structure for data_cleansing_queue
-- ----------------------------
DROP TABLE IF EXISTS `data_cleansing_queue`;
CREATE TABLE `data_cleansing_queue` (
                                        `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
                                        `source_record_id` varchar(255) NOT NULL COMMENT '来源底表中的记录ID或唯一标识',
                                        `source_platform` varchar(50) NOT NULL COMMENT '数据来源平台',
                                        `raw_data_snapshot` json NOT NULL COMMENT '进入匹配时的原始数据JSON快照',
                                        `issue_type` tinyint(4) NOT NULL COMMENT '问题类型 (1:SPU多重匹配, 2:SKU匹配低置信度, 3:数据缺失, 4:未知品牌)',
                                        `issue_description` varchar(512) DEFAULT NULL COMMENT '问题的详细文字描述',
                                        `suggested_spu_ids` json DEFAULT NULL COMMENT '系统推荐的可能匹配的SPU ID列表',
                                        `suggested_sku_ids` json DEFAULT NULL COMMENT '系统推荐的可能匹配的SKU ID列表',
                                        `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '处理状态 (1:待处理, 2:处理中, 3:已解决, 4:已忽略)',
                                        `resolution_type` tinyint(4) DEFAULT NULL COMMENT '解决方案类型 (1:关联到现有SPU/SKU, 2:创建新SPU/SKU, 3:更新别名库)',
                                        `resolved_spu_id` varchar(50) DEFAULT NULL COMMENT '最终解决关联的SPU ID',
                                        `resolved_sku_id` varchar(50) DEFAULT NULL COMMENT '最终解决关联的SKU ID',
                                        `operator_id` bigint(20) unsigned DEFAULT NULL COMMENT '处理人ID',
                                        `resolved_at` datetime DEFAULT NULL COMMENT '解决时间',
                                        `operator_notes` text COMMENT '处理备注',
                                        `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                        PRIMARY KEY (`id`),
                                        KEY `idx_status_type` (`status`,`issue_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='数据清洗与匹配异常处理队列';

-- ----------------------------
-- Table structure for expert_reviews
-- ----------------------------
DROP TABLE IF EXISTS `expert_reviews`;
CREATE TABLE `expert_reviews` (
                                  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
                                  `title` varchar(255) NOT NULL COMMENT '评测文章/视频标题',
                                  `subtitle` varchar(512) DEFAULT NULL COMMENT '副标题或摘要',
                                  `cover_image_url` varchar(512) NOT NULL COMMENT '封面图URL',
                                  `content_type` tinyint(4) NOT NULL DEFAULT '1' COMMENT '内容类型 (1:图文文章, 2:视频)',
                                  `content_body` mediumtext COMMENT '文章内容 (HTML或Markdown)',
                                  `video_url` varchar(512) DEFAULT NULL COMMENT '视频链接 (如果类型是视频)',
                                  `author_name` varchar(100) NOT NULL COMMENT '作者/机构名称',
                                  `author_avatar_url` varchar(512) DEFAULT NULL COMMENT '作者头像',
                                  `source_name` varchar(100) DEFAULT NULL COMMENT '来源平台 (如: TechReviewer, YouTube)',
                                  `original_url` varchar(512) DEFAULT NULL COMMENT '原文/原视频链接',
                                  `reading_time_minutes` int(10) unsigned DEFAULT NULL COMMENT '预计阅读时长 (分钟)',
                                  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '发布状态 (0:草稿, 1:已发布)',
                                  `view_count` int(10) unsigned DEFAULT NULL COMMENT '查看人数',
                                  `recommend_count` int(10) unsigned DEFAULT NULL COMMENT '推荐人数',
                                  `publish_time` datetime DEFAULT NULL COMMENT '发布时间',
                                  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='专家评测内容表';

-- ----------------------------
-- Table structure for homepage_layout
-- ----------------------------
DROP TABLE IF EXISTS `homepage_layout`;
CREATE TABLE `homepage_layout` (
                                   `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
                                   `display_title` varchar(100) NOT NULL COMMENT '模块在首页上实际显示的标题 (可覆盖默认标题)',
                                   `display_order` int(11) NOT NULL DEFAULT '0' COMMENT '模块在首页的显示顺序 (数字越小越靠前)',
                                   `item_count` int(11) NOT NULL DEFAULT '8' COMMENT '此模块展示的内容数量 (如: 8个商品)',
                                   `config_params` json DEFAULT NULL COMMENT '模块的特定配置参数 (JSON格式)',
                                   `is_visible` tinyint(1) NOT NULL DEFAULT '1',
                                   PRIMARY KEY (`id`),
                                   KEY `idx_order` (`is_visible`,`display_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='首页布局配置表';

-- ----------------------------
-- Table structure for homepage_modules
-- ----------------------------
DROP TABLE IF EXISTS `homepage_modules`;
CREATE TABLE `homepage_modules` (
                                    `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
                                    `module_code` varchar(50) NOT NULL COMMENT '模块唯一编码 (如: best_seller_products, expert_reviews, brand_products)',
                                    `module_name` varchar(100) NOT NULL COMMENT '模块名称 (如: 畅销商品推荐, 专家评测)',
                                    `content_type` tinyint(4) NOT NULL COMMENT '内容类型 (1:Product, 2:ExpertReview, 3:Brand, ...)',
                                    `data_source_logic` varchar(255) NOT NULL COMMENT '数据源获取逻辑的标识 (由后端代码定义和识别)',
                                    `default_title` varchar(100) DEFAULT NULL COMMENT '模块的默认标题',
                                    `is_active` tinyint(1) NOT NULL DEFAULT '1',
                                    PRIMARY KEY (`id`),
                                    UNIQUE KEY `uk_module_code` (`module_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='首页可用模块定义表';

-- ----------------------------
-- Table structure for normalization_alias
-- ----------------------------
DROP TABLE IF EXISTS `normalization_alias`;
CREATE TABLE `normalization_alias` (
                                       `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
                                       `entity_type` varchar(50) NOT NULL COMMENT '实体类型 (BRAND, COLOR, CONDITION, ...)',
                                       `alias_name` varchar(255) NOT NULL COMMENT '别名 (从源数据中看到的名字，需小写处理)',
                                       `standard_value` varchar(255) NOT NULL COMMENT '对应的标准值',
                                       `source_platform` varchar(50) DEFAULT NULL COMMENT '此别名所属的平台 (可选，用于平台特定规则)',
                                       PRIMARY KEY (`id`),
                                       UNIQUE KEY `uk_type_alias` (`entity_type`,`alias_name`,`source_platform`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='归一化别名/同义词映射表';

-- ----------------------------
-- Table structure for normalization_rules
-- ----------------------------
DROP TABLE IF EXISTS `normalization_rules`;
CREATE TABLE `normalization_rules` (
                                       `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
                                       `entity_type` varchar(50) NOT NULL COMMENT '要解析的实体类型 (MODEL, SERIES, STORAGE, ...)',
                                       `regex_pattern` varchar(512) NOT NULL COMMENT '用于提取信息的正则表达式',
                                       `priority` int(11) NOT NULL DEFAULT '0' COMMENT '规则优先级 (数字越小越先尝试)',
                                       `is_active` tinyint(1) NOT NULL DEFAULT '1',
                                       PRIMARY KEY (`id`),
                                       KEY `idx_type_priority` (`entity_type`,`priority`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='归一化解析规则表 (正则)';

-- ----------------------------
-- Table structure for price_alert
-- ----------------------------
DROP TABLE IF EXISTS `price_alert`;
CREATE TABLE `price_alert` (
                               `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
                               `user_id` bigint(20) unsigned NOT NULL COMMENT '用户ID',
                               `sku_id` bigint(20) unsigned NOT NULL COMMENT '关注的商品SKU ID',
                               `price_at_creation` decimal(10,2) NOT NULL COMMENT '创建提醒时的商品价格 (计算基准)',
                               `rule_type` tinyint(4) NOT NULL COMMENT '规则类型 (1:降至目标价, 2:下降百分比, 3:任意降价)',
                               `rule_value` decimal(10,2) NOT NULL COMMENT '规则的值 (百分比/金额/目标价)',
                               `target_price` decimal(10,2) NOT NULL COMMENT '触发提醒的目标价格 (创建时计算好)',
                               `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '提醒状态 (1:Active, 2:Triggered, 3:Sent, 4:Disabled, 5:Expired)',
                               `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                               `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                               PRIMARY KEY (`id`),
                               KEY `idx_user_id` (`user_id`),
                               KEY `idx_sku_id` (`sku_id`),
                               KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='价格提醒表';

-- ----------------------------
-- Table structure for price_history
-- ----------------------------
DROP TABLE IF EXISTS `price_history`;
CREATE TABLE `price_history` (
                                 `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
                                 `sku_id` varchar(50) NOT NULL COMMENT '关联到product_sku表的自建SKU ID',
                                 `record_date` date NOT NULL COMMENT '记录日期 (快照日期)',
                                 `lowest_price` decimal(10,2) NOT NULL COMMENT '当天该SKU在全渠道的最低价',
                                 `average_price` decimal(10,2) NOT NULL COMMENT '当天该SKU在全渠道的平均价',
                                 `offer_count` int(10) unsigned NOT NULL COMMENT '当天用于计算的有效报价数量',
                                 PRIMARY KEY (`id`),
                                 UNIQUE KEY `uk_sku_date` (`sku_id`,`record_date`),
                                 KEY `idx_sku_id_date` (`sku_id`,`record_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='SKU每日价格历史快照表';

-- ----------------------------
-- Table structure for product
-- ----------------------------
DROP TABLE IF EXISTS `product`;
CREATE TABLE `product` (
                           `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '商品ID',
                           `category_id` bigint(20) NOT NULL COMMENT '所属分类ID',
                           `brand_id` bigint(20) DEFAULT NULL COMMENT '品牌ID',
                           `name` varchar(128) NOT NULL COMMENT '商品名称',
                           `sub_title` varchar(256) DEFAULT NULL COMMENT '副标题(促销信息)',
                           `main_image` varchar(255) DEFAULT NULL COMMENT '主图URL',
                           `sub_images` text COMMENT '子图URL(JSON数组)',
                           `description` text COMMENT '商品详情(富文本)',
                           `price` decimal(10,2) DEFAULT NULL COMMENT '参考价(展示用)',
                           `status` tinyint(4) DEFAULT '1' COMMENT '状态(0:下架 1:上架)',
                           `sales` int(11) DEFAULT '0' COMMENT '销量',
                           `review_count` int(11) DEFAULT '0' COMMENT '评价数',
                           `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
                           `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                           PRIMARY KEY (`id`),
                           KEY `idx_category_id` (`category_id`),
                           KEY `idx_brand_id` (`brand_id`),
                           KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品SPU表';

-- ----------------------------
-- Table structure for product_ai_analysis
-- ----------------------------
DROP TABLE IF EXISTS `product_ai_analysis`;
CREATE TABLE `product_ai_analysis` (
                                       `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
                                       `spu_id` varchar(50) NOT NULL COMMENT '关联到product_info表的自建SPU ID',
                                       `overall_rating` decimal(3,2) NOT NULL COMMENT 'AI计算的综合评分 (如: 4.5)',
                                       `total_reviews_analyzed` int(10) unsigned NOT NULL COMMENT '参与分析的总评论数',
                                       `rating_distribution` json NOT NULL COMMENT '五星好评分布 ({"5": 313, "4": 106, ...})',
                                       `pros_tags` json DEFAULT NULL COMMENT '优点标签 ( [{"tag": "Long battery", "count": 2200}, ...])',
                                       `cons_tags` json DEFAULT NULL COMMENT '缺点标签 ([{"tag": "Heavy to hold", "count": 102}, ...])',
                                       `dimensional_ratings` json NOT NULL COMMENT '多维度评分雷达图 ({"performance": 4.25, "screen": 4.29, ...})',
                                       `dimension_details` json DEFAULT NULL COMMENT '各维度详细分析列表',
                                       `version` varchar(50) NOT NULL COMMENT 'AI分析模型的版本号',
                                       `last_generated_at` datetime NOT NULL COMMENT '最后生成时间',
                                       `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                       `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                       PRIMARY KEY (`id`),
                                       UNIQUE KEY `uk_spu_id` (`spu_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品AI评论分析聚合表';

-- ----------------------------
-- Table structure for product_attribute
-- ----------------------------
DROP TABLE IF EXISTS `product_attribute`;
CREATE TABLE `product_attribute` (
                                     `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '属性ID',
                                     `category_id` bigint(20) NOT NULL COMMENT '所属分类ID',
                                     `name` varchar(64) NOT NULL COMMENT '属性名称',
                                     `input_type` tinyint(4) DEFAULT '1' COMMENT '输入类型(1:手动输入 2:单选 3:多选)',
                                     `values` text COMMENT '可选值列表(JSON数组)',
                                     `sort` int(11) DEFAULT '0' COMMENT '排序',
                                     `is_filter` tinyint(1) DEFAULT '0' COMMENT '是否用于筛选(0:否 1:是)',
                                     `is_required` tinyint(1) DEFAULT '0' COMMENT '是否必填(0:否 1:是)',
                                     `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
                                     `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                     PRIMARY KEY (`id`),
                                     KEY `idx_category_id` (`category_id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COMMENT='商品属性表';

-- ----------------------------
-- Table structure for product_attribute_value
-- ----------------------------
DROP TABLE IF EXISTS `product_attribute_value`;
CREATE TABLE `product_attribute_value` (
                                           `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
                                           `product_id` bigint(20) DEFAULT NULL COMMENT '商品ID',
                                           `attribute_id` bigint(20) NOT NULL COMMENT '属性ID',
                                           `value` varchar(255) NOT NULL COMMENT '属性值',
                                           `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
                                           PRIMARY KEY (`id`),
                                           UNIQUE KEY `uk_product_attribute` (`product_id`,`attribute_id`),
                                           KEY `idx_attribute_id` (`attribute_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COMMENT='商品属性值表';

-- ----------------------------
-- Table structure for product_category
-- ----------------------------
DROP TABLE IF EXISTS `product_category`;
CREATE TABLE `product_category` (
                                    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '分类ID',
                                    `parent_id` bigint(20) DEFAULT '0' COMMENT '父分类ID(0:一级分类)',
                                    `name` varchar(64) NOT NULL COMMENT '分类名称',
                                    `level` int(11) DEFAULT '1' COMMENT '层级(1:一级 2:二级 3:三级)',
                                    `sort` int(11) DEFAULT '0' COMMENT '排序',
                                    `icon` varchar(255) DEFAULT NULL COMMENT '图标URL',
                                    `is_display` tinyint(1) DEFAULT '1' COMMENT '是否显示(0:隐藏 1:显示)',
                                    `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
                                    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                    PRIMARY KEY (`id`),
                                    KEY `idx_parent_id` (`parent_id`),
                                    KEY `idx_level` (`level`)
) ENGINE=InnoDB AUTO_INCREMENT=52 DEFAULT CHARSET=utf8mb4 COMMENT='商品分类表';

-- ----------------------------
-- Table structure for product_data_offers
-- ----------------------------
DROP TABLE IF EXISTS `product_data_offers`;
CREATE TABLE `product_data_offers` (
                                       `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                       `sku_id` varchar(100) NOT NULL COMMENT '关联的自建SKU ID',
                                       `spu_id` varchar(100) NOT NULL COMMENT '自建的SPUID',
                                       `source_platform` varchar(50) NOT NULL COMMENT '数据来源平台',
                                       `platform_spu_id` varchar(255) DEFAULT NULL COMMENT '来源平台的SPU/ID/ASIN',
                                       `platform_sku_id` varchar(255) DEFAULT NULL COMMENT '来源平台的SKU组合ID',
                                       `item_url` text COMMENT '商品链接',
                                       `list_price` decimal(10,2) DEFAULT NULL COMMENT '商品零售价',
                                       `price` decimal(10,2) DEFAULT NULL COMMENT '价格',
                                       `discount` decimal(10,2) DEFAULT NULL COMMENT '折扣',
                                       `inventory` varchar(50) DEFAULT NULL COMMENT '库存状态',
                                       `sales_last30_days` varchar(50) DEFAULT NULL COMMENT '近30天销量',
                                       `seller` varchar(255) DEFAULT NULL COMMENT '卖家名称',
                                       `merchant_rating` decimal(10,2) DEFAULT NULL COMMENT '卖家评分',
                                       `brand` varchar(100) DEFAULT NULL COMMENT '品牌',
                                       `title` varchar(1024) DEFAULT NULL COMMENT '商品标题',
                                       `series` varchar(100) DEFAULT NULL COMMENT '产品系列',
                                       `upc_code` varchar(50) DEFAULT NULL COMMENT 'UPC编码',
                                       `service_provider` varchar(255) DEFAULT NULL COMMENT '服务商',
                                       `color_image_url` varchar(255) DEFAULT NULL COMMENT '规格颜色图片',
                                       `condition_new` varchar(100) DEFAULT NULL COMMENT '商品状态 全新 开盒 重包装',
                                       `category_level1` varchar(100) DEFAULT NULL COMMENT '一级类目',
                                       `category_level2` varchar(100) DEFAULT NULL COMMENT '二级类目',
                                       `price_update_time` varchar(100) DEFAULT NULL COMMENT '价格更新时间',
                                       `category_level3` varchar(50) DEFAULT NULL COMMENT '三级类目',
                                       `data_channel` tinyint(2) DEFAULT NULL COMMENT '数据渠道：1 爬虫  2 api',
                                       `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
                                       `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
                                       PRIMARY KEY (`id`) USING BTREE,
                                       KEY `idx_sku_platform` (`sku_id`,`source_platform`),
                                       KEY `idx_source_platform_price` (`source_platform`,`price`)
) ENGINE=InnoDB AUTO_INCREMENT=49936 DEFAULT CHARSET=utf8mb4 COMMENT='多平台商品高频表';

-- ----------------------------
-- Table structure for product_data_reviews
-- ----------------------------
DROP TABLE IF EXISTS `product_data_reviews`;
CREATE TABLE `product_data_reviews` (
                                        `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增主键ID, 唯一标识每条评论记录',
                                        `review_id` varchar(100) DEFAULT NULL COMMENT '自建的唯一评论ID (如: PR0000000001)',
                                        `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
                                        `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
                                        `sku_id` varchar(100) DEFAULT NULL COMMENT '关联到商品表的SKU ID',
                                        `spu_id` varchar(100) DEFAULT NULL COMMENT '关联到商品表的SPU ID',
                                        `source_platform` varchar(50) NOT NULL COMMENT '评论来源平台 (例如: amazon, bestbuy)',
                                        `platform_review_id` varchar(100) NOT NULL DEFAULT '' COMMENT '平台的唯一评论id',
                                        `platform_spu_id` varchar(255) DEFAULT NULL COMMENT '来源平台的SPU/ID/ASIN',
                                        `platform_sku_id` varchar(255) DEFAULT NULL COMMENT '来源平台的SKU组合ID',
                                        `review_score` tinyint(3) unsigned DEFAULT NULL COMMENT '评论星级 (1-5)',
                                        `review_user_name` varchar(255) DEFAULT NULL COMMENT '评论用户名称',
                                        `review_title` varchar(512) DEFAULT NULL COMMENT '评论标题',
                                        `review_content` text COMMENT '评论正文内容',
                                        `review_time` timestamp NULL DEFAULT NULL COMMENT '评论发布时间',
                                        `is_helpful_or_not` int(10) unsigned DEFAULT '0' COMMENT '用户认为有帮助',
                                        `review_image_url` text COMMENT '评论附带的图片URL列表 (多个用逗号分隔)',
                                        PRIMARY KEY (`id`),
                                        UNIQUE KEY `uk_review_id` (`review_id`),
                                        KEY `idx_sku_id` (`sku_id`),
                                        KEY `idx_source_platform_rid` (`source_platform`,`platform_review_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3747 DEFAULT CHARSET=utf8mb4 COMMENT='多平台商品评论数据表';

-- ----------------------------
-- Table structure for product_data_simplify
-- ----------------------------
DROP TABLE IF EXISTS `product_data_simplify`;
CREATE TABLE `product_data_simplify` (
                                         `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                         `source_platform` varchar(50) NOT NULL COMMENT '数据的主要来源平台',
                                         `spu_id` varchar(100) DEFAULT NULL COMMENT '自建SPU ID',
                                         `sku_id` varchar(100) DEFAULT NULL COMMENT '自建SKU ID',
                                         `platform_spu_id` varchar(255) DEFAULT NULL COMMENT '来源平台的SPU/ID/ASIN',
                                         `platform_sku_id` varchar(255) DEFAULT NULL COMMENT '来源平台的SKU编码',
                                         `model` varchar(100) DEFAULT NULL COMMENT '商品型号',
                                         `model_back` varchar(100) DEFAULT NULL COMMENT '商品型号备份',
                                         `model_year` varchar(10) DEFAULT NULL COMMENT '型号年份',
                                         `color` varchar(50) DEFAULT NULL COMMENT '颜色',
                                         `storage` varchar(50) DEFAULT NULL COMMENT '内存/存储容量',
                                         `product_main_image_urls` text COMMENT '商品主图URL列表逗号分隔 (url1", "url2")',
                                         `product_spec_color_url` text COMMENT '规格颜色图片URL',
                                         `ram_memory_installed_size` varchar(50) DEFAULT NULL COMMENT '已安装内存RAM大小',
                                         `operating_system` varchar(100) DEFAULT NULL COMMENT '操作系统',
                                         `processor` varchar(100) DEFAULT NULL COMMENT '处理器',
                                         `cellular_technology` varchar(500) DEFAULT NULL COMMENT '蜂窝技术',
                                         `screen_size` varchar(50) DEFAULT NULL COMMENT '屏幕尺寸',
                                         `resolution` varchar(50) DEFAULT NULL COMMENT '分辨率',
                                         `refresh_rate` varchar(50) DEFAULT NULL COMMENT '刷新率',
                                         `display_type` varchar(100) DEFAULT NULL COMMENT '显示类型',
                                         `battery_power` varchar(50) DEFAULT NULL COMMENT '电池电量',
                                         `average_talk_time` varchar(50) DEFAULT NULL COMMENT '平均通话时长',
                                         `battery_charge_time` varchar(50) DEFAULT NULL COMMENT '电池充电时长',
                                         `front_photo_sensor_resolution` varchar(100) DEFAULT NULL COMMENT '前置摄像头分辨率',
                                         `rear_facing_camera_photo_sensor_resolution` varchar(100) DEFAULT NULL COMMENT '后置摄像头分辨率',
                                         `number_of_rear_facing_cameras` tinyint(3) unsigned DEFAULT NULL COMMENT '后置摄像头数量',
                                         `effective_video_resolution` varchar(100) DEFAULT NULL COMMENT '有效视频分辨率',
                                         `video_capture_resolution` varchar(50) DEFAULT NULL COMMENT '视频捕捉帧率',
                                         `sim_card_slot_count` varchar(100) DEFAULT NULL COMMENT 'SIM卡卡槽类型',
                                         `connector_type` varchar(100) DEFAULT NULL COMMENT '连接器类型',
                                         `water_resistance` varchar(100) DEFAULT NULL COMMENT '防水性能',
                                         `dimensions` varchar(100) DEFAULT NULL COMMENT '手机尺寸',
                                         `item_weight` varchar(50) DEFAULT NULL COMMENT '商品重量',
                                         `biometric_security_feature` varchar(255) DEFAULT NULL COMMENT '生物识别安全技术',
                                         `condition_new` varchar(255) DEFAULT NULL COMMENT '商品状态不能为空',
                                         `service_provider` varchar(255) DEFAULT NULL COMMENT '商品服务商',
                                         `shipping_time` varchar(50) DEFAULT NULL COMMENT '发货时间',
                                         `supported_satellite_navigation_system` varchar(255) DEFAULT NULL COMMENT '支持的卫星导航系统',
                                         `features` text COMMENT '特征/功能列表',
                                         `return_policy` text COMMENT '退换货政策',
                                         `payment_installment` text COMMENT '付款方式 如信用卡,分期',
                                         `install_payment` text COMMENT '分期信息',
                                         `warranty_description` text COMMENT '保修说明',
                                         `review_number` int(10) unsigned DEFAULT NULL COMMENT '评论数量',
                                         `review_score` decimal(10,1) DEFAULT NULL COMMENT '评分',
                                         `review_rating_distribution` text COMMENT '商品评价五点图分布 ({"5_star": 120, "1_star": 5})',
                                         `review_dimensional_ratings` text COMMENT '不同维度评分 ({"camera": 4.5, "battery": 4.2})',
                                         `review_overview_pros_cons` text COMMENT '评价-概览-优缺点 ({"pros": ["{"long baettery:200"}"], "cons": ["{"heavy to hold:102"}"]})',
                                         `review_pros_cons_by_star` text COMMENT '评价-1-5星级优缺点',
                                         `data_channel` tinyint(2) DEFAULT NULL COMMENT '数据渠道：1 爬虫  2 api',
                                         `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
                                         `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
                                         `mark` int(1) DEFAULT '0' COMMENT '1 标记缺失 2 标记错误',
                                         PRIMARY KEY (`id`),
                                         KEY `idx_sku_id` (`sku_id`),
                                         KEY `idex_spu_id` (`spu_id`) USING BTREE,
                                         KEY `idx_source_platform` (`source_platform`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=41977 DEFAULT CHARSET=utf8mb4 COMMENT='商品低频数据表';

-- ----------------------------
-- Table structure for product_expert_review_relations
-- ----------------------------
DROP TABLE IF EXISTS `product_expert_review_relations`;
CREATE TABLE `product_expert_review_relations` (
                                                   `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
                                                   `spu_id` varchar(50) NOT NULL COMMENT '关联到product_info表的spu_id',
                                                   `expert_review_id` bigint(20) unsigned NOT NULL COMMENT '关联到expert_reviews表的id',
                                                   `sort_order` int(11) NOT NULL DEFAULT '0' COMMENT '在此商品下展示的排序',
                                                   PRIMARY KEY (`id`),
                                                   UNIQUE KEY `uk_spu_review` (`spu_id`,`expert_review_id`),
                                                   KEY `idx_spu_id` (`spu_id`),
                                                   KEY `idx_expert_review_id` (`expert_review_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='产品与专家评测关联表';

-- ----------------------------
-- Table structure for product_info
-- ----------------------------
DROP TABLE IF EXISTS `product_info`;
CREATE TABLE `product_info` (
                                `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增主键',
                                `spu_id` varchar(50) NOT NULL COMMENT '自建的唯一SPU ID',
                                `brand_id` bigint(20) unsigned NOT NULL COMMENT '关联到品牌表的ID',
                                `category_id` bigint(20) unsigned NOT NULL COMMENT '关联到最末级分类表的ID',
                                `name` varchar(255) NOT NULL COMMENT '产品通用名称/标题',
                                `main_image_url` varchar(512) DEFAULT NULL COMMENT '商品主图URL',
                                `description` text COMMENT '产品通用描述',
                                `primary_source` varchar(50) DEFAULT NULL COMMENT '主要数据来源 (ebay Amazon等)',
                                `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'SPU状态',
                                `release_date` date DEFAULT NULL COMMENT '官方发布日期',
                                `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                PRIMARY KEY (`id`),
                                UNIQUE KEY `uk_spu_id` (`spu_id`),
                                KEY `idx_brand_id` (`brand_id`),
                                KEY `idx_category_id` (`category_id`),
                                CONSTRAINT `fk_product_brand` FOREIGN KEY (`brand_id`) REFERENCES `brand_info` (`id`),
                                CONSTRAINT `fk_product_category` FOREIGN KEY (`category_id`) REFERENCES `category_info` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='核心产品表 (SPU)';

-- ----------------------------
-- Table structure for product_requests
-- ----------------------------
DROP TABLE IF EXISTS `product_requests`;
CREATE TABLE `product_requests` (
                                    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
                                    `source_platform` varchar(50) NOT NULL,
                                    `platform_sku_id` varchar(255) NOT NULL,
                                    `title` varchar(1024) DEFAULT NULL,
                                    `item_url` text,
                                    `request_count` int(10) unsigned NOT NULL DEFAULT '1' COMMENT '被请求的总次数',
                                    `last_requested_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                    PRIMARY KEY (`id`),
                                    UNIQUE KEY `uk_platform_sku` (`source_platform`,`platform_sku_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户商品收录请求表';

-- ----------------------------
-- Table structure for product_sku
-- ----------------------------
DROP TABLE IF EXISTS `product_sku`;
CREATE TABLE `product_sku` (
                               `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
                               `sku_id` varchar(50) NOT NULL COMMENT '自建的唯一SKU ID',
                               `spu_id` varchar(50) NOT NULL COMMENT '所属产品SPU ID',
                               `attributes` json NOT NULL COMMENT '销售属性组合 (JSON格式)',
                               `sku_image_url` varchar(512) DEFAULT NULL COMMENT 'SKU特定图片',
                               `lowest_price` decimal(10,2) DEFAULT NULL COMMENT '此SKU在全渠道的最低报价',
                               `highest_price` decimal(10,2) DEFAULT NULL COMMENT '此SKU在全渠道的最高报价',
                               `offer_count` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '此SKU的有效报价渠道数量',
                               `average_rating` decimal(3,1) DEFAULT NULL COMMENT '此SKU的聚合平均评分',
                               `total_reviews` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '此SKU的聚合总评论数',
                               `sales_last_30_days` int(10) DEFAULT NULL COMMENT '近30天销量',
                               `stock_quantity` int(11) NOT NULL DEFAULT '0' COMMENT '总库存数量',
                               `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'SKU状态 (0:草稿, 1:已上架, 2:已下架)',
                               `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                               `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                               `primary_installment_info` varchar(255) DEFAULT NULL COMMENT '用于列表页展示的主要分期信息',
                               PRIMARY KEY (`id`),
                               UNIQUE KEY `uk_sku_id` (`sku_id`),
                               KEY `idx_spu_id` (`spu_id`),
                               KEY `idx_status_price` (`status`,`lowest_price`),
                               KEY `idx_status_rating` (`status`,`average_rating`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品SKU表';

-- ----------------------------
-- Table structure for product_spec_mobile
-- ----------------------------
DROP TABLE IF EXISTS `product_spec_mobile`;
CREATE TABLE `product_spec_mobile` (
                                       `spu_id` varchar(50) NOT NULL COMMENT '关联到product_info表的spu_id',
                                       `screen_size_inch` decimal(4,2) DEFAULT NULL COMMENT '屏幕尺寸 (单位: 英寸)',
                                       `battery_capacity_mah` int(10) unsigned DEFAULT NULL COMMENT '电池容量 (单位: 毫安时)',
                                       `ram_size_gb` int(10) unsigned DEFAULT NULL COMMENT '内存大小 (单位: GB)',
                                       `weight_g` decimal(10,2) DEFAULT NULL COMMENT '重量 (单位: 克)',
                                       `refresh_rate_hz` int(10) unsigned DEFAULT NULL COMMENT '屏幕刷新率 (单位: Hz)',
                                       `model` varchar(100) DEFAULT NULL COMMENT '商品内部型号',
                                       `model_year` year(4) DEFAULT NULL COMMENT '型号年份',
                                       `operating_system_family` varchar(50) DEFAULT NULL COMMENT '操作系统家族 (如: iOS, Android)',
                                       `operating_system_version` varchar(50) DEFAULT NULL COMMENT '操作系统版本 (如: 17, 14)',
                                       `processor_brand` varchar(50) DEFAULT NULL COMMENT '处理器品牌 (如: Apple, Qualcomm)',
                                       `processor_name` varchar(100) DEFAULT NULL COMMENT '处理器型号 (如: A17 Bionic, Snapdragon 8 Gen 3)',
                                       `cellular_technologies` varchar(255) DEFAULT NULL COMMENT '支持的蜂窝技术 (JSON数组或逗号分隔,  ["5G", "LTE"])',
                                       `water_resistance_level` varchar(50) DEFAULT NULL COMMENT '防水等级 (如: IP68)',
                                       `connector_type` varchar(50) DEFAULT NULL COMMENT '连接器类型 (如: USB-C, Lightning)',
                                       `stand_processor_name` varchar(255) DEFAULT NULL COMMENT '标准化的处理器名称 (需与ranking_items.item_name匹配)',
                                       `stand_display_name` varchar(255) DEFAULT NULL COMMENT '标准化的屏幕名称 (用于关联屏幕排行榜)',
                                       `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                       PRIMARY KEY (`spu_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='产品规格扩展表 - 手机';

-- ----------------------------
-- Table structure for product_user_reviews
-- ----------------------------
DROP TABLE IF EXISTS `product_user_reviews`;
CREATE TABLE `product_user_reviews` (
                                        `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
                                        `review_id` varchar(50) NOT NULL COMMENT '自建的唯一评论ID (如: UR00000001)',
                                        `sku_id` varchar(50) NOT NULL COMMENT '关联到product_sku表的自建SKU ID',
                                        `spu_id` varchar(50) NOT NULL COMMENT '关联到product_info表的自建SPU ID',
                                        `rating` tinyint(3) unsigned NOT NULL COMMENT '用户评分 (1-5星)',
                                        `title` varchar(512) DEFAULT NULL COMMENT '评论标题',
                                        `content` text NOT NULL COMMENT '评论正文内容',
                                        `image_urls` json DEFAULT NULL COMMENT '用户上传的图片URL列表 (JSON数组)',
                                        `is_anonymous` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否匿名评论',
                                        `user_id` bigint(20) unsigned DEFAULT NULL COMMENT '评论用户ID (如果系统有用户体系)',
                                        `user_nickname` varchar(100) NOT NULL COMMENT '用户昵称',
                                        `user_avatar_url` varchar(512) DEFAULT NULL COMMENT '用户头像',
                                        `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '审核状态 (0:待审核, 1:审核通过, 2:审核拒绝)',
                                        `helpful_count` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '认为有帮助的人数',
                                        `unhelpful_count` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '点踩的人数',
                                        `reply_count` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '回复数量',
                                        `operator_id` bigint(20) unsigned DEFAULT NULL COMMENT '审核员ID',
                                        `audit_notes` varchar(255) DEFAULT NULL COMMENT '审核备注',
                                        `review_time` datetime NOT NULL COMMENT '评论提交时间',
                                        `primary_source` varchar(50) DEFAULT NULL COMMENT '主要数据来源 ( crawl/user)',
                                        `topic_tags` json DEFAULT NULL COMMENT '此评论关联的话题标签ID列表 ([1, 5, 12])',
                                        `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                        `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                        PRIMARY KEY (`id`),
                                        UNIQUE KEY `uk_review_id` (`review_id`),
                                        KEY `idx_sku_status_time` (`sku_id`,`status`,`review_time`),
                                        KEY `idx_spu_status_time` (`spu_id`,`status`,`review_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品用户评论表';

-- ----------------------------
-- Table structure for ranking_items
-- ----------------------------
DROP TABLE IF EXISTS `ranking_items`;
CREATE TABLE `ranking_items` (
                                 `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
                                 `list_id` bigint(20) unsigned NOT NULL COMMENT '关联到 ranking_lists.id, 表明属于哪个排行榜',
                                 `item_name` varchar(255) NOT NULL COMMENT '条目/实体官方名称 (如: Apple A17 Pro)',
                                 `item_brand` varchar(100) DEFAULT NULL COMMENT '条目所属品牌 (如: Apple)',
                                 `item_image_url` varchar(512) DEFAULT NULL COMMENT '条目的代表性图片URL (可选)',
                                 `score` decimal(10,2) NOT NULL COMMENT '该条目在此排行榜上的得分',
                                 `rank` int(10) unsigned DEFAULT NULL COMMENT '爬取时的静态排名 (可选)',
                                 `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                 PRIMARY KEY (`id`),
                                 UNIQUE KEY `uk_list_item` (`list_id`,`item_name`),
                                 KEY `idx_list_score` (`list_id`,`score`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='排行榜条目与分数表';

-- ----------------------------
-- Table structure for ranking_lists
-- ----------------------------
DROP TABLE IF EXISTS `ranking_lists`;
CREATE TABLE `ranking_lists` (
                                 `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
                                 `list_code` varchar(50) NOT NULL COMMENT '排行榜唯一编码 (如: nanoreview_soc, dxomark_display)',
                                 `list_name` varchar(255) NOT NULL COMMENT '排行榜显示名称 (如: Nanoreview 处理器排行榜)',
                                 `entity_type` varchar(50) NOT NULL COMMENT '此排行榜评测的实体类型 (如: SOC, DISPLAY)',
                                 `source_name` varchar(100) NOT NULL COMMENT '数据来源网站 (如: nanoreview.net)',
                                 `source_url` varchar(512) DEFAULT NULL COMMENT '排行榜原始URL',
                                 `is_active` tinyint(1) NOT NULL DEFAULT '1',
                                 PRIMARY KEY (`id`),
                                 UNIQUE KEY `uk_list_code` (`list_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='排行榜定义表';

-- ----------------------------
-- Table structure for review_interactions
-- ----------------------------
DROP TABLE IF EXISTS `review_interactions`;
CREATE TABLE `review_interactions` (
                                       `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
                                       `user_id` bigint(20) unsigned NOT NULL COMMENT '执行操作的用户ID',
                                       `target_type` varchar(50) NOT NULL COMMENT '被互动对象的类型 (UserReview, ExpertReview, Attribute)',
                                       `target_id` varchar(50) NOT NULL COMMENT '被互动对象的ID (对应各表的主键或业务ID)',
                                       `interaction_type` tinyint(4) NOT NULL COMMENT '互动类型 (1: 点赞/Helpful, 2: 点踩/Not Helpful)',
                                       `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                       PRIMARY KEY (`id`),
                                       UNIQUE KEY `uk_user_target_interaction` (`user_id`,`target_type`,`target_id`,`interaction_type`),
                                       KEY `idx_target` (`target_type`,`target_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='通用互动记录表 (点赞/点踩)';

-- ----------------------------
-- Table structure for review_tag_relations
-- ----------------------------
DROP TABLE IF EXISTS `review_tag_relations`;
CREATE TABLE `review_tag_relations` (
                                        `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
                                        `review_id` varchar(50) NOT NULL COMMENT '关联到 product_user_reviews.review_id',
                                        `tag_id` int(10) unsigned NOT NULL COMMENT '关联到 review_topic_tags.id',
                                        PRIMARY KEY (`id`),
                                        UNIQUE KEY `uk_review_tag` (`review_id`,`tag_id`),
                                        KEY `idx_tag_id` (`tag_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户评论与话题标签关联表';

-- ----------------------------
-- Table structure for review_topic_tags
-- ----------------------------
DROP TABLE IF EXISTS `review_topic_tags`;
CREATE TABLE `review_topic_tags` (
                                     `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
                                     `tag_name` varchar(100) NOT NULL COMMENT '标签显示名称 (如: Scratches, Screen, Price)',
                                     `category_id` bigint(20) unsigned NOT NULL COMMENT '此标签所属的商品分类ID',
                                     `keywords` text COMMENT '用于AI识别的关键词列表 (JSON或逗号分隔, ["scratch", "blemish", "scuff"])',
                                     `is_active` tinyint(1) NOT NULL DEFAULT '1',
                                     PRIMARY KEY (`id`),
                                     UNIQUE KEY `uk_tag_category` (`tag_name`,`category_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='评论话题标签定义表';

-- ----------------------------
-- Table structure for sys_dict
-- ----------------------------
DROP TABLE IF EXISTS `sys_dict`;
CREATE TABLE `sys_dict` (
                            `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '编号',
                            `type` varchar(100) NOT NULL COMMENT '类型',
                            `description` varchar(100) NOT NULL COMMENT '描述',
                            `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                            `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                            `remarks` varchar(255) DEFAULT NULL COMMENT '备注信息',
                            `del_flag` char(1) NOT NULL DEFAULT '0' COMMENT '逻辑删除标记（0：显示；1：隐藏）',
                            PRIMARY KEY (`id`) USING BTREE,
                            KEY `sys_dict_del_flag` (`del_flag`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=129 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='字典表';

-- ----------------------------
-- Table structure for sys_dict_value
-- ----------------------------
DROP TABLE IF EXISTS `sys_dict_value`;
CREATE TABLE `sys_dict_value` (
                                  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                  `dict_id` bigint(20) NOT NULL COMMENT '字典ID',
                                  `value` varchar(100) NOT NULL COMMENT '数据值',
                                  `label` varchar(100) NOT NULL COMMENT '标签名',
                                  `type` varchar(50) DEFAULT NULL COMMENT '类型',
                                  `description` varchar(500) DEFAULT NULL COMMENT '描述',
                                  `sort` int(11) DEFAULT '0' COMMENT '排序',
                                  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                  `remarks` varchar(500) DEFAULT NULL COMMENT '备注信息',
                                  `del_flag` varchar(1) NOT NULL DEFAULT '0' COMMENT '删除标记(0:正常 1:删除)',
                                  PRIMARY KEY (`id`,`dict_id`) USING BTREE,
                                  UNIQUE KEY `uk_dict_value` (`dict_id`,`value`),
                                  KEY `idx_dict_id` (`dict_id`),
                                  KEY `idx_value` (`value`),
                                  KEY `idx_label` (`label`),
                                  KEY `idx_type` (`type`),
                                  KEY `idx_sort` (`sort`),
                                  KEY `idx_del_flag` (`del_flag`),
                                  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COMMENT='字典项表';

-- ----------------------------
-- Table structure for tracking_events
-- ----------------------------
DROP TABLE IF EXISTS `tracking_events`;
CREATE TABLE `tracking_events` (
                                   `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
                                   `event_time` datetime(3) NOT NULL COMMENT '事件发生时间 (服务器时间戳，精确到毫秒)',
                                   `event_name` varchar(100) NOT NULL COMMENT '事件名称 (PageView, ProductClick)',
                                   `user_id` varchar(100) DEFAULT NULL COMMENT '已登录用户的唯一ID',
                                   `anonymous_id` varchar(100) NOT NULL COMMENT '未登录用户的唯一设备/浏览器ID',
                                   `platform` varchar(50) DEFAULT NULL COMMENT '来源平台 (WebApp, ChromeExtension)',
                                   `page_name` varchar(100) DEFAULT NULL COMMENT '页面名称 (Homepage, ProductDetailPage)',
                                   `page_url` varchar(2048) DEFAULT NULL COMMENT '页面URL',
                                   `sku_id` varchar(50) DEFAULT NULL,
                                   `spu_id` varchar(50) DEFAULT NULL,
                                   `brand_id` bigint(20) unsigned DEFAULT NULL,
                                   `category_id` bigint(20) unsigned DEFAULT NULL,
                                   `ip_address` varchar(45) DEFAULT NULL,
                                   `country` varchar(50) DEFAULT NULL,
                                   `province` varchar(50) DEFAULT NULL,
                                   `city` varchar(50) DEFAULT NULL,
                                   `os` varchar(50) DEFAULT NULL,
                                   `browser` varchar(50) DEFAULT NULL,
                                   `device_type` varchar(50) DEFAULT NULL COMMENT '设备类型 (Desktop, Mobile, Tablet)',
                                   `utm_source` varchar(100) DEFAULT NULL,
                                   `utm_medium` varchar(100) DEFAULT NULL,
                                   `utm_campaign` varchar(100) DEFAULT NULL,
                                   `properties` json DEFAULT NULL COMMENT '存储事件特有的、非结构化的属性',
                                   PRIMARY KEY (`id`,`event_time`),
                                   KEY `idx_event_time` (`event_time`),
                                   KEY `idx_user_lookup` (`anonymous_id`,`user_id`,`event_time`),
                                   KEY `idx_event_name_time` (`event_name`,`event_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
/*!50100 PARTITION BY RANGE (TO_DAYS(event_time))
(PARTITION p20250701 VALUES LESS THAN (739798) ENGINE = InnoDB,
 PARTITION p20250801 VALUES LESS THAN (739829) ENGINE = InnoDB,
 PARTITION pmax VALUES LESS THAN MAXVALUE ENGINE = InnoDB) */;

-- ----------------------------
-- Table structure for user
-- ----------------------------
DROP TABLE IF EXISTS `user`;
CREATE TABLE `user` (
                        `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
                        `username` varchar(50) NOT NULL COMMENT '用户名',
                        `nickname` varchar(255) DEFAULT NULL COMMENT '昵称',
                        `sex` tinyint(4) DEFAULT NULL COMMENT '性别',
                        `password` varchar(100) NOT NULL COMMENT '密码(加密存储)',
                        `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
                        `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
                        `avatar` varchar(255) DEFAULT NULL COMMENT '头像URL',
                        `gender` tinyint(4) DEFAULT '0' COMMENT '性别(0:未知 1:男 2:女)',
                        `birthday` date DEFAULT NULL COMMENT '生日',
                        `status` tinyint(4) DEFAULT '1' COMMENT '状态(0:禁用 1:正常)',
                        `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
                        `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
                        `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                        `access_token` varchar(255) DEFAULT NULL COMMENT '访问令牌',
                        `refresh_token` varchar(255) DEFAULT NULL COMMENT '刷新令牌',
                        `token_expire_time` datetime DEFAULT NULL COMMENT '令牌过期时间',
                        `refresh_token_expire_time` datetime DEFAULT NULL COMMENT '刷新令牌过期时间',
                        PRIMARY KEY (`id`),
                        UNIQUE KEY `uk_username` (`username`),
                        UNIQUE KEY `uk_phone` (`phone`),
                        UNIQUE KEY `uk_email` (`email`)
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

-- ----------------------------
-- Table structure for user_social_account
-- ----------------------------
DROP TABLE IF EXISTS `user_social_account`;
CREATE TABLE `user_social_account` (
                                       `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                       `user_id` bigint(20) NOT NULL COMMENT '用户ID',
                                       `social_type` tinyint(4) NOT NULL COMMENT '社交平台类型（1:Google, 2:Facebook, 3:Twitter, 4:LinkedIn, 5:GitHub, 6:Apple, 7:Microsoft, 8:Amazon）',
                                       `social_id` varchar(255) NOT NULL COMMENT '社交平台用户唯一标识',
                                       `social_name` varchar(255) DEFAULT NULL COMMENT '社交平台用户名或昵称',
                                       `social_avatar` varchar(1000) DEFAULT NULL COMMENT '社交平台用户头像',
                                       `social_email` varchar(255) DEFAULT NULL COMMENT '社交平台用户邮箱',
                                       `access_token` varchar(1000) DEFAULT NULL COMMENT '社交平台访问令牌',
                                       `token_expire_time` datetime DEFAULT NULL COMMENT '令牌过期时间',
                                       `refresh_token` varchar(1000) DEFAULT NULL COMMENT '刷新令牌',
                                       `extra_data` text COMMENT '额外数据（JSON格式）',
                                       `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                       `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                       PRIMARY KEY (`id`),
                                       UNIQUE KEY `uk_social_type_social_id` (`social_type`,`social_id`),
                                       KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COMMENT='用户社交账号关联表';

-- ----------------------------
-- Table structure for wishlist_item
-- ----------------------------
DROP TABLE IF EXISTS `wishlist_item`;
CREATE TABLE `wishlist_item` (
                                 `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
                                 `user_id` bigint(20) unsigned NOT NULL COMMENT '用户ID',
                                 `sku_id` bigint(20) unsigned NOT NULL COMMENT '收藏的商品SKU ID',
                                 `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '收藏时间',
                                 PRIMARY KEY (`id`),
                                 UNIQUE KEY `uk_user_sku` (`user_id`,`sku_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户商品收藏表';

SET FOREIGN_KEY_CHECKS = 1;

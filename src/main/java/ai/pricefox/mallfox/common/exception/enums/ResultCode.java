package ai.pricefox.mallfox.common.exception.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 系统响应状态
 *
 * <AUTHOR>
 * @since 2025/06/15
 */
@Getter
@AllArgsConstructor
public enum ResultCode {
    //=============通用状态码=============
    SUCCESS(200, "成功"),
    FAIL(500, "失败"),
    PARAM_INVALIDATE(400, "不合法参数"),
    TOKEN_VERIFY_FAIL(401, "token校验失败"),
    MESSAGE_NOT_READ(407, "消息不能读取"),
    METHOD_NOT_SUPPORTED(405, "不支持当前请求方法"),
    NOT_FOUND(404, "没找到请求"),
    ERROR(9999, "系统异常"),

    //=============业务状态码=============
    UNSUPPORTED_TASK_TYPE(1000, "不支持的任务类型"),



    ;
    private final Integer code;
    private final String message;

    public static ResultCode of(int code) {
        return Arrays.stream(values()).filter(e -> e.code == code).findFirst().orElse(ERROR);
    }

}

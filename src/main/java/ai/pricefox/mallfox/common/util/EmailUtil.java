package ai.pricefox.mallfox.common.util;

import ai.pricefox.mallfox.service.integration.MicrosoftGraphEmailService;
import jakarta.mail.MessagingException;
import jakarta.mail.internet.MimeMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Component;
import org.springframework.core.io.FileSystemResource;
import java.io.File;
import java.util.List;

/**
 * 邮件发送工具类
 * 优先使用 Microsoft Graph API，备用 SMTP
 */
@Slf4j
@Component
public class EmailUtil {

    private final MicrosoftGraphEmailService graphEmailService;
    private final JavaMailSender mailSender;
    private final String fromEmail;

    public EmailUtil(MicrosoftGraphEmailService graphEmailService,
                     JavaMailSender mailSender,
                     @Value("${spring.mail.username}") String fromEmail) {
        this.graphEmailService = graphEmailService;
        this.mailSender = mailSender;
        this.fromEmail = fromEmail;
    }

    /**
     * 发送简单文本邮件
     * 优先使用 Microsoft Graph API，失败时使用 SMTP 备用
     */
    public void sendTextMail(String to, String subject, String content) {
        try {
            // 优先使用 Microsoft Graph API
            boolean success = graphEmailService.sendTextEmail(to, subject, content);
            if (success) {
                log.info("Microsoft Graph 文本邮件发送成功: to={}, subject={}", to, subject);
                return;
            }

            // 备用 SMTP 方式
            log.warn("Microsoft Graph 发送失败，尝试 SMTP 备用方式");
            SimpleMailMessage message = new SimpleMailMessage();
            message.setFrom(fromEmail);
            message.setTo(to);
            message.setSubject(subject);
            message.setText(content);
            mailSender.send(message);
            log.info("SMTP 文本邮件发送成功: to={}, subject={}", to, subject);
        } catch (Exception e) {
            log.error("文本邮件发送失败: to={}, subject={}", to, subject, e);
            throw new RuntimeException("邮件发送失败", e);
        }
    }

    /**
     * 发送 HTML 邮件
     * 优先使用 Microsoft Graph API，失败时使用 SMTP 备用
     */
    public void sendHtmlMail(String to, String subject, String html) {
        try {
            // 优先使用 Microsoft Graph API
            boolean success = graphEmailService.sendHtmlEmail(to, subject, html);
            if (success) {
                log.info("Microsoft Graph HTML邮件发送成功: to={}, subject={}", to, subject);
                return;
            }

            // 备用 SMTP 方式
            log.warn("Microsoft Graph 发送失败，尝试 SMTP 备用方式");
            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");
            helper.setFrom(fromEmail);
            helper.setTo(to);
            helper.setSubject(subject);
            helper.setText(html, true);
            mailSender.send(message);
            log.info("SMTP HTML邮件发送成功: to={}, subject={}", to, subject);
        } catch (Exception e) {
            log.error("HTML邮件发送失败: to={}, subject={}", to, subject, e);
            throw new RuntimeException("邮件发送失败", e);
        }
    }

    /**
     * 发送带附件的邮件
     */
    public void sendAttachmentMail(String to, String subject, String content, String filePath) {
        try {
            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");
            helper.setFrom(fromEmail);
            helper.setTo(to);
            helper.setSubject(subject);
            helper.setText(content, true);

            FileSystemResource file = new FileSystemResource(new File(filePath));
            helper.addAttachment(file.getFilename(), file);

            mailSender.send(message);
            log.info("带附件邮件发送成功: to={}, subject={}, attachment={}", to, subject, filePath);
        } catch (MessagingException e) {
            log.error("带附件邮件发送失败: to={}, subject={}, attachment={}", to, subject, filePath, e);
            throw new RuntimeException("邮件发送失败", e);
        }
    }

    /**
     * 发送给多个收件人
     * 优先使用 Microsoft Graph API，失败时使用 SMTP 备用
     */
    public void sendBatchMail(List<String> toList, String subject, String content) {
        try {
            // 优先使用 Microsoft Graph API
            boolean success = graphEmailService.sendBatchEmail(toList, subject, content);
            if (success) {
                log.info("Microsoft Graph 批量邮件发送成功: toList={}, subject={}", toList, subject);
                return;
            }

            // 备用 SMTP 方式
            log.warn("Microsoft Graph 发送失败，尝试 SMTP 备用方式");
            SimpleMailMessage message = new SimpleMailMessage();
            message.setFrom(fromEmail);
            message.setTo(toList.toArray(new String[0]));
            message.setSubject(subject);
            message.setText(content);
            mailSender.send(message);
            log.info("SMTP 批量邮件发送成功: toList={}, subject={}", toList, subject);
        } catch (Exception e) {
            log.error("批量邮件发送失败: toList={}, subject={}", toList, subject, e);
            throw new RuntimeException("邮件发送失败", e);
        }
    }
}

package ai.pricefox.mallfox.common.util;

import ai.pricefox.mallfox.mapper.standard.StandardAttributeMapper;
import ai.pricefox.mallfox.mapper.standard.StandardAttributeValueMapper;
import ai.pricefox.mallfox.mapper.standard.StandardCategoryMapper;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @desc ID生成器
 * @since 2025/6/21
 */
@Component
@RequiredArgsConstructor
public class IdGenerator {

    private static final Logger log = LoggerFactory.getLogger(IdGenerator.class);

    private final StandardCategoryMapper standardCategoryMapper;
    private final StandardAttributeMapper standardAttributeMapper;
    private final StandardAttributeValueMapper standardAttributeValueMapper;

    /**
     * 生成SKU
     **/
    public static String generateSku() {
        String nextIdStr = IdUtil.getSnowflakeNextIdStr();
        nextIdStr = nextIdStr.length() > 11 ? nextIdStr.substring(nextIdStr.length() - 11) : nextIdStr;
        return "PK" + nextIdStr;
    }

    /**
     * 生成SPU
     **/
    public static String generateSpu() {
        String nextIdStr = IdUtil.getSnowflakeNextIdStr();
        nextIdStr = nextIdStr.length() > 8 ? nextIdStr.substring(nextIdStr.length() - 8) : nextIdStr;

        return "PP" + nextIdStr;
    }

    /**
     * 生成评论ID
     **/
    public static String generateReviewId() {
        String nextIdStr = IdUtil.getSnowflakeNextIdStr();
        nextIdStr = nextIdStr.length() > 13 ? nextIdStr.substring(nextIdStr.length() - 13) : nextIdStr;
        return "PR" + nextIdStr;
    }

    /**
     * 生成品类编码（自增方式）
     * 格式：CATG + 8位数字
     **/
    public String generateCategoryCode() {
        String maxCode = standardCategoryMapper.selectMaxCategoryCode();
        long nextNumber = 1;

        if (StrUtil.isNotBlank(maxCode) && maxCode.startsWith("CATG")) {
            try {
                String numberPart = maxCode.substring(4);
                nextNumber = Long.parseLong(numberPart) + 1;
            } catch (NumberFormatException e) {
                log.warn("解析最大分类编码失败: {}", maxCode, e);
                nextNumber = 1;
            }
        }

        return String.format("CATG%08d", nextNumber);
    }

    /**
     * 生成属性编码（自增方式）
     * 格式：ATTR + 8位数字
     **/
    public String generateAttributeCode() {
        String maxCode = standardAttributeMapper.selectMaxAttributeCode();
        long nextNumber = 1;

        if (StrUtil.isNotBlank(maxCode) && maxCode.startsWith("ATTR")) {
            try {
                String numberPart = maxCode.substring(4);
                nextNumber = Long.parseLong(numberPart) + 1;
            } catch (NumberFormatException e) {
                log.warn("解析最大属性编码失败: {}", maxCode, e);
                nextNumber = 1;
            }
        }

        return String.format("ATTR%08d", nextNumber);
    }

    /**
     * 生成属性值编码（自增方式）
     * 格式：ATVL + 8位数字
     **/
    public String generateAttributeValueCode() {
        String maxCode = standardAttributeValueMapper.selectMaxValueCode();
        long nextNumber = 1;

        if (StrUtil.isNotBlank(maxCode) && maxCode.startsWith("ATVL")) {
            try {
                String numberPart = maxCode.substring(4);
                nextNumber = Long.parseLong(numberPart) + 1;
            } catch (NumberFormatException e) {
                log.warn("解析最大属性值编码失败: {}", maxCode, e);
                nextNumber = 1;
            }
        }

        return String.format("ATVL%08d", nextNumber);
    }

    /**
     * 生成订单号
     **/
    public String generateOrderNo() {
        return "PO" + IdUtil.fastSimpleUUID();
    }

    // 提供一个更通用的方法
    public String generate(String prefix) {
        return prefix + IdUtil.fastSimpleUUID();
    }

}
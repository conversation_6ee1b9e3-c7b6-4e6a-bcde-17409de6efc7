package ai.pricefox.mallfox.common.util;

import com.alibaba.fastjson.JSONObject;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonSyntaxException;

public class JSONUtils {
    private static final Gson gson = new GsonBuilder()
            .setDateFormat("yyyy-MM-dd HH:mm:ss") // 设置日期格式
            .serializeNulls() // 序列化null值
            .create();
    public static<T> T convert(JSONObject bizParam, Class<T> targetClass) {
        return gson.fromJson(bizParam.toJSONString(), targetClass);
    }

    /**
     * JSON字符串 转 Java 对象
     * @param jsonString JSON字符串
     * @param targetClass 目标类
     * @return 转换后的Java对象
     * @param <T> 泛型类型
     */
    public static <T> T fromJson(String jsonString, Class<T> targetClass) {
        try {
            return gson.fromJson(jsonString, targetClass);
        } catch (JsonSyntaxException e) {
            throw new RuntimeException("JSON转换失败: " + e.getMessage(), e);
        }
    }

    /**
     * Java对象 转 JSONObject
     * @param object Java对象
     * @return JSONObject
     */
    public static JSONObject toJsonObject(Object object) {
        try {
            return JSONObject.parseObject(gson.toJson(object));
        } catch (Exception e) {
            throw new RuntimeException("对象转JSON失败: " + e.getMessage(), e);
        }
    }
}

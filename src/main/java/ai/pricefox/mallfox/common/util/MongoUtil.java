package ai.pricefox.mallfox.common.util;

import ai.pricefox.mallfox.domain.mongo.TestDocument;
import ai.pricefox.mallfox.repository.mongo.TestDocumentRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * MongoDB工具类
 * 提供统一的MongoDB操作方法
 * 
 * <AUTHOR>
 * @since 2025-07-26
 */
@Slf4j
@Component
public class MongoUtil {

    @Autowired
    private TestDocumentRepository testDocumentRepository;
    
    @Autowired
    private MongoTemplate mongoTemplate;

    // ========== 连接测试 ==========

    /**
     * 测试MongoDB连接
     */
    public String testConnection() {
        try {
            long count = testDocumentRepository.count();
            String dbName = mongoTemplate.getDb().getName();
            
            log.info("MongoDB连接测试成功 - 数据库: {}, 文档数量: {}", dbName, count);
            return String.format("MongoDB连接成功！数据库: %s, test_documents集合中共有 %d 个文档", dbName, count);
        } catch (Exception e) {
            log.error("MongoDB连接测试失败", e);
            return "MongoDB连接失败: " + e.getMessage();
        }
    }

    // ========== 文档创建操作 ==========

    /**
     * 创建测试文档
     */
    public TestDocument createTestDocument(String name, String description, String creator) {
        TestDocument document = new TestDocument();
        document.setName(name);
        document.setDescription(description);
        document.setStatus("ACTIVE");
        document.setCreator(creator);
        document.setCreateTime(LocalDateTime.now());
        document.setUpdateTime(LocalDateTime.now());
        document.setValue(Math.random() * 100);
        document.setTags("test,mongodb,demo");
        document.setRemarks("测试文档 - " + LocalDateTime.now());

        return testDocumentRepository.save(document);
    }

    /**
     * 批量创建测试数据
     */
    public List<TestDocument> createBatchTestData(int count) {
        List<TestDocument> documents = new java.util.ArrayList<>();
        
        for (int i = 1; i <= count; i++) {
            TestDocument document = new TestDocument();
            document.setName("测试文档-" + i);
            document.setDescription("这是第" + i + "个测试文档");
            document.setStatus(i % 3 == 0 ? "INACTIVE" : "ACTIVE");
            document.setCreator("system");
            document.setCreateTime(LocalDateTime.now().minusDays(i % 30));
            document.setUpdateTime(LocalDateTime.now());
            document.setValue(Math.random() * 1000);
            document.setTags("test,batch,doc-" + i);
            document.setRemarks("批量创建的测试文档 #" + i);
            
            documents.add(document);
        }
        
        return testDocumentRepository.saveAll(documents);
    }

    // ========== 文档查询操作 ==========

    /**
     * 根据ID查找文档
     */
    public Optional<TestDocument> findById(String id) {
        return testDocumentRepository.findById(id);
    }

    /**
     * 根据名称查找文档
     */
    public Optional<TestDocument> findByName(String name) {
        return testDocumentRepository.findByName(name);
    }

    /**
     * 查找所有文档（分页）
     */
    public Page<TestDocument> findAllDocuments(int page, int size) {
        Pageable pageable = PageRequest.of(page, size, Sort.by("createTime").descending());
        return testDocumentRepository.findAll(pageable);
    }

    /**
     * 查找所有文档
     */
    public List<TestDocument> findAllDocuments() {
        return testDocumentRepository.findAll(Sort.by("createTime").descending());
    }

    /**
     * 根据状态查找文档
     */
    public List<TestDocument> findByStatus(String status) {
        return testDocumentRepository.findByStatus(status);
    }

    /**
     * 根据创建者查找文档
     */
    public List<TestDocument> findByCreator(String creator) {
        return testDocumentRepository.findByCreator(creator);
    }

    /**
     * 模糊查询文档名称
     */
    public List<TestDocument> searchByName(String name) {
        return testDocumentRepository.findByNameContaining(name);
    }

    // ========== 文档更新操作 ==========

    /**
     * 更新文档
     */
    public Optional<TestDocument> updateDocument(String id, String name, String description) {
        Optional<TestDocument> optionalDoc = testDocumentRepository.findById(id);
        if (optionalDoc.isPresent()) {
            TestDocument document = optionalDoc.get();
            document.setName(name);
            document.setDescription(description);
            document.setUpdateTime(LocalDateTime.now());
            return Optional.of(testDocumentRepository.save(document));
        }
        return Optional.empty();
    }

    // ========== 文档删除操作 ==========

    /**
     * 删除文档
     */
    public boolean deleteDocument(String id) {
        if (testDocumentRepository.existsById(id)) {
            testDocumentRepository.deleteById(id);
            return true;
        }
        return false;
    }

    /**
     * 清空所有测试文档
     */
    public long clearAllDocuments() {
        long count = testDocumentRepository.count();
        testDocumentRepository.deleteAll();
        return count;
    }

    // ========== 统计操作 ==========

    /**
     * 统计文档数量
     */
    public long countDocuments() {
        return testDocumentRepository.count();
    }

    /**
     * 根据状态统计文档数量
     */
    public long countByStatus(String status) {
        return testDocumentRepository.countByStatus(status);
    }

    // ========== 通用MongoDB操作 ==========

    /**
     * 获取数据库名称
     */
    public String getDatabaseName() {
        return mongoTemplate.getDb().getName();
    }

    /**
     * 检查集合是否存在
     */
    public boolean collectionExists(String collectionName) {
        return mongoTemplate.collectionExists(collectionName);
    }

    /**
     * 获取集合中的文档总数
     */
    public long getCollectionCount(String collectionName) {
        return mongoTemplate.getCollection(collectionName).countDocuments();
    }
}
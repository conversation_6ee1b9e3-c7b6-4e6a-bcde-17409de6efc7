package ai.pricefox.mallfox.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

/**
 * API 日志切面
 * 记录 Controller 方法的入参和返回值
 */
@Slf4j
@Aspect
@Component
@AllArgsConstructor
public class ApiLoggingAspect {

    private final LoggingProperties loggingProperties;
    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 环绕通知：记录 Controller 方法的执行
     */
    @Around("execution(* ai.pricefox.mallfox.controller..*(..))")
    public Object logApiExecution(ProceedingJoinPoint joinPoint) throws Throwable {
        // 检查是否启用API日志
        if (!loggingProperties.isEnableApiLogging()) {
            return joinPoint.proceed();
        }

        String className = joinPoint.getTarget().getClass().getSimpleName();
        String methodName = joinPoint.getSignature().getName();
        Object[] args = joinPoint.getArgs();

        // 记录方法调用开始
        log.info("========== API方法调用开始 ==========");
        log.info("类名: {}", className);
        log.info("方法名: {}", methodName);
        
        // 记录入参（过滤掉 HttpServletRequest、HttpServletResponse 等对象）
        if (args != null && args.length > 0) {
            for (int i = 0; i < args.length; i++) {
                Object arg = args[i];
                if (arg != null && isLoggableParameter(arg)) {
                    try {
                        log.info("参数[{}]: {}", i, objectMapper.writeValueAsString(arg));
                    } catch (Exception e) {
                        log.info("参数[{}]: {} (序列化失败: {})", i, arg.toString(), e.getMessage());
                    }
                }
            }
        }

        long startTime = System.currentTimeMillis();
        Object result = null;
        Exception exception = null;

        try {
            // 执行目标方法
            result = joinPoint.proceed();
            return result;
        } catch (Exception e) {
            exception = e;
            throw e;
        } finally {
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;

            // 记录方法调用结束
            log.info("========== API方法调用结束 ==========");
            log.info("类名: {}", className);
            log.info("方法名: {}", methodName);
            log.info("执行时间: {}ms", duration);

            // 记录返回值
            if (loggingProperties.isLogResponseBody() && exception == null && result != null) {
                try {
                    String responseJson = objectMapper.writeValueAsString(result);
                    // 限制响应体长度
                    if (responseJson.length() > loggingProperties.getMaxResponseBodyLength()) {
                        responseJson = responseJson.substring(0, loggingProperties.getMaxResponseBodyLength()) + "...(truncated)";
                    }
                    log.info("返回值: {}", responseJson);
                } catch (Exception e) {
                    log.info("返回值: {} (序列化失败: {})", result.toString(), e.getMessage());
                }
            }

            // 记录异常
            if (exception != null) {
                log.error("方法执行异常: {}", exception.getMessage());
            }
        }
    }

    /**
     * 判断参数是否需要记录日志
     */
    private boolean isLoggableParameter(Object arg) {
        String className = arg.getClass().getName();
        
        // 排除 Servlet 相关对象
        if (className.startsWith("jakarta.servlet.") || 
            className.startsWith("javax.servlet.") ||
            className.startsWith("org.springframework.web.") ||
            className.startsWith("org.springframework.ui.")) {
            return false;
        }

        // 排除大型对象
        if (className.startsWith("org.springframework.") ||
            className.startsWith("java.lang.Class") ||
            className.startsWith("java.lang.reflect.")) {
            return false;
        }

        return true;
    }
}

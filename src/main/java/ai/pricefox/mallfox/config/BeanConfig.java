package ai.pricefox.mallfox.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

import java.net.InetSocketAddress;
import java.net.Proxy;

@Configuration
public class BeanConfig {

    //redis
    @Bean
    public RedisTemplate<String, Object> redisTemplate(RedisConnectionFactory redisConnectionFactory) {
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        template.setConnectionFactory(redisConnectionFactory);
        // 可以在这里配置序列化器等
        return template;
    }

    //请求相关
    @Bean
    public RestTemplate restTemplate() {
        // 配置代理（如果需要）
        SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
        
        // 设置超时
        factory.setConnectTimeout(10000); // 10秒
        factory.setReadTimeout(30000);    // 30秒
        
        // 暂时禁用代理
        // factory.setProxy(new Proxy(
        //     Proxy.Type.HTTP, 
        //     new InetSocketAddress("127.0.0.1", 7890)
        // ));
        
        return new RestTemplate(factory);
    }

}

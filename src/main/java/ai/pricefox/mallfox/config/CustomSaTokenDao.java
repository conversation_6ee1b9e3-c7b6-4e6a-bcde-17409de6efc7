package ai.pricefox.mallfox.config;

import ai.pricefox.mallfox.service.auth.TokenService;
import cn.dev33.satoken.dao.SaTokenDao;
import cn.dev33.satoken.session.SaSession;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * 自定义SaToken数据持久化接口
 * 集成Redis缓存和数据库持久化
 */
@Slf4j
@Component
@AllArgsConstructor
public class CustomSaTokenDao implements SaTokenDao {

    private final RedisTemplate<String, Object> redisTemplate;
    private final TokenService tokenService;

    @Override
    public String get(String key) {
        Object value = redisTemplate.opsForValue().get(key);
        return value != null ? value.toString() : null;
    }

    @Override
    public void set(String key, String value, long timeout) {
        if (timeout == SaTokenDao.NEVER_EXPIRE) {
            redisTemplate.opsForValue().set(key, value);
        } else {
            redisTemplate.opsForValue().set(key, value, timeout, TimeUnit.SECONDS);
        }
    }

    @Override
    public void update(String key, String value) {
        // 获取原有的过期时间
        Long expire = redisTemplate.getExpire(key, TimeUnit.SECONDS);
        if (expire != null && expire > 0) {
            redisTemplate.opsForValue().set(key, value, expire, TimeUnit.SECONDS);
        } else {
            redisTemplate.opsForValue().set(key, value);
        }
    }

    @Override
    public void delete(String key) {
        redisTemplate.delete(key);
    }

    @Override
    public long getTimeout(String key) {
        Long expire = redisTemplate.getExpire(key, TimeUnit.SECONDS);
        return expire != null ? expire : SaTokenDao.NOT_VALUE_EXPIRE;
    }

    @Override
    public void updateTimeout(String key, long timeout) {
        if (timeout == SaTokenDao.NEVER_EXPIRE) {
            // 移除过期时间
            redisTemplate.persist(key);
        } else {
            redisTemplate.expire(key, Duration.ofSeconds(timeout));
        }
    }

    @Override
    public Object getObject(String key) {
        return redisTemplate.opsForValue().get(key);
    }

    @Override
    public <T> T getObject(String s, Class<T> aClass) {
        return null;
    }

    @Override
    public void setObject(String key, Object object, long timeout) {
        if (timeout == SaTokenDao.NEVER_EXPIRE) {
            redisTemplate.opsForValue().set(key, object);
        } else {
            redisTemplate.opsForValue().set(key, object, timeout, TimeUnit.SECONDS);
        }
    }

    @Override
    public void updateObject(String key, Object object) {
        // 获取原有的过期时间
        Long expire = redisTemplate.getExpire(key, TimeUnit.SECONDS);
        if (expire != null && expire > 0) {
            redisTemplate.opsForValue().set(key, object, expire, TimeUnit.SECONDS);
        } else {
            redisTemplate.opsForValue().set(key, object);
        }
    }

    @Override
    public void deleteObject(String key) {
        redisTemplate.delete(key);
    }

    @Override
    public long getObjectTimeout(String key) {
        Long expire = redisTemplate.getExpire(key, TimeUnit.SECONDS);
        return expire != null ? expire : SaTokenDao.NOT_VALUE_EXPIRE;
    }

    @Override
    public void updateObjectTimeout(String key, long timeout) {
        if (timeout == SaTokenDao.NEVER_EXPIRE) {
            redisTemplate.persist(key);
        } else {
            redisTemplate.expire(key, Duration.ofSeconds(timeout));
        }
    }

    @Override
    public SaSession getSession(String s) {
        return null;
    }

    @Override
    public void setSession(SaSession saSession, long l) {

    }

    @Override
    public void updateSession(SaSession saSession) {

    }

    @Override
    public void deleteSession(String s) {

    }

    @Override
    public long getSessionTimeout(String s) {
        return 0;
    }

    @Override
    public void updateSessionTimeout(String s, long l) {

    }

    @Override
    public List<String> searchData(String prefix, String keyword, int start, int size, boolean sortType) {
        // 使用Redis的SCAN命令搜索键
        Set<String> keys = redisTemplate.keys(prefix + "*" + keyword + "*");
        return keys != null ? keys.stream().toList() : List.of();
    }
}

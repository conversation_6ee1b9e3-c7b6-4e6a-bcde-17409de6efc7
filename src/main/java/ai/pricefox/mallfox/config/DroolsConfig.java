package ai.pricefox.mallfox.config;

import lombok.extern.slf4j.Slf4j;
import org.kie.api.KieServices;
import org.kie.api.builder.*;
import org.kie.api.runtime.KieContainer;
import org.kie.internal.io.ResourceFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;

import java.io.IOException;

@Slf4j
@Configuration
public class DroolsConfig {

    private static final String RULES_PATH = "ai/pricefox/mallfox/rules/";
    private static final String RULES_CLASSPATH = "classpath*:" + RULES_PATH + "**/*.drl";

    @Bean
    public KieContainer kieContainer() throws IOException {
        KieServices kieServices = KieServices.Factory.get();

        // 添加重要日志输出
        logClasspathResources();

        try {
            log.info("尝试使用类路径容器创建KieContainer...");
            KieContainer container = kieServices.getKieClasspathContainer();
            log.info("✅ 使用类路径容器创建KieContainer成功");
            verifyContainer(container);
            return container;
        } catch (Exception e) {
            log.error("❌ 类路径容器创建失败，尝试手动构建: {}", e.getMessage(), e);
            return buildKieContainerManually(kieServices);
        }
    }

    private KieContainer buildKieContainerManually(KieServices kieServices) throws IOException {
        log.info("🛠️ 尝试手动构建KieContainer...");

        KieFileSystem kieFileSystem = kieServices.newKieFileSystem();
        loadRulesFromResources(kieFileSystem);

        KieBuilder kieBuilder = kieServices.newKieBuilder(kieFileSystem);
        kieBuilder.buildAll();

        Results results = kieBuilder.getResults();
        if (results.hasMessages(Message.Level.ERROR)) {
            log.error("❌ Drools规则编译错误:");
            results.getMessages().forEach(msg -> log.error(">>> {}", msg));
            throw new RuntimeException("Drools规则编译失败");
        }

        KieModule kieModule = kieBuilder.getKieModule();
        KieContainer container = kieServices.newKieContainer(kieModule.getReleaseId());

        log.info("✅ 手动构建KieContainer成功");
        verifyContainer(container);
        return container;
    }

    private void loadRulesFromResources(KieFileSystem kieFileSystem) throws IOException {
        ResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
        Resource[] resources = resolver.getResources(RULES_CLASSPATH);

        log.info("🔍 找到 {} 个规则文件", resources.length);

        for (Resource resource : resources) {
            String resourcePath = RULES_PATH + resource.getFilename();
            log.info("📄 加载规则: {}", resourcePath);

            // 修复：使用正确的资源路径
            kieFileSystem.write(ResourceFactory.newClassPathResource(
                    resourcePath,
                    "UTF-8"
            ));
        }
    }

    private void verifyContainer(KieContainer container) {
        log.info("🔎 验证KieContainer...");
        log.info("🔧 可用的KieBase: {}", container.getKieBaseNames());

        if (container.getKieBaseNames().isEmpty()) {
            log.warn("⚠️ 警告: 没有找到任何规则文件!");
            log.warn("可能原因:");
            log.warn("1. kmodule.xml 位置错误 (应在 META-INF 目录)");
            log.warn("2. 规则文件路径配置错误");
            log.warn("3. 规则文件包声明与kmodule配置不匹配");
        } else {
            for (String kbaseName : container.getKieBaseNames()) {
                log.info("🔨 KieBase '{}' 中的会话: {}",
                        kbaseName, container.getKieSessionNamesInKieBase(kbaseName));
            }
        }
    }

    // 新增：打印类路径资源位置
    private void logClasspathResources() {
        try {
            log.info("🔍 检查类路径资源位置:");

            ResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();

            // 检查 kmodule.xml
            Resource[] kmodules = resolver.getResources("classpath*:META-INF/kmodule.xml");
            log.info("📍 找到 {} 个 kmodule.xml 文件", kmodules.length);
            for (Resource res : kmodules) {
                log.info("   - {}", res.getURI());
            }

            // 检查规则文件
            Resource[] ruleFiles = resolver.getResources(RULES_CLASSPATH);
            log.info("📍 找到 {} 个规则文件", ruleFiles.length);
            for (Resource res : ruleFiles) {
                log.info("   - {}", res.getURI());
            }
        } catch (IOException e) {
            log.error("❌ 资源检查失败: {}", e.getMessage(), e);
        }
    }
}
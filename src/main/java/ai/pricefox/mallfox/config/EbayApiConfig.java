package ai.pricefox.mallfox.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * eBay API 配置
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "ebay.api")
public class EbayApiConfig {

    /**
     * eBay 客户端 ID
     */
    private String clientId = "yunfeiwa-i-SBX-19e7df762-4ba94645";

    /**
     * eBay 客户端密钥
     */
    private String clientSecret = "SBX-9e7df7624b98-43b7-41de-95a8-fd90";

    /**
     * eBay API 基础 URL
     */
    private String baseUrl = "https://api.ebay.com/buy/browse/v1";

    /**
     * eBay 沙箱 客户端 ID
     */
    private String sandboxClientId = "yunfeiwa-i-SBX-19e7df762-4ba94645";

    /**
     * eBay 沙箱 客户端密钥
     */
    private String sandboxClientSecret = "SBX-9e7df7624b98-43b7-41de-95a8-fd90";

    /**
     * eBay 沙箱 API 基础 URL
     */
    private String sandboxBaseUrl = "https://api.sandbox.ebay.com/buy/browse/v1";

    /**
     * OAuth 授权范围
     */
    private String scope = "https://api.ebay.com/oauth/api_scope";

    /**
     * 是否使用沙箱环境
     */
    private boolean sandbox = true;

    /**
     * 是否启用模拟模式
     */
    private boolean mockMode = false;

    /**
     * 请求超时时间（毫秒）
     */
    private int timeout = 30000;

    /**
     * 重试次数
     */
    private int maxRetries = 3;

    /**
     * 重试间隔（毫秒）
     */
    private int retryDelay = 1000;

    /**
     * 获取当前环境的基础 URL
     */
    public String getCurrentBaseUrl() {
        return sandbox ? sandboxBaseUrl : baseUrl;
    }

    /**
     * 获取当前环境的客户 ID
     */
    public String getCurrentClientId() {
        return sandbox ? sandboxClientId : clientId;
    }

    /**
     * 获取当前环境的客户密钥
     */
    public String getCurrentClientSecret() {
        return sandbox ? sandboxClientSecret : clientSecret;
    }

}

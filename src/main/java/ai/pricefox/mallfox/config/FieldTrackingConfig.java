package ai.pricefox.mallfox.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.Set;

/**
 * 字段追踪配置
 * 
 * <AUTHOR>
 * @since 2025-01-14
 */
@ConfigurationProperties(prefix = "field.tracking")
@Component
@Data
public class FieldTrackingConfig {
    /**
     * 总开关
     */
    private Boolean enabled = true;

    /**
     * 异步处理开关
     */
    private Boolean asyncEnabled = true;

    /**
     * 批量处理大小
     */
    private Integer batchSize = 1000;

    /**
     * 队列容量
     */
    private Integer queueCapacity = 10000;

    /**
     * 处理间隔(ms)
     */
    private Integer processInterval = 5000;

    /**
     * 排除字段
     */
    private Set<String> excludeFields = new HashSet<String>() {{
        add("id");
        add("createTime");
        add("updateTime");
    }};

    /**
     * 包含表名
     */
    private Set<String> includeTables = new HashSet<String>() {{
        add("product_data_offers");
        add("product_data_simplify");
    }};

    /**
     * 判断是否应该追踪该表
     */
    public boolean shouldTrackTable(String tableName) {
        return enabled && (includeTables.isEmpty() || includeTables.contains(tableName));
    }

    /**
     * 判断是否应该追踪该字段
     */
    public boolean shouldTrackField(String tableName, String fieldName) {
        if (!shouldTrackTable(tableName)) {
            return false;
        }
        return !excludeFields.contains(fieldName);
    }
}

package ai.pricefox.mallfox.config;

import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalTimeSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.ZonedDateTimeSerializer;
import org.springframework.boot.autoconfigure.jackson.Jackson2ObjectMapperBuilderCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.time.format.DateTimeFormatter;
import java.util.TimeZone;

@Configuration
public class JacksonConfig {

    // 定义多种常见日期时间格式
    private static final String DATE_FORMAT = "yyyy-MM-dd";
    private static final String TIME_FORMAT = "HH:mm:ss";
    private static final String DATE_TIME_FORMAT = "yyyy-MM-dd HH:mm:ss";
    private static final String ISO_DATE_TIME_FORMAT = "yyyy-MM-dd'T'HH:mm:ss";
    private static final String ZONED_DATE_TIME_FORMAT = "yyyy-MM-dd HH:mm:ssXXX";

    @Bean
    public Jackson2ObjectMapperBuilderCustomizer jsonCustomizer() {
        return builder -> {
            // 设置默认时区（根据项目需求调整）
            builder.timeZone(TimeZone.getDefault());

            // 配置各种日期时间类型的序列化和反序列化格式
            builder.serializers(
                    new LocalDateSerializer(DateTimeFormatter.ofPattern(DATE_FORMAT)),
                    new LocalTimeSerializer(DateTimeFormatter.ofPattern(TIME_FORMAT)),
                    new LocalDateTimeSerializer(DateTimeFormatter.ofPattern(DATE_TIME_FORMAT)),
                    new ZonedDateTimeSerializer(DateTimeFormatter.ofPattern(ZONED_DATE_TIME_FORMAT))
            );

            builder.deserializers(
                    new LocalDateDeserializer(DateTimeFormatter.ofPattern(DATE_FORMAT)),
                    new LocalTimeDeserializer(DateTimeFormatter.ofPattern(TIME_FORMAT)),
                    new LocalDateTimeDeserializer(DateTimeFormatter.ofPattern(DATE_TIME_FORMAT))
//                    new ZonedDateTimeDeserializer(DateTimeFormatter.ofPattern(ZONED_DATE_TIME_FORMAT))
            );

            // 同时支持ISO格式和自定义格式
            builder.simpleDateFormat(DATE_TIME_FORMAT);

            // 对于可能接收多种格式的情况，可以添加更多格式支持
            builder.featuresToEnable(
                    com.fasterxml.jackson.databind.DeserializationFeature.ADJUST_DATES_TO_CONTEXT_TIME_ZONE,
                    com.fasterxml.jackson.databind.DeserializationFeature.ACCEPT_SINGLE_VALUE_AS_ARRAY
            );
        };
    }
}
package ai.pricefox.mallfox.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 日志配置属性
 */
@Data
@Component
@ConfigurationProperties(prefix = "app.logging")
public class LoggingProperties {

    /**
     * 是否启用请求日志
     */
    private boolean enableRequestLogging = true;

    /**
     * 是否启用API方法日志
     */
    private boolean enableApiLogging = true;

    /**
     * 是否启用SQL日志
     */
    private boolean enableSqlLogging = true;

    /**
     * 是否记录请求头
     */
    private boolean logRequestHeaders = true;

    /**
     * 是否记录请求参数
     */
    private boolean logRequestParameters = true;

    /**
     * 是否记录请求体
     */
    private boolean logRequestBody = true;

    /**
     * 是否记录响应体
     */
    private boolean logResponseBody = true;

    /**
     * 请求体最大记录长度（字符数）
     */
    private int maxRequestBodyLength = 1000;

    /**
     * 响应体最大记录长度（字符数）
     */
    private int maxResponseBodyLength = 1000;
}

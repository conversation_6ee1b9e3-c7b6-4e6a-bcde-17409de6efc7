package ai.pricefox.mallfox.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.JavaMailSenderImpl;

/**
 * 邮件配置类
 *
 * 注意：Spring Boot 3.x 会自动配置 JavaMailSender，
 * 只需要在 application.properties1 中配置 spring.mail.* 属性即可。
 * 这个配置类保留用于将来可能的自定义配置。
 */
@Configuration
public class MailConfig {

    @Bean
    public JavaMailSender javaMailSender() {
        return new JavaMailSenderImpl();
    }
}
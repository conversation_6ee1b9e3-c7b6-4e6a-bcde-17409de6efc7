package ai.pricefox.mallfox.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;

/**
 * Microsoft Graph API 配置
 */
@Configuration
public class MicrosoftGraphConfig {

    @Bean
    public WebClient microsoftGraphWebClient() {
        return WebClient.builder()
                .baseUrl("https://graph.microsoft.com/v1.0")
                .build();
    }

    /**
     * Microsoft Graph 配置属性
     */
    @Data
    @Component
    @ConfigurationProperties(prefix = "microsoft.graph")
    public static class MicrosoftGraphProperties {
        private String clientId;
        private String tenantId;
        private String clientSecret;
        private String senderEmail;
    }
}

package ai.pricefox.mallfox.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.index.IndexResolver;
import org.springframework.data.mongodb.core.index.MongoPersistentEntityIndexResolver;
import org.springframework.data.mongodb.core.mapping.MongoMappingContext;
import org.springframework.data.mongodb.core.mapping.event.ValidatingMongoEventListener;
import org.springframework.validation.beanvalidation.LocalValidatorFactoryBean;

/**
 * MongoDB配置类
 * 用于配置MongoDB相关设置，包括索引自动创建等
 */
@Configuration
public class MongoConfig {

    /**
     * 创建索引解析器，用于自动创建MongoDB索引
     *
     * @param mongoTemplate MongoDB模板
     * @param mappingContext MongoDB映射上下文
     * @return 索引解析器
     */
    @Bean
    public IndexResolver indexResolver(MongoTemplate mongoTemplate, MongoMappingContext mappingContext) {
        return new MongoPersistentEntityIndexResolver(mappingContext);
    }

    /**
     * 创建MongoDB验证事件监听器
     *
     * @return 验证事件监听器
     */
    @Bean
    public ValidatingMongoEventListener validatingMongoEventListener() {
        return new ValidatingMongoEventListener(validator());
    }

    /**
     * 创建本地验证工厂Bean
     *
     * @return 本地验证工厂Bean
     */
    @Bean
    public LocalValidatorFactoryBean validator() {
        return new LocalValidatorFactoryBean();
    }
}
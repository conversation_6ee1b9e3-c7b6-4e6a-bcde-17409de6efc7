package ai.pricefox.mallfox.config;

import ai.pricefox.mallfox.config.handler.FastJsonTypeHandler;
import ai.pricefox.mallfox.config.handler.CustomJacksonTypeHandler;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.autoconfigure.ConfigurationCustomizer;
import com.baomidou.mybatisplus.autoconfigure.MybatisPlusPropertiesCustomizer;
import com.baomidou.mybatisplus.core.config.GlobalConfig;
import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * mybatis plus 配置类
 *
 * <AUTHOR>
 * @since 2025/3/25
 */
@Configuration
@ConditionalOnClass({MybatisPlusInterceptor.class})
public class MyBatisPlusConfig {

    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        // 分页插件
        interceptor.addInnerInterceptor(new PaginationInnerInterceptor(DbType.MYSQL));
        return interceptor;
    }

    @Bean
    public ConfigurationCustomizer configurationCustomizer() {
        return configuration -> {
            // 注册全局 TypeHandler
            configuration.getTypeHandlerRegistry().register(JSONObject.class, new FastJsonTypeHandler());
            // 注册自定义Jackson TypeHandler（支持LocalDateTime）
            configuration.getTypeHandlerRegistry().register(java.util.Map.class, CustomJacksonTypeHandler.class);
            configuration.getTypeHandlerRegistry().register(Object.class, CustomJacksonTypeHandler.class);
        };
    }

    @Bean
    public MybatisPlusPropertiesCustomizer mybatisPlusPropertiesCustomizer() {
        return properties -> {
            GlobalConfig globalConfig = properties.getGlobalConfig();
            globalConfig.setDbConfig(new GlobalConfig.DbConfig().setInsertStrategy(FieldStrategy.NOT_EMPTY));
        };
    }

    @Component
    static
    class MetaObjectHandlerImpl implements MetaObjectHandler {

        @Override
        public void insertFill(MetaObject metaObject) {
            if (metaObject.hasSetter("createdAt")) {
                this.strictInsertFill(metaObject, "createdAt", LocalDateTime::now, LocalDateTime.class);
            }
            if (metaObject.hasSetter("updatedAt")) {
                this.strictInsertFill(metaObject, "updatedAt", LocalDateTime::now, LocalDateTime.class);
            }
        }

        @Override
        public void updateFill(MetaObject metaObject) {
            if (metaObject.hasSetter("updatedAt")) {
                this.strictUpdateFill(metaObject, "updatedAt", LocalDateTime::now, LocalDateTime.class);
            }
        }
    }


}

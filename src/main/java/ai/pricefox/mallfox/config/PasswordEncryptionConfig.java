package ai.pricefox.mallfox.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 密码加密配置
 */
@Data
@Component
@ConfigurationProperties(prefix = "app.password")
public class PasswordEncryptionConfig {

    /**
     * 是否启用密码加密
     */
    private boolean enableEncryption = true;

    /**
     * AES密钥
     */
    private String secretKey = "PriceFoxMallKey!";

    /**
     * 是否在日志中显示加密过程
     */
    private boolean logEncryption = false;

    /**
     * 密码最小长度
     */
    private int minLength = 6;

    /**
     * 密码最大长度
     */
    private int maxLength = 20;
}

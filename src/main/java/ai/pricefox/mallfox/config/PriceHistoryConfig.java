package ai.pricefox.mallfox.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 价格历史记录配置
 * 
 * <AUTHOR>
 * @since 2025-07-12
 */
@Component
@ConfigurationProperties(prefix = "price.history")
@Data
public class PriceHistoryConfig {
    
    /**
     * 是否启用价格历史记录
     */
    private Boolean enabled = true;
    
    /**
     * 是否启用异步处理
     */
    private Boolean asyncEnabled = true;
    
    /**
     * 批量处理大小
     */
    private Integer batchSize = 500;
    
    /**
     * 历史数据保留天数
     */
    private Integer retentionDays = 365;
    
    /**
     * 是否启用自动清理
     */
    private Boolean cleanupEnabled = true;
    
    /**
     * 清理间隔（毫秒）
     */
    private Long cleanupInterval = 86400000L; // 24小时
    
    /**
     * 是否记录新增操作
     */
    private Boolean recordCreate = true;
    
    /**
     * 是否记录更新操作
     */
    private Boolean recordUpdate = true;
    
    /**
     * 是否记录删除操作
     */
    private Boolean recordDelete = true;
    
    /**
     * 价格变化的最小阈值（元），小于此值的变化不记录
     */
    private Double minimumChangeThreshold = 0.01;
    
    /**
     * 是否启用调试日志
     */
    private Boolean debugLogEnabled = false;
}
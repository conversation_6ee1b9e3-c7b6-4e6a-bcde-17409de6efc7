package ai.pricefox.mallfox.config;

import jakarta.servlet.ReadListener;
import jakarta.servlet.ServletInputStream;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletRequestWrapper;
import lombok.extern.slf4j.Slf4j;

import java.io.*;
import java.nio.charset.StandardCharsets;

/**
 * 可重复读取的HttpServletRequest包装器
 */
@Slf4j
public class RepeatableReadHttpServletRequest extends HttpServletRequestWrapper {

    private final byte[] body;

    public RepeatableReadHttpServletRequest(HttpServletRequest request) {
        super(request);
        this.body = getBodyString(request).getBytes(StandardCharsets.UTF_8);
    }

    /**
     * 获取请求体字符串
     */
    public String getBodyString() {
        return new String(body, StandardCharsets.UTF_8);
    }

    @Override
    public BufferedReader getReader() throws IOException {
        return new BufferedReader(new InputStreamReader(getInputStream()));
    }

    @Override
    public ServletInputStream getInputStream() throws IOException {
        final ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(body);
        
        return new ServletInputStream() {
            @Override
            public boolean isFinished() {
                return byteArrayInputStream.available() == 0;
            }

            @Override
            public boolean isReady() {
                return true;
            }

            @Override
            public void setReadListener(ReadListener readListener) {
                // 不需要实现
            }

            @Override
            public int read() throws IOException {
                return byteArrayInputStream.read();
            }
        };
    }

    /**
     * 从原始请求中读取请求体
     */
    private String getBodyString(HttpServletRequest request) {
        try {
            // 检查Content-Type，避免处理multipart请求
            String contentType = request.getContentType();
            if (contentType != null && contentType.toLowerCase().startsWith("multipart/")) {
                log.debug("跳过multipart请求的请求体读取");
                return "";
            }

            StringBuilder sb = new StringBuilder();
            BufferedReader reader = request.getReader();
            String line;
            while ((line = reader.readLine()) != null) {
                sb.append(line);
            }
            return sb.toString();
        } catch (IOException e) {
            log.warn("读取请求体失败: {}", e.getMessage());
            return "";
        } catch (IllegalStateException e) {
            log.warn("请求体已被读取: {}", e.getMessage());
            return "";
        }
    }
}

package ai.pricefox.mallfox.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import java.io.BufferedReader;
import java.io.IOException;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;

/**
 * 全局请求日志拦截器
 */
@Slf4j
@Component
@AllArgsConstructor
public class RequestLoggingInterceptor implements HandlerInterceptor {

    private final LoggingProperties loggingProperties;
    private final ObjectMapper objectMapper = new ObjectMapper();
    private final ThreadLocal<Long> startTimeThreadLocal = new ThreadLocal<>();

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // 检查是否启用请求日志
        if (!loggingProperties.isEnableRequestLogging()) {
            return true;
        }

        // 记录请求开始时间
        startTimeThreadLocal.set(System.currentTimeMillis());

        // 获取请求信息
        String method = request.getMethod();
        String uri = request.getRequestURI();
        String queryString = request.getQueryString();
        String remoteAddr = getClientIpAddress(request);
        String userAgent = request.getHeader("User-Agent");

        // 构建完整的请求URL
        String fullUrl = uri;
        if (StringUtils.hasText(queryString)) {
            fullUrl += "?" + queryString;
        }

        // 获取请求头
        Map<String, String> headers = new HashMap<>();
        Enumeration<String> headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            // 过滤敏感头信息
            if (!isSensitiveHeader(headerName)) {
                headers.put(headerName, request.getHeader(headerName));
            }
        }

        // 获取请求参数
        Map<String, String[]> parameterMap = request.getParameterMap();

        // 获取请求体（仅对POST、PUT、PATCH请求，但排除multipart请求）
        String requestBody = null;
        if (isRequestBodyMethod(method) && !isMultipartRequest(request)) {
            if (request instanceof RepeatableReadHttpServletRequest) {
                requestBody = ((RepeatableReadHttpServletRequest) request).getBodyString();
            } else {
                requestBody = getRequestBody(request);
            }
        } else if (isMultipartRequest(request)) {
            requestBody = getMultipartRequestInfo(request);
        }

        // 打印请求日志
        log.info("========== 请求开始 ==========");
        log.info("请求方法: {}", method);
        log.info("请求地址: {}", fullUrl);
        log.info("客户端IP: {}", remoteAddr);
        log.info("User-Agent: {}", userAgent);
        
        if (loggingProperties.isLogRequestHeaders() && !headers.isEmpty()) {
            log.info("请求头: {}", objectMapper.writeValueAsString(headers));
        }

        if (loggingProperties.isLogRequestParameters() && !parameterMap.isEmpty()) {
            log.info("请求参数: {}", objectMapper.writeValueAsString(parameterMap));
        }

        if (loggingProperties.isLogRequestBody() && StringUtils.hasText(requestBody)) {
            // 限制请求体长度
            String logBody = requestBody;
            if (requestBody.length() > loggingProperties.getMaxRequestBodyLength()) {
                logBody = requestBody.substring(0, loggingProperties.getMaxRequestBodyLength()) + "...(truncated)";
            }
            log.info("请求体: {}", logBody);
        }

        return true;
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) throws Exception {
        // 可以在这里处理响应前的逻辑
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        try {
            // 检查是否启用请求日志
            if (!loggingProperties.isEnableRequestLogging()) {
                return;
            }

            Long startTime = startTimeThreadLocal.get();
            if (startTime != null) {
                long endTime = System.currentTimeMillis();
                long duration = endTime - startTime;

                String method = request.getMethod();
                String uri = request.getRequestURI();
                int status = response.getStatus();

                log.info("========== 请求结束 ==========");
                log.info("请求方法: {}", method);
                log.info("请求地址: {}", uri);
                log.info("响应状态: {}", status);
                log.info("执行时间: {}ms", duration);

                if (ex != null) {
                    log.error("请求异常: ", ex);
                }
            }
        } finally {
            // 清理ThreadLocal
            startTimeThreadLocal.remove();
        }
    }

    /**
     * 获取客户端真实IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (StringUtils.hasText(xForwardedFor) && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }

        String xRealIp = request.getHeader("X-Real-IP");
        if (StringUtils.hasText(xRealIp) && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }

        String proxyClientIp = request.getHeader("Proxy-Client-IP");
        if (StringUtils.hasText(proxyClientIp) && !"unknown".equalsIgnoreCase(proxyClientIp)) {
            return proxyClientIp;
        }

        String wlProxyClientIp = request.getHeader("WL-Proxy-Client-IP");
        if (StringUtils.hasText(wlProxyClientIp) && !"unknown".equalsIgnoreCase(wlProxyClientIp)) {
            return wlProxyClientIp;
        }

        return request.getRemoteAddr();
    }

    /**
     * 判断是否为敏感头信息
     */
    private boolean isSensitiveHeader(String headerName) {
        String lowerCaseName = headerName.toLowerCase();
        return lowerCaseName.contains("authorization") || 
               lowerCaseName.contains("cookie") || 
               lowerCaseName.contains("token");
    }

    /**
     * 判断是否为需要读取请求体的方法
     */
    private boolean isRequestBodyMethod(String method) {
        return "POST".equalsIgnoreCase(method) || 
               "PUT".equalsIgnoreCase(method) || 
               "PATCH".equalsIgnoreCase(method);
    }

    /**
     * 判断是否为multipart请求
     */
    private boolean isMultipartRequest(HttpServletRequest request) {
        String contentType = request.getContentType();
        return contentType != null && contentType.toLowerCase().startsWith("multipart/");
    }

    /**
     * 获取multipart请求信息
     */
    private String getMultipartRequestInfo(HttpServletRequest request) {
        try {
            StringBuilder info = new StringBuilder("[MULTIPART REQUEST]");

            String contentType = request.getContentType();
            if (contentType != null) {
                info.append(" Content-Type: ").append(contentType);
            }

            String contentLength = request.getHeader("Content-Length");
            if (contentLength != null) {
                info.append(", Content-Length: ").append(contentLength).append(" bytes");
            }

            // 尝试获取文件参数信息（不读取文件内容）
            try {
                // 只有在multipart已经被解析的情况下才尝试获取parts信息
                // 这里不主动触发解析，避免与Spring的解析冲突
                info.append(", Parts: [待解析]");
            } catch (Exception e) {
                // 忽略异常
                info.append(", Parts: [获取失败]");
            }

            return info.toString();
        } catch (Exception e) {
            log.debug("获取multipart请求信息失败: {}", e.getMessage());
            return "[MULTIPART REQUEST - 信息获取失败]";
        }
    }

    /**
     * 获取请求体内容
     */
    private String getRequestBody(HttpServletRequest request) {
        try {
            // 再次检查是否为multipart请求，避免调用getReader()
            if (isMultipartRequest(request)) {
                return "[MULTIPART REQUEST]";
            }

            BufferedReader reader = request.getReader();
            StringBuilder sb = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                sb.append(line);
            }
            return sb.toString();
        } catch (IOException e) {
            log.warn("读取请求体失败: {}", e.getMessage());
            return null;
        } catch (IllegalStateException e) {
            log.warn("请求体已被读取: {}", e.getMessage());
            return "[REQUEST BODY ALREADY READ]";
        }
    }
}

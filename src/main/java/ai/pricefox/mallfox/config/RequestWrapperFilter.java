package ai.pricefox.mallfox.config;

import jakarta.servlet.*;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * 请求包装过滤器
 * 将HttpServletRequest包装为可重复读取的请求
 */
@Slf4j
@Component
@Order(1) // 确保在其他过滤器之前执行
public class RequestWrapperFilter implements Filter {

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {

        if (request instanceof HttpServletRequest) {
            HttpServletRequest httpRequest = (HttpServletRequest) request;

            // 只对有请求体的方法进行包装，但排除multipart请求
            if (isRequestBodyMethod(httpRequest.getMethod()) && !isMultipartRequest(httpRequest)) {
                RepeatableReadHttpServletRequest wrappedRequest =
                    new RepeatableReadHttpServletRequest(httpRequest);
                chain.doFilter(wrappedRequest, response);
            } else {
                chain.doFilter(request, response);
            }
        } else {
            chain.doFilter(request, response);
        }
    }

    /**
     * 判断是否为需要读取请求体的方法
     */
    private boolean isRequestBodyMethod(String method) {
        return "POST".equalsIgnoreCase(method) ||
               "PUT".equalsIgnoreCase(method) ||
               "PATCH".equalsIgnoreCase(method);
    }

    /**
     * 判断是否为multipart请求
     */
    private boolean isMultipartRequest(HttpServletRequest request) {
        String contentType = request.getContentType();
        return contentType != null && contentType.toLowerCase().startsWith("multipart/");
    }
}

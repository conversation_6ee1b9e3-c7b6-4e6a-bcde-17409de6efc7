package ai.pricefox.mallfox.config;

import ai.pricefox.mallfox.common.exception.enums.ErrorCodeConstants;
import ai.pricefox.mallfox.domain.admin.AdminUser;
import ai.pricefox.mallfox.domain.auth.User;
import ai.pricefox.mallfox.service.admin.AdminTokenService;
import ai.pricefox.mallfox.service.auth.TokenService;
import cn.dev33.satoken.context.SaHolder;
import cn.dev33.satoken.dao.SaTokenDao;
import cn.dev33.satoken.exception.NotLoginException;
import cn.dev33.satoken.interceptor.SaInterceptor;
import cn.dev33.satoken.router.SaRouter;
import cn.dev33.satoken.stp.StpUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.util.StringUtils;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import static ai.pricefox.mallfox.common.exception.util.ServiceExceptionUtil.exception;

/**
 * [Sa-Token 权限认证] 配置类
 */
@Slf4j
@Configuration
@AllArgsConstructor
public class SaTokenConfigure implements WebMvcConfigurer {

    private final RequestLoggingInterceptor requestLoggingInterceptor;
    private final CustomSaTokenDao customSaTokenDao;
    private final TokenService tokenService;
    private final AdminTokenService adminTokenService;

    /**
     * 注册拦截器
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 注册请求日志拦截器（优先级最高）
        registry.addInterceptor(requestLoggingInterceptor)
                .addPathPatterns("/**")
                .order(0);

        // 注册 Sa-Token 拦截器，校验规则为 StpUtil.checkLogin() 登录校验。
        registry.addInterceptor(new SaInterceptor(handle -> {
            // 指定一条 match 规则
            SaRouter
                    // 拦截的 path 列表，可以写多个 */
                    .match("/**")
                    // 排除掉的 path 列表，可以写多个
                    .notMatch(getExcludePatterns())
                    // 要执行的校验动作，可以写完整的 lambda 表达式
                    .check(r -> {
                        log.info("拦截器执行登录校验，路径: {}", SaHolder.getRequest().getRequestPath());

                        // 使用自定义的Token验证逻辑
                        String token = getTokenFromRequest();
                        String requestPath = SaHolder.getRequest().getRequestPath();

                        // 如果token为空，直接拒绝访问
                        if (!StringUtils.hasText(token)) {
                            log.warn("访问被拒绝：请求头中缺少有效的token，路径: {}", requestPath);
                            throw exception(ErrorCodeConstants.AUTH_TOKEN_NOT_FOUND, "刷新令牌不存在");
                        }

                        // 根据请求路径判断是门户用户还是后台用户
                        if (requestPath.startsWith("/admin/")) {
                            // 后台用户Token验证
                            AdminUser adminUser = getAdminUserByToken(token);
                            if (adminUser != null) {
                                // 设置SaToken的登录状态
                                if (!StpUtil.isLogin()) {
                                    StpUtil.login(adminUser.getId());
                                }
                                log.info("后台用户token验证成功，用户ID: {}, 路径: {}", adminUser.getId(), requestPath);
                                return;
                            } else {
                                log.warn("后台用户token验证失败，token: {}, 路径: {}", token, requestPath);
                            }
                        } else {
                            // 门户用户Token验证
                            User user = getUserByToken(token);
                            if (user != null) {
                                // 设置SaToken的登录状态
                                if (!StpUtil.isLogin()) {
                                    StpUtil.login(user.getId());
                                }
                                log.info("门户用户token验证成功，用户ID: {}, 路径: {}", user.getId(), requestPath);
                                return;
                            } else {
                                log.warn("门户用户token验证失败，token: {}, 路径: {}", token, requestPath);
                            }
                        }

                        // 如果token验证失败，抛出未登录异常
                        log.warn("token验证失败，拒绝访问，token: {}, 路径: {}", token, requestPath);
                        throw exception(ErrorCodeConstants.AUTH_TOKEN_NOT_FOUND, "刷新令牌不存在");
                    });
        })).addPathPatterns("/**").order(1);
    }

    /**
     * 获取排除路径列表
     */
    private String[] getExcludePatterns() {
        String[] patterns = new String[]{
                // 静态资源
                "/favicon.ico",
                "/error",
                "/actuator/**",

                // Swagger 文档
                "/doc.html",
                "/webjars/**",
                "/swagger-resources/**",
                "/v3/api-docs/**",

                // 门户认证相关接口（无需登录）
                "/v1/auth/login",
                "/v1/auth/register",
                "/v1/auth/send-code",
                "/v1/auth/send-reset-link",
                "/v1/auth/reset-password",
                "/v1/auth/refresh-token",
                "/v1/auth/status",
                "/v1/auth/test",
                "/v1/products/**",

                // 后台认证相关接口（无需登录）
                "/admin/v1/auth/login",
                "/admin/v1/auth/send-code",
                "/admin/v1/auth/refresh-token",
                "/admin/v1/auth/status",

                // 邮件测试接口（临时，用于调试）
                "/admin/v1/email-test/**",
                // 外部调用，无法登陆
                "/product/data/**",

                // 管理后台接口不做过滤
                //"/admin/**",

                // 测试接口
                "/v1/base/**",
                "/v1/common/upload",

                // google获取url和回调接口
                "/oauth/callback/google",
                "/oauth/google/*",
                "/google-login.html",

                // eBay API 接口（测试用）
                "/admin/v1/ebay/**",
                "/admin/v1/bestbuy/**",

                // 测试规则引擎接口
                "/api/standardization/**",
                "/standardization/**",
        };

        log.info("SaToken 排除路径配置: {}", java.util.Arrays.toString(patterns));
        return patterns;
    }

    /**
     * 注册自定义的SaToken数据持久化接口
     */
    @Bean
    @Primary
    public SaTokenDao saTokenDao() {
        return customSaTokenDao;
    }

    /**
     * 从请求中获取Token
     */
    private String getTokenFromRequest() {
        try {
            String tokenValue = SaHolder.getRequest().getHeader("Authorization");
            if (StringUtils.hasText(tokenValue)) {
                if (tokenValue.startsWith("Bearer ")) {
                    return tokenValue.substring(7);
                }
                return tokenValue;
            }

            tokenValue = SaHolder.getRequest().getParam("token");
            if (StringUtils.hasText(tokenValue)) {
                return tokenValue;
            }

            return null;
        } catch (Exception e) {
            log.warn("获取Token失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 根据Token获取门户用户信息
     */
    private User getUserByToken(String token) {
        try {
            return tokenService.getUserByToken(token);
        } catch (Exception e) {
            log.warn("根据Token获取门户用户失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 根据Token获取后台用户信息
     */
    private AdminUser getAdminUserByToken(String token) {
        try {
            return adminTokenService.getUserByToken(token);
        } catch (Exception e) {
            log.warn("根据Token获取后台用户失败: {}", e.getMessage());
            return null;
        }
    }

}

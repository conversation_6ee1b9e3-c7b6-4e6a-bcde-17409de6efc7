package ai.pricefox.mallfox.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 通用线程池工具类
 * 支持执行不同类型的任务：Runnable、Callable、定时任务等
 *
 * <AUTHOR>
 */
@Slf4j
@Configuration
@EnableAsync
public class ThreadPoolConfig {

    @Value("${thread.pool.core-size:2}")
    private int corePoolSize;

    @Value("${thread.pool.max-size:5}")
    private int maxPoolSize;

    @Value("${thread.pool.queue-capacity:500}")
    private int queueCapacity;

    @Value("${thread.pool.keep-alive:60}")
    private int keepAliveSeconds;

    @Value("${thread.pool.thread-name-prefix:async-task-}")
    private String threadNamePrefix;

    private ThreadPoolTaskExecutor taskExecutor;
    private ScheduledExecutorService scheduledExecutor;
    private ExecutorService customExecutor;

    /**
     * 配置Spring异步任务执行器
     */
    @Bean(name = "taskExecutor", destroyMethod = "shutdown")
    public ThreadPoolTaskExecutor taskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(corePoolSize);
        executor.setMaxPoolSize(maxPoolSize);
        executor.setQueueCapacity(queueCapacity);
        executor.setKeepAliveSeconds(keepAliveSeconds);
        executor.setThreadNamePrefix(threadNamePrefix);

        // 拒绝策略：调用者运行
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());

        // 等待所有任务结束后再关闭线程池
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(60);

        executor.initialize();
        this.taskExecutor = executor;

        log.info("线程池初始化完成 - 核心线程数: {}, 最大线程数: {}, 队列容量: {}",
                corePoolSize, maxPoolSize, queueCapacity);

        return executor;
    }

    /**
     * 配置定时任务执行器
     */
    @Bean(name = "scheduledExecutor", destroyMethod = "shutdown")
    public ScheduledExecutorService scheduledExecutor() {
        this.scheduledExecutor = Executors.newScheduledThreadPool(5, new CustomThreadFactory("scheduled-task-"));
        log.info("定时任务线程池初始化完成");
        return this.scheduledExecutor;
    }

    /**
     * 配置自定义线程池执行器
     */
    @Bean(name = "customExecutor", destroyMethod = "shutdown")
    public ExecutorService customExecutor() {
        this.customExecutor = new ThreadPoolExecutor(
                corePoolSize,
                maxPoolSize,
                keepAliveSeconds,
                TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(queueCapacity),
                new CustomThreadFactory("custom-task-"),
                new ThreadPoolExecutor.CallerRunsPolicy()
        );
        log.info("自定义线程池初始化完成");
        return this.customExecutor;
    }

    /**
     * 异步执行Runnable任务
     */
    public void executeAsync(Runnable task) {
        try {
            taskExecutor.execute(task);
            log.debug("异步任务已提交执行");
        } catch (Exception e) {
            log.error("异步任务执行失败", e);
            throw new RuntimeException("异步任务执行失败", e);
        }
    }

    /**
     * 异步执行Callable任务并返回Future
     */
    public <T> Future<T> submitAsync(Callable<T> task) {
        try {
            Future<T> future = taskExecutor.submit(task);
            log.debug("异步Callable任务已提交执行");
            return future;
        } catch (Exception e) {
            log.error("异步Callable任务提交失败", e);
            throw new RuntimeException("异步Callable任务提交失败", e);
        }
    }

    /**
     * 延迟执行任务
     */
    public ScheduledFuture<?> scheduleWithDelay(Runnable task, long delay, TimeUnit timeUnit) {
        try {
            ScheduledFuture<?> future = scheduledExecutor.schedule(task, delay, timeUnit);
            log.debug("延迟任务已提交，延迟时间: {} {}", delay, timeUnit);
            return future;
        } catch (Exception e) {
            log.error("延迟任务提交失败", e);
            throw new RuntimeException("延迟任务提交失败", e);
        }
    }

    /**
     * 定时执行任务（固定频率）
     */
    public ScheduledFuture<?> scheduleAtFixedRate(Runnable task, long initialDelay, long period, TimeUnit timeUnit) {
        try {
            ScheduledFuture<?> future = scheduledExecutor.scheduleAtFixedRate(task, initialDelay, period, timeUnit);
            log.debug("定时任务已提交，初始延迟: {} {}，执行周期: {} {}", initialDelay, timeUnit, period, timeUnit);
            return future;
        } catch (Exception e) {
            log.error("定时任务提交失败", e);
            throw new RuntimeException("定时任务提交失败", e);
        }
    }

    /**
     * 定时执行任务（固定延迟）
     */
    public ScheduledFuture<?> scheduleWithFixedDelay(Runnable task, long initialDelay, long delay, TimeUnit timeUnit) {
        try {
            ScheduledFuture<?> future = scheduledExecutor.scheduleWithFixedDelay(task, initialDelay, delay, timeUnit);
            log.debug("固定延迟任务已提交，初始延迟: {} {}，执行延迟: {} {}", initialDelay, timeUnit, delay, timeUnit);
            return future;
        } catch (Exception e) {
            log.error("固定延迟任务提交失败", e);
            throw new RuntimeException("固定延迟任务提交失败", e);
        }
    }

    /**
     * 使用自定义线程池执行任务
     */
    public void executeWithCustomPool(Runnable task) {
        try {
            customExecutor.execute(task);
            log.debug("自定义线程池任务已提交执行");
        } catch (Exception e) {
            log.error("自定义线程池任务执行失败", e);
            throw new RuntimeException("自定义线程池任务执行失败", e);
        }
    }

    /**
     * 使用自定义线程池提交Callable任务
     */
    public <T> Future<T> submitWithCustomPool(Callable<T> task) {
        try {
            Future<T> future = customExecutor.submit(task);
            log.debug("自定义线程池Callable任务已提交执行");
            return future;
        } catch (Exception e) {
            log.error("自定义线程池Callable任务提交失败", e);
            throw new RuntimeException("自定义线程池Callable任务提交失败", e);
        }
    }

    /**
     * 获取线程池状态信息
     */
    public ThreadPoolStatus getThreadPoolStatus() {
        if (taskExecutor.getThreadPoolExecutor() instanceof ThreadPoolExecutor) {
            ThreadPoolExecutor executor = (ThreadPoolExecutor) taskExecutor.getThreadPoolExecutor();
            return ThreadPoolStatus.builder()
                    .corePoolSize(executor.getCorePoolSize())
                    .maximumPoolSize(executor.getMaximumPoolSize())
                    .currentPoolSize(executor.getPoolSize())
                    .activeThreadCount(executor.getActiveCount())
                    .queueSize(executor.getQueue().size())
                    .completedTaskCount(executor.getCompletedTaskCount())
                    .totalTaskCount(executor.getTaskCount())
                    .build();
        }
        return null;
    }

    /**
     * 自定义线程工厂
     */
    private static class CustomThreadFactory implements ThreadFactory {
        private final AtomicInteger threadNumber = new AtomicInteger(1);
        private final String namePrefix;

        CustomThreadFactory(String namePrefix) {
            this.namePrefix = namePrefix;
        }

        @Override
        public Thread newThread(Runnable r) {
            Thread thread = new Thread(r, namePrefix + threadNumber.getAndIncrement());
            thread.setDaemon(false);
            thread.setPriority(Thread.NORM_PRIORITY);
            return thread;
        }
    }

    /**
     * 线程池状态信息
     */
    public static class ThreadPoolStatus {
        private int corePoolSize;
        private int maximumPoolSize;
        private int currentPoolSize;
        private int activeThreadCount;
        private int queueSize;
        private long completedTaskCount;
        private long totalTaskCount;

        public static ThreadPoolStatusBuilder builder() {
            return new ThreadPoolStatusBuilder();
        }

        // Getters
        public int getCorePoolSize() {
            return corePoolSize;
        }

        public int getMaximumPoolSize() {
            return maximumPoolSize;
        }

        public int getCurrentPoolSize() {
            return currentPoolSize;
        }

        public int getActiveThreadCount() {
            return activeThreadCount;
        }

        public int getQueueSize() {
            return queueSize;
        }

        public long getCompletedTaskCount() {
            return completedTaskCount;
        }

        public long getTotalTaskCount() {
            return totalTaskCount;
        }

        @Override
        public String toString() {
            return String.format(
                    "ThreadPoolStatus{核心线程数=%d, 最大线程数=%d, 当前线程数=%d, 活跃线程数=%d, 队列大小=%d, 已完成任务数=%d, 总任务数=%d}",
                    corePoolSize, maximumPoolSize, currentPoolSize, activeThreadCount, queueSize, completedTaskCount, totalTaskCount
            );
        }

        public static class ThreadPoolStatusBuilder {
            private ThreadPoolStatus status = new ThreadPoolStatus();

            public ThreadPoolStatusBuilder corePoolSize(int corePoolSize) {
                status.corePoolSize = corePoolSize;
                return this;
            }

            public ThreadPoolStatusBuilder maximumPoolSize(int maximumPoolSize) {
                status.maximumPoolSize = maximumPoolSize;
                return this;
            }

            public ThreadPoolStatusBuilder currentPoolSize(int currentPoolSize) {
                status.currentPoolSize = currentPoolSize;
                return this;
            }

            public ThreadPoolStatusBuilder activeThreadCount(int activeThreadCount) {
                status.activeThreadCount = activeThreadCount;
                return this;
            }

            public ThreadPoolStatusBuilder queueSize(int queueSize) {
                status.queueSize = queueSize;
                return this;
            }

            public ThreadPoolStatusBuilder completedTaskCount(long completedTaskCount) {
                status.completedTaskCount = completedTaskCount;
                return this;
            }

            public ThreadPoolStatusBuilder totalTaskCount(long totalTaskCount) {
                status.totalTaskCount = totalTaskCount;
                return this;
            }

            public ThreadPoolStatus build() {
                return status;
            }
        }
    }

    /**
     * L
     * 优雅关闭线程池
     */
    public void shutdown() {
        log.info("开始关闭线程池...");

        if (taskExecutor != null) {
            taskExecutor.shutdown();
        }

        if (scheduledExecutor != null && !scheduledExecutor.isShutdown()) {
            scheduledExecutor.shutdown();
            try {
                if (!scheduledExecutor.awaitTermination(30, TimeUnit.SECONDS)) {
                    scheduledExecutor.shutdownNow();
                }
            } catch (InterruptedException e) {
                scheduledExecutor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }

        if (customExecutor != null && !customExecutor.isShutdown()) {
            customExecutor.shutdown();
            try {
                if (!customExecutor.awaitTermination(30, TimeUnit.SECONDS)) {
                    customExecutor.shutdownNow();
                }
            } catch (InterruptedException e) {
                customExecutor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }

        log.info("线程池关闭完成");
    }
}

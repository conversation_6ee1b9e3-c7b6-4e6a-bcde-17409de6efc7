package ai.pricefox.mallfox.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.Callable;
import java.util.concurrent.Future;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;

/**
 * 线程池使用示例
 * 演示如何使用ThreadPoolConfig执行不同类型的任务
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class ThreadPoolExample {

    @Autowired
    private ThreadPoolConfig threadPoolConfig;

    /**
     * 示例1：执行简单的异步任务
     */
    public void executeSimpleAsyncTask() {
        threadPoolConfig.executeAsync(() -> {
            try {
                Thread.sleep(2000);
                log.info("简单异步任务执行完成，线程: {}", Thread.currentThread().getName());
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.error("任务被中断", e);
            }
        });
    }

    /**
     * 示例2：执行有返回值的异步任务
     */
    public void executeCallableTask() {
        Callable<String> task = () -> {
            Thread.sleep(1000);
            String result = "任务执行结果: " + System.currentTimeMillis();
            log.info("Callable任务执行完成，结果: {}", result);
            return result;
        };

        Future<String> future = threadPoolConfig.submitAsync(task);
        
        try {
            // 获取任务执行结果（会阻塞直到任务完成）
            String result = future.get(5, TimeUnit.SECONDS);
            log.info("获取到任务结果: {}", result);
        } catch (Exception e) {
            log.error("获取任务结果失败", e);
        }
    }

    /**
     * 示例3：执行延迟任务
     */
    public void executeDelayedTask() {
        ScheduledFuture<?> future = threadPoolConfig.scheduleWithDelay(() -> {
            log.info("延迟任务执行，当前时间: {}", System.currentTimeMillis());
        }, 3, TimeUnit.SECONDS);

        log.info("延迟任务已提交，3秒后执行");
    }

    /**
     * 示例4：执行定时任务（固定频率）
     */
    public void executeFixedRateTask() {
        ScheduledFuture<?> future = threadPoolConfig.scheduleAtFixedRate(() -> {
            log.info("定时任务执行，当前时间: {}", System.currentTimeMillis());
        }, 1, 5, TimeUnit.SECONDS);

        log.info("定时任务已提交，1秒后开始执行，每5秒执行一次");
        
        // 可以在需要时取消任务
        // future.cancel(false);
    }

    /**
     * 示例5：执行固定延迟任务
     */
    public void executeFixedDelayTask() {
        ScheduledFuture<?> future = threadPoolConfig.scheduleWithFixedDelay(() -> {
            try {
                // 模拟任务执行时间
                Thread.sleep(2000);
                log.info("固定延迟任务执行完成，当前时间: {}", System.currentTimeMillis());
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.error("任务被中断", e);
            }
        }, 1, 3, TimeUnit.SECONDS);

        log.info("固定延迟任务已提交，1秒后开始执行，每次执行完成后延迟3秒再执行下一次");
    }

    /**
     * 示例6：使用自定义线程池执行任务
     */
    public void executeWithCustomPool() {
        threadPoolConfig.executeWithCustomPool(() -> {
            log.info("自定义线程池任务执行，线程: {}", Thread.currentThread().getName());
        });

        Callable<Integer> calculation = () -> {
            int result = 0;
            for (int i = 1; i <= 100; i++) {
                result += i;
            }
            log.info("计算任务完成，结果: {}", result);
            return result;
        };

        Future<Integer> future = threadPoolConfig.submitWithCustomPool(calculation);
        try {
            Integer result = future.get();
            log.info("计算结果: {}", result);
        } catch (Exception e) {
            log.error("获取计算结果失败", e);
        }
    }

    /**
     * 示例7：批量执行任务
     */
    public void executeBatchTasks() {
        log.info("开始批量执行任务");
        
        for (int i = 1; i <= 10; i++) {
            final int taskId = i;
            threadPoolConfig.executeAsync(() -> {
                try {
                    Thread.sleep(1000 + (taskId * 100)); // 模拟不同的执行时间
                    log.info("批量任务 {} 执行完成，线程: {}", taskId, Thread.currentThread().getName());
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    log.error("任务 {} 被中断", taskId, e);
                }
            });
        }
        
        log.info("批量任务已全部提交");
    }

    /**
     * 示例8：监控线程池状态
     */
    public void monitorThreadPoolStatus() {
        ThreadPoolConfig.ThreadPoolStatus status = threadPoolConfig.getThreadPoolStatus();
        if (status != null) {
            log.info("线程池状态: {}", status);
        } else {
            log.warn("无法获取线程池状态");
        }
    }

    /**
     * 示例9：异常处理
     */
    public void executeTaskWithExceptionHandling() {
        threadPoolConfig.executeAsync(() -> {
            try {
                // 模拟可能出现异常的任务
                if (Math.random() > 0.5) {
                    throw new RuntimeException("模拟任务执行异常");
                }
                log.info("任务执行成功");
            } catch (Exception e) {
                log.error("任务执行过程中发生异常", e);
                // 这里可以添加异常处理逻辑，比如重试、记录错误等
            }
        });
    }

    /**
     * 示例10：复杂业务场景 - 数据处理管道
     */
    public void executeDataProcessingPipeline() {
        log.info("开始执行数据处理管道");
        
        // 第一步：数据获取
        Future<String> dataFuture = threadPoolConfig.submitAsync(() -> {
            Thread.sleep(1000);
            log.info("数据获取完成");
            return "原始数据";
        });
        
        // 第二步：数据处理（依赖第一步的结果）
        Future<String> processedDataFuture = threadPoolConfig.submitAsync(() -> {
            try {
                String rawData = dataFuture.get();
                Thread.sleep(2000);
                String processedData = "处理后的" + rawData;
                log.info("数据处理完成: {}", processedData);
                return processedData;
            } catch (Exception e) {
                log.error("数据处理失败", e);
                throw new RuntimeException("数据处理失败", e);
            }
        });
        
        // 第三步：数据保存
        threadPoolConfig.executeAsync(() -> {
            try {
                String finalData = processedDataFuture.get();
                Thread.sleep(500);
                log.info("数据保存完成: {}", finalData);
            } catch (Exception e) {
                log.error("数据保存失败", e);
            }
        });
        
        log.info("数据处理管道已启动");
    }
}

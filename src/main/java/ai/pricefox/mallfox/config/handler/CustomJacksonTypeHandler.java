package ai.pricefox.mallfox.config.handler;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Map;

/**
 * 自定义Jackson TypeHandler，支持LocalDateTime等JSR310类型
 * 使用Spring配置的ObjectMapper，支持完整的Jackson功能
 * 
 * <AUTHOR>
 * @since 2025-01-14
 */
@Component
@MappedTypes({Map.class, Object.class})
@MappedJdbcTypes({JdbcType.VARCHAR, JdbcType.OTHER})
public class CustomJacksonTypeHandler extends BaseTypeHandler<Object> {

    private static ObjectMapper objectMapper;

    /**
     * 注入Spring配置的ObjectMapper
     */
    @Autowired
    public void setObjectMapper(ObjectMapper objectMapper) {
        CustomJacksonTypeHandler.objectMapper = objectMapper;
    }

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, Object parameter, JdbcType jdbcType) throws SQLException {
        try {
            if (objectMapper == null) {
                throw new RuntimeException("ObjectMapper未初始化，请检查Spring配置");
            }
            String json = objectMapper.writeValueAsString(parameter);
            ps.setString(i, json);
        } catch (JsonProcessingException e) {
            throw new SQLException("JSON序列化失败: " + e.getMessage(), e);
        }
    }

    @Override
    public Object getNullableResult(ResultSet rs, String columnName) throws SQLException {
        String json = rs.getString(columnName);
        return parseJson(json);
    }

    @Override
    public Object getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        String json = rs.getString(columnIndex);
        return parseJson(json);
    }

    @Override
    public Object getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        String json = cs.getString(columnIndex);
        return parseJson(json);
    }

    private Object parseJson(String json) throws SQLException {
        if (json == null || json.trim().isEmpty()) {
            return null;
        }
        
        try {
            if (objectMapper == null) {
                throw new RuntimeException("ObjectMapper未初始化，请检查Spring配置");
            }
            // 尝试解析为Map类型，这是最常见的JSON对象类型
            return objectMapper.readValue(json, Map.class);
        } catch (JsonProcessingException e) {
            throw new SQLException("JSON反序列化失败: " + json, e);
        }
    }
}

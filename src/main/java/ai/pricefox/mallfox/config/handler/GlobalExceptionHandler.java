package ai.pricefox.mallfox.config.handler;

import ai.pricefox.mallfox.common.exception.util.BizException;
import ai.pricefox.mallfox.common.exception.ServiceException;
import ai.pricefox.mallfox.common.exception.enums.GlobalErrorCodeConstants;
import ai.pricefox.mallfox.vo.base.CommonResult;
import cn.dev33.satoken.exception.SaTokenException;
import cn.hutool.core.exceptions.ExceptionUtil;
import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.ConstraintViolationException;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnWebApplication;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.ServletRequestBindingException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import org.springframework.web.multipart.MaxUploadSizeExceededException;
import org.springframework.web.multipart.MultipartException;
import org.springframework.web.servlet.NoHandlerFoundException;

import java.net.BindException;
import java.util.Map;

/**
 * 全局异常处理器
 */
@Slf4j
@RestControllerAdvice
@ConditionalOnWebApplication(type = ConditionalOnWebApplication.Type.SERVLET)
public class GlobalExceptionHandler {

    @ExceptionHandler(Exception.class)
    public CommonResult<Object> handleException(Exception e) {
        log.error("系统内部异常，异常信息", e);
        return CommonResult.error(GlobalErrorCodeConstants.INTERNAL_SERVER_ERROR);
    }

    /**
     * 404 找不到请求地址的处理方式
     *
     * @param e
     * @return
     */
    @ExceptionHandler(NoHandlerFoundException.class)
    @ResponseBody
    public CommonResult<Object> handleError(NoHandlerFoundException e) {
        log.warn(String.format("404没找到请求:%s", e.getMessage()), e);
        return CommonResult.error(GlobalErrorCodeConstants.NOT_FOUND);
    }

    /**
     * 处理路径参数类型转换异常
     * 例如：/admin/v1/dicts/values/{id} 中的 {id} 没有被替换为实际值
     */
    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    @ResponseBody
    public CommonResult<Object> handleMethodArgumentTypeMismatch(MethodArgumentTypeMismatchException e, HttpServletRequest request) {
        log.warn("路径参数类型转换异常: {}, 请求: {}", e.getMessage(), getRequestInfo(request), e);
        String message = String.format("路径参数 '%s' 的值 '%s' 无法转换为 %s 类型，请检查请求路径是否正确",
                e.getName(), e.getValue(), e.getRequiredType().getSimpleName());
        return CommonResult.error(GlobalErrorCodeConstants.BAD_REQUEST.getCode(), message);
    }

    @ExceptionHandler({
            ServletRequestBindingException.class,
            MethodArgumentNotValidException.class,
            BindException.class,
            ConstraintViolationException.class,
            HttpMessageNotReadableException.class,
            IllegalArgumentException.class
    })
    @ResponseBody
    public CommonResult<Object> handleBadRequestException(Exception e, HttpServletRequest request) {
        log.warn("参数异常: {}, 请求: {}", e.getMessage(), getRequestInfo(request), e);
        return CommonResult.error(GlobalErrorCodeConstants.BAD_REQUEST.getCode(), e.getMessage());
    }

    @ExceptionHandler(MissingServletRequestParameterException.class)
    public CommonResult<Object> handleMissingParam(MissingServletRequestParameterException e) {
        log.warn("缺少请求参数: {}", e.getMessage());
        return CommonResult.error(GlobalErrorCodeConstants.BAD_REQUEST.getCode(), "缺少参数: " + e.getParameterName());
    }

    @ExceptionHandler(SaTokenException.class)
    public CommonResult<Object> handleError(SaTokenException e) {
        log.error("发生token校验错误: ", e);
        return CommonResult.error(GlobalErrorCodeConstants.UNAUTHORIZED.getCode(), ExceptionUtil.getRootCause(e).getMessage());
    }

    @ExceptionHandler(value = BizException.class)
    public CommonResult<Object> businessException(BizException be) {
        log.error("发生业务异常: ", be);
        return CommonResult.error(be.getCode(), be.getMessage());
    }

    @ExceptionHandler(value = ServiceException.class)
    public CommonResult<Object> serviceException(ServiceException se) {
        log.error("发生服务异常: ", se);
        return CommonResult.error(se.getCode(), se.getMessage());
    }

    public String getRequestInfo(HttpServletRequest request) {
        RequestMsg requestMsg = new RequestMsg();
        Map<String, String[]> param = request.getParameterMap();
        String url = request.getRequestURI();
        requestMsg.setParams(param);
        requestMsg.setUrl(url);
        return JSON.toJSONString(requestMsg);
    }

    /**
     * 处理文件上传大小超限异常
     */
    @ExceptionHandler(MaxUploadSizeExceededException.class)
    @ResponseBody
    public CommonResult<String> handleMaxUploadSizeExceededException(MaxUploadSizeExceededException ex, HttpServletRequest request) {
        log.error("文件上传大小超限，请求信息：{}", getRequestInfo(request), ex);
        return CommonResult.error(GlobalErrorCodeConstants.BAD_REQUEST.getCode(),
                "文件大小超过限制，最大允许上传500MB");
    }

    /**
     * 处理multipart解析异常
     */
    @ExceptionHandler(MultipartException.class)
    @ResponseBody
    public CommonResult<String> handleMultipartException(MultipartException ex, HttpServletRequest request) {
        log.error("文件上传解析异常，请求信息：{}", getRequestInfo(request), ex);

        String message = "文件上传失败";
        if (ex.getCause() instanceof IllegalStateException) {
            message = "请求体已被读取，请检查过滤器配置";
        } else if (ex.getMessage().contains("size")) {
            message = "文件大小超过限制";
        } else if (ex.getMessage().contains("multipart")) {
            message = "文件格式错误，请检查是否为有效的文件";
        }

        return CommonResult.error(GlobalErrorCodeConstants.BAD_REQUEST.getCode(), message);
    }

    @Data
    @JsonPropertyOrder({"url", "params"})
    public static class RequestMsg {
        private String url;
        private Map<String, String[]> params;
    }
}

package ai.pricefox.mallfox.controller;

import ai.pricefox.mallfox.event.StandardDataEvent;
import ai.pricefox.mallfox.job.integration.MarketplaceDataSyncJob;
import ai.pricefox.mallfox.service.rules.DataStandardizationService;
import ai.pricefox.mallfox.service.rules.StandardDataCacheService;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.kie.api.runtime.KieContainer;
import org.kie.api.runtime.KieSession;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.web.bind.annotation.*;

import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.Map;

/**
 * 数据标准化控制器
 * 用于测试和调用数据标准化服务
 */
@Slf4j
@AllArgsConstructor
@RestController
@RequestMapping("/api/standardization")
public class DataStandardizationTestController {

    private final DataStandardizationService dataStandardizationService;
    private final StandardDataCacheService standardDataCacheService;
    // 注入已存在的KieContainer
    private final KieContainer kieContainer;
    private final ApplicationEventPublisher eventPublisher;

    /**
     * 启动分步标准化BestBuy商品数据
     *
     * @return 标准化结果
     */
    @GetMapping("/bestBuyApiStepByStepJson")
    public Map<String, Object> standardizeBestBuyProductStepByStepJson() {
        log.info("=== 开始分步标准化BestBuy商品数据===");
//        marketplaceDataSyncJob.syncEbayData();
        try {
            standardDataCacheService.clearAllStandardDataCache();
            log.info("[控制器] 创建BestBuy测试数据用于分步标准化");
            // 创建BestBuy测试数据
            String originalDataJson = createBestBuyTestDataAsJson();
            log.debug("[控制器] 测试数据创建完成: {}", originalDataJson);

            log.info("[控制器] 解析JSON数据");
            Gson gson = new Gson();
            Type stringObjectMap = new TypeToken<Map<String, Object>>(){}.getType();
            Map<String, Object> originalDataMap = gson.fromJson(originalDataJson, stringObjectMap);

            log.info("[控制器] 启动分步标准化处理");
            // 启动分步标准化处理
            eventPublisher.publishEvent(new StandardDataEvent( this, originalDataJson));
            log.info("[控制器] 分步标准化已启动");

            // 构建响应结果
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "已启动分步数据标准化流程");

            log.info("已启动分步数据标准化流程");

            return null;

        } catch (Exception e) {
            log.error("[控制器] 启动分步数据标准化过程中发生错误", e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "启动分步数据标准化失败: " + e.getMessage());
            return errorResult;
        }
    }

    /**
     * 创建BestBuy测试数据 (JSON格式)
     *
     * @return JSON字符串
     */
    private String createAmazonestDataAsJson() {
        log.debug("[控制器] 开始创建amazon测试数据(JSON格式)");
        String jsonData = "{\n" +
                "    \"sourcePlatform\": \"AMAZON\",\n" +
                "\t\"dataChannel\":\"CRAWLER\",\n" +
                "\t\"selfOperated\":false,\n" +
                "    \"asin\": \"B08PP5MSVB\",\n" +
                "    \"id\": \"19a62c35-9a09-4c00-b549-9cb9a6397783\",\n" +
                "    \"uuid\": \"19a62c35-9a09-4c00-b549-9cb9a6397783\",\n" +
                "    \"thumbImage\": \"https://m.media-amazon.com/images/I/31nbgjFrvRL._AC_SR38,50_.jpg\",\n" +
                "    \"title\": \"Apple iPhone 12, 64GB, Black - Fully Unlocked (Renewed)\",\n" +
                "    \"url\": \"https://www.amazon.com/Apple-iPhone-12-64GB-Black/dp/B08PP5MSVB/ref=sr_1_1?dib=eyJ2IjoiMSJ9.5zLyhKdodVAmx_ceDY9BEiLXmAIahBHsEsNpgMTFZtHh9PzPr9fgmQ_MBEYIMAwTMp3KnCCa_XY7E4T3m-Wq0LFPerAZyiy7Vlv4hE1UqeJTowifG7NqW32WCscaVCfC2i02JPnqSfPNGAxGIvxUNIQFKR52OI730YMRZbNFTrINSKJi09X9XfvrDToxeN4dQjV1ZHP9JfVe1gnjagBMXolO2wxEQKcXfGBkIRmugKgeYHhNprbgWBTqMDWZpSBN_Cx_tQL2Ip5G6yYntWLwsqC21eRyZhE4Jx0r6ggtTZg.JLmKNSa0TYIzop6IMJ7-fHgLVXGETnalvIrLm3pTVOw&dib_tag=se&qid=1753873807&refinements=p_123%3A110955%7C21446%7C370584%7C46655&refresh=1&rnid=2335752011&s=mobile&sr=1-1&xpid=wsmSyA6GCMYYN\",\n" +
                "    \"parentAsin\": \"B09KW9M48M\",\n" +
                "    \"mediaAsin\": \"B08PP5MSVB\",\n" +
                "    \"dpRequestId\": \"GNDDHNQQ14JV85D2JY48\",\n" +
                "    \"promo\": \"Apply now and get a $80 Amazon Gift Card upon approval of the Amazon Store Card, or see if you pre-qualify with no impact to your credit bureau score.\",\n" +
                "    \"availableStock\": \"Only 2 left in stock - order soon.\",\n" +
                "    \"offerFeatureList\": {\n" +
                "        \"shipsFrom\": \"Ships from\",\n" +
                "        \"soldBy\": \"JemJem\",\n" +
                "        \"condition\": \"Condition\",\n" +
                "        \"returns\": \"Returns\"\n" +
                "    },\n" +
                "    \"shippingTime\": \"FREE delivery Friday, August 1. Order within 8 hrs 49 mins. Details\",\n" +
                "    \"listPrice\": \"$221.72 with 11 percent savings\",\n" +
                "    \"inventory\": 1,\n" +
                "    \"SaleForLast30Days\": \"7K+ bought in past month\",\n" +
                "    \"category\": [\n" +
                "        \"Cell Phones & Accessories\",\n" +
                "        \"Cell Phones\"\n" +
                "    ],\n" +
                "    \"About\": [\n" +
                "        \"Fully unlocked and compatible with any carrier of choice (e.g. AT&T, T-Mobile, Sprint, Verizon, US-Cellular, Cricket, Metro, etc.).\",\n" +
                "        \"Inspected and guaranteed to have minimal cosmetic damage, which is not noticeable when the device is held at arm's length.\",\n" +
                "        \"Successfully passed a full diagnostic test which ensures like-new functionality and removal of any prior-user personal information.\",\n" +
                "        \"The device does not come with headphones or a SIM card. It does include a charging cable that may be generic.\",\n" +
                "        \"Tested for battery health and guaranteed to have a minimum battery capacity of 80%.\"\n" +
                "    ],\n" +
                "    \"productBaseInfo\": {\n" +
                "        \"Brand\": \"Apple\",\n" +
                "        \"Operating System\": \"iOS 16\",\n" +
                "        \"Ram Memory Installed Size\": \"8 GB\",\n" +
                "        \"CPU Model\": \"1.2GHz Cortex A8 Processor\",\n" +
                "        \"CPU Speed\": \"1.2 GHz\",\n" +
                "        \"Memory Storage Capacity\": \"64 GB\",\n" +
                "        \"Screen Size\": \"6.1 Inches\",\n" +
                "        \"Resolution\": \"2532 x 1170\",\n" +
                "        \"Refresh Rate\": \"60 Hz\",\n" +
                "        \"Model Name\": \"iPhone 12\"\n" +
                "    },\n" +
                "    \"productDetailInfomation\": {\n" +
                "        \"Product Dimensions\": \"7 x 4 x 5 inches\",\n" +
                "        \"Item Weight\": \"7 ounces\",\n" +
                "        \"ASIN\": \"B08PP5MSVB\",\n" +
                "        \"Item model number\": \"iPhone 12\",\n" +
                "        \"Batteries\": \"1 Lithium Polymer batteries required. (included)\",\n" +
                "        \"Customer Reviews\": \"4.1 out of 5 stars(4.1 4.1 out of 5 stars 31,736 ratings)\",\n" +
                "        \"Best Sellers Rank\": \"#116 in Cell Phones & Accessories (See Top 100 in Cell Phones & Accessories)\\n#1 in Renewed Smartphones\\n#1 in Cell Phones\",\n" +
                "        \"Is Discontinued By Manufacturer\": \"No\",\n" +
                "        \"OS\": \"iOS 16\",\n" +
                "        \"RAM\": \"4 GB\",\n" +
                "        \"Wireless communication technologies\": \"Cellular\",\n" +
                "        \"Connectivity technologies\": \"Wi-Fi\",\n" +
                "        \"GPS\": \"True\",\n" +
                "        \"Special features\": \"Built-In GPS\",\n" +
                "        \"Display technology\": \"OLED\",\n" +
                "        \"Other display features\": \"Wireless\",\n" +
                "        \"Human Interface Input\": \"Touchscreen\",\n" +
                "        \"Scanner Resolution\": \"2532 x 1170\",\n" +
                "        \"Other camera features\": \"Rear, Front\",\n" +
                "        \"Form Factor\": \"Slate\",\n" +
                "        \"Color\": \"Black\",\n" +
                "        \"Battery Power Rating\": \"3600 Milliamp Hours\",\n" +
                "        \"Whats in the box\": \"Apple iPhone, USB Cable\",\n" +
                "        \"Manufacturer\": \"Apple Computer\",\n" +
                "        \"Date First Available\": \"December 3, 2020\",\n" +
                "        \"Memory Storage Capacity\": \"64 GB\",\n" +
                "        \"Standing screen display size\": \"6.1 Inches\",\n" +
                "        \"Ram Memory Installed Size\": \"8 GB\",\n" +
                "        \"Battery Capacity\": \"2815 Milliamp Hours\",\n" +
                "        \"Weight\": \"7 Ounces\"\n" +
                "    },\n" +
                "    \"platformSpuId\": \"B08PP5MSVB\",\n" +
                "    \"platformSkuId\": \"B0DLBDTXPG\",\n" +
                "    \"storage\": \"128GB\",\n" +
                "    \"color\": \"Green\",\n" +
                "    \"serviceProvider\": \"Cricket\",\n" +
                "    \"grade\": \"Renewed\",\n" +
                "    \"sellers\": [\n" +
                "        {\n" +
                "            \"heading\": \"Refurbished - Acceptable\",\n" +
                "            \"price\": \"$179.99 with 28 percent savings List Price: $249.00\",\n" +
                "            \"shipping\": \"Ships from iBlueberry\",\n" +
                "            \"soldBy\": \"[iBlueberry](https://www.amazon.com/gp/aag/main?ie=UTF8&seller=A1NO4B2WVO4T9J&isAmazonFulfilled=0&asin=B08PP5MSVB&ref_=olp_merch_name_1) \"\n" +
                "        },\n" +
                "        {\n" +
                "            \"heading\": \"Refurbished - Acceptable\",\n" +
                "            \"price\": \"$184.29 with 26 percent savings List Price: $249.00\",\n" +
                "            \"shipping\": \"Ships from Amazon.com\",\n" +
                "            \"soldBy\": \"[WirelessSource](https://www.amazon.com/gp/aag/main?ie=UTF8&seller=A3J9LL0SKEEIO0&isAmazonFulfilled=1&asin=B08PP5MSVB&ref_=olp_merch_name_2) \"\n" +
                "        },\n" +
                "        {\n" +
                "            \"heading\": \"Refurbished - Good\",\n" +
                "            \"price\": \"$186.00 with 25 percent savings List Price: $249.00\",\n" +
                "            \"shipping\": \"Ships from 2ndphoneclub\",\n" +
                "            \"soldBy\": \"[2ndphoneclub](https://www.amazon.com/gp/aag/main?ie=UTF8&seller=AI86EL0CODOMZ&isAmazonFulfilled=0&asin=B08PP5MSVB&ref_=olp_merch_name_3) \"\n" +
                "        },\n" +
                "        {\n" +
                "            \"heading\": \"Refurbished - Acceptable\",\n" +
                "            \"price\": \"$189.00 with 24 percent savings List Price: $249.00\",\n" +
                "            \"shipping\": \"Ships from ELECTRONIC DEALS\",\n" +
                "            \"soldBy\": \"[ELECTRONIC DEALS](https://www.amazon.com/gp/aag/main?ie=UTF8&seller=A230ZFNKXE1WUD&isAmazonFulfilled=0&asin=B08PP5MSVB&ref_=olp_merch_name_4) \"\n" +
                "        },\n" +
                "        {\n" +
                "            \"heading\": \"Refurbished - Good\",\n" +
                "            \"price\": \"$189.76 with 24 percent savings List Price: $249.00\",\n" +
                "            \"shipping\": \"Ships from All Out Apple\",\n" +
                "            \"soldBy\": \"[All Out Apple](https://www.amazon.com/gp/aag/main?ie=UTF8&seller=AJUUH5VZRMO0E&isAmazonFulfilled=0&asin=B08PP5MSVB&ref_=olp_merch_name_5) \"\n" +
                "        },\n" +
                "        {\n" +
                "            \"heading\": \"Refurbished - Acceptable\",\n" +
                "            \"price\": \"$190.00 with 24 percent savings List Price: $249.00\",\n" +
                "            \"shipping\": \"Ships from BuybackBoss\",\n" +
                "            \"soldBy\": \"[BuybackBoss](https://www.amazon.com/gp/aag/main?ie=UTF8&seller=A321S04U16E9UY&isAmazonFulfilled=0&asin=B08PP5MSVB&ref_=olp_merch_name_6) \"\n" +
                "        },\n" +
                "        {\n" +
                "            \"heading\": \"Refurbished - Good\",\n" +
                "            \"price\": \"$194.00 with 22 percent savings List Price: $249.00\",\n" +
                "            \"shipping\": \"Ships from The Whiz Cells 2\",\n" +
                "            \"soldBy\": \"[The Whiz Cells 2](https://www.amazon.com/gp/aag/main?ie=UTF8&seller=A1Y7ZFOYKU41V8&isAmazonFulfilled=0&asin=B08PP5MSVB&ref_=olp_merch_name_7) \"\n" +
                "        },\n" +
                "        {\n" +
                "            \"heading\": \"Refurbished - Acceptable\",\n" +
                "            \"price\": \"$199.99 with 20 percent savings List Price: $249.00\",\n" +
                "            \"shipping\": \"Ships from BREED\",\n" +
                "            \"soldBy\": \"[BREED](https://www.amazon.com/gp/aag/main?ie=UTF8&seller=A3NSC2CS6ZUMAC&isAmazonFulfilled=0&asin=B08PP5MSVB&ref_=olp_merch_name_8) \"\n" +
                "        },\n" +
                "        {\n" +
                "            \"heading\": \"Refurbished - Acceptable\",\n" +
                "            \"price\": \"$206.00 with 17 percent savings List Price: $249.00\",\n" +
                "            \"shipping\": \"Ships from WDO\",\n" +
                "            \"soldBy\": \"[WDO](https://www.amazon.com/gp/aag/main?ie=UTF8&seller=AKK0KPXL0XG54&isAmazonFulfilled=0&asin=B08PP5MSVB&ref_=olp_merch_name_9) \"\n" +
                "        },\n" +
                "        {\n" +
                "            \"heading\": \"Refurbished - Acceptable\",\n" +
                "            \"price\": \"$206.00 with 17 percent savings List Price: $249.00\",\n" +
                "            \"shipping\": \"Ships from JemJem\",\n" +
                "            \"soldBy\": \"[JemJem](https://www.amazon.com/gp/aag/main?ie=UTF8&seller=AM4AB724EBAWW&isAmazonFulfilled=0&asin=B08PP5MSVB&ref_=olp_merch_name_10) \"\n" +
                "        }\n" +
                "    ],\n" +
                "    \"model\": \"iPhone 12\",\n" +
                "    \"condition\": \"Condition\",\n" +
                "    \"price\": {\n" +
                "        \"heading\": \"Refurbished - Acceptable\",\n" +
                "        \"price\": \"$179.99 with 28 percent savings List Price: $249.00\",\n" +
                "        \"shipping\": \"Ships from iBlueberry\",\n" +
                "        \"soldBy\": \"[iBlueberry](https://www.amazon.com/gp/aag/main?ie=UTF8&seller=A1NO4B2WVO4T9J&isAmazonFulfilled=0&asin=B08PP5MSVB&ref_=olp_merch_name_1) \"\n" +
                "    },\n" +
                "    \"reviewCount\": \"736\"\n" +
                "}";
        log.debug("[控制器] BestBuy测试数据创建完成");
        return jsonData;
    }

    /**
     * 创建BestBuy测试数据 (JSON格式)
     *
     * @return JSON字符串
     */
    private String createBestBuyTestDataAsJson() {
        log.debug("[控制器] 开始创建BestBuy测试数据(JSON格式)");
        String jsonData = "{\n" +
                "            \"sourcePlatform\":\"BESTBUY\",\n" +
                "            \"dataChannel\":\"API\",\n" +
                "            \"sku\": \"6525428\",\n" +
                "            \"name\": \"Apple - iPhone 15 Pro Max 512GB - Apple Intelligence - Blue Titanium (AT&T)\",\n" +
                "            \"type\": \"HardGood\",\n" +
                "            \"startDate\": \"2023-09-12\",\n" +
                "            \"isNew\": false,\n" +
                "            \"active\": true,\n" +
                "            \"lowPriceGuarantee\": false,\n" +
                "            \"activeUpdateDate\": \"2024-12-09T15:46:55\",\n" +
                "            \"regularPrice\": 1299.99,\n" +
                "            \"salePrice\": 1199.99,\n" +
                "            \"clearance\": false,\n" +
                "            \"freeShipping\": true,\n" +
                "            \"freeShippingEligible\": true,\n" +
                "            \"inStoreAvailability\": true,\n" +
                "            \"inStoreAvailabilityUpdateDate\": \"2024-12-09T15:46:55\",\n" +
                "            \"itemUpdateDate\": \"2025-07-18T02:05:28\",\n" +
                "            \"onlineAvailability\": true,\n" +
                "            \"onlineAvailabilityUpdateDate\": \"2024-12-09T15:46:55\",\n" +
                "            \"releaseDate\": \"2023-09-22\",\n" +
                "            \"specialOrder\": false,\n" +
                "            \"longDescription\": \"iPhone 15 Pro Max. Forged in titanium and featuring the groundbreaking A17 Pro chip, a customizable Action button, and the most powerful iPhone camera system ever.\",\n" +
                "            \"categoryPath\": [\n" +
                "                {\n" +
                "                    \"id\": \"cat00000\",\n" +
                "                    \"name\": \"Best Buy\"\n" +
                "                },\n" +
                "                {\n" +
                "                    \"id\": \"abcat0800000\",\n" +
                "                    \"name\": \"Cell Phones\"\n" +
                "                },\n" +
                "                {\n" +
                "                    \"id\": \"pcmcat305200050000\",\n" +
                "                    \"name\": \"iPhone\"\n" +
                "                },\n" +
                "                {\n" +
                "                    \"id\": \"pcmcat1683750935583\",\n" +
                "                    \"name\": \"All iPhone\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"customerReviewAverage\": 4.70,\n" +
                "            \"customerReviewCount\": 223,\n" +
                "            \"manufacturer\": \"Apple\",\n" +
                "            \"modelNumber\": \"MU6E3LL/A\",\n" +
                "            \"image\": \"https://pisces.bbystatic.com/prescaled/500/500/image2/BestBuy_US/images/products/6525/6525428_sd.jpg\",\n" +
                "            \"largeFrontImage\": \"https://pisces.bbystatic.com/image2/BestBuy_US/images/products/6525/6525428_sd.jpg\",\n" +
                "            \"thumbnailImage\": \"https://pisces.bbystatic.com/prescaled/108/54/image2/BestBuy_US/images/products/6525/6525428_sd.jpg\",\n" +
                "            \"largeImage\": \"https://pisces.bbystatic.com/prescaled/160/220/image2/BestBuy_US/images/products/6525/6525428_sd.jpg\",\n" +
                "            \"alternateViewsImage\": \"https://pisces.bbystatic.com/image2/BestBuy_US/images/products/6525/6525428cv11d.jpg\",\n" +
                "            \"upc\": \"400065254286\",\n" +
                "            \"url\": \"https://api.bestbuy.com/click/-/6525428/pdp\",\n" +
                "            \"mobileUrl\": \"https://api.bestbuy.com/click/-/6525428/pdp\",\n" +
                "            \"accessories\": [],\n" +
                "            \"relatedProducts\": [],\n" +
                "            \"color\": \"Blue Titanium\",\n" +
                "            \"quantityLimit\": 5,\n" +
                "            \"inStorePickup\": true,\n" +
                "            \"homeDelivery\": false,\n" +
                "            \"dollarSavings\": 100.00,\n" +
                "            \"percentSavings\": 7.69,\n" +
                "            \"onSale\": true,\n" +
                "            \"preowned\": false,\n" +
                "            \"customerTopRated\": true,\n" +
                "            \"features\": [\n" +
                "                \"FORGED IN TITANIUM &#8212; iPhone 15 Pro Max has a strong and light aerospace-grade titanium design with a textured matte-glass back. It also features a Ceramic Shield front that&#8217;s tougher than any smartphone glass. And it&#8217;s splash, water, and dust resistant.&#185;\",\n" +
                "                \"ADVANCED DISPLAY &#8212; The 6.7&#8221; Super Retina XDR display&#178; with ProMotion ramps up refresh rates to 120Hz when you need exceptional graphics performance. Dynamic Island bubbles up alerts and Live Activities. Plus, with Always-On display, your Lock Screen stays glanceable, so you don&#8217;t have to tap it to stay in the know.\",\n" +
                "                \"GAME-CHANGING A17 PRO CHIP &#8212; A Pro-class GPU makes mobile games feel so immersive, with rich environments and realistic characters. A17 Pro is also incredibly efficient and helps to deliver amazing all-day battery life.&#179;\",\n" +
                "                \"POWERFUL PRO CAMERA SYSTEM &#8212; Get incredible framing flexibility with 7 pro lenses. Capture super high-resolution photos with more color and detail using the 48MP Main camera. And take sharper close-ups from farther away with the 5x Telephoto camera on iPhone 15 Pro Max.\",\n" +
                "                \"CUSTOMIZABLE ACTION BUTTON &#8212; Action button is a fast track to your favorite feature. Just set the one you want, like Silent mode, Camera, Voice Memo, Shortcut, and more. Then press and hold to launch the action.\",\n" +
                "                \"PRO CONNECTIVITY &#8212; The new USB-C connector lets you charge your Mac or iPad with the same cable you use to charge iPhone 15 Pro Max. With USB 3, you get a huge leap in data transfer speeds.&#8304; And you can download files up to 2x faster using Wi-Fi 6E.&#8309;\",\n" +
                "                \"VITAL SAFETY FEATURES &#8212; If your car breaks down when you&#8217;re off the grid, you can get help with Roadside Assistance via satellite.&#8310; And if you need emergency services and you don&#8217;t have cell service or Wi-Fi, you can use Emergency SOS via satellite.&#8310; With Crash Detection, iPhone can detect a severe car crash and call for help if you can&#8217;t.&#8311;\",\n" +
                "                \"DESIGNED TO MAKE A DIFFERENCE &#8212; iPhone comes with privacy protections that help keep you in control of your data. It&#8217;s made from more recycled materials to minimize environmental impact. And it has built-in features that make iPhone more accessible to all.\",\n" +
                "                \"COMES WITH APPLECARE WARRANTY &#8212; Every iPhone comes with a one-year limited warranty and up to 90 days of complimentary technical support. Get AppleCare+ or AppleCare+ with Theft and Loss to extend your coverage.\"\n" +
                "            ],\n" +
                "            \"includedItemList\": [\n" +
                "                \"{includedItem=iPhone 15 Pro Max 512GB - Apple Intelligence}\"\n" +
                "            ],\n" +
                "            \"shippingCost\": 0.00,\n" +
                "            \"shippingWeight\": 0.8885,\n" +
                "            \"warrantyLabor\": \"1 year\",\n" +
                "            \"warrantyParts\": \"1 year\",\n" +
                "            \"weight\": 7.81,\n" +
                "            \"width\": 3.02,\n" +
                "            \"height\": 6.29,\n" +
                "            \"depth\": 0.32,\n" +
                "            \"condition\": \"New\"\n" +
                "        }";
        log.debug("[控制器] BestBuy测试数据创建完成");
        return jsonData;
    }

    /**
     * 健康检查接口
     * 
     * @return 健康状态
     */
    @GetMapping("/health")
    public Map<String, Object> healthCheck() {
        Map<String, Object> response = new HashMap<>();
        response.put("status", "healthy");
        response.put("message", "数据标准化服务正常运行");
        response.put("version", "1.0");
        return response;
    }

    /**
     * 调试Drools配置
     *
     * @return Drools配置信息
     */
    @GetMapping("/debug")
    public Map<String, Object> debugDrools() {
        log.info("[控制器] 开始调试Drools配置");
        Map<String, Object> result = new HashMap<>();

        try {
            // 使用已注入的KieContainer而不是重新创建
            result.put("kieContainer", "已成功注入");
            result.put("kieBaseNames", kieContainer.getKieBaseNames());
            log.debug("[控制器] KieContainer信息: kieBaseNames={}", kieContainer.getKieBaseNames());

            // 检查是否有可用的KieBase
            if (kieContainer.getKieBaseNames().isEmpty()) {
                log.warn("[控制器] 没有加载任何规则，请检查配置");
                result.put("warning", "没有加载任何规则，请检查配置");
                return result;
            }

            for (String kbaseName : kieContainer.getKieBaseNames()) {
                result.put("kieSessionNamesIn_" + kbaseName, kieContainer.getKieSessionNamesInKieBase(kbaseName));
                log.debug("[控制器] KieBase {} 中的会话名称: {}", kbaseName, kieContainer.getKieSessionNamesInKieBase(kbaseName));
            }

            // 尝试创建KieSession
            try {
                log.debug("[控制器] 尝试创建命名会话: ksession-rules");
                KieSession kieSession = kieContainer.newKieSession("ksession-rules");
                result.put("kieSession", "命名会话创建成功");
                log.info("[控制器] 命名会话创建成功");
                if (kieSession != null) {
                    kieSession.dispose();
                    log.debug("[控制器] 命名会话已释放");
                }
            } catch (Exception e) {
                log.error("[控制器] 命名会话创建失败", e);
                result.put("kieSessionError", "命名会话创建失败: " + e.getMessage());
                try {
                    log.debug("[控制器] 尝试创建默认会话");
                    KieSession kieSession = kieContainer.newKieSession();
                    result.put("defaultKieSession", "默认会话创建成功");
                    log.info("[控制器] 默认会话创建成功");
                    if (kieSession != null) {
                        kieSession.dispose();
                        log.debug("[控制器] 默认会话已释放");
                    }
                } catch (Exception ex) {
                    log.error("[控制器] 默认会话创建失败", ex);
                    result.put("defaultKieSessionError", "默认会话创建失败: " + ex.getMessage());
                }
            }
        } catch (Exception e) {
            log.error("[控制器] 调试Drools配置时发生错误", e);
            result.put("error", e.getMessage());
        }

        log.info("[控制器] Drools配置调试完成");
        return result;
    }

}
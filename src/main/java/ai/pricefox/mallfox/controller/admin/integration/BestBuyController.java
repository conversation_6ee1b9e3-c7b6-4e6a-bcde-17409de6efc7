package ai.pricefox.mallfox.controller.admin.integration;

import ai.pricefox.mallfox.convert.bestbuy.BestBuyConvert;
import ai.pricefox.mallfox.convert.bestbuy.BestBuyToProductDataConverter;
import ai.pricefox.mallfox.job.bestbuy.BestBuyApiService;
import ai.pricefox.mallfox.job.bestbuy.BestBuyProductSearchResponse;
import ai.pricefox.mallfox.service.integration.BestBuyDataService;
import ai.pricefox.mallfox.service.rules.DataStandardizationService;
import ai.pricefox.mallfox.vo.base.CommonResult;
import ai.pricefox.mallfox.vo.bestbuy.BestBuyProductDetailRespVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * BestBuy API 控制器
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/admin/v1/bestbuy")
@Validated
@Tag(name = "管理后台-BestBuy管理", description = "BestBuy 商品信息接口")
public class BestBuyController {

    private final BestBuyApiService bestBuyApiService;
    private final BestBuyConvert bestBuyConvert;
    private final BestBuyDataService bestBuyDataService;

    /**
     * 获取 BestBuy 产品详情
     *
     * @param sku      BestBuy 产品 SKU
     * @param syncData 是否同步数据到产品数据库
     * @return 产品详情
     */
    @GetMapping("/product/{sku}")
    @Operation(summary = "获取 BestBuy 产品详情", description = "根据 SKU 获取 BestBuy 产品的详细信息")
    public CommonResult<BestBuyProductDetailRespVO> getProductDetail(@Parameter(description = "BestBuy 产品 SKU", example = "6418599") @PathVariable String sku, @Parameter(description = "是否同步数据到产品数据库", example = "false") @RequestParam(defaultValue = "false") Boolean syncData) {

        try {
            log.info("获取 BestBuy 产品详情，SKU: {}, syncData: {}", sku, syncData);

            // 检查服务是否可用
            if (!bestBuyApiService.isServiceAvailable()) {
                return CommonResult.error(500, "BestBuy API 服务不可用，请检查API密钥配置");
            }

            BestBuyProductDetailRespVO respVO = bestBuyApiService.getProductDetail(sku);

            // 如果需要同步数据，则调用数据同步服务
            if (syncData) {
                try {
                    log.info("开始同步 BestBuy 产品数据到产品数据库，SKU: {}", sku);

                    // TODO: 实现数据同步逻辑
                    // 转换为 ProductDataDTO
//                     ProductDataDTO productDataDTO = bestBuyToProductDataConverter.convertToProductDataDTO(respVO);

                    // 标准化为标准产品数据
//                    DynamicStandardProduct dynamicStandardProduct = dataStandardizationService.standardizeProductData(productDataDTO);

                    log.info("成功同步 BestBuy 产品数据，SKU: {}", sku);
                } catch (Exception syncException) {
                    log.error("同步 BestBuy 产品数据失败，SKU: {}", sku, syncException);
                    // 同步失败不影响主要功能，只记录错误
                }
            }

            log.info("成功获取 BestBuy 产品详情，SKU: {}", sku);
            return CommonResult.success(respVO);

        } catch (Exception e) {
            log.error("获取 BestBuy 产品详情失败，SKU: {}", sku, e);
            return CommonResult.error(500, "获取产品详情失败: " + e.getMessage());
        }
    }

    /**
     * 搜索产品 (GET)
     *
     * @param query  搜索关键字
     * @param limit  返回数量限制
     * @param offset 偏移量
     * @return 搜索结果
     */
    @GetMapping("/search")
    @Operation(summary = "搜索 BestBuy 产品", description = "根据关键字搜索 BestBuy 产品")
    public CommonResult<List<BestBuyProductDetailRespVO>> searchProducts(@Parameter(description = "搜索关键字", example = "iPhone", required = true) @RequestParam String query, @Parameter(description = "返回数量限制", example = "10") @RequestParam(defaultValue = "10") Integer limit, @Parameter(description = "偏移量", example = "0") @RequestParam(defaultValue = "0") Integer offset) {

        bestBuyDataService.searchProducts(query, limit, offset);
        return CommonResult.success();
    }

    /**
     * 获取 API 服务状态
     *
     * @return API 服务状态信息
     */
    @GetMapping("/status")
    @Operation(summary = "获取 BestBuy API 服务状态", description = "检查 BestBuy API 服务的可用性和配置状态")
    public CommonResult<String> getApiStatus() {
        try {
            String apiInfo = bestBuyApiService.getApiInfo();
            boolean isAvailable = bestBuyApiService.isServiceAvailable();

            String status = String.format("服务状态: %s, %s", isAvailable ? "可用" : "不可用", apiInfo);

            return CommonResult.success(status);
        } catch (Exception e) {
            log.error("获取 BestBuy API 状态失败", e);
            return CommonResult.error(500, "获取API状态失败: " + e.getMessage());
        }
    }
}

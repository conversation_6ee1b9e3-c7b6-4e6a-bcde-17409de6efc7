package ai.pricefox.mallfox.controller.admin.mapping;

import ai.pricefox.mallfox.model.param.AttributeMappingCreateRequest;
import ai.pricefox.mallfox.model.param.AttributeMappingQueryRequest;
import ai.pricefox.mallfox.model.param.AttributeMappingUpdateRequest;
import ai.pricefox.mallfox.model.response.AttributeMappingBatchImportResultResponse;
import ai.pricefox.mallfox.model.response.AttributeMappingDetailResponse;
import ai.pricefox.mallfox.model.response.AttributeMappingResponse;
import ai.pricefox.mallfox.model.response.ExportFileResponse;
import ai.pricefox.mallfox.service.mapping.AttributeMappingService;
import ai.pricefox.mallfox.vo.base.CommonResult;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 属性映射管理
 *
 * <AUTHOR>
 * @date 2025-08-03
 */
@RestController
@AllArgsConstructor
@RequestMapping("/admin/v1/attribute-mapping")
@Tag(name = "管理后台-属性映射管理", description = "属性映射管理相关接口")
public class AttributeMappingController {

    private final AttributeMappingService attributeMappingService;

    /**
     * 属性映射列表
     *
     * @param queryRequest 查询请求
     * @return 分页列表
     */
    @Operation(summary = "属性映射分页列表", description = "分页查询属性映射列表，支持按属性编码和映射名称筛选")
    @GetMapping("/page")
    public CommonResult<Page<AttributeMappingResponse>> getAttributeMappingList(@Valid AttributeMappingQueryRequest queryRequest) {
        return attributeMappingService.getAttributeMappingList(queryRequest);
    }

    /**
     * 新建属性映射
     *
     * @param reqVO 创建请求
     * @return 创建结果
     */
    @Operation(summary = "新建属性映射", description = "创建新的属性映射关系")
    @PostMapping("/add")
    public CommonResult<AttributeMappingResponse> createAttributeMapping(@Valid @RequestBody AttributeMappingCreateRequest reqVO) {
        return attributeMappingService.createAttributeMapping(reqVO);
    }

    /**
     * 编辑属性映射
     *
     * @param reqVO 更新请求
     * @return 更新结果
     */
    @Operation(summary = "编辑属性映射", description = "编辑属性映射关系")
    @PostMapping("/edit")
    public CommonResult<AttributeMappingResponse> updateAttributeMapping(@Valid @RequestBody AttributeMappingUpdateRequest reqVO) {
        return attributeMappingService.updateAttributeMapping(reqVO);
    }

    /**
     * 删除属性映射（支持批量）
     *
     * @param ids 映射ID列表
     * @return 删除结果
     */
    @Operation(summary = "删除属性映射", description = "根据ID删除属性映射关系，支持批量删除")
    @DeleteMapping("/batch-delete")
    public CommonResult<Boolean> deleteAttributeMapping(
            @Parameter(description = "映射ID列表", example = "[1,2,3]") @RequestBody List<Long> ids) {
        return attributeMappingService.batchDeleteAttributeMapping(ids);
    }

    /**
     * 删除单个属性映射
     *
     * @param id 映射ID
     * @return 删除结果
     */
    @Operation(summary = "删除单个属性映射", description = "根据ID删除单个属性映射关系")
    @DeleteMapping("/delete")
    public CommonResult<Boolean> deleteAttributeMappingById(
            @Parameter(description = "映射ID", example = "1") Long id) {
        return attributeMappingService.deleteAttributeMapping(id);
    }


    /**
     * 属性映射详情
     *
     * @param id 属性映射ID
     * @return 属性映射详情
     */
    @Operation(summary = "属性映射详情", description = "根据ID查询属性映射详情信息")
    @GetMapping("/detail")
    public CommonResult<AttributeMappingDetailResponse> getAttributeMappingById(
            @Parameter(description = "属性映射ID", example = "1") Long id) {
        return attributeMappingService.getAttributeMappingById(id);
    }

    /**
     * 批量新增属性映射-属性和属性值
     *
     * @param file Excel文件
     * @return 导入结果
     */
    @Operation(summary = "批量新增", description = "通过Excel文件批量导入属性映射")
    @PostMapping("/batch-import")
    public CommonResult<AttributeMappingBatchImportResultResponse> batchImportAttributeMapping(
            @Parameter(description = "Excel文件", required = true) @RequestParam("file") MultipartFile file) {
        return attributeMappingService.batchImportAttributeMapping(file);
    }

    /**
     * 导出属性映射
     *
     * @param exportType 导出类型：filtered-筛选结果数据，all-全部数据
     * @param queryRequest 查询条件（当exportType为filtered时使用）
     * @return 导出文件信息
     */
    @Operation(summary = "导出属性映射", description = "导出筛选条件下的属性映射数据至Excel表格，支持导出筛选结果或全部数据")
    @GetMapping("/export")
    public CommonResult<ExportFileResponse> exportAttributeMapping(
            @Parameter(description = "导出类型", example = "filtered") @RequestParam(defaultValue = "filtered") String exportType,
            @Valid AttributeMappingQueryRequest queryRequest) {
        return attributeMappingService.exportAttributeMapping(exportType, queryRequest);
    }

}

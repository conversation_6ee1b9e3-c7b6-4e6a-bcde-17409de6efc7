package ai.pricefox.mallfox.controller.admin.mapping;

import ai.pricefox.mallfox.model.param.AttributeValueMappingCreateRequest;
import ai.pricefox.mallfox.model.param.AttributeValueMappingEditRequest;
import ai.pricefox.mallfox.model.param.AttributeValueMappingQueryRequest;
import ai.pricefox.mallfox.model.response.AttributeValueMappingDetailResponse;
import ai.pricefox.mallfox.model.response.AttributeValueMappingPageInfo;
import ai.pricefox.mallfox.model.response.AttributeValueMappingResponse;
import ai.pricefox.mallfox.model.response.AttributeValueOption;
import ai.pricefox.mallfox.model.response.ExportFileResponse;
import ai.pricefox.mallfox.service.mapping.AttributeValueMappingService;
import ai.pricefox.mallfox.vo.base.CommonResult;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 属性值映射管理
 *
 * <AUTHOR>
 * @date 2025-08-04
 */
@RestController
@AllArgsConstructor
@RequestMapping("/admin/v1/attribute-value-mapping")
@Tag(name = "管理后台-属性值映射管理", description = "属性值映射管理相关接口")
public class AttributeValueMappingController {

    private final AttributeValueMappingService attributeValueMappingService;

    /**
     * 属性值映射列表
     *
     * @param queryRequest 查询请求
     * @return 属性值映射列表
     */
    @Operation(summary = "属性值映射分页列表", description = "根据属性ID查询属性值映射列表，支持按属性值编码、名称筛选")
    @GetMapping("/page")
    public CommonResult<Page<AttributeValueMappingResponse>> getAttributeValueMappingList(@Valid AttributeValueMappingQueryRequest queryRequest) {
        return attributeValueMappingService.getAttributeValueMappingList(queryRequest);
    }

    /**
     * 获取属性值选项
     *
     * @param attributeCode 属性编码
     * @param valueName 属性值名称（模糊搜索）
     * @return 属性值选项列表
     */
    @Operation(summary = "获取属性值选项", description = "获取指定属性下的属性值选项，用于新建时选择")
    @GetMapping("/attribute-values")
    public CommonResult<List<AttributeValueOption>> getAttributeValueOptions(
            @Parameter(description = "属性编码", example = "ATTR001") @RequestParam String attributeCode,
            @Parameter(description = "属性值名称", example = "Apple") @RequestParam(required = false) String valueName) {
        return attributeValueMappingService.getAttributeValueOptions(attributeCode, valueName);
    }

    /**
     * 获取属性值映射详情
     *
     * @param valueCode 属性值编码
     * @return 属性值映射详情
     */
    @Operation(summary = "属性值映射详情", description = "获取属性值映射详情信息，用于抽屉页展示")
    @GetMapping("/detail")
    public CommonResult<AttributeValueMappingDetailResponse> getAttributeValueMappingDetail(
            @Parameter(description = "属性值编码", example = "VALUE001") @RequestParam String valueCode) {
        return attributeValueMappingService.getAttributeValueMappingDetail(valueCode);
    }

    /**
     * 新建属性值映射
     *
     * @param reqVO 创建请求
     * @return 创建结果
     */
    @Operation(summary = "新建属性值映射", description = "为选定的属性值创建映射关系")
    @PostMapping("/add")
    public CommonResult<AttributeValueMappingDetailResponse> createAttributeValueMapping(@Valid @RequestBody AttributeValueMappingCreateRequest reqVO) {
        return attributeValueMappingService.createAttributeValueMapping(reqVO);
    }

    /**
     * 编辑属性值映射
     *
     * @param reqVO 编辑请求
     * @return 编辑结果
     */
    @Operation(summary = "编辑属性值映射", description = "编辑属性值的映射名称列表")
    @PostMapping("/edit")
    public CommonResult<Boolean> updateAttributeValueMapping(@Valid @RequestBody AttributeValueMappingEditRequest reqVO) {
        return attributeValueMappingService.updateAttributeValueMapping(reqVO);
    }

    /**
     * 删除属性值映射
     *
     * @param valueCode 属性值编码
     * @return 删除结果
     */
    @Operation(summary = "删除属性值映射", description = "删除指定属性值的所有映射关系")
    @DeleteMapping("/delete")
    public CommonResult<Boolean> deleteAttributeValueMapping(
            @Parameter(description = "属性值编码", example = "VALUE001") String valueCode) {
        return attributeValueMappingService.deleteAttributeValueMapping(valueCode);
    }

    /**
     * 批量删除属性值映射
     *
     * @param valueCodes 属性值编码列表
     * @return 删除结果
     */
    @Operation(summary = "批量删除属性值映射", description = "批量删除属性值映射关系")
    @DeleteMapping("/batch-delete")
    public CommonResult<Boolean> batchDeleteAttributeValueMapping(
            @Parameter(description = "属性值编码列表", example = "['VALUE001','VALUE002']") @RequestBody List<String> valueCodes) {
        return attributeValueMappingService.batchDeleteAttributeValueMapping(valueCodes);
    }

    /**
     * 导出属性值映射
     *
     * @param attributeId 属性映射ID
     * @param valueCode 属性值编码
     * @param valueName 属性值名称
     * @param valueNameCn 属性值CN
     * @return 导出文件信息
     */
    @Operation(summary = "导出属性值映射", description = "导出属性值映射数据至Excel表格")
    @GetMapping("/export")
    public CommonResult<ExportFileResponse> exportAttributeValueMapping(
            @Parameter(description = "属性映射ID", example = "1") @RequestParam Long attributeId,
            @Parameter(description = "属性值编码", example = "VALUE001") @RequestParam(required = false) String valueCode,
            @Parameter(description = "属性值名称", example = "Apple") @RequestParam(required = false) String valueName,
            @Parameter(description = "属性值CN", example = "苹果") @RequestParam(required = false) String valueNameCn) {
        AttributeValueMappingQueryRequest queryRequest = new AttributeValueMappingQueryRequest();
        queryRequest.setAttributeId(attributeId);
        queryRequest.setValueCode(valueCode);
        queryRequest.setValueName(valueName);
        queryRequest.setValueNameCn(valueNameCn);
        return attributeValueMappingService.exportAttributeValueMapping(queryRequest);
    }
}
package ai.pricefox.mallfox.controller.admin.mapping;

import ai.pricefox.mallfox.model.param.CategoryMappingCreateRequest;
import ai.pricefox.mallfox.model.param.CategoryMappingUpdateRequest;
import ai.pricefox.mallfox.model.response.CategoryMappingResponse;
import ai.pricefox.mallfox.model.response.CategoryMappingBatchUpdateResultResponse;
import ai.pricefox.mallfox.model.response.ExportFileResponse;
import ai.pricefox.mallfox.service.mapping.CategoryMappingService;
import ai.pricefox.mallfox.vo.base.CommonResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 类目映射管理
 *
 * <AUTHOR>
 * @date 2025-08-02
 */
@RestController
@AllArgsConstructor
@RequestMapping("/admin/v1/category-mapping")
@Tag(name = "管理后台-类目映射管理", description = "类目映射管理相关接口")
public class CategoryMappingController {

    private final CategoryMappingService categoryMappingService;

    /**
     * 获取类目映射树形结构列表
     *
     * @param categoryCode   类目编码
     * @param categoryName   类目名称
     * @param categoryNameCn 类目中文名称
     * @return 树形结构列表
     */
    @Operation(summary = "分页查询类目映射列表", description = "树形结构展示，默认展开，支持筛选：类目编码（精确）、类目名称（模糊）、类目CN（模糊）")
    @GetMapping("/tree")
    public CommonResult<List<CategoryMappingResponse>> getCategoryMappingTree(
            @Parameter(description = "类目编码", example = "ECOM00000001") @RequestParam(required = false) String categoryCode,
            @Parameter(description = "类目名称", example = "Electronics") @RequestParam(required = false) String categoryName,
            @Parameter(description = "类目中文名称", example = "电子产品") @RequestParam(required = false) String categoryNameCn) {
        return categoryMappingService.getCategoryMappingTree(categoryCode, categoryName, categoryNameCn);
    }

    /**
     * 新建类目映射
     *
     * @param reqVO 创建请求
     * @return 创建结果
     */
    @Operation(summary = "新建类目映射", description = "创建新的类目映射关系")
    @PostMapping("/add")
    public CommonResult<CategoryMappingResponse> createCategoryMapping(@Valid @RequestBody CategoryMappingCreateRequest reqVO) {
        return categoryMappingService.createCategoryMapping(reqVO);
    }

    /**
     * 编辑类目映射关系
     *
     * @param reqVO 更新请求
     * @return 更新结果
     */
    @Operation(summary = "编辑类目映射关系", description = "编辑类目映射关系")
    @PostMapping("/edit")
    public CommonResult<CategoryMappingResponse> updateCategoryMapping(@Valid @RequestBody CategoryMappingUpdateRequest reqVO) {
        return categoryMappingService.updateCategoryMapping(reqVO);
    }


    /**
     * 根据ID查询类目映射详情
     *
     * @param id 映射ID
     * @return 映射详情
     */
    @Operation(summary = "查询类目映射详情", description = "根据ID查询类目映射详细信息")
    @GetMapping("/detail")
    public CommonResult<CategoryMappingResponse> getCategoryMapping(
            @Parameter(description = "映射ID", example = "1") @RequestParam Long id) {
        return categoryMappingService.getCategoryMappingById(id);
    }

    /**
     * 删除类目映射关系
     *
     * @param id 映射ID
     * @return 删除结果
     */
    @Operation(summary = "删除映射关系", description = "根据ID删除类目映射关系")
    @DeleteMapping("/delete")
    public CommonResult<Boolean> deleteCategoryMapping(
            @Parameter(description = "映射ID", example = "1") @RequestParam Long id) {
        return categoryMappingService.deleteCategoryMapping(id);
    }

    /**
     * 批量更新类目映射
     *
     * @param file Excel文件
     * @return 更新结果
     */
    @Operation(summary = "批量更新类目映射", description = "通过Excel文件批量更新类目映射关系，采用增量更新方式")
    @PostMapping("/batch-update")
    public CommonResult<CategoryMappingBatchUpdateResultResponse> batchUpdateCategoryMapping(
            @Parameter(description = "Excel文件", required = true) @RequestParam("file") MultipartFile file) {
        return categoryMappingService.batchUpdateCategoryMapping(file);
    }


//    @Operation(summary = "下载更新模版", description = "下载类目映射批量更新的Excel模版")
//    @GetMapping("/template")
//    public CommonResult<String> downloadTemplate() {
//        return categoryMappingService.downloadTemplate();
//    }

    /**
     * 导出类目映射
     *
     * @param categoryCode   类目编码
     * @param categoryName   类目名称
     * @param categoryNameCn 类目中文名称
     * @return 导出文件信息
     */
    @Operation(summary = "导出类目映射", description = "导出筛选条件下的类目映射数据至Excel表格")
    @GetMapping("/export")
    public CommonResult<ExportFileResponse> exportCategoryMapping(
            @Parameter(description = "类目编码", example = "ECOM00000001") @RequestParam(required = false) String categoryCode,
            @Parameter(description = "类目名称", example = "Electronics") @RequestParam(required = false) String categoryName,
            @Parameter(description = "类目中文名称", example = "电子产品") @RequestParam(required = false) String categoryNameCn) {
        return categoryMappingService.exportCategoryMapping(categoryCode, categoryName, categoryNameCn);
    }
}

package ai.pricefox.mallfox.controller.admin.mapping;

import ai.pricefox.mallfox.model.param.PlatformCategoryCreateRequest;
import ai.pricefox.mallfox.model.param.PlatformCategoryUpdateRequest;
import ai.pricefox.mallfox.model.response.ExportFileResponse;
import ai.pricefox.mallfox.model.response.PlatformCategoryResponse;
import ai.pricefox.mallfox.service.mapping.PlatformCategoryService;
import ai.pricefox.mallfox.vo.base.CommonResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 平台类目管理
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
@RestController
@AllArgsConstructor
@RequestMapping("/admin/v1/platform-categories")
@Tag(name = "管理后台-平台类目管理", description = "平台类目管理相关接口")
public class PlatformCategoryController {

    private final PlatformCategoryService platformCategoryService;

    /**
     * 获取平台类目树形结构
     *
     * @param platformCode 平台编码
     * @param categoryName 类目名称
     * @param categoryNameCn 类目中文名称
     * @return 树形结构
     */
    @Operation(summary = "获取平台类目树形结构", description = "获取平台类目树形结构，支持查询类目名称和中文名称")
    @GetMapping("/tree")
    public CommonResult<List<PlatformCategoryResponse>> getPlatformCategoryTree(
            @Parameter(description = "平台编码", example = "ECOM00000001") @RequestParam(required = false) String platformCode,
            @Parameter(description = "类目名称", example = "Electronics") @RequestParam(required = false) String categoryName,
            @Parameter(description = "类目中文名称", example = "电子产品") @RequestParam(required = false) String categoryNameCn) {
        return platformCategoryService.getPlatformCategoryTree(platformCode, categoryName, categoryNameCn);
    }

    /**
     * 创建平台类目
     *
     * @param reqVO 创建请求
     * @return 创建结果
     */
    @Operation(summary = "创建平台类目", description = "创建新的平台类目，支持一级和子级")
    @PostMapping("/add")
    public CommonResult<PlatformCategoryResponse> createPlatformCategory(@Valid @RequestBody PlatformCategoryCreateRequest reqVO) {
        return platformCategoryService.createPlatformCategory(reqVO);
    }

    /**
     * 更新平台类目
     *
     * @param reqVO 更新请求
     * @return 更新结果
     */
    @Operation(summary = "更新平台类目", description = "更新平台类目信息")
    @PostMapping("/edit")
    public CommonResult<PlatformCategoryResponse> updatePlatformCategory(@Valid @RequestBody PlatformCategoryUpdateRequest reqVO) {
        return platformCategoryService.updatePlatformCategory(reqVO);
    }

    /**
     * 根据ID查询平台类目详情
     *
     * @param id 类目ID
     * @return 类目详情
     */
    @Operation(summary = "查询平台类目详情", description = "根据ID查询平台类目详细信息")
    @GetMapping("/detail")
    public CommonResult<PlatformCategoryResponse> getPlatformCategory(
            @Parameter(description = "类目ID", example = "1") @RequestParam Long id) {
        return platformCategoryService.getPlatformCategoryById(id);
    }

    /**
     * 删除平台类目
     *
     * @param id 类目ID
     * @return 删除结果
     */
    @Operation(summary = "删除平台类目", description = "根据ID删除平台类目")
    @DeleteMapping("/delete")
    public CommonResult<Boolean> deletePlatformCategory(
            @Parameter(description = "类目ID", example = "1") @RequestParam Long id) {
        return platformCategoryService.deletePlatformCategory(id);
    }

    /**
     * 导出平台类目
     *
     * @param platformCode 平台编码
     * @param categoryName 类目名称
     * @param categoryNameCn 类目中文名称
     * @return 导出文件信息
     */
    @Operation(summary = "导出平台类目", description = "导出筛选条件下的平台类目数据至Excel表格")
    @GetMapping("/export")
    public CommonResult<ExportFileResponse> exportPlatformCategories(
            @Parameter(description = "平台编码", example = "TMALL") @RequestParam(required = false) String platformCode,
            @Parameter(description = "类目名称", example = "Electronics") @RequestParam(required = false) String categoryName,
            @Parameter(description = "类目中文名称", example = "电子产品") @RequestParam(required = false) String categoryNameCn) {
        return platformCategoryService.exportPlatformCategories(platformCode, categoryName, categoryNameCn);
    }
}
package ai.pricefox.mallfox.controller.admin.product;

import ai.pricefox.mallfox.service.standard.StandardAttributeService;
import ai.pricefox.mallfox.vo.attribute.*;
import ai.pricefox.mallfox.vo.base.CommonResult;
import ai.pricefox.mallfox.vo.base.PageResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 商品属性管理
 *
 * <AUTHOR>
 * @date 2025-08-03
 */
@RestController
@AllArgsConstructor
@RequestMapping("/admin/v1/attributes")
@Tag(name = "管理后台-商品属性管理", description = "商品属性管理相关接口")
public class AttributeController {

    private final StandardAttributeService standardAttributeService;

    /**
     * 创建属性
     *
     * @param reqVO 创建请求
     * @return 创建结果
     */
    @Operation(summary = "创建属性", description = "创建新的商品属性")
    @PostMapping
    public CommonResult<AttributeDetailRespVO> createAttribute(@Valid @RequestBody AttributeCreateReqVO reqVO) {
        return standardAttributeService.createAttribute(reqVO);
    }

    /**
     * 更新属性
     *
     * @param reqVO 更新请求
     * @return 更新结果
     */
    @Operation(summary = "更新属性", description = "根据ID更新属性信息")
    @PutMapping("/{id}")
    public CommonResult<AttributeDetailRespVO> updateAttribute(@Valid @RequestBody AttributeUpdateReqVO reqVO) {
        return standardAttributeService.updateAttribute(reqVO);
    }

    /**
     * 分页查询属性列表
     *
     * @param reqVO 查询条件
     * @return 分页结果
     */
    @Operation(summary = "分页查询属性", description = "分页查询属性列表")
    @GetMapping
    public CommonResult<PageResult<AttributeDetailRespVO>> getAttributePage(AttributePageReqVO reqVO) {
        return standardAttributeService.getAttributePage(reqVO);
    }

    /**
     * 根据ID获取属性详情
     *
     * @param id 属性ID
     * @return 属性详情
     */
    @Operation(summary = "获取属性详情", description = "根据ID获取属性详细信息")
    @GetMapping("/{id}")
    public CommonResult<AttributeDetailRespVO> getAttributeById(
            @Parameter(description = "属性ID", example = "1") @PathVariable Long id) {
        return standardAttributeService.getAttributeById(id);
    }

    /**
     * 删除属性
     *
     * @param id 属性ID
     * @return 删除结果
     */
    @Operation(summary = "删除属性", description = "根据ID删除属性")
    @DeleteMapping("/{id}")
    public CommonResult<Boolean> deleteAttribute(
            @Parameter(description = "属性ID", example = "1") @PathVariable Long id) {
        return standardAttributeService.deleteAttribute(id);
    }

    /**
     * 批量删除属性
     *
     * @param reqVO 批量删除请求
     * @return 删除结果
     */
    @Operation(summary = "批量删除属性", description = "批量删除属性，自动过滤有映射关系的属性")
    @DeleteMapping("/batch")
    public CommonResult<String> batchDeleteAttributes(@Valid @RequestBody AttributeBatchDeleteReqVO reqVO) {
        return standardAttributeService.batchDeleteAttributes(reqVO);
    }

    /**
     * 批量更新类目属性和属性值
     *
     * @param file Excel文件
     * @return 更新结果
     */
    @Operation(summary = "批量更新属性", description = "通过Excel文件批量更新类目属性和属性值")
    @PostMapping("/batch-update")
    public CommonResult<AttributeBatchUpdateResultRespVO> batchUpdateAttributes(
            @Parameter(description = "Excel文件") @RequestParam("file") MultipartFile file) {
        return standardAttributeService.batchUpdateAttributes(file);
    }

    /**
     * 下载更新模版
     *
     * @return 模版文件信息（URL和文件名）
     */
    @Operation(summary = "下载更新模版", description = "下载属性批量更新的Excel模版，支持15天缓存")
    @GetMapping("/template")
    public CommonResult<AttributeFileRespVO> downloadTemplate() {
        return standardAttributeService.downloadTemplate();
    }

    /**
     * 导出属性数据
     *
     * @param reqVO 导出条件
     * @return 导出文件信息（URL和文件名）
     */
    @Operation(summary = "导出属性", description = "导出属性数据到Excel")
    @GetMapping("/export")
    public CommonResult<AttributeFileRespVO> exportAttributes(AttributePageReqVO reqVO) {
        return standardAttributeService.exportAttributes(reqVO);
    }

    // ========== 规格管理相关接口 ==========

    /**
     * 新增规格
     *
     * @param id 属性ID
     * @return 操作结果
     */
    @Operation(summary = "新增规格", description = "将指定属性标记为规格")
    @PostMapping("/{id}/spec")
    public CommonResult<Boolean> createSpec(
            @Parameter(description = "属性ID", example = "1") @PathVariable Long id) {
        return standardAttributeService.createSpec(id);
    }

    /**
     * 删除规格
     *
     * @param id 属性ID
     * @return 操作结果
     */
    @Operation(summary = "删除规格", description = "取消属性的规格标记")
    @DeleteMapping("/{id}/spec")
    public CommonResult<Boolean> deleteSpec(
            @Parameter(description = "属性ID", example = "1") @PathVariable Long id) {
        return standardAttributeService.deleteSpec(id);
    }

    /**
     * 规格分页列表
     *
     * @param reqVO 查询条件
     * @return 分页结果
     */
    @Operation(summary = "规格分页列表", description = "分页查询规格列表")
    @GetMapping("/specs")
    public CommonResult<PageResult<SpecRespVO>> getSpecPage(SpecPageReqVO reqVO) {
        return standardAttributeService.getSpecPage(reqVO);
    }

    /**
     * 属性列表
     *
     * @param reqVO 查询条件
     * @return 属性列表
     */
    @Operation(summary = "属性列表", description = "查询属性列表，用于规格选择")
    @GetMapping("/list")
    public CommonResult<List<AttributeListRespVO>> getAttributeList(AttributeListReqVO reqVO) {
        return standardAttributeService.getAttributeList(reqVO);
    }
}

package ai.pricefox.mallfox.controller.admin.product;

import ai.pricefox.mallfox.service.standard.StandardBrandService;
import ai.pricefox.mallfox.vo.base.CommonResult;
import ai.pricefox.mallfox.vo.base.PageResult;
import ai.pricefox.mallfox.vo.brand.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 品牌管理
 *
 * <AUTHOR>
 * @date 2025-07-04
 */
@RestController
@AllArgsConstructor
@RequestMapping("/admin/v1/brands")
@Tag(name = "管理后台-品牌管理", description = "品牌管理相关接口")
public class BrandController {

    private final StandardBrandService standardBrandService;

    // ==================== 品牌管理 ====================

    /**
     * 创建品牌
     *
     * @param reqVO 创建请求
     * @return 创建结果
     */
    @Operation(summary = "创建品牌", description = "创建新的品牌")
    @PostMapping
    public CommonResult<BrandInfoRespVO> createBrand(@Valid @RequestBody BrandInfoCreateReqVO reqVO) {
        return standardBrandService.createBrandInfo(reqVO);
    }

    /**
     * 更新品牌
     *
     * @param reqVO 更新请求
     * @return 更新结果
     */
    @Operation(summary = "更新品牌", description = "更新品牌信息")
    @PutMapping("/{id}")
    public CommonResult<BrandInfoRespVO> updateBrand(@Valid @RequestBody BrandInfoUpdateReqVO reqVO) {
        return standardBrandService.updateBrandInfo(reqVO);
    }

    /**
     * 根据ID获取品牌详情
     *
     * @param id 品牌ID
     * @return 品牌详情
     */
    @Operation(summary = "获取品牌详情", description = "根据ID获取品牌详细信息")
    @GetMapping("/{id}")
    public CommonResult<BrandInfoRespVO> getBrand(
            @Parameter(description = "品牌ID", example = "1") @PathVariable Long id) {
        return standardBrandService.getBrandInfoById(id);
    }

    /**
     * 分页查询品牌
     *
     * @param reqVO 分页查询请求
     * @return 分页结果
     */
    @Operation(summary = "分页查询品牌", description = "分页查询品牌列表")
    @GetMapping
    public CommonResult<PageResult<BrandInfoRespVO>> getBrandPage(BrandInfoPageReqVO reqVO) {
        return standardBrandService.getBrandInfoPage(reqVO);
    }

    /**
     * 获取所有品牌列表
     *
     * @return 品牌列表
     */
    @Operation(summary = "获取所有品牌列表", description = "获取所有品牌列表，无需分页")
    @GetMapping("/list")
    public CommonResult<List<BrandInfoRespVO>> getAllBrands() {
        return standardBrandService.getAllBrandInfos();
    }

    /**
     * 删除品牌
     *
     * @param id 品牌ID
     * @return 删除结果
     */
    @Operation(summary = "删除品牌", description = "根据ID删除品牌")
    @DeleteMapping("/{id}")
    public CommonResult<Boolean> deleteBrand(
            @Parameter(description = "品牌ID", example = "1") @PathVariable Long id) {
        return standardBrandService.deleteBrandInfo(id);
    }
}

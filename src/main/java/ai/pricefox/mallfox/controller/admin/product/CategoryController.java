package ai.pricefox.mallfox.controller.admin.product;

import ai.pricefox.mallfox.service.standard.StandardCategoryService;
import ai.pricefox.mallfox.vo.base.CommonResult;
import ai.pricefox.mallfox.vo.base.PageResult;
import ai.pricefox.mallfox.vo.category.*;
import cn.hutool.core.lang.tree.Tree;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 分类管理
 *
 * <AUTHOR>
 * @date 2025-05-18 12:19:29
 */
@RestController
@AllArgsConstructor
@RequestMapping("/admin/v1/categories")
@Tag(name = "管理后台-分类管理", description = "分类管理相关接口")
public class CategoryController {

    private final StandardCategoryService standardCategoryService;

    // ==================== 分类管理 ====================

    /**
     * 获取分类树形结构
     *
     * @param reqVO 查询条件
     * @return 分类树
     */
    @Operation(summary = "获取分类树", description = "获取分类树形结构列表")
    @GetMapping("/tree")
    public CommonResult<List<CategoryTreeRespVO>> getCategoryTree(CategoryListReqVO reqVO) {
        return standardCategoryService.getCategoryTree(reqVO);
    }

    /**
     * 分页查询分类列表
     *
     * @param reqVO 查询条件
     * @return 分页结果
     */
    @Operation(summary = "分页查询分类", description = "分页查询分类列表")
    @GetMapping("/list")
    public CommonResult<PageResult<CategoryDetailRespVO>> getCategoryList(CategoryListReqVO reqVO) {
        return standardCategoryService.getCategoryList(reqVO);
    }

    /**
     * 创建分类
     *
     * @param reqVO 创建请求
     * @return 创建结果
     */
    @Operation(summary = "创建分类", description = "创建新的分类")
    @PostMapping
    public CommonResult<CategoryDetailRespVO> createCategory(@Valid @RequestBody CategoryCreateReqVO reqVO) {
        return standardCategoryService.createCategory(reqVO);
    }

    /**
     * 更新分类
     *
     * @param reqVO 更新请求
     * @return 更新结果
     */
    @Operation(summary = "更新分类", description = "根据ID更新分类信息")
    @PutMapping("/{id}")
    public CommonResult<CategoryDetailRespVO> updateCategory(@Valid @RequestBody CategoryUpdateReqVO reqVO) {
        return standardCategoryService.updateCategory(reqVO);
    }

    /**
     * 根据ID获取分类详情
     *
     * @param id 分类ID
     * @return 分类详情
     */
    @Operation(summary = "获取分类详情", description = "根据ID获取分类详细信息")
    @GetMapping("/{id}")
    public CommonResult<CategoryDetailRespVO> getCategoryById(
            @Parameter(description = "分类ID", example = "1") @PathVariable Long id) {
        return standardCategoryService.getCategoryById(id);
    }

    /**
     * 删除分类
     *
     * @param id 分类ID
     * @return 删除结果
     */
    @Operation(summary = "删除分类", description = "根据ID删除分类")
    @DeleteMapping("/{id}")
    public CommonResult<Boolean> deleteCategory(
            @Parameter(description = "分类ID", example = "1") @PathVariable Long id) {
        return standardCategoryService.deleteCategory(id);
    }

    /**
     * 导出分类数据
     *
     * @param reqVO 导出条件
     * @return 导出文件路径
     */
    @Operation(summary = "导出分类", description = "导出分类数据到Excel")
    @GetMapping("/export")
    public CommonResult<String> exportCategories(CategoryListReqVO reqVO) {
        return standardCategoryService.exportCategories(reqVO);
    }
}

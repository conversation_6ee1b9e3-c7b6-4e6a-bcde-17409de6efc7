package ai.pricefox.mallfox.controller.admin.product;

import ai.pricefox.mallfox.common.exception.enums.ErrorCodeConstants;
import ai.pricefox.mallfox.event.StandardDataEvent;
import ai.pricefox.mallfox.model.dto.ProductDataDTO;
import ai.pricefox.mallfox.model.dto.ProductDataReviewDTO;
import ai.pricefox.mallfox.model.dto.ProductPriceInventoryDTO;
import ai.pricefox.mallfox.model.vo.product.data.ProductModelMergeReqVO;
import ai.pricefox.mallfox.model.vo.product.data.ProductModelMergeRespVO;
import ai.pricefox.mallfox.model.vo.product.data.ProductModelProcessReqVO;
import ai.pricefox.mallfox.model.vo.product.data.ProductModelProcessRespVO;
import ai.pricefox.mallfox.model.vo.product.data.ProductSkuMergeReqVO;
import ai.pricefox.mallfox.model.vo.product.data.ProductSkuMergeRespVO;
import ai.pricefox.mallfox.model.vo.product.data.UpcCodeFixReqVO;
import ai.pricefox.mallfox.model.vo.product.data.UpcCodeFixRespVO;
import ai.pricefox.mallfox.service.product.ProductDataSimplifyService;
import ai.pricefox.mallfox.service.product.UnifiedProductDataService;
import ai.pricefox.mallfox.utils.JsonUtils;
import ai.pricefox.mallfox.vo.base.CommonResult;
import com.alibaba.fastjson.JSON;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.checkerframework.checker.units.qual.A;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @desc 商品数据服务接口
 * @since 2025/6/23
 */
@Tag(name = "数据处理 - 商品数据接口")
@RestController
@RequestMapping("/product/data")
@AllArgsConstructor
public class ProductDataController {

    private final UnifiedProductDataService unifiedProductDataService;
    private final ProductDataSimplifyService productDataSimplifyService;
    private final ApplicationEventPublisher eventPublisher;

    /**
     * 批量处理商品数据接口
     *
     * @param dtoList 包含产品和报价信息的列表-接口已废弃
     * @return 处理结果
     */
    @PostMapping("/batchProduct")
    @Deprecated
    public CommonResult<Boolean> batchProcess(@RequestBody @Validated List<ProductDataDTO> dtoList) {
        if (dtoList == null || dtoList.isEmpty()) {
            return CommonResult.error(ErrorCodeConstants.DATA_PRODUCT_NOT_EXIST);
        }
        if (dtoList.size() > 100) {
            return CommonResult.error(ErrorCodeConstants.DATA_PRODUCT_LIMIT);
        }
        unifiedProductDataService.batchProcessProductData(dtoList);
        return CommonResult.success(true);
    }


    /**
     * 批量处理商品评论接口
     *
     * @param reviewDtos
     * @return 处理结果
     */
    @PostMapping("/batchReview")
    public CommonResult<Boolean> batchProcessReviews(@RequestBody @Validated List<ProductDataReviewDTO> reviewDtos) {
        if (reviewDtos == null || reviewDtos.isEmpty()) {
            return CommonResult.error(ErrorCodeConstants.DATA_PRODUCT_NOT_EXIST);
        }
        if (reviewDtos.size() > 100) {
            return CommonResult.error(ErrorCodeConstants.DATA_PRODUCT_LIMIT);
        }
        unifiedProductDataService.batchProcessNewReviews(reviewDtos);
        return CommonResult.success(true);
    }

    /**
     * 批量处理商品价格和库存信息接口
     * 专门用于处理价格和库存变化，记录历史变动-接口已废弃
     *
     * @param dtoList 包含价格和库存信息的列表
     * @return 处理结果
     */
    @Operation(summary = "批量处理商品价格和库存信息",
            description = "专门处理商品价格和库存变化，记录价格历史和库存历史变动。适用于爬虫定时调用。")
    @PostMapping("/batchPriceAndInventory")
    @Deprecated
    public CommonResult<Boolean> batchProcessPriceAndInventory(@RequestBody @Validated List<ProductPriceInventoryDTO> dtoList) {
        if (dtoList == null || dtoList.isEmpty()) {
            return CommonResult.error(ErrorCodeConstants.DATA_PRODUCT_NOT_EXIST);
        }
        if (dtoList.size() > 100) {
            return CommonResult.error(ErrorCodeConstants.DATA_PRODUCT_LIMIT);
        }
        unifiedProductDataService.batchProcessPriceAndInventory(dtoList);
        return CommonResult.success(true);
    }

    /**
     * 商品型号处理接口
     * 根据品牌信息去除型号中的品牌内容，并格式化型号
     *
     * @param reqVO 处理请求参数
     * @return 处理结果
     */
    @Operation(summary = "商品型号处理",
            description = "根据品牌信息去除型号中的品牌内容，并格式化型号。支持预览模式和批量处理模式。")
    @PostMapping("/processModel")
    public CommonResult<ProductModelProcessRespVO> processProductModel(@RequestBody @Validated ProductModelProcessReqVO reqVO) {
        ProductModelProcessRespVO result = productDataSimplifyService.processProductModel(reqVO);
        return CommonResult.success(result);
    }

    /**
     * 商品型号合并接口
     * 1.相同UPC数据合并到统一的SKU
     * 2.根据型号匹配（不区分大小写和空格），将相同型号的数据合并到统一的SPU下
     *
     * @param reqVO 合并请求参数
     * @return 合并结果
     */
    @Operation(summary = "商品型号合并",
            description = "1.相同UPC数据合并到统一的SKU 2.根据型号匹配（不区分大小写和空格），将相同型号的数据合并到统一的SPU下。")
    @PostMapping("/mergeModel")
    public CommonResult<ProductModelMergeRespVO> mergeProductModel(@RequestBody @Validated ProductModelMergeReqVO reqVO) {
        ProductModelMergeRespVO result = productDataSimplifyService.mergeProductModel(reqVO);
        return CommonResult.success(result);
    }

    /**
     * 商品SKU合并接口
     * 在相同spuid下，根据color、storage、condition_new、service_provider四个字段匹配，将相同SKU的数据合并
     *
     * @param reqVO 合并请求参数
     * @return 合并结果
     */
    @Operation(summary = "商品SKU合并",
            description = "在相同spuid下，根据color、storage、condition_new、service_provider四个字段匹配，将相同SKU的数据合并。storage忽略大小写和空格。优先以Amazon数据为基准。")
    @PostMapping("/mergeSku")
    public CommonResult<ProductSkuMergeRespVO> mergeProductSku(@RequestBody @Validated ProductSkuMergeReqVO reqVO) {
        ProductSkuMergeRespVO result = productDataSimplifyService.mergeProductSku(reqVO);
        return CommonResult.success(result);
    }

    /**
     * UPC码修复接口
     * 处理upc_code="Does not apply"的记录，重新应用SPU生成逻辑
     *
     * @param reqVO 修复请求参数
     * @return 修复结果
     */
    @Operation(summary = "UPC码修复",
            description = "处理upc_code='Does not apply'的记录，重新应用SPU匹配逻辑，让这些记录按照正常的业务规则重新分配到正确的spuid。")
    @PostMapping("/fixUpcCodeDoesNotApply")
    public CommonResult<UpcCodeFixRespVO> fixUpcCodeDoesNotApply(@RequestBody @Validated UpcCodeFixReqVO reqVO) {
        UpcCodeFixRespVO result = productDataSimplifyService.fixUpcCodeDoesNotApplySpuIds(reqVO);
        return CommonResult.success(result);
    }


    /**
     * 商品数据保存到mongoDB接口
     *
     * @param data 商品原始数据
     * @return 处理结果
     */
    @PostMapping("/saveToMongo")
    public CommonResult<Boolean> saveProductDataToMongo(@RequestBody @Validated String data) {
        if (!JsonUtils.isValidJson(data)) {
            return CommonResult.error(ErrorCodeConstants.DATA_PRODUCT_NOT_EXIST);
        }
        // 发布事件
        eventPublisher.publishEvent(new StandardDataEvent(this, data));
        return CommonResult.success(true);
    }



}
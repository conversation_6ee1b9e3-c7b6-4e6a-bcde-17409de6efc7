package ai.pricefox.mallfox.controller.admin.product;

import ai.pricefox.mallfox.common.exception.enums.GlobalErrorCodeConstants;
import ai.pricefox.mallfox.model.param.CalibrationTagRequest;
import ai.pricefox.mallfox.model.param.ProductDataSearchRequest;
import ai.pricefox.mallfox.model.param.ProductMarkReadRequest;
import ai.pricefox.mallfox.model.response.ProductDataExportResponse;
import ai.pricefox.mallfox.model.response.ProductDataViewResponse;
import ai.pricefox.mallfox.model.response.SpuGroupViewResponse;
import ai.pricefox.mallfox.service.product.impl.ProductViewGroupedService;
import ai.pricefox.mallfox.vo.base.CommonResult;
import ai.pricefox.mallfox.vo.base.PageResult;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @desc 商品数据展示接口
 * @since 2025/6/24
 */
@Tag(name = "管理后台 - 商品数据展示")
@RestController
@RequestMapping("/admin/v1/product/view")
public class ProductViewController {

    @Autowired
    private ProductViewGroupedService productViewGroupedService;

    /**
     * 首页SPU列表查询
     *
     * @param request 请求参数（支持排序：sortField=字段名, sortOrder=asc/desc）
     * @return 分组结果
     */
    @GetMapping("/masterPage")
    public PageResult<SpuGroupViewResponse> getSpuGroupedList(ProductDataSearchRequest request) {
        IPage<SpuGroupViewResponse> result = productViewGroupedService.getSpuGroupedView(request);
        return new PageResult<>(result.getRecords(), result.getTotal());
    }

    /**
     * 二级：根据SPU ID获取SKU列表
     *
     * @param request 分页参数
     * @return SKU列表
     */
    @GetMapping("/skuPage")
    public PageResult<SpuGroupViewResponse> getSkuListBySpuId(ProductDataSearchRequest request) {
        if (request.getId() == null) {
            return PageResult.empty();
        }
        request.setSpuId(request.getSpuId());
        IPage<SpuGroupViewResponse> result = productViewGroupedService.getSkuListBySpuId(request);
        return new PageResult<>(result.getRecords(), result.getTotal());
    }

    /**
     * 三级：获取所有平台商品sku详情列表
     *
     * @param id
     * @return 商品详情列表
     */
    @GetMapping("/details/{id}")
    public CommonResult<List<ProductDataViewResponse>> getAllPlatformOffers(@PathVariable Long id) {
        if (id == null) {
            return CommonResult.error(GlobalErrorCodeConstants.BAD_REQUEST);
        }
        List<ProductDataViewResponse> details = productViewGroupedService.getAllPlatformOffersBySkuId(id);
        for (ProductDataViewResponse detail : details) {
            detail.setPid(id);
        }
        return CommonResult.success(details);
    }

    /**
     * 商品数据标记
     *
     * @param request 请求参数
     * @return 操作结果
     */
    @PostMapping("/calibrateTag")
    public CommonResult<Boolean> calibrateOffer(@Validated @RequestBody List<CalibrationTagRequest> request) {
        productViewGroupedService.updateCalibrationTags(request);
        return CommonResult.success(true);
    }

    /**
     * 商品数据标记已读
     * @param request
     * @return
     */
    @PostMapping("/markRead")
    public CommonResult<Boolean> markRead(@Validated @RequestBody List<ProductMarkReadRequest> request) {
        productViewGroupedService.markRead(request);
        return CommonResult.success(true);
    }

    /**
     * 商品数据导出
     * 聚合SPU、SKU、平台SKU三级数据
     *
     * @param request 查询参数
     * @return 导出数据列表
     */
    @Operation(summary = "商品数据导出",
               description = "导出SPU、SKU、平台SKU三级聚合数据，sign=1(SPU), sign=2(SKU), sign=3(平台SKU)")
    @GetMapping("/exportDataList")
    public CommonResult<List<ProductDataExportResponse>> exportDataList(ProductDataSearchRequest request) {
        List<ProductDataExportResponse> exportData = productViewGroupedService.exportProductData(request);
        return CommonResult.success(exportData);
    }

}

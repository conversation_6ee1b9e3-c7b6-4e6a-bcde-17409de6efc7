package ai.pricefox.mallfox.controller.admin.standard;

import ai.pricefox.mallfox.model.vo.PlatformFieldMappingImportRespVO;
import ai.pricefox.mallfox.service.standard.PlatformFieldMappingImportService;
import ai.pricefox.mallfox.vo.base.CommonResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * 平台字段映射导入控制器
 *
 * <AUTHOR>
 * @since 2025-07-26
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/admin/v1/standard/platform-field-mapping")
@Tag(name = "管理后台-平台字段映射", description = "平台字段映射导入相关接口")
public class PlatformFieldMappingImportController {

    private final PlatformFieldMappingImportService platformFieldMappingImportService;

    /**
     * 导入Excel文件到平台字段映射表
     *
     * @param file Excel文件
     * @return 导入结果
     */
    @Operation(summary = "导入平台字段映射Excel", description = "读取Excel文件并导入到平台字段映射表，使用Excel中的平台编码")
    @PostMapping("/import")
    public CommonResult<PlatformFieldMappingImportRespVO> importExcel(
            @Parameter(description = "Excel文件", required = true)
            @RequestParam("file") MultipartFile file) {
        
        try {
            log.info("开始导入平台字段映射Excel，文件名: {}", file.getOriginalFilename());
            
            PlatformFieldMappingImportRespVO result = platformFieldMappingImportService.importExcel(file);
            
            if (result.getSuccess()) {
                log.info("平台字段映射Excel导入成功，总数: {}, 成功: {}, 失败: {}, 跳过: {}", 
                    result.getTotalCount(), result.getSuccessCount(), result.getFailureCount(), result.getSkippedCount());
                return CommonResult.success(result);
            } else {
                log.warn("平台字段映射Excel导入失败，错误信息: {}", result.getErrorMessage());
                return CommonResult.error(result.getErrorMessage());
            }
            
        } catch (Exception e) {
            log.error("平台字段映射Excel导入异常", e);
            return CommonResult.error("导入失败: " + e.getMessage());
        }
    }
}
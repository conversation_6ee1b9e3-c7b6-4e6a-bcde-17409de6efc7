package ai.pricefox.mallfox.controller.admin.standard;

import ai.pricefox.mallfox.domain.standard.StandardProductMargeData;
import ai.pricefox.mallfox.model.param.ProductDataSearchRequest;
import ai.pricefox.mallfox.service.standard.StandardProductMargeDataService;
import ai.pricefox.mallfox.vo.base.CommonResult;
import ai.pricefox.mallfox.vo.product.HeaderInfoVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.*;

@Tag(name = "数据标准化 - 标准商品合并数据")
@RestController
@RequestMapping("/api/standardization/product-marge")
public class StandardProductMargeDataController {

    @Resource
    private StandardProductMargeDataService standardProductMargeDataService;

    @Operation(summary = "获取商品数据表头信息")
    @GetMapping("/headers")
    public CommonResult<List<HeaderInfoVO>> getProductHeaders() {
        return CommonResult.success(standardProductMargeDataService.getProductHeaders());
    }


    @Operation(summary = "根据动态表头分页查询商品数据")
    @GetMapping("/page-by-fields")
    public CommonResult<Page<Map<String, Object>>> getPageByFields(ProductDataSearchRequest request) {
        if (request.getPageSize() > 200) {
            request.setPageSize(200);
        }
        // 执行查询
        return CommonResult.success(standardProductMargeDataService.getPageByFields(request));
    }
}
package ai.pricefox.mallfox.controller.admin.supplement;

import ai.pricefox.mallfox.model.dto.SalesDataManualSupplementRequest;
import ai.pricefox.mallfox.model.dto.SalesDataSupplementResponse;
import ai.pricefox.mallfox.service.supplement.SalesDataSupplementService;
import ai.pricefox.mallfox.vo.base.CommonResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 销量数据补充控制器
 * 
 * <AUTHOR>
 * @since 2025-01-20
 */
@Slf4j
@RestController
@RequestMapping("/admin/v1/sales-supplement")
@Tag(name = "管理后台-销量数据补充", description = "销量数据补充相关接口")
public class SalesDataSupplementController {

    @Autowired
    private SalesDataSupplementService salesDataSupplementService;

    /**
     * 手动执行销量数据补充
     * 根据指定的平台和时间范围，从product_data_reviews表查询评论数量进行销量反推
     */
    @PostMapping("/manual-execute")
    @Operation(summary = "手动执行销量数据补充", 
               description = "根据指定的平台和时间范围，从product_data_reviews表查询评论数量进行销量反推")
    public CommonResult<SalesDataSupplementResponse> manualExecute(
            @RequestBody @Validated SalesDataManualSupplementRequest request) {
        
        log.info("开始手动执行销量数据补充，参数: {}", request);
        
        try {
            long startTime = System.currentTimeMillis();
            
            // 执行手动补充任务
            SalesDataSupplementResponse response = salesDataSupplementService.executeManualSupplementTask(request);
            
            long executionTime = System.currentTimeMillis() - startTime;
            response.setExecutionTimeMs(executionTime);
            
            log.info("手动执行销量数据补充完成，结果: {}", response);
            return CommonResult.success(response);
            
        } catch (Exception e) {
            log.error("手动执行销量数据补充失败", e);
            return CommonResult.error(500, "执行失败: " + e.getMessage());
        }
    }

    /**
     * 获取补充统计信息
     */
    @GetMapping("/statistics/{month}")
    @Operation(summary = "获取补充统计信息", description = "获取指定月份的销量补充统计信息")
    public CommonResult<String> getStatistics(@PathVariable String month) {
        try {
            String statistics = salesDataSupplementService.getSupplementStatistics(month);
            return CommonResult.success(statistics);
        } catch (Exception e) {
            log.error("获取补充统计信息失败，月份: {}", month, e);
            return CommonResult.error(500, "获取统计信息失败: " + e.getMessage());
        }
    }

    /**
     * 手动触发月度补充任务（用于测试）
     */
    @PostMapping("/trigger-monthly")
    @Operation(summary = "手动触发月度补充任务", description = "手动触发月度补充任务，用于测试定时任务逻辑")
    public CommonResult<String> triggerMonthlyTask() {
        try {
            String currentMonth = salesDataSupplementService.getCurrentMonth();
            salesDataSupplementService.executeMonthlySupplementTask(currentMonth);
            String statistics = salesDataSupplementService.getSupplementStatistics(currentMonth);
            return CommonResult.success("月度补充任务执行完成 - " + statistics);
        } catch (Exception e) {
            log.error("手动触发月度补充任务失败", e);
            return CommonResult.error(500, "执行失败: " + e.getMessage());
        }
    }
}

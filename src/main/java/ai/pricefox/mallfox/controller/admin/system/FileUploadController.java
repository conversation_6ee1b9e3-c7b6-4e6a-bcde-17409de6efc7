package ai.pricefox.mallfox.controller.admin.system;


import ai.pricefox.mallfox.common.util.AliOssUtil;
import ai.pricefox.mallfox.model.param.FileUploadRequest;
import ai.pricefox.mallfox.model.response.FileUploadResponseDTO;

import ai.pricefox.mallfox.vo.base.CommonResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;

import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

/**
 * 文件上传控制器
 * 支持图片、视频、音频等多种文件类型的上传
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/admin/v1/file")
@Tag(name = "管理后台-文件上传", description = "文件上传相关接口")
public class FileUploadController {


    @Autowired
    private AliOssUtil aliOssUtil;

    /**
     * 文件上传
     */
    @PostMapping(value = "/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @Operation(summary = "单文件上传", description = "上传单个文件到阿里云OSS")
    public CommonResult<FileUploadResponseDTO> uploadFile(FileUploadRequest request) throws IOException {
        MultipartFile file = request.getFile();
        String fileUrl = aliOssUtil.upload(request.getFile(), request.getCategory());
        log.info("开始上传文件: {}, 分类: {} 上传文件：{} ", file.getOriginalFilename(), request.getCategory(), fileUrl);
        FileUploadResponseDTO responseDTO = new FileUploadResponseDTO(fileUrl, file.getOriginalFilename());
        return CommonResult.success(responseDTO);
    }


}

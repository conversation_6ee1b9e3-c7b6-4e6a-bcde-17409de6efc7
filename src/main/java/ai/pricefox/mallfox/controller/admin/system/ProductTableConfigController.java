package ai.pricefox.mallfox.controller.admin.system;

import ai.pricefox.mallfox.model.param.ProductTableConfigRequest;
import ai.pricefox.mallfox.model.response.ProductTableConfigResponse;
import ai.pricefox.mallfox.service.system.ProductTableConfigService;
import ai.pricefox.mallfox.vo.base.CommonResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 产品表格配置控制器
 *
 * <AUTHOR>
 */
@RestController
@AllArgsConstructor
@RequestMapping("/admin/v1/table-config")
@Tag(name = "管理后台-商品表格配置", description = "商品表格配置管理相关接口")
public class ProductTableConfigController {

    private final ProductTableConfigService productTableConfigService;

    /**
     * 根据类型查询配置列表
     *
     * @param type 类型：1-字段 2-配置
     * @return 配置列表
     */
    @Operation(summary = "根据类型查询配置列表", description = "根据类型和标签查询表格配置列表，按权重排序")
    @GetMapping("/list")
    public CommonResult<List<ProductTableConfigResponse>> getConfigsByType(@Parameter(description = "类型：1-字段配置 2-其他配置", example = "1") @RequestParam Integer type, @Parameter(description = "表格标签，用于区分不同表格", example = "product_list") @RequestParam String tag) {
        return productTableConfigService.getConfigsByType(type, tag);
    }

    /**
     * 根据字段更新配置
     *
     * @param request 更新请求
     * @return 更新结果
     */
    @Operation(summary = "根据字段更新配置", description = "根据字段名称更新表格配置信息")
    @PutMapping("/update-by-field")
    public CommonResult<ProductTableConfigResponse> updateByField(@Valid @RequestBody ProductTableConfigRequest.UpdateByFieldRequest request) {
        return productTableConfigService.updateByField(request);
    }

    /**
     * 批量插入配置
     *
     * @param request 批量插入请求
     * @return 批量操作结果
     */
    @Operation(summary = "批量插入配置", description = "批量插入表格配置，最多支持100条记录")
    @PostMapping("/batch-insert")
    public CommonResult<Boolean> batchInsert(@Valid @RequestBody List<ProductTableConfigRequest> request) {
        return productTableConfigService.batchInsert(request);
    }

    /**
     * 更新权重
     *
     * @param request
     * @return
     */
    @Operation(summary = "更新字段权重", description = "支持拖拽排序和新增配置的权重更新接口")
    @PostMapping("/update-weight")
    public CommonResult<Boolean> updateWeight(@Valid @RequestBody ProductTableConfigRequest.UpdateWeightRequest request) {
        return productTableConfigService.updateWeight(request);
    }

}

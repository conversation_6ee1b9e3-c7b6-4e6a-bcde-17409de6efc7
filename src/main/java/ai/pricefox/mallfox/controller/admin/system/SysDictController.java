package ai.pricefox.mallfox.controller.admin.system;

import ai.pricefox.mallfox.service.system.SysDictService;
import ai.pricefox.mallfox.service.system.SysDictValueService;
import ai.pricefox.mallfox.vo.base.CommonResult;
import ai.pricefox.mallfox.vo.base.PageResult;
import ai.pricefox.mallfox.vo.dict.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 字典表 前端控制器
 * </p>
 */
@RestController
@AllArgsConstructor
@RequestMapping("/admin/v1/dicts")
@Tag(name = "管理后台-字典管理", description = "字典管理相关接口")
public class SysDictController {
	private final SysDictService sysDictService;
	private final SysDictValueService sysDictValueService;

	// ==================== 字典管理 ====================

	/**
	 * 创建字典
	 *
	 * @param reqVO 创建请求
	 * @return 创建结果
	 */
	@Operation(summary = "创建字典", description = "创建新的字典")
	@PostMapping
	public CommonResult<SysDictRespVO> createDict(@Valid @RequestBody SysDictCreateReqVO reqVO) {
		return sysDictService.createDict(reqVO);
	}

	/**
	 * 更新字典
	 *
	 * @param reqVO 更新请求
	 * @return 更新结果
	 */
	@Operation(summary = "更新字典", description = "根据ID更新字典信息")
	@PutMapping("/{id}")
	public CommonResult<SysDictRespVO> updateDict(@Valid @RequestBody SysDictUpdateReqVO reqVO) {
		return sysDictService.updateDict(reqVO);
	}

	/**
	 * 根据ID获取字典
	 *
	 * @param id 字典ID
	 * @return 字典信息
	 */
	@Operation(summary = "获取字典详情", description = "根据ID获取字典详细信息")
	@GetMapping("/{id}")
	public CommonResult<SysDictRespVO> getDictById(
			@Parameter(description = "字典ID", example = "1") @PathVariable Long id) {
		return sysDictService.getDictById(id);
	}

	/**
	 * 分页查询字典
	 *
	 * @param reqVO 分页查询请求
	 * @return 分页结果
	 */
	@Operation(summary = "分页查询字典", description = "分页查询字典列表")
	@GetMapping
	public CommonResult<PageResult<SysDictRespVO>> getDictPage(SysDictPageReqVO reqVO) {
		return sysDictService.getDictPage(reqVO);
	}

	/**
	 * 获取所有字典列表
	 *
	 * @return 字典列表
	 */
	@Operation(summary = "获取所有字典列表", description = "获取所有字典列表，无需分页")
	@GetMapping("/list")
	public CommonResult<List<SysDictRespVO>> getAllDicts() {
		return sysDictService.getAllDicts();
	}

	/**
	 * 删除字典
	 *
	 * @param id 字典ID
	 * @return 删除结果
	 */
	@Operation(summary = "删除字典", description = "根据ID删除字典及其相关字典项")
	@DeleteMapping("/{id}")
	public CommonResult<Boolean> deleteDict(
			@Parameter(description = "字典ID", example = "1") @PathVariable Long id) {
		return sysDictService.deleteDict(id);
	}

	// ==================== 字典项管理 ====================

	/**
	 * 创建字典项
	 *
	 * @param reqVO 创建请求
	 * @return 创建结果
	 */
	@Operation(summary = "创建字典项", description = "创建新的字典项")
	@PostMapping("/values")
	public CommonResult<SysDictValueRespVO> createDictValue(@Valid @RequestBody SysDictValueCreateReqVO reqVO) {
		return sysDictValueService.createDictValue(reqVO);
	}

	/**
	 * 更新字典项
	 *
	 * @param reqVO 更新请求
	 * @return 更新结果
	 */
	@Operation(summary = "更新字典项", description = "根据ID更新字典项信息")
	@PutMapping("/values/{id}")
	public CommonResult<SysDictValueRespVO> updateDictValue(
			@Valid @RequestBody SysDictValueUpdateReqVO reqVO) {
		return sysDictValueService.updateDictValue(reqVO);
	}

	/**
	 * 根据ID获取字典项
	 *
	 * @param id 字典项ID
	 * @return 字典项信息
	 */
	@Operation(summary = "获取字典项详情", description = "根据ID获取字典项详细信息")
	@GetMapping("/values/{id}")
	public CommonResult<SysDictValueRespVO> getDictValueById(
			@Parameter(description = "字典项ID", example = "1") @PathVariable Long id) {
		return sysDictValueService.getDictValueById(id);
	}

	/**
	 * 分页查询字典项
	 *
	 * @param reqVO 分页查询请求
	 * @return 分页结果
	 */
	@Operation(summary = "分页查询字典项", description = "分页查询字典项列表")
	@GetMapping("/values")
	public CommonResult<PageResult<SysDictValueRespVO>> getDictValuePage(SysDictValuePageReqVO reqVO) {
		return sysDictValueService.getDictValuePage(reqVO);
	}

	/**
	 * 根据类型获取字典项列表
	 *
	 * @param type 字典类型
	 * @return 字典项列表
	 */
	@Operation(summary = "根据类型获取字典项", description = "根据字典类型获取字典项列表")
	@GetMapping("/values/type/{type}")
	public CommonResult<List<SysDictValueRespVO>> getDictValuesByType(
			@Parameter(description = "字典类型", example = "user_status") @PathVariable String type) {
		return sysDictValueService.getDictValuesByType(type);
	}

	/**
	 * 根据类型获取单个字典项
	 *
	 * @param type 字典类型
	 * @return 字典项
	 */
	@Operation(summary = "根据类型获取单个字典项", description = "根据字典类型获取单个字典项（按排序倒序取第一个）")
	@GetMapping("/values/type/{type}/one")
	public CommonResult<SysDictValueRespVO> getDictValueByType(
			@Parameter(description = "字典类型", example = "user_status") @PathVariable String type) {
		return sysDictValueService.getDictValueByType(type);
	}

	/**
	 * 删除字典项
	 *
	 * @param id 字典项ID
	 * @return 删除结果
	 */
	@Operation(summary = "删除字典项", description = "根据ID删除字典项")
	@DeleteMapping("/values/{id}")
	public CommonResult<Boolean> deleteDictValue(
			@Parameter(description = "字典项ID", example = "1") @PathVariable Long id) {
		return sysDictValueService.deleteDictValue(id);
	}
}

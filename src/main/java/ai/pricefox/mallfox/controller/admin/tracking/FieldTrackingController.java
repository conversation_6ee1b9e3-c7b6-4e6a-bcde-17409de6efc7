package ai.pricefox.mallfox.controller.admin.tracking;

import ai.pricefox.mallfox.domain.product.ProductDataOffers;
import ai.pricefox.mallfox.domain.product.ProductDataSimplify;
import ai.pricefox.mallfox.service.product.ProductDataOffersService;
import ai.pricefox.mallfox.service.product.ProductDataSimplifyService;
import ai.pricefox.mallfox.service.tracking.FieldTrackingService;
import ai.pricefox.mallfox.vo.base.CommonResult;
import ai.pricefox.mallfox.vo.tracking.CacheClearRequest;
import ai.pricefox.mallfox.vo.tracking.CacheClearResult;
import ai.pricefox.mallfox.vo.tracking.InitializeResult;
import ai.pricefox.mallfox.vo.tracking.ProcessResult;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 字段追踪控制器
 *
 * <AUTHOR>
 * @since 2025-01-14
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/admin/v1/field-tracking")
@Tag(name = "管理后台-字段追踪管理", description = "字段追踪数据初始化接口")
public class FieldTrackingController {

    private final FieldTrackingService fieldTrackingService;
    private final ProductDataOffersService offersService;
    private final ProductDataSimplifyService simplifyService;

    /**
     * 初始化字段追踪数据
     * 扫描product_data_offers和product_data_simplify表中的所有数据，
     * 根据data_channel字段来确定每条数据中每个字段的数据来源，
     * 将这两张表中的所有数据写入到字段追踪表中
     */
    @PostMapping("/initialize")
    @Operation(summary = "初始化字段追踪数据", description = "根据现有数据的data_channel字段初始化字段追踪记录")
    public CommonResult<InitializeResult> initializeFieldTracking(
            @Parameter(description = "每批处理的记录数量", example = "1000")
            @RequestParam(defaultValue = "1000") Integer batchSize,
            @Parameter(description = "指定要处理的表名", example = "product_data_offers")
            @RequestParam(required = false) String tableName,
            @Parameter(description = "是否使用同步模式", example = "false")
            @RequestParam(defaultValue = "false") Boolean syncMode) {

        log.info("开始初始化字段追踪数据，batchSize={}, tableName={}, syncMode={}", batchSize, tableName, syncMode);

        try {
            InitializeResult result = new InitializeResult();
            result.setStartTime(LocalDateTime.now());

            // 处理product_data_offers表
            if (tableName == null || "product_data_offers".equals(tableName)) {
                log.info("开始处理product_data_offers表，同步模式：{}", syncMode);
                ProcessResult offersResult = fieldTrackingService.processProductDataOffers(batchSize, syncMode);
                result.setOffersProcessed(offersResult.getProcessedCount());
                result.setOffersFieldsCreated(offersResult.getFieldsCreated());
                log.info("product_data_offers表处理完成，处理记录数：{}，创建字段追踪记录数：{}",
                    offersResult.getProcessedCount(), offersResult.getFieldsCreated());
            }

            // 处理product_data_simplify表
            if (tableName == null || "product_data_simplify".equals(tableName)) {
                log.info("开始处理product_data_simplify表，同步模式：{}", syncMode);
                ProcessResult simplifyResult = fieldTrackingService.processProductDataSimplify(batchSize, syncMode);
                result.setSimplifyProcessed(simplifyResult.getProcessedCount());
                result.setSimplifyFieldsCreated(simplifyResult.getFieldsCreated());
                log.info("product_data_simplify表处理完成，处理记录数：{}，创建字段追踪记录数：{}",
                    simplifyResult.getProcessedCount(), simplifyResult.getFieldsCreated());
            }

            result.setEndTime(LocalDateTime.now());
            result.setTotalProcessed(result.getOffersProcessed() + result.getSimplifyProcessed());
            result.setTotalFieldsCreated(result.getOffersFieldsCreated() + result.getSimplifyFieldsCreated());

            log.info("字段追踪数据初始化完成，总处理记录数：{}，总创建字段追踪记录数：{}",
                result.getTotalProcessed(), result.getTotalFieldsCreated());

            return CommonResult.success(result);
        } catch (Exception e) {
            log.error("初始化字段追踪数据失败", e);
            return CommonResult.error(500, "初始化失败: " + e.getMessage());
        }
    }



    /**
     * 清除字段来源缓存
     */
    @PostMapping("/clear-cache")
    @Operation(summary = "清除字段来源缓存", description = "支持多种清除模式：全部、按表、按记录、按模式")
    public CommonResult<CacheClearResult> clearFieldSourceCache(
            @RequestBody CacheClearRequest request) {

        log.info("开始清除字段来源缓存，请求参数: {}", request);

        try {
            CacheClearResult result = new CacheClearResult();
            result.setStartTime(LocalDateTime.now());
            result.setClearType(request.getClearType());

            switch (request.getClearType()) {
                case "all":
                    // 清除所有缓存
                    fieldTrackingService.clearAllFieldSourceCache();
                    result.setClearedCount(0L); // Spring Cache注解无法返回具体数量
                    result.setMessage("已清除所有字段来源缓存");
                    break;

                case "table":
                    // 按表名清除缓存
                    if (request.getTableName() == null || request.getTableName().trim().isEmpty()) {
                        return CommonResult.error(400, "清除类型为table时，tableName参数不能为空");
                    }
                    long tableCount = fieldTrackingService.clearFieldSourceCacheByTable(request.getTableName());
                    result.setClearedCount(tableCount);
                    result.setMessage("已清除表 " + request.getTableName() + " 的字段来源缓存");
                    break;

                case "record":
                    // 按记录清除缓存
                    if (request.getTableName() == null || request.getRecordId() == null) {
                        return CommonResult.error(400, "清除类型为record时，tableName和recordId参数不能为空");
                    }
                    fieldTrackingService.clearFieldSourceCache(request.getTableName(), request.getRecordId());
                    result.setClearedCount(1L);
                    result.setMessage("已清除记录 " + request.getTableName() + ":" + request.getRecordId() + " 的字段来源缓存");
                    break;

                case "pattern":
                    // 按模式清除缓存
                    if (request.getPattern() == null || request.getPattern().trim().isEmpty()) {
                        return CommonResult.error(400, "清除类型为pattern时，pattern参数不能为空");
                    }
                    long patternCount = fieldTrackingService.clearFieldSourceCacheByPattern(request.getPattern());
                    result.setClearedCount(patternCount);
                    result.setMessage("已按模式 " + request.getPattern() + " 清除字段来源缓存");
                    break;

                default:
                    return CommonResult.error(400, "不支持的清除类型: " + request.getClearType());
            }

            result.setEndTime(LocalDateTime.now());
            result.setSuccess(true);

            log.info("字段来源缓存清除完成: {}", result);
            return CommonResult.success(result);

        } catch (Exception e) {
            log.error("清除字段来源缓存失败", e);
            return CommonResult.error(500, "清除缓存失败: " + e.getMessage());
        }
    }

    /**
     * 获取字段来源缓存统计信息
     */
    @GetMapping("/cache-stats")
    @Operation(summary = "获取字段来源缓存统计信息", description = "查看当前缓存的统计信息")
    public CommonResult<Map<String, Object>> getFieldSourceCacheStats() {
        try {
            Map<String, Object> stats = fieldTrackingService.getFieldSourceCacheStats();
            return CommonResult.success(stats);
        } catch (Exception e) {
            log.error("获取字段来源缓存统计信息失败", e);
            return CommonResult.error(500, "获取缓存统计失败: " + e.getMessage());
        }
    }

    /**
     * 验证字段追踪数据
     */
    @GetMapping("/verify/{recordId}")
    @Operation(summary = "验证字段追踪数据", description = "检查指定记录ID在两个表中的字段追踪情况")
    public CommonResult<Map<String, Object>> verifyFieldTracking(
            @PathVariable Long recordId) {

        try {
            Map<String, Object> result = new HashMap<>();

            // 查询product_data_offers表的字段追踪记录
            Map<String, Integer> offersFieldSources = fieldTrackingService.getFieldSources("product_data_offers", recordId);
            result.put("offersFieldSources", offersFieldSources);
            result.put("offersFieldCount", offersFieldSources != null ? offersFieldSources.size() : 0);

            // 查询product_data_simplify表的字段追踪记录
            Map<String, Integer> simplifyFieldSources = fieldTrackingService.getFieldSources("product_data_simplify", recordId);
            result.put("simplifyFieldSources", simplifyFieldSources);
            result.put("simplifyFieldCount", simplifyFieldSources != null ? simplifyFieldSources.size() : 0);

            // 检查数据库中的实际记录
            long offersTrackingCount = fieldTrackingService.countTrackingRecords("product_data_offers", recordId);
            long simplifyTrackingCount = fieldTrackingService.countTrackingRecords("product_data_simplify", recordId);

            result.put("offersTrackingCount", offersTrackingCount);
            result.put("simplifyTrackingCount", simplifyTrackingCount);
            result.put("totalTrackingCount", offersTrackingCount + simplifyTrackingCount);

            // 验证结果
            boolean hasOffersRecord = offersTrackingCount > 0;
            boolean hasSimplifyRecord = simplifyTrackingCount > 0;
            boolean hasBothRecords = hasOffersRecord && hasSimplifyRecord;

            result.put("hasOffersRecord", hasOffersRecord);
            result.put("hasSimplifyRecord", hasSimplifyRecord);
            result.put("hasBothRecords", hasBothRecords);
            result.put("recordId", recordId);

            if (!hasBothRecords) {
                result.put("warning", "记录ID " + recordId + " 在两个表中应该都有字段追踪记录，但实际情况不符合预期");
            }

            log.info("验证记录ID {} 的字段追踪情况: offers记录数={}, simplify记录数={}, 两表都有记录={}",
                recordId, offersTrackingCount, simplifyTrackingCount, hasBothRecords);

            return CommonResult.success(result);

        } catch (Exception e) {
            log.error("验证字段追踪数据失败，recordId={}", recordId, e);
            return CommonResult.error(500, "验证失败: " + e.getMessage());
        }
    }
}

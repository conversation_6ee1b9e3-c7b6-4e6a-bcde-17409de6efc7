package ai.pricefox.mallfox.controller.admin.user;

import ai.pricefox.mallfox.common.exception.enums.ErrorCodeConstants;
import ai.pricefox.mallfox.service.admin.AdminUserService;
import ai.pricefox.mallfox.service.auth.VerifyCodeService;
import ai.pricefox.mallfox.vo.admin.AdminAuthRespVO;
import ai.pricefox.mallfox.vo.admin.AdminLoginReqVO;
import ai.pricefox.mallfox.vo.admin.AdminSendCodeReqVO;
import ai.pricefox.mallfox.vo.base.CommonResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import static ai.pricefox.mallfox.common.exception.util.ServiceExceptionUtil.exception;

/**
 * 后台认证控制器
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/admin/v1/auth")
@Tag(name = "管理后台-认证管理", description = "后台用户认证相关接口")
public class AdminAuthController {

    private final AdminUserService adminUserService;
    private final VerifyCodeService verifyCodeService;

    /**
     * 后台用户登录
     *
     * @param reqVO 登录请求
     * @param request HTTP请求
     * @return 登录结果
     */
    @PostMapping("/login")
    @Operation(summary = "后台用户登录", description = "支持用户名密码、手机号密码、邮箱密码、手机号验证码、邮箱验证码登录")
    public CommonResult<AdminAuthRespVO> login(@Valid @RequestBody AdminLoginReqVO reqVO, 
                                               HttpServletRequest request) {
        String loginIp = getClientIpAddress(request);
        return adminUserService.login(reqVO, loginIp);
    }

    /**
     * 后台用户登出
     *
     * @return 登出结果
     */
    @PostMapping("/logout")
    @Operation(summary = "后台用户登出", description = "清除后台用户登录状态")
    public CommonResult<String> logout() {
        return adminUserService.logout();
    }

    /**
     * 刷新Token
     *
     * @param refreshToken 刷新令牌
     * @return 新的Token信息
     */
    @PostMapping("/refresh-token")
    @Operation(summary = "刷新Token", description = "使用刷新令牌获取新的访问令牌")
    public CommonResult<AdminAuthRespVO> refreshToken(@RequestParam String refreshToken) {
        return adminUserService.refreshToken(refreshToken);
    }

    /**
     * 获取当前后台用户信息
     *
     * @return 用户信息
     */
    @GetMapping("/me")
    @Operation(summary = "获取当前后台用户信息", description = "获取当前登录后台用户的详细信息")
    public CommonResult<AdminAuthRespVO> getCurrentAdminUser() {
        return adminUserService.getCurrentAdminUser();
    }

    /**
     * 查询后台登录状态
     *
     * @return 登录状态
     */
    @GetMapping("/status")
    @Operation(summary = "查询后台登录状态", description = "查询当前后台用户的登录状态")
    public CommonResult<String> getLoginStatus() {
        boolean isLogin = ai.pricefox.mallfox.utils.AdminTokenUtil.isLogin();
        String status = "当前后台会话是否登录：" + isLogin;
        if (isLogin) {
            Long userId = ai.pricefox.mallfox.utils.AdminTokenUtil.getCurrentUserIdOrNull();
            status += "，用户ID：" + userId;
        }
        return CommonResult.success(status);
    }

    /**
     * 发送后台验证码
     *
     * @param reqVO 发送验证码请求
     * @return 发送结果
     */
    @PostMapping("/send-code")
    @Operation(summary = "发送后台验证码", description = "发送后台短信/邮箱验证码，支持登录等场景")
    public CommonResult<String> sendCode(@Valid @RequestBody AdminSendCodeReqVO reqVO) {
        // 验证发送频率
        validateSendFrequency(reqVO);

        // 根据验证码类型发送
        if (reqVO.getCodeType() == AdminSendCodeReqVO.CodeType.SMS) {
            // 发送短信验证码
            sendSmsCode(reqVO.getPhone(), reqVO.getBusinessType());
            return CommonResult.success("短信验证码发送成功");
        } else if (reqVO.getCodeType() == AdminSendCodeReqVO.CodeType.EMAIL) {
            // 发送邮箱验证码
            sendEmailCode(reqVO.getEmail(), reqVO.getBusinessType());
            return CommonResult.success("邮箱验证码发送成功");
        }

        return CommonResult.success("验证码发送成功");
    }

    /**
     * 测试后台Token验证 - 需要登录
     *
     * @return 测试结果
     */
    @GetMapping("/test-token")
    @Operation(summary = "测试后台Token验证", description = "测试后台Token验证，需要登录")
    public CommonResult<String> testToken() {
        Long userId = ai.pricefox.mallfox.utils.AdminTokenUtil.getCurrentUserIdOrNull();
        return CommonResult.success("后台Token验证成功，当前用户ID: " + userId);
    }

    /**
     * 获取客户端真实IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }

        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }

        String proxyClientIp = request.getHeader("Proxy-Client-IP");
        if (proxyClientIp != null && !proxyClientIp.isEmpty() && !"unknown".equalsIgnoreCase(proxyClientIp)) {
            return proxyClientIp;
        }

        String wlProxyClientIp = request.getHeader("WL-Proxy-Client-IP");
        if (wlProxyClientIp != null && !wlProxyClientIp.isEmpty() && !"unknown".equalsIgnoreCase(wlProxyClientIp)) {
            return wlProxyClientIp;
        }

        return request.getRemoteAddr();
    }

    /**
     * 验证发送频率
     */
    private void validateSendFrequency(AdminSendCodeReqVO reqVO) {
        String target = reqVO.getCodeType() == AdminSendCodeReqVO.CodeType.EMAIL ? reqVO.getEmail() : reqVO.getPhone();
        String type = reqVO.getCodeType() == AdminSendCodeReqVO.CodeType.EMAIL ? "EMAIL" : "SMS";

        if (!verifyCodeService.checkSendFrequency(target, type)) {
            throw exception(ErrorCodeConstants.AUTH_CODE_SEND_TOO_FAST);
        }
    }

    /**
     * 发送短信验证码
     */
    private void sendSmsCode(String phone, AdminSendCodeReqVO.BusinessType businessType) {
        boolean success = verifyCodeService.sendSmsCode(phone, businessType.name());
        if (!success) {
            throw exception(ErrorCodeConstants.AUTH_MOBILE_CODE_SEND_FAIL);
        }
    }

    /**
     * 发送邮箱验证码
     */
    private void sendEmailCode(String email, AdminSendCodeReqVO.BusinessType businessType) {
        boolean success = verifyCodeService.sendEmailCode(email, businessType.name());
        if (!success) {
            throw exception(ErrorCodeConstants.AUTH_EMAIL_CODE_SEND_FAIL);
        }
    }
}

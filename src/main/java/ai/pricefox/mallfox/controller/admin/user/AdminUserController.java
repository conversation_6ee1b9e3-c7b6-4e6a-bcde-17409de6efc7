package ai.pricefox.mallfox.controller.admin.user;

import ai.pricefox.mallfox.service.admin.AdminUserService;
import ai.pricefox.mallfox.vo.admin.*;
import ai.pricefox.mallfox.vo.base.CommonResult;
import ai.pricefox.mallfox.vo.base.PageResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * 后台用户管理
 *
 * <AUTHOR>
 * @date 2025-05-18 12:19:29
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/admin/v1/users")
@Tag(name = "管理后台-用户管理", description = "后台用户管理相关接口")
public class AdminUserController {

    private final AdminUserService adminUserService;

    /**
     * 创建后台用户
     *
     * @param reqVO 创建请求
     * @return 创建结果
     */
    @Operation(summary = "创建后台用户", description = "创建新的后台用户")
    @PostMapping
    public CommonResult<AdminUserRespVO> createAdminUser(@Valid @RequestBody AdminUserCreateReqVO reqVO) {
        return adminUserService.createAdminUser(reqVO);
    }

    /**
     * 更新后台用户
     *
     * @param id 用户ID
     * @param reqVO 更新请求
     * @return 更新结果
     */
    @Operation(summary = "更新后台用户", description = "根据ID更新后台用户信息")
    @PutMapping("/{id}")
    public CommonResult<AdminUserRespVO> updateAdminUser(
            @Parameter(description = "用户ID", example = "1") @PathVariable Long id,
            @Valid @RequestBody AdminUserUpdateReqVO reqVO) {
        reqVO.setId(id);
        return adminUserService.updateAdminUser(reqVO);
    }

    /**
     * 根据ID获取后台用户
     *
     * @param id 用户ID
     * @return 用户信息
     */
    @Operation(summary = "获取后台用户详情", description = "根据ID获取后台用户详细信息")
    @GetMapping("/{id}")
    public CommonResult<AdminUserRespVO> getAdminUserById(
            @Parameter(description = "用户ID", example = "1") @PathVariable Long id) {
        return adminUserService.getAdminUserById(id);
    }

    /**
     * 分页查询后台用户
     *
     * @param reqVO 分页查询请求
     * @return 分页结果
     */
    @Operation(summary = "分页查询后台用户", description = "分页查询后台用户列表")
    @GetMapping
    public CommonResult<PageResult<AdminUserRespVO>> getAdminUserPage(AdminUserPageReqVO reqVO) {
        return adminUserService.getAdminUserPage(reqVO);
    }

    /**
     * 删除后台用户
     *
     * @param id 用户ID
     * @return 删除结果
     */
    @Operation(summary = "删除后台用户", description = "根据ID删除后台用户")
    @DeleteMapping("/{id}")
    public CommonResult<Boolean> deleteAdminUser(
            @Parameter(description = "用户ID", example = "1") @PathVariable Long id) {
        return adminUserService.deleteAdminUser(id);
    }

}

package ai.pricefox.mallfox.controller.client.auth;

import ai.pricefox.mallfox.service.auth.GoogleOAuthService;
import ai.pricefox.mallfox.vo.auth.AuthRespVO;
import ai.pricefox.mallfox.vo.auth.GoogleLoginReqVO;
import ai.pricefox.mallfox.vo.base.CommonResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.servlet.view.RedirectView;

/**
 * Google OAuth 登录控制器
 */
@Slf4j
@RestController
@RequestMapping("/oauth/google")
@RequiredArgsConstructor
@Tag(name = "客户端-Google OAuth登录", description = "Google OAuth登录相关接口")
public class GoogleOAuthController {

    private final GoogleOAuthService googleOAuthService;
    private final RestTemplate restTemplate;
    
    /**
     * 获取Google授权URL
     * @return Google授权URL
     */
    @GetMapping("/auth-url")
    @ResponseBody
    @Operation(summary = "获取Google登录链接", description = "获取Google OAuth授权URL")
    public CommonResult<String> getAuthUrl() {
        String url = googleOAuthService.getAuthorizationUrl();
        return CommonResult.success(url);
    }
    
    /**
     * 直接跳转到Google登录
     * @return 重定向到Google授权页面
     */
    @GetMapping("/login")
    @Operation(summary = "跳转到Google登录", description = "直接跳转到Google OAuth授权页面")
    public RedirectView redirectToGoogle() {
        String url = googleOAuthService.getAuthorizationUrl();
        return new RedirectView(url);
    }
    
    /**
     * Google OAuth回调处理
     * @param code 授权码
     * @return 登录结果页面
     */
    @GetMapping("/callback")
    @Operation(summary = "Google OAuth回调", description = "处理Google OAuth回调并完成登录")
    public CommonResult<AuthRespVO> callback(@RequestParam("code") String code) {
        log.info("收到Google OAuth回调，授权码：{}", code);
        return googleOAuthService.handleOAuthCallback(code);
    }
    
    /**
     * 前端登录接口
     * @param reqVO 登录请求
     * @return 登录结果
     */
    @PostMapping("/login")
    @ResponseBody
    @Operation(summary = "Google OAuth登录API", description = "使用Google授权码进行登录")
    public CommonResult<AuthRespVO> login(@RequestBody GoogleLoginReqVO reqVO) {
        log.info("Google登录请求，授权码：{}", reqVO.getCode());
        return googleOAuthService.handleOAuthCallback(reqVO.getCode());
    }
} 
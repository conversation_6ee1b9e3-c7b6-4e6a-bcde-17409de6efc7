package ai.pricefox.mallfox.controller.client.auth;

import ai.pricefox.mallfox.domain.auth.UserSocialAccount;
import ai.pricefox.mallfox.service.auth.SocialAccountService;
import ai.pricefox.mallfox.utils.TokenUtil;
import ai.pricefox.mallfox.vo.base.CommonResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@RequiredArgsConstructor
@RequestMapping("/v1/social")
@Tag(name = "客户端-用户社交账号管理", description = "社交账号管理相关接口")
public class SocialAccountController {

    private final SocialAccountService socialAccountService;

    /**
     * 获取当前用户绑定的社交账号列表
     *
     * @return 社交账号列表
     */
    @GetMapping("/accounts")
    @Operation(summary = "获取绑定的社交账号列表", description = "获取当前用户绑定的所有社交账号")
    public CommonResult<List<UserSocialAccount>> getUserSocialAccounts() {
        Long userId = TokenUtil.getCurrentUserId();
        List<UserSocialAccount> accounts = socialAccountService.getUserSocialAccounts(userId);
        return CommonResult.success(accounts);
    }

    /**
     * 绑定社交账号
     *
     * @param socialType 社交平台类型（1:Google, 2:Facebook, 3:Twitter, 4:LinkedIn, 5:GitHub, 6:Apple, 7:Microsoft, 8:Amazon）
     * @param socialId 社交平台ID
     * @param userInfo 社交平台用户信息
     * @return 绑定结果
     */
    @PostMapping("/bind")
    @Operation(summary = "绑定社交账号", description = "将社交账号绑定到当前用户")
    public CommonResult<String> bindSocialAccount(
            @Parameter(description = "社交平台类型：1-Google, 2-Facebook, 3-Twitter, 4-LinkedIn, 5-GitHub, 6-Apple, 7-Microsoft, 8-Amazon")
            @RequestParam Integer socialType,
            @RequestParam String socialId,
            @RequestBody Map<String, Object> userInfo) {
        Long userId = TokenUtil.getCurrentUserId();
        return socialAccountService.bindSocialAccount(userId, socialType, socialId, userInfo);
    }

    /**
     * 解绑社交账号
     *
     * @param socialType 社交平台类型（1:Google, 2:Facebook, 3:Twitter, 4:LinkedIn, 5:GitHub, 6:Apple, 7:Microsoft, 8:Amazon）
     * @return 解绑结果
     */
    @PostMapping("/unbind")
    @Operation(summary = "解绑社交账号", description = "解除当前用户与社交账号的绑定")
    public CommonResult<String> unbindSocialAccount(
            @Parameter(description = "社交平台类型：1-Google, 2-Facebook, 3-Twitter, 4-LinkedIn, 5-GitHub, 6-Apple, 7-Microsoft, 8-Amazon") 
            @RequestParam Integer socialType) {
        Long userId = TokenUtil.getCurrentUserId();
        return socialAccountService.unbindSocialAccount(userId, socialType);
    }
} 
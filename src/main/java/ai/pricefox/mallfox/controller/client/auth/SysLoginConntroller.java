package ai.pricefox.mallfox.controller.client.auth;

import ai.pricefox.mallfox.service.auth.AuthService;
import ai.pricefox.mallfox.vo.auth.*;
import ai.pricefox.mallfox.vo.base.CommonResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/v1/auth")
@Tag(name = "客户端-门户认证管理", description = "用户认证相关接口")
public class SysLoginConntroller {

    private final AuthService authService;

    /**
     * 用户注册
     *
     * @param reqVO 注册请求
     * @return 注册结果
     */
    @PostMapping("/register")
    @Operation(summary = "用户注册", description = "用户注册接口，支持手机号/邮箱注册")
    public CommonResult<AuthRespVO> register(@Valid @RequestBody RegisterReqVO reqVO) {
        return authService.register(reqVO);
    }

    /**
     * 用户登录
     *
     * @param reqVO 登录请求
     * @return 登录结果
     */
    @PostMapping("/login")
    @Operation(summary = "用户登录", description = "用户登录接口，支持多种登录方式")
    public CommonResult<AuthRespVO> login(@Valid @RequestBody LoginReqVO reqVO) {
        return authService.login(reqVO);
    }

    /**
     * 用户登出
     *
     * @return 登出结果
     */
    @PostMapping("/logout")
    @Operation(summary = "用户登出", description = "用户登出接口")
    public CommonResult<String> logout() {
        return authService.logout();
    }

    /**
     * 查询登录状态
     *
     * @return 登录状态
     */
    @GetMapping("/status")
    @Operation(summary = "查询登录状态", description = "查询当前用户登录状态")
    public CommonResult<String> getLoginStatus() {
        return authService.getLoginStatus();
    }

    /**
     * 发送验证码
     *
     * @param reqVO 发送验证码请求
     * @return 发送结果
     */
    @PostMapping("/send-code")
    @Operation(summary = "发送验证码", description = "发送短信/邮箱验证码")
    public CommonResult<String> sendCode(@Valid @RequestBody SendCodeReqVO reqVO) {
        return authService.sendCode(reqVO);
    }

    /**
     * 发送重置密码链接
     *
     * @param reqVO 发送重置密码链接请求
     * @return 发送结果
     */
    @PostMapping("/send-reset-link")
    @Operation(summary = "发送重置密码链接", description = "向邮箱发送重置密码链接")
    public CommonResult<String> sendResetLink(@Valid @RequestBody SendResetLinkReqVO reqVO) {
        return authService.sendResetLink(reqVO);
    }

    /**
     * 重置密码
     *
     * @param reqVO 重置密码请求
     * @return 重置结果
     */
    @PostMapping("/reset-password")
    @Operation(summary = "重置密码", description = "通过重置令牌重置密码")
    public CommonResult<String> resetPassword(@Valid @RequestBody ResetPasswordReqVO reqVO) {
        return authService.resetPassword(reqVO);
    }

    /**
     * 获取当前用户信息
     *
     * @return 用户信息
     */
    @GetMapping("/me")
    @Operation(summary = "获取当前用户信息", description = "获取当前登录用户的详细信息")
    public CommonResult<AuthRespVO> getCurrentUser() {
        return authService.getCurrentUser();
    }

    /**
     * 刷新Token
     *
     * @param refreshToken 刷新令牌
     * @return 新的Token信息
     */
    @PostMapping("/refresh-token")
    @Operation(summary = "刷新Token", description = "使用刷新令牌获取新的访问令牌")
    public CommonResult<AuthRespVO> refreshToken(@RequestParam String refreshToken) {
        return authService.refreshToken(refreshToken);
    }

    /**
     * 测试接口 - 无需登录
     *
     * @return 测试结果
     */
    @GetMapping("/test")
    @Operation(summary = "测试接口", description = "测试接口，无需登录")
    public CommonResult<String> test() {
        return CommonResult.success("测试接口调用成功，无需登录");
    }

    /**
     * 测试Token验证 - 需要登录
     *
     * @return 测试结果
     */
    @GetMapping("/test-token")
    @Operation(summary = "测试Token验证", description = "测试Token验证，需要登录")
    public CommonResult<String> testToken() {
        Long userId = ai.pricefox.mallfox.utils.TokenUtil.getCurrentUserIdOrNull();
        return CommonResult.success("Token验证成功，当前用户ID: " + userId);
    }

}

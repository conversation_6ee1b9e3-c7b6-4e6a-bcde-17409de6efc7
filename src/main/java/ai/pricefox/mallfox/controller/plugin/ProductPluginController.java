package ai.pricefox.mallfox.controller.plugin;
import ai.pricefox.mallfox.model.param.PluginMatchRequest;
import ai.pricefox.mallfox.model.response.PluginMatchResponse;
import ai.pricefox.mallfox.service.plugin.PluginMatchService;
import ai.pricefox.mallfox.vo.base.CommonResult;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @desc 商品插件接口
 * @since 2025/7/31
 */
@RestController
@RequestMapping("/v1/plugin")
public class ProductPluginController {

    @Autowired
    private PluginMatchService pluginMatchService;

    /**
     * 插件核心接口：匹配商品并返回比价结果 触发场景: 用户正在浏览的商品页面，在我们的比价系统中存在，并且我们在其他渠道找到了更低的价格。
     *
     * @param request 插件从电商页面抓取的数据
     * @return 包含插件状态和对应数据的响应体
     */
    @PostMapping("/match-and-compare")
    @Operation(summary = "插件匹配与比价接口", description = "接收插件抓取数据，返回插件应展示的状态和信息")
    public CommonResult<PluginMatchResponse> matchAndCompare(@RequestBody @Validated PluginMatchRequest request) {
        PluginMatchResponse response = pluginMatchService.processMatchRequest(request);
        return CommonResult.success(response);
    }
}

package ai.pricefox.mallfox.convert.bestbuy;

import ai.pricefox.mallfox.job.bestbuy.BestBuyProductDetailResponse;
import ai.pricefox.mallfox.job.bestbuy.BestBuyProductSearchResponse;
import ai.pricefox.mallfox.vo.bestbuy.BestBuyProductDetailRespVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * BestBuy 数据转换器
 */
@Slf4j
@Component
public class BestBuyConvert {

    /**
     * 转换产品详情响应为响应VO
     *
     * @param response BestBuy API 产品详情响应
     * @return 产品详情响应VO
     */
    public BestBuyProductDetailRespVO convertProductDetailResponseToRespVO(BestBuyProductDetailResponse response) {
        if (response == null) {
            return null;
        }

        BestBuyProductDetailRespVO respVO = new BestBuyProductDetailRespVO();

        // 使用 BeanUtils 复制基础字段（会自动复制所有同名字段）
        BeanUtils.copyProperties(response, respVO);

        // 手动处理复杂字段映射
        // 处理类别路径
        if (response.getCategoryPath() != null && !response.getCategoryPath().isEmpty()) {
            List<BestBuyProductDetailRespVO.CategoryPathVO> categoryPathVOs = convertCategoryPathList(response.getCategoryPath());
            respVO.setCategoryPath(categoryPathVOs);
        }

        // 手动处理复杂的 features 字段
        if (response.getFeatures() != null && !response.getFeatures().isEmpty()) {
            List<String> featureStrings = response.getFeatures().stream().map(feature -> {
                if (feature.getFeature() != null) {
                    return feature.getFeature();
                } else if (feature.getFeatureTitle() != null && feature.getFeatureValue() != null) {
                    return feature.getFeatureTitle() + ": " + feature.getFeatureValue();
                } else if (feature.getFeatureTitle() != null) {
                    return feature.getFeatureTitle();
                } else if (feature.getFeatureValue() != null) {
                    return feature.getFeatureValue();
                }
                return null;
            }).filter(java.util.Objects::nonNull).collect(Collectors.toList());
            respVO.setFeatures(featureStrings);
        }

        // 处理 technicalSpecifications 字段
        respVO.setTechnicalSpecifications(convertObjectToStringList(response.getTechnicalSpecifications()));

        // 处理 includedItemList 字段
        respVO.setIncludedItemList(convertObjectToStringList(response.getIncludedItemList()));

        // 验证关键新字段是否被正确复制（用于调试）
        // 注意：BeanUtils.copyProperties() 会自动复制所有同名字段，包括：
        // color, colorCategory, quantityLimit, inStorePickup, homeDelivery,
        // gtin, isbn, mpn, dollarSavings, percentSavings, msrp, onSale,
        // preowned, membersOnlyItem, customerTopRated, includedItemList,
        // shippingCost, shippingWeight, warrantyLabor, warrantyParts,
        // weight, width, height, depth, condition, conditionId, marketplace, marketplaceId
        // 注意：features 字段需要特殊处理，已在上面手动转换

        return respVO;
    }

    /**
     * 转换产品详情响应列表为响应VO列表
     *
     * @param responses BestBuy API 产品详情响应列表
     * @return 产品详情响应VO列表
     */
    public List<BestBuyProductDetailRespVO> convertProductDetailResponseListToRespVOList(List<BestBuyProductDetailResponse> responses) {
        if (responses == null) {
            return null;
        }

        return responses.stream().map(this::convertProductDetailResponseToRespVO).collect(Collectors.toList());
    }

    /**
     * 转换类别路径
     *
     * @param categoryPath 类别路径
     * @return 类别路径VO
     */
    public BestBuyProductDetailRespVO.CategoryPathVO convertCategoryPath(BestBuyProductDetailResponse.CategoryPath categoryPath) {
        if (categoryPath == null) {
            return null;
        }

        BestBuyProductDetailRespVO.CategoryPathVO categoryPathVO = new BestBuyProductDetailRespVO.CategoryPathVO();
        BeanUtils.copyProperties(categoryPath, categoryPathVO);

        return categoryPathVO;
    }

    /**
     * 转换类别路径列表
     *
     * @param categoryPaths 类别路径列表
     * @return 类别路径VO列表
     */
    public List<BestBuyProductDetailRespVO.CategoryPathVO> convertCategoryPathList(List<BestBuyProductDetailResponse.CategoryPath> categoryPaths) {
        if (categoryPaths == null) {
            return null;
        }

        return categoryPaths.stream().map(this::convertCategoryPath).collect(Collectors.toList());
    }

    /**
     * 转换搜索响应为产品详情VO列表
     *
     * @param searchResponse 搜索响应
     * @return 产品详情VO列表
     */
    public List<BestBuyProductDetailRespVO> convertSearchResponseToProductList(BestBuyProductSearchResponse searchResponse) {
        if (searchResponse == null || searchResponse.getProducts() == null) {
            return null;
        }
        return convertProductDetailResponseListToRespVOList(searchResponse.getProducts());
    }

    /**
     * 将 Object 转换为 String 列表的辅助方法
     * 处理 BestBuy API 返回的复杂字段
     */
    @SuppressWarnings("unchecked")
    private List<String> convertObjectToStringList(Object obj) {
        if (obj == null) {
            return null;
        }

        if (obj instanceof List) {
            List<?> list = (List<?>) obj;
            return list.stream().map(item -> {
                if (item instanceof String) {
                    return (String) item;
                } else if (item instanceof java.util.Map) {
                    // 如果是 Map 对象，尝试提取有用信息
                    java.util.Map<?, ?> map = (java.util.Map<?, ?>) item;
                    return map.toString(); // 简单转换，可以根据需要优化
                } else {
                    return item != null ? item.toString() : null;
                }
            }).filter(java.util.Objects::nonNull).collect(Collectors.toList());
        } else if (obj instanceof String) {
            return List.of((String) obj);
        } else {
            return List.of(obj.toString());
        }
    }
}

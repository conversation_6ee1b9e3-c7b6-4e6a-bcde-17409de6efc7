package ai.pricefox.mallfox.convert.bestbuy;

import ai.pricefox.mallfox.enums.DataChannelEnum;
import ai.pricefox.mallfox.enums.ProductPlatformEnum;
import ai.pricefox.mallfox.model.dto.ProductDataDTO;
import ai.pricefox.mallfox.vo.bestbuy.BestBuyProductDetailRespVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * BestBuy 商品数据转换为 ProductDataDTO 的转换服务
 */
@Service
@Slf4j
public class BestBuyToProductDataConverter {

    /**
     * 将 BestBuyProductDetailRespVO 转换为 ProductDataDTO
     *
     * @param bestBuyResp BestBuy 商品详情响应
     * @return ProductDataDTO
     */
    public ProductDataDTO convertToProductDataDTO(BestBuyProductDetailRespVO bestBuyResp) {
        if (bestBuyResp == null) {
            return null;
        }

        ProductDataDTO dto = new ProductDataDTO();

        try {
            // 系统字段
            dto.setForceUpdate(0);  // 默认不强制更新
            dto.setSourcePlatform(ProductPlatformEnum.BESTBUY);  // 来源平台
            dto.setDataChannel(DataChannelEnum.API);  // {@link ai.pricefox.mallfox.enums.DataChannelEnum#API} API数据

            // 基本字段映射
            dto.setPlatformSpuId(bestBuyResp.getSku());
            dto.setPlatformSkuId(bestBuyResp.getSku());
            dto.setTitle(bestBuyResp.getName());
            dto.setBrand(bestBuyResp.getManufacturer());
            dto.setModel(bestBuyResp.getModelNumber());
            dto.setColor(bestBuyResp.getColor());
            dto.setUpcCode(bestBuyResp.getUpc());
            dto.setItemUrl(bestBuyResp.getUrl());
            
            // 价格字段转换
            if (bestBuyResp.getSalePrice() != null) {
                dto.setPrice(bestBuyResp.getSalePrice());
            } else if (bestBuyResp.getRegularPrice() != null) {
                dto.setPrice(bestBuyResp.getRegularPrice());
            }

            // 设置必填字段的默认值（如果为空）
            setDefaultValuesForRequiredFields(dto, bestBuyResp);

            log.info("成功转换 BestBuy 商品数据: itemId={}, title={}", bestBuyResp.getSku(), bestBuyResp.getName());

        } catch (Exception e) {
            log.error("转换 BestBuy 商品数据时发生错误: itemId={}", bestBuyResp.getSku(), e);
            throw new RuntimeException("BestBuy 商品数据转换失败", e);
        }

        return dto;
    }

    /**
     * 为必填字段设置默认值
     */
    private void setDefaultValuesForRequiredFields(ProductDataDTO dto, BestBuyProductDetailRespVO bestBuyResp) {
        // 商品规格 - 必填
        if (!StringUtils.hasText(dto.getModel())) {
            dto.setModel(StringUtils.hasText(bestBuyResp.getModelNumber()) ? bestBuyResp.getModelNumber() : "Unknown Model");
        }

        // 商品颜色 - 必填
        if (!StringUtils.hasText(dto.getColor())) {
            dto.setColor(StringUtils.hasText(bestBuyResp.getColor()) ? bestBuyResp.getColor() : "Unknown Color");
        }

        // 商品存储 - 必填
        if (!StringUtils.hasText(dto.getStorage())) {
            dto.setStorage("Unknown Storage");
        }

        // 商品服务商 - 必填
        dto.setServiceProvider("BestBuy");

        // 商品状态 - 必填
        if (!StringUtils.hasText(dto.getCondition())) {
            dto.setCondition("New");
        }

        // 平台SPU ID - 必填
        if (!StringUtils.hasText(dto.getPlatformSpuId())) {
            dto.setPlatformSpuId(bestBuyResp.getSku() != null ? bestBuyResp.getSku() : "");
        }

        // 平台SKU ID - 必填
        if (!StringUtils.hasText(dto.getPlatformSkuId())) {
            dto.setPlatformSkuId(bestBuyResp.getSku() != null ? bestBuyResp.getSku() : "");
        }
    }
}

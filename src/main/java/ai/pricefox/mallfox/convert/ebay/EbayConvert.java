package ai.pricefox.mallfox.convert.ebay;

import ai.pricefox.mallfox.job.ebay.*;
import ai.pricefox.mallfox.vo.ebay.*;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * eBay 数据转换器
 */
@Component
public class EbayConvert {

    /**
     * 转换商品详情
     */
    public EbayItemDetailRespVO convertToItemDetailRespVO(EbayProductDetail productDetail) {
        if (productDetail == null) {
            return null;
        }

        EbayItemDetailRespVO respVO = new EbayItemDetailRespVO();
        BeanUtils.copyProperties(productDetail, respVO);
        respVO.setFetchTime(LocalDateTime.now());
        return respVO;
    }

    /**
     * 转换搜索结果
     */
    public EbaySearchRespVO convertToSearchRespVO(EbaySearchResponse searchResponse) {
        if (searchResponse == null) {
            return null;
        }

        EbaySearchRespVO respVO = new EbaySearchRespVO();
        respVO.setHref(searchResponse.getHref());
        respVO.setTotal(searchResponse.getTotal());
        respVO.setLimit(searchResponse.getLimit());
        respVO.setOffset(searchResponse.getOffset());
        respVO.setNext(searchResponse.getNext());
        respVO.setPrev(searchResponse.getPrev());

        if (searchResponse.getItemSummaries() != null) {
            List<EbaySearchRespVO.ItemSummaryVO> itemSummaries = searchResponse.getItemSummaries().stream()
                    .map(this::convertToItemSummaryVO)
                    .collect(Collectors.toList());
            respVO.setItemSummaries(itemSummaries);
        }

        // 处理警告信息
        if (searchResponse.getWarnings() != null && !searchResponse.getWarnings().isEmpty()) {
            List<String> warningMessages = searchResponse.getWarnings().stream()
                    .map(warning -> warning.getMessage() != null ? warning.getMessage() :
                            (warning.getLongMessage() != null ? warning.getLongMessage() : "Warning"))
                    .collect(Collectors.toList());
            respVO.setWarnings(warningMessages);
        }

        respVO.setSearchTime(LocalDateTime.now());
        return respVO;
    }

    /**
     * 转换商品摘要
     */
    public EbaySearchRespVO.ItemSummaryVO convertToItemSummaryVO(EbaySearchResponse.ItemSummary itemSummary) {
        if (itemSummary == null) {
            return null;
        }

        EbaySearchRespVO.ItemSummaryVO summaryVO = new EbaySearchRespVO.ItemSummaryVO();
        BeanUtils.copyProperties(itemSummary, summaryVO);

        // 处理联盟链接
        summaryVO.setItemAffiliateWebUrl(itemSummary.getItemAffiliateWebUrl());

        // 处理基本信息字段（只设置在EbaySearchResponse.ItemSummary中实际存在的字段）
        // 注意：以下字段只在getItem详情接口中可用，搜索接口中不包含：
        // subtitle, gtin, epid, inferredEpid, categoryId, categoryIdPath, quantityLimitPerBuyer, sellerItemRevision

        // 设置shortDescription（搜索接口中可用）
        summaryVO.setDescription(itemSummary.getShortDescription()); // 将shortDescription映射到description字段

        // 处理价格信息
        if (itemSummary.getPrice() != null) {
            summaryVO.setPrice(itemSummary.getPrice().getValue());
            summaryVO.setCurrency(itemSummary.getPrice().getCurrency());
        }

        // 处理营销价格信息
        if (itemSummary.getMarketingPrice() != null) {
            if (itemSummary.getMarketingPrice().getOriginalPrice() != null) {
                summaryVO.setOriginalPrice(itemSummary.getMarketingPrice().getOriginalPrice().getValue());
            }
            summaryVO.setDiscountPercentage(itemSummary.getMarketingPrice().getDiscountPercentage());
            if (itemSummary.getMarketingPrice().getDiscountAmount() != null) {
                summaryVO.setDiscountAmount(itemSummary.getMarketingPrice().getDiscountAmount().getValue());
            }
        }

        // 处理图片信息
        if (itemSummary.getImage() != null) {
            summaryVO.setImageUrl(itemSummary.getImage().getImageUrl());
        }

        // 处理卖家信息
        if (itemSummary.getSeller() != null) {
            summaryVO.setSellerUsername(itemSummary.getSeller().getUsername());
            summaryVO.setSellerFeedbackPercentage(itemSummary.getSeller().getFeedbackPercentage());
            summaryVO.setSellerFeedbackScore(itemSummary.getSeller().getFeedbackScore());

            // 处理ADDITIONAL_SELLER_DETAILS fieldgroups 新增字段
            summaryVO.setSellerUserId(itemSummary.getSeller().getUserId());
            summaryVO.setSellerAccountType(itemSummary.getSeller().getSellerAccountType());
            summaryVO.setSellerTopRated(itemSummary.getSeller().getTopRatedSeller());
            summaryVO.setSellerFeedbackRatingStar(itemSummary.getSeller().getFeedbackRatingStar());
            summaryVO.setSellerPositiveFeedbackPercent(itemSummary.getSeller().getPositiveFeedbackPercent());
        }

        // 处理配送信息
        if (itemSummary.getShippingOptions() != null && !itemSummary.getShippingOptions().isEmpty()) {
            EbaySearchResponse.ShippingOption shipping = itemSummary.getShippingOptions().get(0);
            if (shipping.getShippingCost() != null) {
                summaryVO.setShippingCost(shipping.getShippingCost().getValue());
                summaryVO.setShippingCurrency(shipping.getShippingCost().getCurrency());
            }
        }

        // 处理位置信息
        if (itemSummary.getItemLocation() != null) {
            summaryVO.setItemLocationCountry(itemSummary.getItemLocation().getCountry());
            summaryVO.setItemLocationPostalCode(itemSummary.getItemLocation().getPostalCode());
            summaryVO.setItemLocationCity(itemSummary.getItemLocation().getCity());
            summaryVO.setItemLocationStateOrProvince(itemSummary.getItemLocation().getStateOrProvince());
            summaryVO.setItemLocationCountyName(itemSummary.getItemLocation().getCountyName());
            summaryVO.setItemLocationAddressLine1(itemSummary.getItemLocation().getAddressLine1());
            summaryVO.setItemLocationAddressLine2(itemSummary.getItemLocation().getAddressLine2());
        }

        // 处理购买选项
        if (itemSummary.getBuyingOptions() != null && !itemSummary.getBuyingOptions().isEmpty()) {
            summaryVO.setBuyingOptions(String.join(",", itemSummary.getBuyingOptions()));
        }

        // 处理当前出价（拍卖商品）
        if (itemSummary.getCurrentBidPrice() != null) {
            summaryVO.setCurrentBidPrice(itemSummary.getCurrentBidPrice().getValue());
        }

        // 处理EXTENDED fieldgroups 新增字段
        summaryVO.setShortDescription(itemSummary.getShortDescription());

        // 处理叶子分类ID列表
        if (itemSummary.getLeafCategoryIds() != null && !itemSummary.getLeafCategoryIds().isEmpty()) {
            summaryVO.setLeafCategoryIds(String.join(",", itemSummary.getLeafCategoryIds()));
        }

        // 处理分类信息
        if (itemSummary.getCategories() != null && !itemSummary.getCategories().isEmpty()) {
            String categoriesStr = itemSummary.getCategories().stream()
                    .map(category -> category.getCategoryName())
                    .filter(name -> name != null)
                    .collect(Collectors.joining(" > "));
            summaryVO.setCategories(categoriesStr);
        }

        // 处理缩略图列表
        if (itemSummary.getThumbnailImages() != null && !itemSummary.getThumbnailImages().isEmpty()) {
            String thumbnailUrls = itemSummary.getThumbnailImages().stream()
                    .map(image -> image.getImageUrl())
                    .filter(url -> url != null)
                    .collect(Collectors.joining(","));
            summaryVO.setThumbnailImages(thumbnailUrls);
        }

        // 处理额外图片列表
        if (itemSummary.getAdditionalImages() != null && !itemSummary.getAdditionalImages().isEmpty()) {
            String additionalUrls = itemSummary.getAdditionalImages().stream()
                    .map(image -> image.getImageUrl())
                    .filter(url -> url != null)
                    .collect(Collectors.joining(","));
            summaryVO.setAdditionalImages(additionalUrls);
        }

        summaryVO.setItemOriginDate(itemSummary.getItemOriginDate());
        summaryVO.setWatchCount(itemSummary.getWatchCount());
        summaryVO.setBidCount(itemSummary.getBidCount());
        summaryVO.setReserveMet(itemSummary.getReserveMet());
        summaryVO.setHighBidder(itemSummary.getHighBidder());

        // 处理额外的商品详细信息
        summaryVO.setUnitPrice(itemSummary.getUnitPrice());

        // 处理单价计量单位
        if (itemSummary.getUnitPricingMeasure() != null) {
            String unitMeasure = itemSummary.getUnitPricingMeasure().getUnit();
            if (itemSummary.getUnitPricingMeasure().getUnitType() != null) {
                unitMeasure += " (" + itemSummary.getUnitPricingMeasure().getUnitType() + ")";
            }
            summaryVO.setUnitPricingMeasure(unitMeasure);
        }

        // 处理符合的程序
        if (itemSummary.getQualifiedPrograms() != null && !itemSummary.getQualifiedPrograms().isEmpty()) {
            summaryVO.setQualifiedPrograms(String.join(",", itemSummary.getQualifiedPrograms()));
        }

        summaryVO.setAuthenticityGuarantee(itemSummary.getAuthenticityGuarantee());
        summaryVO.setAuthenticityVerification(itemSummary.getAuthenticityVerification());

        // 处理可用优惠券列表
        if (itemSummary.getAvailableCouponsList() != null && !itemSummary.getAvailableCouponsList().isEmpty()) {
            String couponsStr = itemSummary.getAvailableCouponsList().stream()
                    .map(coupon -> coupon.getRedemptionCode() != null ? coupon.getRedemptionCode() : coupon.getCouponType())
                    .filter(code -> code != null)
                    .collect(Collectors.joining(","));
            summaryVO.setAvailableCouponsList(couponsStr);
        }

        summaryVO.setEnergyEfficiencyClass(itemSummary.getEnergyEfficiencyClass());

        // 处理危险品信息
        if (itemSummary.getHazmatItems() != null && !itemSummary.getHazmatItems().isEmpty()) {
            String hazmatStr = itemSummary.getHazmatItems().stream()
                    .map(hazmat -> hazmat.getDescription() != null ? hazmat.getDescription() : hazmat.getType())
                    .filter(desc -> desc != null)
                    .collect(Collectors.joining("; "));
            summaryVO.setHazmatItems(hazmatStr);
        }

        summaryVO.setLotSize(itemSummary.getLotSize());

        // 处理自提选项
        if (itemSummary.getPickupOptions() != null && !itemSummary.getPickupOptions().isEmpty()) {
            StringBuilder pickupStr = new StringBuilder();
            for (EbaySearchResponse.PickupOption pickup : itemSummary.getPickupOptions()) {
                if (pickupStr.length() > 0) pickupStr.append("; ");
                if (pickup.getPickupMethod() != null) {
                    pickupStr.append("Method: ").append(pickup.getPickupMethod());
                }
                if (pickup.getPickupLocationType() != null) {
                    if (pickupStr.length() > 0) pickupStr.append(", ");
                    pickupStr.append("Type: ").append(pickup.getPickupLocationType());
                }
                if (pickup.getPickupLocationName() != null) {
                    if (pickupStr.length() > 0) pickupStr.append(", ");
                    pickupStr.append("Location: ").append(pickup.getPickupLocationName());
                }
            }
            summaryVO.setPickupOptions(pickupStr.toString());
        }

        // 处理退货条款
        if (itemSummary.getReturnTerms() != null) {
            StringBuilder returnTermsStr = new StringBuilder();
            if (itemSummary.getReturnTerms().getReturnsAccepted() != null) {
                returnTermsStr.append("Returns: ").append(itemSummary.getReturnTerms().getReturnsAccepted() ? "Accepted" : "Not Accepted");
            }
            if (itemSummary.getReturnTerms().getReturnPeriod() != null) {
                if (returnTermsStr.length() > 0) returnTermsStr.append(", ");
                returnTermsStr.append("Period: ").append(itemSummary.getReturnTerms().getReturnPeriod());
            }
            summaryVO.setReturnTerms(returnTermsStr.toString());
        }

        summaryVO.setFastAndFreeShipping(itemSummary.getFastAndFreeShipping());

        // 处理税务信息
        if (itemSummary.getTaxes() != null) {
            StringBuilder taxStr = new StringBuilder();
            if (itemSummary.getTaxes().getIncludedInPrice() != null) {
                taxStr.append("Included: ").append(itemSummary.getTaxes().getIncludedInPrice());
            }
            if (itemSummary.getTaxes().getType() != null) {
                if (taxStr.length() > 0) taxStr.append(", ");
                taxStr.append("Type: ").append(itemSummary.getTaxes().getType());
            }
            summaryVO.setTaxes(taxStr.toString());
        }

        summaryVO.setEstimatedAvailabilities(itemSummary.getEstimatedAvailabilities());
        summaryVO.setCategoryPath(itemSummary.getCategoryPath());

        // 处理产品信息
        if (itemSummary.getProducts() != null && !itemSummary.getProducts().isEmpty()) {
            String productsStr = itemSummary.getProducts().stream()
                    .map(product -> {
                        StringBuilder productStr = new StringBuilder();
                        if (product.getBrand() != null) {
                            productStr.append("Brand: ").append(product.getBrand());
                        }
                        if (product.getMpn() != null) {
                            if (productStr.length() > 0) productStr.append(", ");
                            productStr.append("MPN: ").append(product.getMpn());
                        }
                        return productStr.toString();
                    })
                    .filter(str -> !str.isEmpty())
                    .collect(Collectors.joining("; "));
            summaryVO.setProducts(productsStr);
        }

        // 处理兼容性匹配
        if (itemSummary.getCompatibilityMatch() != null) {
            StringBuilder compatStr = new StringBuilder();
            if (itemSummary.getCompatibilityMatch().getCompatibilityStatus() != null) {
                compatStr.append("Status: ").append(itemSummary.getCompatibilityMatch().getCompatibilityStatus());
            }
            if (itemSummary.getCompatibilityMatch().getNotes() != null && !itemSummary.getCompatibilityMatch().getNotes().isEmpty()) {
                if (compatStr.length() > 0) compatStr.append(", ");
                compatStr.append("Notes: ").append(String.join("; ", itemSummary.getCompatibilityMatch().getNotes()));
            }
            summaryVO.setCompatibilityMatch(compatStr.toString());
        }

        // 注意：localizedAspects字段只在getItem详情接口中可用，搜索接口中不包含此字段
        // 如果需要商品规格信息，需要调用getItem接口获取详细信息

        // 注意：primaryProductReviewRating字段只在getItem详情接口中可用，搜索接口中不包含此字段
        // 如果需要评价信息，需要调用getItem接口获取详细信息

        // 处理变体相关
        // 注意：itemGroupId字段只在getItem详情接口中可用，搜索接口中只有itemGroupHref和itemGroupType
        // 可以从itemGroupHref中提取itemGroupId，但这里暂时不处理

        // 注意：conditionDescription字段只在getItem详情接口中可用，搜索接口中不包含此字段
        // 如果需要商品状态详细描述，需要调用getItem接口获取详细信息

        return summaryVO;
    }

    /**
     * 转换商品项目组
     */
    public EbayItemGroupRespVO convertToItemGroupRespVO(EbayItemGroupResponse itemGroupResponse) {
        if (itemGroupResponse == null) {
            return null;
        }

        EbayItemGroupRespVO respVO = new EbayItemGroupRespVO();

        // 转换商品列表
        if (itemGroupResponse.getItems() != null) {
            List<EbayItemDetailRespVO> items = itemGroupResponse.getItems().stream()
                    .map(this::convertItemDetailResponseToRespVO)
                    .collect(Collectors.toList());
            respVO.setItems(items);
        }

        // 转换通用描述
        if (itemGroupResponse.getCommonDescriptions() != null) {
            List<EbayItemGroupRespVO.CommonDescriptionVO> commonDescriptions = 
                    itemGroupResponse.getCommonDescriptions().stream()
                    .map(this::convertToCommonDescriptionVO)
                    .collect(Collectors.toList());
            respVO.setCommonDescriptions(commonDescriptions);
        }

        respVO.setFetchTime(LocalDateTime.now());
        return respVO;
    }

    /**
     * 转换 EbayItemDetailResponse 到 EbayItemDetailRespVO
     */
    public EbayItemDetailRespVO convertItemDetailResponseToRespVO(EbayItemDetailResponse response) {
        if (response == null) {
            return null;
        }

        EbayItemDetailRespVO respVO = new EbayItemDetailRespVO();

        // 基本信息
        respVO.setItemId(response.getItemId());
        respVO.setTitle(response.getTitle());
        respVO.setShortDescription(response.getShortDescription());
        respVO.setDescription(response.getDescription());
        respVO.setBrand(response.getBrand());
        respVO.setMpn(response.getMpn());
        respVO.setCondition(response.getCondition());
        respVO.setConditionId(response.getConditionId());
        respVO.setCategoryPath(response.getCategoryPath());
        respVO.setCategoryId(response.getCategoryId());
        respVO.setLegacyItemId(response.getLegacyItemId());
        respVO.setAdultOnly(response.getAdultOnly());

        // 基于实际 API 返回的顶级字段
        respVO.setColor(response.getColor());
        respVO.setMaterial(response.getMaterial());
        // pattern 和 sizeType 字段在 EbayItemDetailRespVO 中没有对应字段，可以放在 remarks 或其他字段中
        if (StringUtils.hasText(response.getPattern())) {
            String currentRemarks = respVO.getRemarks();
            String patternInfo = "Pattern: " + response.getPattern();
            respVO.setRemarks(StringUtils.hasText(currentRemarks) ?
                currentRemarks + ", " + patternInfo : patternInfo);
        }

        // 价格信息
        if (response.getPrice() != null) {
            respVO.setPrice(response.getPrice().getValue() != null ? response.getPrice().getValue().toString() : null);
            respVO.setCurrency(response.getPrice().getCurrency());
        }

        // 图片信息
        if (response.getImage() != null) {
            respVO.setImageUrl(response.getImage().getImageUrl());
        }

        // 卖家信息
        if (response.getSeller() != null) {
            respVO.setSellerUsername(response.getSeller().getUsername());
            respVO.setSellerFeedbackPercentage(response.getSeller().getFeedbackPercentage() != null ?
                    response.getSeller().getFeedbackPercentage().toString() : null);
            respVO.setSellerFeedbackScore(response.getSeller().getFeedbackScore());
        }

        // 位置信息
        if (response.getItemLocation() != null) {
            respVO.setItemLocationCity(response.getItemLocation().getCity());
            respVO.setItemLocationState(response.getItemLocation().getStateOrProvince());
            respVO.setItemLocationCountry(response.getItemLocation().getCountry());
            respVO.setItemLocationPostalCode(response.getItemLocation().getPostalCode());
        }

        // 库存信息 - 从 EstimatedAvailability 中获取
        if (response.getEstimatedAvailabilities() != null && !response.getEstimatedAvailabilities().isEmpty()) {
            EbayItemDetailResponse.EstimatedAvailability availability = response.getEstimatedAvailabilities().get(0);
            respVO.setAvailabilityStatus(availability.getEstimatedAvailabilityStatus());
            respVO.setAvailableQuantity(availability.getEstimatedAvailableQuantity());
            respVO.setSoldQuantity(availability.getEstimatedSoldQuantity());
            // 类型转换：Integer -> String
            respVO.setAvailabilityThreshold(availability.getAvailabilityThreshold() != null ?
                    availability.getAvailabilityThreshold().toString() : null);
            respVO.setEstimatedRemainingQuantity(availability.getEstimatedRemainingQuantity() != null ?
                    availability.getEstimatedRemainingQuantity().toString() : null);
            // 注意：EstimatedAvailability类中没有restockDate字段，已删除
            // respVO.setRestockDate(availability.getRestockDate());
        }

        // 配送信息
        if (response.getShippingOptions() != null && !response.getShippingOptions().isEmpty()) {
            EbayItemDetailResponse.ShippingOption shipping = response.getShippingOptions().get(0);
            respVO.setShippingServiceCode(shipping.getShippingServiceCode());
            respVO.setShippingType(shipping.getType());
            if (shipping.getShippingCost() != null) {
                respVO.setShippingCost(shipping.getShippingCost().getValue());
                respVO.setShippingCurrency(shipping.getShippingCost().getCurrency());
            }
            respVO.setMinDeliveryDate(shipping.getMinEstimatedDeliveryDate());
            respVO.setMaxDeliveryDate(shipping.getMaxEstimatedDeliveryDate());
            // 注意：以下字段在ShippingOption类中不存在，需要从其他地方获取或删除
            // respVO.setGuaranteedDelivery(shipping.getGuaranteedDelivery());
            // respVO.setShippingCarrier(shipping.getShippingCarrier());
            // respVO.setExpeditedShipping(shipping.getExpeditedShipping());
            // respVO.setFreeShipping(shipping.getFreeShipping());
        }

        // 退货信息
        if (response.getReturnTerms() != null) {
            respVO.setReturnsAccepted(response.getReturnTerms().getReturnsAccepted());
            respVO.setRefundMethod(response.getReturnTerms().getRefundMethod());
            respVO.setReturnShippingCostPayer(response.getReturnTerms().getReturnShippingCostPayer());
            respVO.setReturnPeriodValue(response.getReturnTerms().getReturnPeriod() != null ?
                    response.getReturnTerms().getReturnPeriod().getValue() : null);
            respVO.setReturnPeriodUnit(response.getReturnTerms().getReturnPeriod() != null ?
                    response.getReturnTerms().getReturnPeriod().getUnit() : null);
        }

        // 评价信息 - 从 PrimaryProductReviewRating 中获取
        if (response.getPrimaryProductReviewRating() != null) {
            respVO.setReviewCount(response.getPrimaryProductReviewRating().getReviewCount());
            respVO.setAverageRating(response.getPrimaryProductReviewRating().getAverageRating());
        }

        // 其他基本字段
        respVO.setPriorityListing(response.getPriorityListing());
        respVO.setTopRatedBuyingExperience(response.getTopRatedBuyingExperience());
        if (response.getBuyingOptions() != null) {
            respVO.setBuyingOptions(String.join(",", response.getBuyingOptions()));
        }
        respVO.setItemWebUrl(response.getItemWebUrl());
        respVO.setImmediatePay(response.getImmediatePay());
        respVO.setEnabledForGuestCheckout(response.getEnabledForGuestCheckout());
        respVO.setEligibleForInlineCheckout(response.getEligibleForInlineCheckout());
        respVO.setLotSize(response.getLotSize());

        // 支付方式 - 需要处理 PaymentMethod 对象
        if (response.getPaymentMethods() != null) {
            List<String> paymentMethodNames = response.getPaymentMethods().stream()
                    .map(pm -> pm.getPaymentMethodType() != null ? pm.getPaymentMethodType() : "UNKNOWN")
                    .collect(Collectors.toList());
            respVO.setPaymentMethods(String.join(",", paymentMethodNames));
        }

        // 拍卖相关字段
        respVO.setBidCount(response.getBidCount());
        if (response.getCurrentBidPrice() != null) {
            respVO.setCurrentBidPrice(response.getCurrentBidPrice().getValue() != null ?
                    response.getCurrentBidPrice().getValue().toString() : null);
        }
        respVO.setReserveMet(response.getReserveMet());
        respVO.setHighBidder(response.getHighBidder());
        respVO.setItemEndDate(response.getItemEndDate());

        // 时间信息
        respVO.setItemCreationDate(response.getItemCreationDate());

        // 市场和促销信息
        respVO.setListingMarketplaceId(response.getListingMarketplaceId());
        respVO.setWatchCount(response.getWatchCount());
        respVO.setSubtitle(response.getSubtitle());

        // 处理信息
        respVO.setFastNHandling(response.getFastNHandling());
        respVO.setHandlingTime(response.getHandlingTime());
        respVO.setConditionDescription(response.getConditionDescription());
        respVO.setConditionDisplayName(response.getConditionDisplayName());

        // 链接信息
        respVO.setItemAffiliateWebUrl(response.getItemAffiliateWebUrl());

        // 营销价格信息
        if (response.getMarketingPrice() != null) {
            if (response.getMarketingPrice().getOriginalPrice() != null) {
                respVO.setOriginalPrice(response.getMarketingPrice().getOriginalPrice().getValue() != null ?
                        response.getMarketingPrice().getOriginalPrice().getValue().toString() : null);
            }
            respVO.setDiscountPercentage(response.getMarketingPrice().getDiscountPercentage());
            if (response.getMarketingPrice().getDiscountAmount() != null) {
                respVO.setDiscountAmount(response.getMarketingPrice().getDiscountAmount().getValue() != null ?
                        response.getMarketingPrice().getDiscountAmount().getValue().toString() : null);
            }
        }

        // 多图片信息
        if (response.getAdditionalImages() != null && !response.getAdditionalImages().isEmpty()) {
            List<String> imageUrls = response.getAdditionalImages().stream()
                    .map(EbayItemDetailResponse.Image::getImageUrl)
                    .collect(Collectors.toList());
            respVO.setAdditionalImageUrls(String.join(",", imageUrls));
        }
        if (response.getThumbnailImages() != null && !response.getThumbnailImages().isEmpty()) {
            respVO.setThumbnailImageUrls(String.join(",", response.getThumbnailImages()));
        }

        // 项目信息
        if (response.getQualifiedPrograms() != null) {
            respVO.setQualifiedPrograms(String.join(",", response.getQualifiedPrograms()));
        }

        // 变体相关信息
        respVO.setItemGroupId(response.getItemGroupId());
        respVO.setParentItemId(response.getParentItemId());
        respVO.setVariationId(response.getVariationId());
        respVO.setItemGroupType(response.getItemGroupType());
        respVO.setHasVariations(response.getHasVariations());
        respVO.setDefaultVariationId(response.getDefaultVariationId());

        // 处理变体列表
        if (response.getItemVariations() != null && !response.getItemVariations().isEmpty()) {
            List<String> variationStrings = response.getItemVariations().stream()
                    .map(variation -> variation.getVariationId() + ":" + variation.getVariationValue())
                    .collect(Collectors.toList());
            respVO.setItemVariations(String.join(",", variationStrings));
        }

        // 处理 PrimaryItemGroup 信息
        if (response.getPrimaryItemGroup() != null) {
            EbayItemDetailResponse.PrimaryItemGroup primaryGroup = response.getPrimaryItemGroup();
            // 如果没有设置 itemGroupId，从 primaryItemGroup 中获取
            if (!StringUtils.hasText(respVO.getItemGroupId())) {
                respVO.setItemGroupId(primaryGroup.getItemGroupId());
            }
            // 如果没有设置 itemGroupType，从 primaryItemGroup 中获取
            if (!StringUtils.hasText(respVO.getItemGroupType())) {
                respVO.setItemGroupType(primaryGroup.getItemGroupType());
            }

            // 处理商品组额外图片
            if (primaryGroup.getItemGroupAdditionalImages() != null && !primaryGroup.getItemGroupAdditionalImages().isEmpty()) {
                List<String> groupImageUrls = primaryGroup.getItemGroupAdditionalImages().stream()
                        .map(EbayItemDetailResponse.Image::getImageUrl)
                        .collect(Collectors.toList());
                // 如果没有设置额外图片，使用商品组的图片
                if (!StringUtils.hasText(respVO.getAdditionalImageUrls())) {
                    respVO.setAdditionalImageUrls(String.join(",", groupImageUrls));
                }
            }
        }

        // 商品规格和属性
        respVO.setItemSpecifics(response.getItemSpecifics());
        if (response.getCompatibilityProperties() != null) {
            respVO.setCompatibilityProperties(String.join(",", response.getCompatibilityProperties()));
        }
        respVO.setItemConditionDescription(response.getItemConditionDescription());
        respVO.setSellerNotes(response.getSellerNotes());

        // 真品保证 - 设置为布尔值表示是否有真品保证
        respVO.setAuthenticityGuarantee(response.getAuthenticityGuarantee() != null);
        // 真品验证 - 如果有验证程序则设置描述，否则为null
        respVO.setAuthenticityVerification(response.getAuthenticityVerification() != null ?
                response.getAuthenticityVerification().getDescription() : null);

        // 警告和免责声明
        if (response.getWarnings() != null && !response.getWarnings().isEmpty()) {
            List<String> warningMessages = response.getWarnings().stream()
                    .map(warning -> warning.getMessage() != null ? warning.getMessage() :
                            (warning.getLongMessage() != null ? warning.getLongMessage() : "Warning"))
                    .collect(Collectors.toList());
            respVO.setWarnings(String.join(",", warningMessages));
        }
        if (response.getDisclaimers() != null) {
            respVO.setDisclaimers(String.join(",", response.getDisclaimers()));
        }

        // 税务信息
        if (response.getTaxes() != null && !response.getTaxes().isEmpty()) {
            List<String> taxInfoList = response.getTaxes().stream()
                    .map(tax -> tax.getTaxType() + ":" + tax.getTaxPercentage() + "%")
                    .collect(Collectors.toList());
            respVO.setTaxInfo(String.join(",", taxInfoList));
        }

        // 从 LocalizedAspects 中提取的独立字段和基本属性字段
        if (response.getLocalizedAspects() != null) {
            for (EbayItemDetailResponse.LocalizedAspect aspect : response.getLocalizedAspects()) {
                if (aspect.getName() == null || aspect.getValue() == null) {
                    continue;
                }
                switch (aspect.getName()) {
                    case "Processor":
                        respVO.setProcessor(aspect.getValue());
                        break;
                    case "Screen Size":
                        respVO.setScreenSize(aspect.getValue());
                        break;
                    case "Memory Card Type":
                        respVO.setMemoryCardType(aspect.getValue());
                        break;
                    case "Internet Connectivity":
                        respVO.setInternetConnectivity(aspect.getValue());
                        break;
                    case "Lock Status":
                        respVO.setLockStatus(aspect.getValue());
                        break;
                    case "Model Number":
                        respVO.setModelNumber(aspect.getValue());
                        break;
                    case "SIM Card Slot":
                        respVO.setSimCardSlot(aspect.getValue());
                        break;
                    case "Colour":
                    case "Color":
                        respVO.setColour(aspect.getValue());
                        respVO.setColor(aspect.getValue()); // 同时设置 color 字段
                        break;
                    case "Condition/Grade":
                        respVO.setConditionGrade(aspect.getValue());
                        break;
                    case "Network":
                        respVO.setNetwork(aspect.getValue());
                        break;
                    case "Connectivity":
                        respVO.setConnectivity(aspect.getValue());
                        break;
                    case "Style":
                        respVO.setStyle(aspect.getValue());
                        break;
                    case "Operating System":
                        respVO.setOperatingSystem(aspect.getValue());
                        break;
                    case "Storage Capacity":
                        respVO.setStorageCapacity(aspect.getValue());
                        respVO.setStorage(aspect.getValue()); // 同时设置 storage 字段
                        break;
                    case "Contract":
                        respVO.setContract(aspect.getValue());
                        break;
                    case "RAM":
                        respVO.setRam(aspect.getValue());
                        break;
                    case "Model":
                        respVO.setModel(aspect.getValue());
                        break;
                    case "Size (Men's)":
                    case "Size":
                        respVO.setSize(aspect.getValue());
                        break;
                    case "Material":
                        respVO.setMaterial(aspect.getValue());
                        break;
                    case "Features":
                        respVO.setFeatures(aspect.getValue());
                        break;
                    case "Pattern":
                        // Pattern 信息可以添加到 remarks 中
                        String currentRemarks = respVO.getRemarks();
                        String patternInfo = "Pattern: " + aspect.getValue();
                        respVO.setRemarks(StringUtils.hasText(currentRemarks) ?
                            currentRemarks + ", " + patternInfo : patternInfo);
                        break;
                    case "Size Type":
                        // Size Type 信息可以添加到 remarks 中
                        String currentRemarksForSizeType = respVO.getRemarks();
                        String sizeTypeInfo = "Size Type: " + aspect.getValue();
                        respVO.setRemarks(StringUtils.hasText(currentRemarksForSizeType) ?
                            currentRemarksForSizeType + ", " + sizeTypeInfo : sizeTypeInfo);
                        break;
                    case "Country/Region of Manufacture":
                        // 制造国家信息可以添加到 remarks 中
                        String currentRemarksForCountry = respVO.getRemarks();
                        String countryInfo = "Made in: " + aspect.getValue();
                        respVO.setRemarks(StringUtils.hasText(currentRemarksForCountry) ?
                            currentRemarksForCountry + ", " + countryInfo : countryInfo);
                        break;
                    case "Sleeve Length":
                        // 袖长信息可以添加到 features 中
                        String currentFeatures = respVO.getFeatures();
                        String sleeveInfo = "Sleeve: " + aspect.getValue();
                        respVO.setFeatures(StringUtils.hasText(currentFeatures) ?
                            currentFeatures + ", " + sleeveInfo : sleeveInfo);
                        break;
                }
            }
        }

        // 设置备注字段 - 可以从卖家备注或其他描述信息中获取
        if (StringUtils.hasText(response.getSellerNotes())) {
            respVO.setRemarks(response.getSellerNotes());
        } else if (StringUtils.hasText(response.getItemConditionDescription())) {
            respVO.setRemarks(response.getItemConditionDescription());
        }

        // 基于eBay官方文档补充的新字段转换
        respVO.setGtin(response.getGtin());
        respVO.setEpid(response.getEpid());
        respVO.setInferredEpid(response.getInferredEpid());
        respVO.setAgeGroup(response.getAgeGroup());
        respVO.setGender(response.getGender());
        respVO.setEnergyEfficiencyClass(response.getEnergyEfficiencyClass());
        respVO.setTyreLabelImageUrl(response.getTyreLabelImageUrl());
        respVO.setUniqueBidderCount(response.getUniqueBidderCount());

        // 单价信息
        if (response.getUnitPrice() != null) {
            respVO.setUnitPrice(response.getUnitPrice().getValue());
            respVO.setUnitPriceCurrency(response.getUnitPrice().getCurrency());
        }
        respVO.setUnitPricingMeasure(response.getUnitPricingMeasure());

        // 环保参与费
        if (response.getEcoParticipationFee() != null) {
            respVO.setEcoParticipationFee(response.getEcoParticipationFee().getValue());
            respVO.setEcoParticipationFeeCurrency(response.getEcoParticipationFee().getCurrency());
        }

        // 卖家用户ID
        if (response.getSeller() != null) {
            respVO.setSellerUserId(response.getSeller().getUserId());
        }

        // 图片尺寸信息
        if (response.getImage() != null) {
            respVO.setImageHeight(response.getImage().getHeight());
            respVO.setImageWidth(response.getImage().getWidth());
        }

        // 退货方式
        if (response.getReturnTerms() != null) {
            respVO.setReturnMethod(response.getReturnTerms().getReturnMethod());
        }

        // 配送相关新字段
        if (response.getShippingOptions() != null && !response.getShippingOptions().isEmpty()) {
            EbayItemDetailResponse.ShippingOption shipping = response.getShippingOptions().get(0);
            respVO.setShippingCarrierCode(shipping.getShippingCarrierCode());
            respVO.setTrademarkSymbol(shipping.getTrademarkSymbol());
            if (shipping.getShipToLocationUsedForEstimate() != null) {
                respVO.setShipToLocationCountry(shipping.getShipToLocationUsedForEstimate().getCountry());
                respVO.setShipToLocationPostalCode(shipping.getShipToLocationUsedForEstimate().getPostalCode());
            }
        }

        // 库存阈值类型
        if (response.getEstimatedAvailabilities() != null && !response.getEstimatedAvailabilities().isEmpty()) {
            EbayItemDetailResponse.EstimatedAvailability availability = response.getEstimatedAvailabilities().get(0);
            respVO.setAvailabilityThresholdType(availability.getAvailabilityThresholdType());
        }

        // 产品标识符信息
        if (response.getProduct() != null && response.getProduct().getAdditionalProductIdentities() != null
                && !response.getProduct().getAdditionalProductIdentities().isEmpty()) {
            EbayItemDetailResponse.AdditionalProductIdentity identity =
                    response.getProduct().getAdditionalProductIdentities().get(0);
            if (identity.getProductIdentity() != null) {
                respVO.setProductIdentifierType(identity.getProductIdentity().getIdentifierType());
                respVO.setProductIdentifierValue(identity.getProductIdentity().getIdentifierValue());
            }
        }

        // 状态描述符
        if (response.getConditionDescriptors() != null && !response.getConditionDescriptors().isEmpty()) {
            List<String> descriptors = response.getConditionDescriptors().stream()
                    .map(cd -> cd.getName() + ": " + (cd.getValues() != null && !cd.getValues().isEmpty()
                            ? cd.getValues().get(0).getContent() : ""))
                    .collect(Collectors.toList());
            respVO.setConditionDescriptors(String.join(", ", descriptors));
        }

        // 危险材料标签
        if (response.getHazardousMaterialsLabels() != null) {
            respVO.setHazardSignalWord(response.getHazardousMaterialsLabels().getSignalWord());
            respVO.setHazardAdditionalInfo(response.getHazardousMaterialsLabels().getAdditionalInformation());
        }

        // 附加服务
        if (response.getAddonServices() != null && !response.getAddonServices().isEmpty()) {
            List<String> services = response.getAddonServices().stream()
                    .map(EbayItemDetailResponse.AddonService::getServiceType)
                    .collect(Collectors.toList());
            respVO.setAddonServices(String.join(", ", services));
        }

        // 真品保证
        if (response.getAuthenticityGuarantee() != null) {
            respVO.setAuthenticityGuaranteeDescription(response.getAuthenticityGuarantee().getDescription());
            respVO.setAuthenticityGuaranteeTermsUrl(response.getAuthenticityGuarantee().getTermsWebUrl());
        }

        // 真品验证
        if (response.getAuthenticityVerification() != null) {
            respVO.setAuthenticityVerificationDescription(response.getAuthenticityVerification().getDescription());
            respVO.setAuthenticityVerificationTermsUrl(response.getAuthenticityVerification().getTermsWebUrl());
        }

        // 优惠券信息
        if (response.getAvailableCoupons() != null && !response.getAvailableCoupons().isEmpty()) {
            EbayItemDetailResponse.AvailableCoupon coupon = response.getAvailableCoupons().get(0);
            respVO.setCouponRedemptionCode(coupon.getRedemptionCode());
            respVO.setCouponDiscountType(coupon.getDiscountType());
            if (coupon.getDiscountAmount() != null) {
                respVO.setCouponDiscountAmount(coupon.getDiscountAmount().getValue());
            }
            respVO.setCouponMessage(coupon.getMessage());
            respVO.setCouponTermsUrl(coupon.getTermsWebUrl());
            if (coupon.getConstraint() != null) {
                respVO.setCouponExpirationDate(coupon.getConstraint().getExpirationDate());
            }

            // 设置可用优惠券列表
            List<String> couponCodes = response.getAvailableCoupons().stream()
                    .map(EbayItemDetailResponse.AvailableCoupon::getRedemptionCode)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            respVO.setAvailableCoupons(String.join(",", couponCodes));
        }

        // 设置获取时间
        respVO.setFetchTime(LocalDateTime.now());

        return respVO;
    }

    /**
     * 转换通用描述
     */
    private EbayItemGroupRespVO.CommonDescriptionVO convertToCommonDescriptionVO(
            EbayItemGroupResponse.CommonDescription commonDescription) {
        if (commonDescription == null) {
            return null;
        }

        EbayItemGroupRespVO.CommonDescriptionVO descriptionVO = new EbayItemGroupRespVO.CommonDescriptionVO();
        descriptionVO.setDescription(commonDescription.getDescription());
        descriptionVO.setItemIds(commonDescription.getItemIds());
        return descriptionVO;
    }



}

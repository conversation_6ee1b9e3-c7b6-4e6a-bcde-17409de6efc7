package ai.pricefox.mallfox.convert.ebay;

import ai.pricefox.mallfox.enums.DataChannelEnum;
import ai.pricefox.mallfox.enums.ProductPlatformEnum;
import ai.pricefox.mallfox.job.ebay.EbaySearchResponse;
import ai.pricefox.mallfox.model.dto.ProductDataDTO;
import ai.pricefox.mallfox.vo.ebay.EbayItemDetailRespVO;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * eBay 商品数据转换为 ProductDataDTO 的转换服务
 */
@Service
@Slf4j
public class EbayToProductDataConverter {

    /**
     * 将 EbayItemDetailRespVO 转换为 ProductDataDTO
     *
     * @param ebayResp eBay 商品详情响应
     * @return ProductDataDTO
     */
    public ProductDataDTO convertToProductDataDTO(EbayItemDetailRespVO ebayResp) {
        if (ebayResp == null) {
            return null;
        }

        ProductDataDTO dto = new ProductDataDTO();

        try {
            // 系统字段
            dto.setForceUpdate(0);  // 默认不强制更新
            dto.setSourcePlatform(ProductPlatformEnum.EBAY);  // 来源平台
            dto.setDataChannel(DataChannelEnum.API);  // {@link ai.pricefox.mallfox.enums.DataChannelEnum#API} API数据

            // 完全一样的字段 (12个)
            dto.setTitle(ebayResp.getTitle());
            dto.setBrand(ebayResp.getBrand());
            dto.setCondition(ebayResp.getCondition());
            dto.setModel(ebayResp.getModel());
            dto.setColor(ebayResp.getColor());
            dto.setFeatures(ebayResp.getFeatures());
            dto.setStorage(ebayResp.getStorage());
            dto.setProcessor(ebayResp.getProcessor());
            dto.setOperatingSystem(ebayResp.getOperatingSystem());
            dto.setScreenSize(ebayResp.getScreenSize());

            // 价格字段转换
            if (StringUtils.hasText(ebayResp.getPrice())) {
                try {
                    dto.setPrice(new BigDecimal(ebayResp.getPrice()));
                } catch (NumberFormatException e) {
                    log.warn("价格转换失败: {}", ebayResp.getPrice());
                }
            }

            // 意思相近的字段 (25个)
            dto.setPlatformSpuId(ebayResp.getLegacyItemId());
            dto.setPlatformSkuId(ebayResp.getItemId());
            dto.setItemUrl(ebayResp.getItemWebUrl());
            dto.setSeller(ebayResp.getSellerUsername());
            dto.setInventory(String.valueOf(ebayResp.getAvailableQuantity()));
            dto.setSalesLast30Days(String.valueOf(ebayResp.getSoldQuantity()));
            dto.setUpcCode(ebayResp.getGtin());

            // 图片信息处理
            if (StringUtils.hasText(ebayResp.getImageUrl())) {
                dto.setProductMainImageUrls(ebayResp.getImageUrl());
            }
            if (StringUtils.hasText(ebayResp.getThumbnailImageUrls())) {
                dto.setProductSpecColorUrl(ebayResp.getThumbnailImageUrls());
            }

            // 原价和折扣处理
            if (StringUtils.hasText(ebayResp.getOriginalPrice())) {
                try {
                    dto.setListPrice(new BigDecimal(ebayResp.getOriginalPrice()));
                } catch (NumberFormatException e) {
                    log.warn("原价转换失败: {}", ebayResp.getOriginalPrice());
                }
            }
            if (StringUtils.hasText(ebayResp.getDiscountAmount())) {
                try {
                    dto.setDiscount(new BigDecimal(ebayResp.getDiscountAmount()));
                } catch (NumberFormatException e) {
                    log.warn("折扣金额转换失败: {}", ebayResp.getDiscountAmount());
                }
            }

            // 卖家评分转换
            if (StringUtils.hasText(ebayResp.getSellerFeedbackPercentage())) {
                try {
                    // 将百分比转换为5分制评分 (98.5% -> 4.925分)
                    BigDecimal percentage = new BigDecimal(ebayResp.getSellerFeedbackPercentage());
                    BigDecimal rating = percentage.divide(new BigDecimal("20"), 2, BigDecimal.ROUND_HALF_UP);
                    dto.setMerchantRating(rating);
                } catch (NumberFormatException e) {
                    log.warn("卖家评分转换失败: {}", ebayResp.getSellerFeedbackPercentage());
                }
            }

            // 评论信息转换
            if (StringUtils.hasText(String.valueOf(ebayResp.getReviewCount()))) {
                try {
                    dto.setReviewNumber(ebayResp.getReviewCount());
                } catch (NumberFormatException e) {
                    log.warn("评论数量转换失败: {}", ebayResp.getReviewCount());
                }
            }
            if (StringUtils.hasText(ebayResp.getAverageRating())) {
                try {
                    dto.setReviewScore(new BigDecimal(ebayResp.getAverageRating()));
                } catch (NumberFormatException e) {
                    log.warn("平均评分转换失败: {}", ebayResp.getAverageRating());
                }
            }

            // 分类信息处理
            if (StringUtils.hasText(ebayResp.getCategoryPath())) {
                String[] categories = ebayResp.getCategoryPath().split("\\|");
                if (categories.length >= 1) {
                    dto.setCategoryLevel1(categories[0].trim());
                }
                if (categories.length >= 2) {
                    dto.setCategoryLevel2(categories[1].trim());
                }
                if (categories.length >= 3) {
                    dto.setCategoryLevel3(categories[2].trim());
                }
            }

            // 技术规格字段映射
            dto.setRamMemoryInstalledSize(ebayResp.getRam());
            
            // 网络技术映射
            if (StringUtils.hasText(ebayResp.getNetwork()) || StringUtils.hasText(ebayResp.getConnectivity())) {
                String cellularTech = "";
                if (StringUtils.hasText(ebayResp.getNetwork())) {
                    cellularTech = ebayResp.getNetwork();
                }
                if (StringUtils.hasText(ebayResp.getConnectivity())) {
                    if (StringUtils.hasText(cellularTech)) {
                        cellularTech += ", " + ebayResp.getConnectivity();
                    } else {
                        cellularTech = ebayResp.getConnectivity();
                    }
                }
                dto.setCellularTechnology(cellularTech);
            }

            // SIM卡槽信息
            dto.setSimCardSlotCount(ebayResp.getSimCardSlot());

            // 退货政策
            if (StringUtils.hasText(String.valueOf(ebayResp.getReturnPeriodValue())) && StringUtils.hasText(ebayResp.getReturnPeriodUnit())) {
                dto.setReturnPolicy(ebayResp.getReturnPeriodValue() + " " + ebayResp.getReturnPeriodUnit());
            }

            // 支付方式
            dto.setInstallPayment(ebayResp.getPaymentMethods());

            // 设置必填字段的默认值（如果为空）
            setDefaultValuesForRequiredFields(dto, ebayResp);

            log.info("成功转换 eBay 商品数据: itemId={}, title={}", ebayResp.getItemId(), ebayResp.getTitle());

        } catch (Exception e) {
            log.error("转换 eBay 商品数据时发生错误: itemId={}", ebayResp.getItemId(), e);
            throw new RuntimeException("eBay 商品数据转换失败", e);
        }

        return dto;
    }

    /**
     * 为必填字段设置默认值
     */
    private void setDefaultValuesForRequiredFields(ProductDataDTO dto, EbayItemDetailRespVO ebayResp) {
        // 商品规格 - 必填
        if (!StringUtils.hasText(dto.getModel())) {
            dto.setModel(StringUtils.hasText(ebayResp.getModelNumber()) ? ebayResp.getModelNumber() : "Unknown Model");
        }

        // 商品颜色 - 必填
        if (!StringUtils.hasText(dto.getColor())) {
            dto.setColor(StringUtils.hasText(ebayResp.getColour()) ? ebayResp.getColour() : "Unknown Color");
        }

        // 商品存储 - 必填
        if (!StringUtils.hasText(dto.getStorage())) {
            dto.setStorage(StringUtils.hasText(ebayResp.getStorageCapacity()) ? ebayResp.getStorageCapacity() : "Unknown Storage");
        }

        // 商品服务商 - 必填
        dto.setServiceProvider(StrUtil.isEmpty(ebayResp.getSellerUsername()) ? "eBay" : ebayResp.getSellerUsername());

        // 平台SPU ID - 必填
        if (!StringUtils.hasText(dto.getPlatformSpuId())) {
            dto.setPlatformSpuId(ebayResp.getItemId());
        }

        // 平台SKU ID - 必填
        if (!StringUtils.hasText(dto.getPlatformSkuId())) {
            dto.setPlatformSkuId(StringUtils.hasText(ebayResp.getLegacyItemId()) ? 
                    ebayResp.getLegacyItemId() : ebayResp.getItemId());
        }
    }

    /**
     * 批量转换 eBay 商品数据
     *
     * @param ebayRespList eBay 商品详情响应列表
     * @return ProductDataDTO 列表
     */
    public List<ProductDataDTO> convertToProductDataDTOList(List<EbayItemDetailRespVO> ebayRespList) {
        if (ebayRespList == null || ebayRespList.isEmpty()) {
            return new ArrayList<>();
        }

        List<ProductDataDTO> dtoList = new ArrayList<>();
        for (EbayItemDetailRespVO ebayResp : ebayRespList) {
            try {
                ProductDataDTO dto = convertToProductDataDTO(ebayResp);
                if (dto != null) {
                    dtoList.add(dto);
                }
            } catch (Exception e) {
                log.error("批量转换时跳过错误数据: itemId={}", ebayResp.getItemId(), e);
            }
        }

        log.info("批量转换完成: 输入{}条，成功转换{}条", ebayRespList.size(), dtoList.size());
        return dtoList;
    }

    /**
     * 将 EbaySearchResponse.ItemSummary 转换为 ProductDataDTO
     * 用于搜索结果的数据同步
     *
     * @param itemSummary eBay 搜索结果商品摘要
     * @return ProductDataDTO
     */
    public ProductDataDTO convertSearchItemToProductDataDTO(EbaySearchResponse.ItemSummary itemSummary) {
        if (itemSummary == null) {
            return null;
        }

        ProductDataDTO dto = new ProductDataDTO();

        try {
            // 系统字段
            dto.setForceUpdate(0);  // 默认不强制更新
            dto.setSourcePlatform(ProductPlatformEnum.EBAY);  // 来源平台
            dto.setDataChannel(DataChannelEnum.API);  // {@link ai.pricefox.mallfox.enums.DataChannelEnum#API} API数据

            // 基础字段映射
            dto.setTitle(itemSummary.getTitle());
            dto.setCondition(itemSummary.getCondition());

            // 价格处理
            if (itemSummary.getPrice() != null) {
                try {
                    dto.setPrice(new BigDecimal(itemSummary.getPrice().getValue()));
                } catch (Exception e) {
                    log.warn("价格转换失败: {}", itemSummary.getPrice().getValue());
                }
            }

            // 营销价格处理
            if (itemSummary.getMarketingPrice() != null &&
                itemSummary.getMarketingPrice().getOriginalPrice() != null) {
                try {
                    dto.setListPrice(new BigDecimal(itemSummary.getMarketingPrice().getOriginalPrice().getValue()));
                } catch (Exception e) {
                    log.warn("原价转换失败: {}", itemSummary.getMarketingPrice().getOriginalPrice().getValue());
                }
            }

            // 卖家信息
            if (itemSummary.getSeller() != null) {
                dto.setSeller(itemSummary.getSeller().getUsername());

                // 卖家评分转换 (百分比转5分制)
                if (StringUtils.hasText(itemSummary.getSeller().getFeedbackPercentage())) {
                    try {
                        double percentage = Double.parseDouble(itemSummary.getSeller().getFeedbackPercentage());
                        dto.setMerchantRating(new BigDecimal(percentage / 20.0)); // 100% = 5分
                    } catch (Exception e) {
                        log.warn("卖家评分转换失败: {}", itemSummary.getSeller().getFeedbackPercentage());
                    }
                }
            }

            // 商品链接
            dto.setItemUrl(itemSummary.getItemWebUrl());

            // 图片处理
            if (itemSummary.getImage() != null) {
                dto.setProductMainImageUrls(itemSummary.getImage().getImageUrl());
            }

            // 平台ID映射
            dto.setPlatformSpuId(StringUtils.hasText(itemSummary.getLegacyItemId()) ?
                    itemSummary.getLegacyItemId() : itemSummary.getItemId());
            dto.setPlatformSkuId(itemSummary.getItemId());

        } catch (Exception e) {
            log.error("转换搜索商品数据失败，itemId: {}", itemSummary.getItemId(), e);
            return null;
        }

        return dto;
    }
}

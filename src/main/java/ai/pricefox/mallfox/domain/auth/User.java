package ai.pricefox.mallfox.domain.auth;

import com.baomidou.mybatisplus.annotation.*;

import java.time.LocalDateTime;
import java.util.Date;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 用户表
 * @TableName user
 */
@TableName(value ="user")
@Data
public class User {
    /**
     * 用户ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户名
     */
    @TableField(condition = SqlCondition.LIKE)
    private String username;

    /**
     * 昵称
     */
    private String nickname;

    /**
     * 性别
     */
    private Integer sex;

    /**
     * 密码(加密存储)
     */
    private String password;

    /**
     * 手机号
     */
    @TableField(condition = SqlCondition.LIKE)
    private String phone;

    /**
     * 邮箱
     */
    @TableField(condition = SqlCondition.LIKE)
    private String email;

    /**
     * 头像URL
     */
    private String avatar;

    /**
     * 性别(0:未知 1:男 2:女)
     */
    private Integer gender;

    /**
     * 生日
     */
    private LocalDateTime birthday;

    /**
     * 状态(0:禁用 1:正常)
     */
    private Integer status;

    /**
     * 最后登录时间
     */
    private LocalDateTime lastLoginTime;

    /**
     * 
     */
    private LocalDateTime createTime;

    /**
     *
     */
    private LocalDateTime updateTime;

    /**
     * 访问令牌
     */
    private String accessToken;

    /**
     * 刷新令牌
     */
    private String refreshToken;

    /**
     * 令牌过期时间
     */
    private LocalDateTime tokenExpireTime;

    /**
     * 刷新令牌过期时间
     */
    private LocalDateTime refreshTokenExpireTime;
}
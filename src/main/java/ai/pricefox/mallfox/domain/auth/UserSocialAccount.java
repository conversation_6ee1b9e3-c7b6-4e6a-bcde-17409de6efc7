package ai.pricefox.mallfox.domain.auth;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户社交账号关联表
 * @TableName user_social_account
 */
@TableName(value ="user_social_account")
@Data
public class UserSocialAccount {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 社交平台类型（1:Google, 2:Facebook, 3:Twitter, 4:LinkedIn, 5:GitHub, 6:Apple, 7:Microsoft, 8:Amazon）
     */
    private Integer socialType;

    /**
     * 社交平台用户唯一标识
     */
    private String socialId;

    /**
     * 社交平台用户名或昵称
     */
    private String socialName;

    /**
     * 社交平台用户头像
     */
    private String socialAvatar;

    /**
     * 社交平台用户邮箱
     */
    private String socialEmail;

    /**
     * 社交平台访问令牌
     */
    private String accessToken;

    /**
     * 令牌过期时间
     */
    private LocalDateTime tokenExpireTime;

    /**
     * 刷新令牌
     */
    private String refreshToken;

    /**
     * 额外数据（JSON格式）
     */
    private String extraData;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
} 
package ai.pricefox.mallfox.domain.auth;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户商品收藏表
 * @TableName wishlist_item
 */
@TableName(value = "wishlist_item")
@Data
public class WishlistItem {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 收藏的商品SKU ID
     */
    private String skuId;

    /**
     * 收藏时间
     */
    private LocalDateTime createTime;
}

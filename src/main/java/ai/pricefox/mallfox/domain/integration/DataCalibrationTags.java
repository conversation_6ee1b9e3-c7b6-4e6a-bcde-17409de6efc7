package ai.pricefox.mallfox.domain.integration;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2025/6/25
 * @desc 数据校准标记表
 */
@TableName(value = "data_calibration_tags")
@Data
public class DataCalibrationTags {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 被标记的表名 (product_data_offers, product_data_simplify)
     */
    private String targetTable;

    /**
     * 被标记的记录ID (offers.id 或 simplify.id)
     */
    private Long targetId;

    /**
     * 被标记的字段名 (title, price)
     */
    private String fieldName;

    /**
     * 标记状态 (1: 标记缺失, 2: 标记错误)
     */
    private Integer tagStatus;

    /**
     * 操作员ID
     */
    private Long operatorId;

    /**
     * 备注信息
     */
    private String remark;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}

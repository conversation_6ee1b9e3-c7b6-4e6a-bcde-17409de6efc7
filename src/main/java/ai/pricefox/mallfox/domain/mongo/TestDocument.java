package ai.pricefox.mallfox.domain.mongo;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.time.LocalDateTime;

/**
 * MongoDB 测试文档实体类
 * 用于测试MongoDB的基本CRUD操作
 *
 * <AUTHOR>
 * @date 2025-07-25
 */
@Data
@Document(collection = "test_documents")
public class TestDocument {

    /**
     * 主键ID，MongoDB会自动生成ObjectId
     */
    @Id
    private String id;

    /**
     * 文档名称
     */
    @Field("name")
    private String name;

    /**
     * 文档描述
     */
    @Field("description")
    private String description;

    /**
     * 文档状态 (ACTIVE, INACTIVE, DELETED)
     */
    @Field("status")
    private String status;

    /**
     * 文档标签，用逗号分隔
     */
    @Field("tags")
    private String tags;

    /**
     * 数值字段，用于测试数值查询
     */
    @Field("value")
    private Double value;

    /**
     * 创建时间
     */
    @Field("create_time")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @Field("update_time")
    private LocalDateTime updateTime;

    /**
     * 创建者
     */
    @Field("creator")
    private String creator;

    /**
     * 备注信息
     */
    @Field("remarks")
    private String remarks;
}
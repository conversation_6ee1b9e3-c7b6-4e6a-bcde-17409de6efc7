package ai.pricefox.mallfox.domain.product;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 渠道商品报价表 (高频)
 * @TableName channel_offers
 */
@TableName(value = "channel_offers")
@Data
public class ChannelOffers {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * skuCode
     */
    private String skuCode;

    /**
     * 平台编码
     */
    private String platformCode;

    /**
     * 商品在渠道平台的唯一ID (用于反查)
     */
    private String platformSkuId;

    /**
     * 当前售价
     */
    private BigDecimal price;

    /**
     * 划线价
     */
    private BigDecimal listPrice;

    /**
     * 库存 (In Stock,10)
     */
    private String inventory;

    /**
     * 卖家名称
     */
    private String sellerName;

    /**
     * 发货时间
     */
    private String shippingTime;

    /**
     * 卖家评分
     */
    private BigDecimal merchantRating;

    /**
     * 引导用户购买的商品链接
     */
    private String itemUrl;

    /**
     * 近30天销量
     */
    private String salesLast30Days;

    /**
     * 最后一次采集/更新时间
     */
    private LocalDateTime lastRecordTime;

    /**
     * 记录更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 卖家类型 (platform=自营, thirdParty=第三方)
     */
     private String sellerType;

    /**
     * 退货政策
     */
    private String returnPolicy;

    /**
     * 分期信息
     */
    private String installmentInfo;

    /**
     * 商品成色 (New, Open Box, Refurbished)
     */
     private String condition;

    /**
     * 卖家评分总数
     */
     private Integer merchantRatingCount;
}

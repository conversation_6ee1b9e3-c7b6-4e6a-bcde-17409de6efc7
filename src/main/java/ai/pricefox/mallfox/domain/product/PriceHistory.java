package ai.pricefox.mallfox.domain.product;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

@Data
@TableName("price_history")
public class PriceHistory {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 关联到product_sku表的自建SKU ID
     */
    private String skuId;

    /**
     * 记录日期 (快照日期)
     */
    private LocalDate recordDate;

    /**
     * 当天该SKU在全渠道的最低价
     */
    private BigDecimal lowestPrice;

    /**
     * 当天该SKU在全渠道的平均价
     */
    private BigDecimal averagePrice;

    /**
     * 当天用于计算的有效报价数量
     */
    private Integer offerCount;
}

package ai.pricefox.mallfox.domain.product;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

import lombok.Data;

/**
 * 商品SPU表
 * @TableName product
 */
@TableName(value ="product")
@Data
public class Product {
    /**
     * 商品ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 所属分类ID
     */
    private Long categoryId;

    /**
     * 品牌ID
     */
    private Long brandId;

    /**
     * 商品名称
     */
    @TableField(condition = SqlCondition.LIKE)
    private String name;

    /**
     * 副标题(促销信息)
     */
    private String subTitle;

    /**
     * 主图URL
     */
    private String mainImage;

    /**
     * 子图URL(JSON数组)
     */
    private JSONObject subImages;

    /**
     * 商品详情(富文本)
     */
    private String description;

    /**
     * 参考价(展示用)
     */
    private BigDecimal price;

    /**
     * 状态(0:下架 1:上架)
     */
    private Integer status;

    /**
     * 销量
     */
    private Integer sales;

    /**
     * 评价数
     */
    private Integer reviewCount;

    /**
     * 
     */
    private LocalDateTime createTime;

    /**
     * 
     */
    private LocalDateTime updateTime;

    @TableField(exist = false)
    private List<ProductSku> productSkuList;


}
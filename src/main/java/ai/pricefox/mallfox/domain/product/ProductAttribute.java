package ai.pricefox.mallfox.domain.product;

import com.baomidou.mybatisplus.annotation.*;

import java.time.LocalDateTime;

import lombok.Data;

/**
 * 商品属性表
 * @TableName product_attribute
 */
@TableName(value ="product_attribute")
@Data
public class ProductAttribute {
    /**
     * 属性ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 所属分类ID
     */
    private Long categoryId;

    /**
     * 属性名称
     */
    @TableField(condition = SqlCondition.LIKE)
    private String name;

    /**
     * 输入类型(1:手动输入 2:单选 3:多选)
     */
    private Integer inputType;

    /**
     * 可选值列表(JSON数组)
     */
//    private String values;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 是否用于筛选(0:否 1:是)
     */
    private Integer isFilter;

    /**
     * 是否必填(0:否 1:是)
     */
    private Integer isRequired;

    /**
     * 
     */
    private LocalDateTime createTime;

    /**
     * 
     */
    private LocalDateTime updateTime;



}
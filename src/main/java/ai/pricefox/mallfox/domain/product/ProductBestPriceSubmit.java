package ai.pricefox.mallfox.domain.product;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 商品最佳价格提交表
 * @TableName product_best_price_submit
 */
@TableName(value = "product_best_price_submit")
@Data
public class ProductBestPriceSubmit {
    
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 关联的自建SKU Code
     */
    private String skuCode;
    
    /**
     * 最低价格
     */
    private BigDecimal price;
    
    /**
     * 商品链接
     */
    private String itemUrl;
    
    /**
     * 提交商品数据
     */
    private String optional;
    
    /**
     * 用户邮箱
     */
    private String userEmail;
    
    /**
     * 提交用户id
     */
    private Long submitUser;
    
    /**
     * 提交时间
     */
    private LocalDateTime submitTime;
}
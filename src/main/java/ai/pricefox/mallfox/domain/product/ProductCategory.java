package ai.pricefox.mallfox.domain.product;

import com.baomidou.mybatisplus.annotation.*;

import java.time.LocalDateTime;

import lombok.Data;

/**
 * 商品分类表
 * @TableName product_category
 */
@TableName(value ="product_category")
@Data
public class ProductCategory {
    /**
     * 分类ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 父分类ID(0:一级分类)
     */
    private Long parentId;

    /**
     * 分类名称
     */
    @TableField(condition = SqlCondition.LIKE)
    private String name;

    /**
     * 层级(1:一级 2:二级 3:三级)
     */
    private Integer level;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 图标URL
     */
    private String icon;

    /**
     * 是否显示(0:隐藏 1:显示)
     */
    private Integer isDisplay;

    /**
     * 
     */
    private LocalDateTime createTime;

    /**
     * 
     */
    private LocalDateTime updateTime;

}
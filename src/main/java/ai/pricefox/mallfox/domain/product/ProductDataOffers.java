package ai.pricefox.mallfox.domain.product;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 多平台商品高频表
 * @TableName product_data_offers
 */
@TableName(value ="product_data_offers")
@Data
public class ProductDataOffers {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 关联的自建SKU ID
     */
    private String skuId;

    /**
     * 源商品ID
     */
    private String spuId;

    /**
     * 数据来源平台
     */
    private String sourcePlatform;

    /**
     * 来源平台的SPU/ID/ASIN
     */
    private String platformSpuId;

    /**
     * 来源平台的SKU组合ID
     */
    private String platformSkuId;

    /**
     * 商品链接
     */
    private String itemUrl;

    /**
     * 商品零售价
     */
    private BigDecimal listPrice;

    /**
     * 价格
     */
    private BigDecimal price;

    /**
     * 折扣
     */
    private BigDecimal discount;

    /**
     * 库存状态
     */
    private String inventory;

    /**
     * 库存更新时间
     */
    private LocalDateTime inventoryUpdateTime;

    /**
     * 近30天销量
     */
    private String salesLast30Days;

    /**
     * 卖家名称
     */
    private String seller;

    /**
     * 卖家评分
     */
    private BigDecimal merchantRating;

    /**
     * 品牌
     */
    private String brand;

    /**
     * 商品标题
     */
    private String title;

    /**
     * 产品系列
     */
    private String series;

    /**
     * UPC编码
     */
    private String upcCode;

    /**
     * 服务商
     */
    private String serviceProvider;

    /**
     * 商品状态 全新 开盒 重包装
     */
    private String conditionNew;

    /**
     * 一级类目
     */
    private String categoryLevel1;

    /**
     * 二级类目
     */
    private String categoryLevel2;

    /**
     * 三级类目
     */
    private String categoryLevel3;

    /**
     * 商品颜色图片链接
     */
    private String colorImageUrl;


    /**
     * 数据渠道：{@link ai.pricefox.mallfox.enums.DataChannelEnum#CRAWLER} 爬虫  {@link ai.pricefox.mallfox.enums.DataChannelEnum#API} API
     */
    private Integer dataChannel;

    /**
     * 价格更新时间
     */
    private String priceUpdateTime;

    /**
     * 记录创建时间
     */
    private LocalDateTime createTime;

    /**
     * 记录更新时间
     */
    private LocalDateTime updateTime;


    /**
     * 判断字段是否来自product_data_offers表
     * 根据selectSpuList SQL中的字段映射
     */
    public static boolean isOffersTableField(String fieldName) {
        // 来自product_data_offers表的字段（以o.开头的字段）
        return "series".equals(fieldName) ||
                "upcCode".equals(fieldName) ||
                "itemUrl".equals(fieldName) ||
                "title".equals(fieldName) ||
                "brand".equals(fieldName) ||
                "price".equals(fieldName) ||
                "listPrice".equals(fieldName) ||
                "discount".equals(fieldName) ||
                "inventory".equals(fieldName) ||
                "salesLast30Days".equals(fieldName) ||
                "seller".equals(fieldName) ||
                "merchantRating".equals(fieldName) ||
                "categoryLevel1".equals(fieldName) ||
                "categoryLevel2".equals(fieldName) ||
                "categoryLevel3".equals(fieldName) ||
                "offerUpdateTime".equals(fieldName) ||
                "priceUpdateTime".equals(fieldName);
    }
}
package ai.pricefox.mallfox.domain.product;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 多平台商品评论数据表
 * @TableName product_data_reviews
 */
@TableName(value ="product_data_reviews")
@Data
public class ProductDataReviews {
    /**
     * 自增主键ID, 唯一标识每条评论记录
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 自建的唯一评论ID (如: PR0000000001)
     */
    private String reviewId;

    /**
     * 来源平台评论ID
     */
    private String platformReviewId;

    /**
     * 记录创建时间
     */
    private LocalDateTime createTime;

    /**
     * 记录更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 关联到商品表的SKU ID
     */
    private String skuId;

    /**
     * 关联到商品表的SPU ID
     */
    private String spuId;

    /**
     * 评论来源平台 (例如: amazon, bestbuy)
     */
    private String sourcePlatform;

    /**
     * 来源平台的SPU/ID/ASIN
     */
    private String platformSpuId;

    /**
     * 来源平台的SKU组合ID
     */
    private String platformSkuId;

    /**
     * 评论星级 (1-5)
     */
    private Integer reviewScore;

    /**
     * 评论用户名称
     */
    private String reviewUserName;

    /**
     * 评论标题
     */
    private String reviewTitle;

    /**
     * 评论正文内容
     */
    private String reviewContent;

    /**
     * 评论发布时间
     */
    private LocalDateTime reviewTime;

    /**
     * 用户认为有帮助
     */
    private Integer isHelpfulOrNot;

    /**
     * 评论附带的图片URL列表 (多个用逗号分隔)
     */
    private String reviewImageUrl;
}
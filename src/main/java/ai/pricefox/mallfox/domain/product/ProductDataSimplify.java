package ai.pricefox.mallfox.domain.product;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 商品低频数据表
 * @TableName product_data_simplify
 */
@TableName(value ="product_data_simplify")
@Data
public class ProductDataSimplify {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 数据的主要来源平台
     */
    private String sourcePlatform;

    /**
     * 自建SPU ID
     */
    private String spuId;

    /**
     * 自建SKU ID
     */
    private String skuId;

    /**
     * 来源平台的SPU/ID/ASIN
     */
    private String platformSpuId;

    /**
     * 来源平台的SKU编码
     */
    private String platformSkuId;

    /**
     * 商品型号
     */
    private String model;

    /**
     * 商品型号备份
     */
    private String modelBack;

    /**
     * 型号年份
     */
    private String modelYear;

    /**
     * 颜色
     */
    private String color;

    /**
     * 内存/存储容量
     */
    private String storage;

    /**
     * 商品主图URL列表逗号分隔 (url1", "url2")
     */
    private String productMainImageUrls;

    /**
     * 规格颜色图片URL
     */
    private String productSpecColorUrl;

    /**
     * 已安装内存RAM大小
     */
    private String ramMemoryInstalledSize;

    /**
     * 操作系统
     */
    private String operatingSystem;

    /**
     * 处理器
     */
    private String processor;

    /**
     * 蜂窝技术
     */
    private String cellularTechnology;

    /**
     * 屏幕尺寸
     */
    private String screenSize;

    /**
     * 分辨率
     */
    private String resolution;

    /**
     * 刷新率
     */
    private String refreshRate;

    /**
     * 显示类型
     */
    private String displayType;

    /**
     * 电池电量
     */
    private String batteryPower;

    /**
     * 平均通话时长
     */
    private String averageTalkTime;

    /**
     * 电池充电时长
     */
    private String batteryChargeTime;

    /**
     * 前置摄像头分辨率
     */
    private String frontPhotoSensorResolution;

    /**
     * 后置摄像头分辨率
     */
    private String rearFacingCameraPhotoSensorResolution;

    /**
     * 后置摄像头数量
     */
    private Integer numberOfRearFacingCameras;

    /**
     * 有效视频分辨率
     */
    private String effectiveVideoResolution;

    /**
     * 视频捕捉帧率
     */
    private String videoCaptureResolution;

    /**
     * SIM卡卡槽类型
     */
    private String simCardSlotCount;

    /**
     * 连接器类型
     */
    private String connectorType;

    /**
     * 防水性能
     */
    private String waterResistance;

    /**
     * 手机尺寸
     */
    private String dimensions;

    /**
     * 商品重量
     */
    private String itemWeight;

    /**
     * 生物识别安全技术
     */
    private String biometricSecurityFeature;

    /**
     * 支持的卫星导航系统
     */
    private String supportedSatelliteNavigationSystem;

    /**
     * 特征/功能列表
     */
    private String features;

    /**
     * 退换货政策
     */
    private String returnPolicy;

    /**
     * 付款方式 如信用卡,分期
     */
    private String paymentInstallment;

    /**
     * 分期信息
     */
    private String installPayment;

    /**
     * 保修说明
     */
    private String warrantyDescription;

    /**
     * 评论数量
     */
    private Integer reviewNumber;

    /**
     * 评分
     */
    private BigDecimal reviewScore;

    /**
     * 商品评价五点图分布 ({"5_star": 120, "1_star": 5})
     */
    private String reviewRatingDistribution;

    /**
     * 不同维度评分 ({"camera": 4.5, "battery": 4.2})
     */
    private String reviewDimensionalRatings;

    /**
     * 评价-概览-优缺点 ({"pros": ["{"long baettery:200"}"], "cons": ["{"heavy to hold:102"}"]})
     */
    private String reviewOverviewProsCons;

    /**
     * 评价-1-5星级优缺点
     */
    private String reviewProsConsByStar;

    /**
     * 服务提供商
     */
    private String serviceProvider;

    /**
     * 商品状态
     */
    private String conditionNew;

    /**
     * 数据渠道：{@link ai.pricefox.mallfox.enums.DataChannelEnum#CRAWLER} 爬虫  {@link ai.pricefox.mallfox.enums.DataChannelEnum#API} API
     */
    private Integer dataChannel;

    /**
     * 记录创建时间
     */
    private LocalDateTime createTime;

    /**
     * 记录更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 标记
     */
    private Integer mark;

    /**
     * 发货时间
     */
    private String shippingTime;


    /**
     * 判断字段是否来自product_data_simplify表
     * 根据selectSpuList SQL中的字段映射
     */
    public static boolean isSimplifyTableField(String fieldName) {
        // 来自product_data_simplify表的字段（以s.开头的字段）
        return "skuId".equals(fieldName) ||
                "model".equals(fieldName) ||
                "modelYear".equals(fieldName) ||
                "color".equals(fieldName) ||
                "storage".equals(fieldName) ||
                "serviceProvider".equals(fieldName) ||
                "conditionNew".equals(fieldName) ||
                "sourcePlatform".equals(fieldName) ||
                "platformSpuId".equals(fieldName) ||
                "platformSkuId".equals(fieldName) ||
                "productMainImageUrls".equals(fieldName) ||
                "productSpecColorUrl".equals(fieldName) ||
                "ramMemoryInstalledSize".equals(fieldName) ||
                "operatingSystem".equals(fieldName) ||
                "processor".equals(fieldName) ||
                "cellularTechnology".equals(fieldName) ||
                "screenSize".equals(fieldName) ||
                "resolution".equals(fieldName) ||
                "refreshRate".equals(fieldName) ||
                "displayType".equals(fieldName) ||
                "batteryPower".equals(fieldName) ||
                "averageTalkTime".equals(fieldName) ||
                "batteryChargeTime".equals(fieldName) ||
                "frontPhotoSensorResolution".equals(fieldName) ||
                "rearFacingCameraPhotoSensorResolution".equals(fieldName) ||
                "numberOfRearFacingCameras".equals(fieldName) ||
                "effectiveVideoResolution".equals(fieldName) ||
                "videoCaptureResolution".equals(fieldName) ||
                "simCardSlotCount".equals(fieldName) ||
                "connectorType".equals(fieldName) ||
                "waterResistance".equals(fieldName) ||
                "dimensions".equals(fieldName) ||
                "itemWeight".equals(fieldName) ||
                "biometricSecurityFeature".equals(fieldName) ||
                "supportedSatelliteNavigationSystem".equals(fieldName) ||
                "features".equals(fieldName) ||
                "returnPolicy".equals(fieldName) ||
                "paymentInstallment".equals(fieldName) ||
                "installPayment".equals(fieldName) ||
                "warrantyDescription".equals(fieldName) ||
                "reviewNumber".equals(fieldName) ||
                "reviewScore".equals(fieldName) ||
                "reviewRatingDistribution".equals(fieldName) ||
                "reviewDimensionalRatings".equals(fieldName) ||
                "reviewOverviewProsCons".equals(fieldName) ||
                "reviewProsConsByStar".equals(fieldName) ||
                "mark".equals(fieldName) ||
                "simplifyCreateTime".equals(fieldName) ||
                "shippingTime".equals(fieldName);
    }
}
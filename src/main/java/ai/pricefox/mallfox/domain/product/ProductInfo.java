package ai.pricefox.mallfox.domain.product;


import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 核心产品表 (SPU) 实体类
 * @TableName product_info
 */
@TableName(value ="product_info")
@Data
public class ProductInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键ID
     */
    private Long id;

    /**
     * 自建的唯一SPU ID (如: PP00000001)
     */
    private String spuId;

    /**
     * 关联到品牌表的ID
     */
    private Long brandId;

    /**
     * 关联到最末级分类表的ID
     */
    private Long categoryId;

    /**
     * 产品通用名称/标题 (如: iPhone 15 Pro)
     */
    private String name;

    /**
     * 商品主图URL
     */
    private String mainImageUrl;

    /**
     * 产品通用描述 (富文本)
     */
    private String description;

    /**
     * 此SPU记录的主要或首次创建来源
     */
    private String primarySource;

    /**
     * SPU状态 (0:草稿/待上架, 1:已上架, 2:已下架)
     */
    private Integer status;

    /**
     * 官方发布日期
     */
    private LocalDate releaseDate;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

}

package ai.pricefox.mallfox.domain.product;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;

/**
 * 商品库存历史记录表
 *
 * <AUTHOR>
 * @TableName product_inventory_history
 * @since 2025年07月23日
 */
@TableName(value = "product_inventory_history")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductInventoryHistory {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 关联product_data_offers表的ID
     */
    private Long offerId;

    /**
     * 关联的SKU ID
     */
    private String skuId;

    /**
     * 关联的SPU ID
     */
    private String spuId;

    /**
     * 数据来源平台
     */
    private String sourcePlatform;

    /**
     * 来源平台的商品ID
     */
    private String platformSpuId;

    /**
     * 来源平台的SKU ID
     */
    private String platformSkuId;

    /**
     * 变更前库存
     */
    private String oldQuantity;

    /**
     * 变更后库存
     */
    private String newQuantity;

    /**
     * 变化类型: 1-新增, 2-更新, 3-删除
     */
    private Integer changeType;

    /**
     * 数据渠道：1-爬虫 2-API
     */
    private Integer dataChannel;

    /**
     * 触发来源: batch_process/manual_update/scheduled_job
     */
    private String triggerSource;

    /**
     * 库存更新时间(来源数据时间)
     */
    private LocalDateTime inventoryUpdateTime;

    /**
     * 记录创建时间
     */
    private LocalDateTime recordTime;

}
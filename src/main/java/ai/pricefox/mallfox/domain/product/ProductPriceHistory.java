package ai.pricefox.mallfox.domain.product;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 商品价格变化历史记录表
 * @TableName product_price_history
 * <AUTHOR>
 * @since 2025-07-12
 */
@TableName(value = "product_price_history")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductPriceHistory {
    
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 关联product_data_offers表的ID
     */
    private Long offerId;
    
    /**
     * 关联的SKU ID
     */
    private String skuId;
    
    /**
     * 关联的SPU ID
     */
    private String spuId;
    
    /**
     * 数据来源平台
     */
    private String sourcePlatform;
    
    /**
     * 来源平台的商品ID
     */
    private String platformSpuId;
    
    /**
     * 来源平台的SKU ID
     */
    private String platformSkuId;
    
    /**
     * 变更前价格
     */
    private BigDecimal oldPrice;
    
    /**
     * 变更后价格
     */
    private BigDecimal newPrice;
    
    /**
     * 变更前零售价
     */
    private BigDecimal oldListPrice;
    
    /**
     * 变更后零售价
     */
    private BigDecimal newListPrice;
    
    /**
     * 变更前折扣
     */
    private BigDecimal oldDiscount;
    
    /**
     * 变更后折扣
     */
    private BigDecimal newDiscount;
    
    /**
     * 价格变化金额(new-old)
     */
    private BigDecimal priceChangeAmount;
    
    /**
     * 价格变化百分比
     */
    private BigDecimal priceChangePercent;
    
    /**
     * 变化类型: 1-新增, 2-更新, 3-删除
     */
    private Integer changeType;
    
    /**
     * 数据渠道：1-爬虫 2-API
     */
    private Integer dataChannel;
    
    /**
     * 触发来源: batch_process/manual_update/scheduled_job
     */
    private String triggerSource;
    
    /**
     * 价格更新时间(来源数据时间)
     */
    private String priceUpdateTime;
    
    /**
     * 记录创建时间
     */
    private LocalDateTime recordTime;
    
    /**
     * 变化类型枚举
     */
    public enum ChangeType {
        CREATE(1, "新增"),
        UPDATE(2, "更新"), 
        DELETE(3, "删除");
        
        private final Integer code;
        private final String desc;
        
        ChangeType(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }
        
        public Integer getCode() { 
            return code; 
        }
        
        public String getDesc() { 
            return desc; 
        }
        
        public static ChangeType fromCode(Integer code) {
            if (code == null) return null;
            for (ChangeType type : values()) {
                if (type.code.equals(code)) {
                    return type;
                }
            }
            return null;
        }
    }
    
    /**
     * 触发源枚举
     */
    public enum TriggerSource {
        BATCH_PROCESS("batch_process", "批量处理"),
        MANUAL_UPDATE("manual_update", "手动更新"),
        SCHEDULED_JOB("scheduled_job", "定时任务");
        
        private final String code;
        private final String desc;
        
        TriggerSource(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }
        
        public String getCode() { 
            return code; 
        }
        
        public String getDesc() { 
            return desc; 
        }
        
        public static TriggerSource fromCode(String code) {
            if (code == null) return null;
            for (TriggerSource source : values()) {
                if (source.code.equals(code)) {
                    return source;
                }
            }
            return null;
        }
    }
}
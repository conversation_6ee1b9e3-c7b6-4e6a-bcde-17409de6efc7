package ai.pricefox.mallfox.domain.product;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * 商品SKU表 (含聚合数据) 实体类
 * @TableName product_sku
 */
@TableName(value ="product_sku", autoResultMap = true)
@Data
public class ProductSku implements Serializable {
    /**
     * 自增主键ID
     */
    private Long id;

    /**
     * 自建的唯一SKU ID
     */
    private String skuId;

    /**
     * 所属产品SPU ID
     */
    private String spuId;

    /**
     * 销售属性组合 (JSON格式)
     * mybatis-plus需要配置typeHandler来处理JSON
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> attributes;

    /**
     * SKU特定图片
     */
    private String skuImageUrl;

    /**
     * 此SKU在全渠道的最低报价 (冗余字段)
     */
    private BigDecimal lowestPrice;

    /**
     * 此SKU在全渠道的最高报价 (冗余字段)
     */
    private BigDecimal highestPrice;

    /**
     * 此SKU的有效报价渠道数量 (冗余字段)
     */
    private Integer offerCount;

    /**
     * 此SKU的聚合平均评分 (冗余字段)
     */
    private BigDecimal averageRating;

    /**
     * 此SKU的聚合总评论数 (冗余字段)
     */
    private Integer totalReviews;

    /**
     * 总库存数量 (冗余字段)
     */
    private Integer stockQuantity;

    /**
     * SKU状态 (0:草稿, 1:已上架, 2:已下架)
     */
    private Integer status;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}
//package ai.pricefox.mallfox.domain.product;
//
//import com.alibaba.fastjson.JSONObject;
//import com.baomidou.mybatisplus.annotation.IdType;
//import com.baomidou.mybatisplus.annotation.TableId;
//import com.baomidou.mybatisplus.annotation.TableName;
//import java.math.BigDecimal;
//import java.time.LocalDateTime;
//import lombok.Data;
//
///**
// * 商品SKU表
// * @TableName product_sku
// */
//@TableName(value ="product_sku")
//@Data
//public class ProductSku {
//    /**
//     * SKU ID
//     */
//    @TableId(type = IdType.AUTO)
//    private Long id;
//
//    /**
//     * 关联商品ID
//     */
//    private Long productId;
//
//    /**
//     * SKU编码(唯一)
//     */
//    private String skuCode;
//
//    /**
//     * 规格属性(JSON格式)
//     */
//    private JSONObject specs;
//
//    /**
//     * 销售价
//     */
//    private BigDecimal price;
//
//    /**
//     * 成本价
//     */
//    private BigDecimal costPrice;
//
//    /**
//     * 原价
//     */
//    private BigDecimal originalPrice;
//
//    /**
//     * 库存
//     */
//    private Integer stock;
//
//    /**
//     * 库存预警值
//     */
//    private Integer lowStock;
//
//    /**
//     * SKU主图
//     */
//    private String image;
//
//    /**
//     * 重量(kg)
//     */
//    private BigDecimal weight;
//
//    /**
//     * 体积(m³)
//     */
//    private BigDecimal volume;
//
//    /**
//     * 状态(0:禁用 1:启用)
//     */
//    private Integer status;
//
//    /**
//     *
//     */
//    private LocalDateTime createTime;
//
//    /**
//     *
//     */
//    private LocalDateTime updateTime;
//
//}
package ai.pricefox.mallfox.domain.product;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 产品表格配置实体类
 * 
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("product_table_config")
@Schema(description = "产品表格配置")
public class ProductTableConfig {

    @TableId(value = "id", type = IdType.AUTO)
    @Schema(description = "主键ID")
    private Integer id;

    @TableField("field")
    @Schema(description = "字段")
    private String field;

    @TableField("type")
    @Schema(description = "类型：1-字段 2-配置")
    private Integer type;

    @TableField("info")
    @Schema(description = "JSON信息")
    private String info;

    @TableField("weight")
    @Schema(description = "权重")
    private String weight;

    @TableField("tag")
    @Schema(description = "表格标签")
    private String tag;

    @TableField("create_time")
    @Schema(description = "创建时间")
    private LocalDateTime createTime;


    @TableField("update_time")
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    /**
     * 类型枚举
     */
    public enum TypeEnum {
        FIELD(1, "字段"),
        CONFIG(2, "配置");

        private final Integer code;
        private final String desc;

        TypeEnum(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public Integer getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }

        public static TypeEnum getByCode(Integer code) {
            if (code == null) {
                return null;
            }
            for (TypeEnum typeEnum : values()) {
                if (typeEnum.getCode().equals(code)) {
                    return typeEnum;
                }
            }
            return null;
        }
    }
}

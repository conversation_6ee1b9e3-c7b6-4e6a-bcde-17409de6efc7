package ai.pricefox.mallfox.domain.product;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @desc 商品表记录已读状态
 * @since 2025/7/18
 */
@Data
@TableName("product_table_read")
public class ProductTableRead {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 目标表名
     */
    private String targetTable;

    /**
     * 目标记录ID
     */
    private Long targetId;

    /**
     * 是否已读 0-否 1-是
     */
    private Integer isRead;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}
package ai.pricefox.mallfox.domain.product;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 标准商品信息实体类
 * 对应 standard_product 表
 * 
 * <AUTHOR>
 * @date 2025-07-22
 */
@Data
@TableName("standard_product")
public class StandardProduct {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 创建时间
     */
    @TableField("create_date")
    private LocalDateTime createDate;

    /**
     * 修改时间
     */
    @TableField("update_date")
    private LocalDateTime updateDate;

    /**
     * 创建人名称
     */
    @TableField("create_username")
    private String createUsername;

    /**
     * 更新人名称
     */
    @TableField("update_username")
    private String updateUsername;

    /**
     * spu编码,自建唯一
     */
    @TableField("spu_code")
    private String spuCode;

    /**
     * spu
     */
    @TableField("spu")
    private String spu;

    /**
     * 品牌编号，来自于standard_brand表
     */
    @TableField("brand_code")
    private String brandCode;

    /**
     * 品牌名称，来自于standard_brand表
     */
    @TableField("brand_name")
    private String brandName;

    /**
     * 品类编号，来自于standard_categroy表
     */
    @TableField("category_code")
    private String categoryCode;

    /**
     * 商品标题
     */
    @TableField("title")
    private String title;

    /**
     * 短标题
     */
    @TableField("sort_title")
    private String sortTitle;

    /**
     * 简介
     */
    @TableField("brief")
    private String brief;

    /**
     * 前缀域名
     */
    @TableField("handle")
    private String handle;

    /**
     * 描述
     */
    @TableField("description")
    private String description;

    /**
     * 商品主图链接
     */
    @TableField("master_img_url")
    private String masterImgUrl;

    /**
     * 是否单一变体 0：多变体  1：单一属性
     */
    @TableField("has_only_default_variant")
    private Boolean hasOnlyDefaultVariant;

    /**
     * 是否需要运输
     */
    @TableField("requires_shipping")
    private Boolean requiresShipping;

    /**
     * 是否需要缴税
     */
    @TableField("taxable")
    private Boolean taxable;

    /**
     * 是否跟踪库存
     */
    @TableField("inventory_tracking")
    private Boolean inventoryTracking;

    /**
     * 跟踪策略
     */
    @TableField("inventory_policy")
    private String inventoryPolicy;

    /**
     * 存货数量
     */
    @TableField("inventory_quantity")
    private Integer inventoryQuantity;

    /**
     * 是否已发布
     */
    @TableField("published")
    private Boolean published;

    /**
     * 发布时间
     */
    @TableField("published_at")
    private LocalDateTime publishedAt;

    /**
     * seo标题
     */
    @TableField("seo_title")
    private String seoTitle;

    /**
     * seo描述
     */
    @TableField("seo_description")
    private String seoDescription;

    /**
     * seo关键词
     */
    @TableField("seo_keywords")
    private String seoKeywords;

    /**
     * 是否显示虚假销售
     */
    @TableField("display_fake_sales")
    private Boolean displayFakeSales;

    /**
     * 虚假销量
     */
    @TableField("fake_sales")
    private Integer fakeSales;

    /**
     * 商品标签
     */
    @TableField("tags")
    private String tags;

    /**
     * 货源连接
     */
    @TableField("source_link")
    private String sourceLink;

    /**
     * 专辑ID
     */
    @TableField("collections_code")
    private String collectionsCode;

    /**
     * 专辑标题
     */
    @TableField("collections_title")
    private String collectionsTitle;

    /**
     * 审核状态
     */
    @TableField("examine_status")
    private String examineStatus;

    /**
     * 审核人
     */
    @TableField("examine_user")
    private String examineUser;
}
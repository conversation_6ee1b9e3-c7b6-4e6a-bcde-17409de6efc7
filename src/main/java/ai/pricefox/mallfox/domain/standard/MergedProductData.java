package ai.pricefox.mallfox.domain.standard;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

// 合并数据实体
@TableName("tripartite_merged_product_data")
@Data
public class MergedProductData {
    @TableId(type = IdType.AUTO)
    private Long id;
    private Date createDate;
    private String createUsername;
    private Date updateDate;
    private String updateUsername;
    private String skuId;
    private String platformCode;
    private String mergedData;  // JSON格式
    private String dataId;  // 关联的原始数据ID
    private String mergeStatus;  // 合并状态
}

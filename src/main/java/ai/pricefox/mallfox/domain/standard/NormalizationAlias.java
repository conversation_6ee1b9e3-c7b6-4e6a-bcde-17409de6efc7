package ai.pricefox.mallfox.domain.standard;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 归一化别名/同义词映射表
 */
@TableName(value = "normalization_alias")
@Data
public class NormalizationAlias implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 实体类型 (BRAND, COLOR, CONDITION, ...)
     */
    private String entityType;

    /**
     * 别名 (从源数据中看到的名字，需小写处理)
     */
    private String aliasName;

    /**
     * 对应的标准值
     */
    private String standardValue;

    /**
     * 此别名所属的平台 (可选，用于平台特定规则)
     */
    private String sourcePlatform;
}
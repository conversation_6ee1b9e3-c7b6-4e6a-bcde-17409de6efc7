package ai.pricefox.mallfox.domain.standard;

import ai.pricefox.mallfox.enums.ValueTypeEnum;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 归一化值库
 */
@TableName(value ="normalization_library")
@Data
public class NormalizationLibrary implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 修改时间
     */
    private Date updateDate;

    /**
     * 创建人名称
     */
    private String createUsername;

    /**
     * 更新人名称
     */
    private String updateUsername;

    /**
     * 归一编码
     */
    private String normCode;

    /**
     * 值类型
     */
    private ValueTypeEnum valueType;

    /**
     * 原始值
     */
    private String originalValue;

    /**
     * 归一化值
     */
    private String normalizedValue;

    /**
     * 等价值(逗号分隔)
     */
    private String equivalentValues;
}
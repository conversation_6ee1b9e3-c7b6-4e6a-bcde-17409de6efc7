package ai.pricefox.mallfox.domain.standard;

import ai.pricefox.mallfox.enums.ApplyToEnum;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 无差异转换规则
 */
@TableName(value ="normalization_rules")
@Data
public class NormalizationRules implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 修改时间
     */
    private Date updateDate;

    /**
     * 创建人名称
     */
    private String createUsername;

    /**
     * 更新人名称
     */
    private String updateUsername;

    /**
     * 匹配模式
     */
    private String pattern;

    /**
     * 替换内容
     */
    private String replacement;

    /**
     * 应用范围
     */
    private ApplyToEnum applyTo;
}
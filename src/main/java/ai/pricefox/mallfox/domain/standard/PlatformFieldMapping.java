package ai.pricefox.mallfox.domain.standard;

import ai.pricefox.mallfox.enums.TransformLogicEnum;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 平台字段映射表
 * 定义了第三方平台字段与标准字段的映射关系
 */
@TableName(value ="platform_field_mapping")
@Data
public class PlatformFieldMapping implements Serializable {
    private static final long serialVersionUID = 1L;
    
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 修改时间
     */
    private Date updateDate;

    /**
     * 创建人名称
     */
    private String createUsername;

    /**
     * 更新人名称
     */
    private String updateUsername;

    /**
     * 平台编码
     */
    private String platformCode;

    /**
     * 平台字段名
     */
    private String sourceFieldName;

    /**
     * 标准字段编码
     */
    private String standardFieldCode;

    /**
     * 匹配逻辑
     */
    private TransformLogicEnum transformLogic;

    /**
     * 位置信息
     */
    private String positionInfo;


    /**
     * 自定义脚本
     */
    private String customScript;

}
package ai.pricefox.mallfox.domain.standard;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 标准属性库
 */
@TableName(value = "standard_attribute")
@Data
public class StandardAttribute implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    private LocalDateTime createDate;

    /**
     * 修改时间
     */
    private LocalDateTime updateDate;

    /**
     * 创建人名称
     */
    private String createUsername;

    /**
     * 更新人名称
     */
    private String updateUsername;

    /**
     * 属性编码
     */
    private String attributeCode;

    /**
     * 属性名称cn
     */
    private String attributeNameCn;

    /**
     * 属性名称en
     */
    private String attributeNameEn;

    /**
     * 标准分类编码
     */
    private String standardCategoryCode;

    /**
     * 类目中文
     */
    private String standardCategoryNameCn;

    /**
     * 类目英文
     */
    private String standardCategoryNameEn;

    /**
     * 是否删除
     */
    private Boolean deleted;

    /**
     * 是否是规格 0 否 1是
     */
    private Boolean isSpec;
}

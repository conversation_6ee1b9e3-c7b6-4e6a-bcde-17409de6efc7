package ai.pricefox.mallfox.domain.standard;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 映射属性值
 */
@TableName(value = "standard_attribute_value_mapping")
@Data
public class StandardAttributeValueMapping implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    private LocalDateTime createDate;

    /**
     * 修改时间
     */
    private LocalDateTime updateDate;

    /**
     * 创建人名称
     */
    private String createUsername;

    /**
     * 更新人名称
     */
    private String updateUsername;

    /**
     * 属性名code
     */
    private String attributeCode;

    /**
     * 属性值code
     */
    private String valueCode;

    /**
     * 映射值
     */
    private String valueName;

    /**
     * 是否删除
     */
    private Boolean deleted;
}

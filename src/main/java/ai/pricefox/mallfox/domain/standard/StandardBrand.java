package ai.pricefox.mallfox.domain.standard;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 标准品牌库
 */
@TableName(value ="standard_brand")
@Data
public class StandardBrand implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 创建时间
     */
    private LocalDateTime createDate;

    /**
     * 修改时间
     */
    private LocalDateTime updateDate;

    /**
     * 创建人名称
     */
    private String createUsername;

    /**
     * 更新人名称
     */
    private String updateUsername;

    /**
     * 品牌编码
     */
    private String brandCode;

    /**
     * 品牌英文名称
     */
    private String brandNameEn;

    /**
     * 品牌中文名称
     */
    private String brandNameCn;

    /**
     * 品牌logo地址
     */
    private String logoUrl;

    /**
     * 品牌官网
     */
    private String website;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 是否激活 1：激活 0：未激活
     */
    private Boolean isActive;
}
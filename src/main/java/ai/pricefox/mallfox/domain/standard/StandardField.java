package ai.pricefox.mallfox.domain.standard;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 中央标准字段表
 * 定义了所有标准字段的元数据信息
 */
@TableName(value ="standard_field")
@Data
public class StandardField implements Serializable {
    private static final long serialVersionUID = 1L;
    
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 修改时间
     */
    private Date updateDate;

    /**
     * 创建人名称
     */
    private String createUsername;

    /**
     * 更新人名称
     */
    private String updateUsername;

    /**
     * 字段编码 (如: Raw0000001)
     */
    private String fieldCode;

    /**
     * 字段英文名称 (对应StandardProduct类中的属性名)
     */
    private String fieldNameEn;

    /**
     * 字段中文名称
     */
    private String fieldNameCn;

    /**
     * 提取逻辑 (字段值提取规则)
     */
    private String extractionLogic;
}
package ai.pricefox.mallfox.domain.standard;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * <AUTHOR> 合并属性数据
 */
@Data
@TableName(value = "standard_product_attribute_data")
public class StandardProductAttributeData implements Serializable {
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    private LocalDateTime createDate;

    /**
     * 创建人
     */
    private String createUsername;

    /**
     * 更新时间
     */
    private LocalDateTime updateDate;

    /**
     * 更新人
     */
    private String updateUsername;

    /**
     * 属性名
     */
    private String attributeName;

    /**
     * 属性值
     */
    private String attributeValue;

    /**
     * sku code
     */
    private String skuCode;

    /**
     * 是否匹配
     */
    private Boolean isMatch;

    private static final long serialVersionUID = 1L;
}
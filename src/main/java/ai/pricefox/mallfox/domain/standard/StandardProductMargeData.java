package ai.pricefox.mallfox.domain.standard;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

import ai.pricefox.mallfox.enums.DataChannelEnum;
import ai.pricefox.mallfox.enums.ProductExamineStatusEnum;
import ai.pricefox.mallfox.enums.ProductPlatformEnum;
import ai.pricefox.mallfox.enums.StandardizationStepEnum;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.springframework.data.mongodb.core.mapping.Field;

/**
 * <AUTHOR>
 */
@Data
@TableName(value = "standard_product_marge_data")
public class StandardProductMargeData implements Serializable {
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    private LocalDateTime createDate;

    /**
     * 创建人
     */
    private String createUsername;

    /**
     * 更新时间
     */
    private LocalDateTime updateDate;

    /**
     * 更新人
     */
    private String updateUsername;

    /**
     * 来源平台名称
     */
    private String sourcePlatformName;

    /**
     * 来源平台code
     */
    private String sourcePlatformCode;

    /**
     * 数据渠道 API   爬虫
     */
    private DataChannelEnum dataChannel;

    /**
     * 自建spu code
     */
    private String spuCode;

    /**
     * 自建sku code
     */
    private String skuCode;

    /**
     * 第三方平台的spuid
     */
    private String platformSpuId;

    /**
     * 第三放平台skuId
     */
    private String platformSkuId;

    /**
     * 步骤
     */
    private StandardizationStepEnum step;

    /**
     * 审核状态
     */
    private ProductExamineStatusEnum examineStatus;

    /**
     * 一级类目
     */
    private String categoryLevel1;

    /**
     * 二级类目
     */
    private String categoryLevel2;

    /**
     * 三级类目
     */
    private String categoryLevel3;

    /**
     *  品牌
     */
    private String brand;

    /**
     * 型号
     */
    private String model;

    /**
     * 标题
     */
    private String title;

    /**
     * UPC码
     */
    private String upcCode;

    /**
     * 颜色
     */
    private String color;

    /**
     * 系列
     */
    private String series;

    /**
     * 状态
     */
    private String conditionNew;

    /**
     * 价格
     */
    private BigDecimal price;

    /**
     * 三级类目code
     */
    private String categoryLeve3Code;

    /**
     * 自营
     */
    private Boolean selfOperated;

    /**
     * 商品标识
     */
    private String productIdentifier;

    /**
     * 是否匹配
     */
    private Boolean isMatch;

    /**
     * 子集
     */
    @TableField(exist = false)
    private List<StandardProductMargeData> child;

    private static final long serialVersionUID = 1L;
}
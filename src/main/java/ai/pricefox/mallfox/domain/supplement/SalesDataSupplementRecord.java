package ai.pricefox.mallfox.domain.supplement;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 销量数据补充记录表
 * @TableName sales_data_supplement_record
 */
@TableName(value = "sales_data_supplement_record")
@Data
public class SalesDataSupplementRecord {
    /**
     * 自增主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * product_data_offers表的id
     */
    private Long offerId;

    /**
     * 补充月份(YYYY-MM格式)
     */
    private String supplementMonth;

    /**
     * 补充的销量数据
     */
    private String supplementSales;

    /**
     * 用于计算的评论数量
     */
    private Integer reviewNumber;

    /**
     * 计算比例(默认3%)
     */
    private BigDecimal calculationRatio;

    /**
     * 记录创建时间
     */
    private LocalDateTime createTime;

    /**
     * 记录更新时间
     */
    private LocalDateTime updateTime;
}
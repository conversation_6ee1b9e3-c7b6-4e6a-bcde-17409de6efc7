package ai.pricefox.mallfox.domain.tracking;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 字段数据来源追踪表（JSON格式）
 * @TableName product_field_source_tracking
 */
@TableName(value = "product_field_source_tracking", autoResultMap = true)
@Data
public class ProductFieldSourceTracking {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 表名(product_data_offers/product_data_simplify)
     */
    private String tableName;

    /**
     * 记录ID
     */
    private Long recordId;

    /**
     * 字段来源JSON
     * 格式: {"fieldName": {"dataSource": 1, "sourcePlatform": "amazon", "lastUpdate": "2025-01-14T10:30:00", "oldValue": "old", "newValue": "new"}}
     */
    @com.baomidou.mybatisplus.annotation.TableField(typeHandler = ai.pricefox.mallfox.config.handler.CustomJacksonTypeHandler.class)
    private Map<String, FieldSourceInfo> fieldSources;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 字段来源信息内部类
     */
    @Data
    public static class FieldSourceInfo {
        /**
         * 数据来源：1-爬虫 2-API
         */
        private Integer dataSource;

        /**
         * 来源平台
         */
        private String sourcePlatform;

        /**
         * 最后更新时间
         */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime lastUpdate;

        /**
         * 旧值
         */
        private String oldValue;

        /**
         * 新值
         */
        private String newValue;
    }
}

package ai.pricefox.mallfox.enums;


import lombok.Getter;

/**
 * <AUTHOR>
 * @desc 商品数据操作类型
 * @since 2025/7/24
 */
@Getter
public enum DataChangeTypeEnum {
    CREATE(1, "新增"),
    UPDATE(2, "更新"),
    DELETE(3, "删除");

    private final Integer code;
    private final String desc;

    DataChangeTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static DataChangeTypeEnum fromCode(Integer code) {
        if (code == null) return null;
        for (DataChangeTypeEnum type : values()) {
            if (type.code.equals(code)) {
                return type;
            }
        }
        return null;
    }
}

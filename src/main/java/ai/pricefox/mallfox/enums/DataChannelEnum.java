package ai.pricefox.mallfox.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 数据渠道枚举
 * 
 * <AUTHOR>
 * @since 2025-01-07
 */
@Getter
@AllArgsConstructor
public enum DataChannelEnum {

    /**
     * 爬虫数据
     */
    CRAWLER(1, "爬虫"),

    /**
     * API数据
     */
    API(2, "API");

    /**
     * 渠道代码
     */
    private final Integer code;

    /**
     * 渠道描述
     */
    private final String description;

    /**
     * 根据代码获取枚举
     *
     * @param code 渠道代码
     * @return 对应的枚举，如果不存在返回null
     */
    public static DataChannelEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (DataChannelEnum channel : values()) {
            if (channel.getCode().equals(code)) {
                return channel;
            }
        }
        return null;
    }

    /**
     * 判断是否为爬虫数据
     *
     * @param code 渠道代码
     * @return true-爬虫数据，false-其他
     */
    public static boolean isCrawler(Integer code) {
        return CRAWLER.getCode().equals(code);
    }

    /**
     * 判断是否为API数据
     *
     * @param code 渠道代码
     * @return true-API数据，false-其他
     */
    public static boolean isApi(Integer code) {
        return API.getCode().equals(code);
    }
}

package ai.pricefox.mallfox.enums;


import lombok.Getter;

/**
 * <AUTHOR>
 * @desc 数据处理来源
 * @since 2025/7/24
 */
@Getter
public enum DataTriggerSource {

    BATCH_PROCESS("batch_process", "批量处理"),
    MANUAL_UPDATE("manual_update", "手动更新"),
    SCHEDULED_JOB("scheduled_job", "定时任务");

    private  String code;
    private  String desc;

    DataTriggerSource(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static DataTriggerSource fromCode(String code) {
        if (code == null) return null;
        for (DataTriggerSource source : values()) {
            if (source.code.equals(code)) {
                return source;
            }
        }
        return null;
    }
}

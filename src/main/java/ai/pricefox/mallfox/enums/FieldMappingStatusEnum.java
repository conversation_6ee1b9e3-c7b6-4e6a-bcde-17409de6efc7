package ai.pricefox.mallfox.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 字段映射状态枚举
 */
@Getter
public enum FieldMappingStatusEnum {
    /**
     * 原始字段，直接从原始数据中获取
     */
    ORIGINAL("original", "原始字段"),

    /**
     * 匹配成功，字段已标准化
     */
    MATCHED("matched", "匹配成功"),
    /**
     * 匹配成功，字段已从标题中获取
     */
    MATCHED_TITLE("matched_title","标题撞库"),

    /**
     * 未匹配，字段未找到对应的标准字段
     */
    UNMATCHED("unmatched", "未匹配"),

    /**
     * 空值，字段存在但值为空
     */
    EMPTY("empty", "空值"),

    /**
     * 原始无映射，字段来自原始数据但没有映射关系
     */
    ORIGINAL_NO_MAPPING("original_no_mapping", "原始无映射"),

    /**
     * 从原始数据填充，未匹配字段使用原始数据填充
     */
    FILLED_FROM_ORIGINAL("filled_from_original", "从原始数据填充"),
    /**
     *  数据异常
     */
    ACCESS_ERROR("access_error","数据异常");

    private final String code;
    private final String description;

    FieldMappingStatusEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 用于JSON序列化，返回code值
     *
     * @return 状态代码
     */
    @JsonValue
    public String getCode() {
        return code;
    }


    /**
     * 根据代码获取枚举值
     *
     * @param code 状态代码
     * @return 对应的枚举值，如果未找到返回null
     */
    public static FieldMappingStatusEnum fromCode(String code) {
        for (FieldMappingStatusEnum status : FieldMappingStatusEnum.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }

    @Override
    public String toString() {
        return code;
    }
}

package ai.pricefox.mallfox.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @description 插件匹配状态枚举
 * @date 2025-07-31
 */
@Getter
@RequiredArgsConstructor
public enum PluginMatchStatusEnum {

    /**
     * 状态A: 成功匹配到商品，并且在其他渠道发现了更低的价格。
     * 插件应引导用户到最低价的链接。
     */
    LOWER_PRICE_FOUND("lower_price_found"),

    /**
     * 状态B: 成功匹配到商品，并且确认当前页面的价格是最低价（或在允许的误差范围内）。
     * 插件应给予用户信心，并可能提供返利机会。
     */
    BEST_PRICE_CONFIRMED("best_price_confirmed"),

    /**
     * 状态C: 未能将当前页面的商品匹配到我们系统中的任何已知SKU。
     * 插件应引导用户提交收录请求。
     */
    PRODUCT_NOT_FOUND("product_not_found");

    /**
     * 这个值将作为API响应中status字段的JSON值。
     */
    @JsonValue
    private final String value;
}

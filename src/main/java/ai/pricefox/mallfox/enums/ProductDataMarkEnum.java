package ai.pricefox.mallfox.enums;


import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @desc 商品数据标记
 * @since 2025/6/24
 */
@Getter
public enum ProductDataMarkEnum {

    NORMAL(0, "正常"),
    MISSING(1, "缺失"),
    ERROR(2, "错误");

    private static final Map<Integer, String> CODE_TO_NAME_MAP = new HashMap<>();

    static {
        for (ProductDataMarkEnum value : values()) {
            CODE_TO_NAME_MAP.put(value.code, value.name);
        }
    }

    private final int code;
    private final String name;

    ProductDataMarkEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getName(Integer code) {
        if (code == null) {
            return "";
        }
        return CODE_TO_NAME_MAP.getOrDefault(code, "");
    }

    // 异常的编码
    public static List<Integer> getErrorMarkCode() {
        return List.of(MISSING.code, ERROR.code);
    }


}


package ai.pricefox.mallfox.enums;

import lombok.Getter;

/**
 * 商品审核状态枚举
 */
@Getter
public enum ProductExamineStatusEnum {
    
    /**
     * 待审核 - 初始状态，等待审核人员处理
     */
    PENDING("PENDING", "待审核"),
    
    /**
     * 审核中 - 正在进行审核过程
     */
    IN_REVIEW("IN_REVIEW", "审核中"),
    
    /**
     * 审核通过 - 商品信息符合要求，可以发布或上架
     */
    APPROVED("APPROVED", "审核通过"),
    
    /**
     * 审核拒绝 - 商品信息不符合要求，需要修改
     */
    REJECTED("REJECTED", "审核拒绝"),
    
    /**
     * 需要修改 - 商品信息基本符合要求，但需要少量修改
     */
    NEEDS_MODIFICATION("NEEDS_MODIFICATION", "需要修改"),
    
    /**
     * 已撤回 - 提交者主动撤回审核申请
     */
    WITHDRAWN("WITHDRAWN", "已撤回"),
    
    /**
     * 已过期 - 审核超时未处理，自动过期
     */
    EXPIRED("EXPIRED", "已过期"),
    
    /**
     * 冻结 - 审核通过后因某些原因被冻结
     */
    FROZEN("FROZEN", "已冻结"),
    
    /**
     * 自动审核通过 - 系统自动审核通过
     */
    AUTO_APPROVED("AUTO_APPROVED", "自动审核通过");

    private final String code;
    private final String description;

    ProductExamineStatusEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 根据code获取枚举值
     *
     * @param code 状态码
     * @return 对应的枚举值，未找到则返回null
     */
    public static ProductExamineStatusEnum getByCode(String code) {
        for (ProductExamineStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }

    @Override
    public String toString() {
        return code;
    }
}

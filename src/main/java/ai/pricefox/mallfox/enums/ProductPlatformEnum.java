package ai.pricefox.mallfox.enums;


import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @desc 商品平台枚举
 * @since 2025/7/7
 */
@AllArgsConstructor
@Getter
public enum ProductPlatformEnum {


    AMAZON("amazon", "亚马逊"),
    EBAY("ebay", "ebay"),
    BESTBUY("bestbuy", "bestbuy"),
    WALMART("walmart","walmart");


    public static ProductPlatformEnum fromCode(String code) {
        return getByCode(code);
    }

    public static ProductPlatformEnum getByCode(String code) {
        for (ProductPlatformEnum value : values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }


    private String code;

    private String name;
}

package ai.pricefox.mallfox.enums;

import lombok.Getter;

/**
 * 规则组枚举，用于定义不同步骤对应的规则名称
 * 注释格式说明：
 * - 规则名称后用括号标注所属文件
 * - 简要描述规则核心功能
 * - 标明规则主要处理逻辑或触发条件
 */
@Getter
public enum RuleGroup {
    ATTRIBUTE_MAPPING(new String[]{
            // 字段映射规则 (PlatformFieldMapping.drl)
            // 功能：将平台特定字段映射到标准字段
            // 特点：支持动态字段查找，自动处理大小写不匹配
            "Platform Field Mapping"}),

    CATEGORY_MAPPING(new String[]{
            // 品类标准化规则 (CategoryStandardization.drl)
            // 功能：将品类信息标准化为标准库定义格式
            // 特点：支持标准库匹配、标题提取、原始数据回退
            "Process Category Standardization",

            // 平台品类映射规则 (PlatformFieldMapping.drl)
            // 功能：处理平台特定品类代码到标准品类的映射
            // 特点：与PlatformFieldMappingRule配合使用
            "Platform Category Mapping Rule"}),

    BRAND_MAPPING(new String[]{
            // 品牌标准化规则 (BrandStandardization.drl)
            // 功能：将品牌名称标准化为标准库定义格式
            // 特点：支持多语言匹配，包含智能标题识别
            "Process Brand Standardization",

            // 标题品牌提取规则 (EntityStandardization.drl)
            // 功能：从商品标题自动提取品牌信息
            // 特点：内置常见品牌识别逻辑（Apple/Samsung等）
            "Process Entity Brand Standardization"}),

    MODEL_MAPPING(new String[]{
            // 型号标准化规则 (ModelStandardization.drl)
            // 功能：将型号信息标准化为标准库定义格式
            // 特点：支持复杂型号识别和正则提取
            "Process Model Standardization"});


    private final String[] ruleNames;

    RuleGroup(String[] ruleNames) {
        this.ruleNames = ruleNames;
    }

}

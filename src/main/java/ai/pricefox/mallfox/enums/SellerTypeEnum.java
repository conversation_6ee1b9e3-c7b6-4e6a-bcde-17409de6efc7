package ai.pricefox.mallfox.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 数据渠道枚举
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
@Getter
@AllArgsConstructor
public enum SellerTypeEnum {
    /**
     * 自营数据
     */
    PLATFORM("platform", "自营"),

    /**
     * 第三方数据
     */
    THIRD_PARTY("thirdParty", "第三方"),

    /**
     * 亚马逊自营
     */
    AMAZON_RETAIL("Amazon Retail", "亚马逊自营"),
    /**
     * 亚马逊第三方
     */
    AMAZON_SELLER("Amazon Seller", "亚马逊第三方"),
    /**
     * Bestbuy
     */
    BESTBUY("Bestbuy", "Bestbuy"),
    /**
     * Ebay第三方
     */
    EBAY_SELLER("Ebay Seller", "Ebay第三方"),
    /**
     * Walmart自营
     */
    WALMART_RETAIL("Walmart Retail", " Walmart自营"),
    /**
     * Walmart第三方
     */
    WALMART_SELLER("Walmart Seller", "Walmart第三方");

    /**
     * 渠道代码
     */
    private final String code;

    /**
     * 渠道描述
     */
    private final String description;

    /**
     * 根据代码获取枚举
     *
     * @param code 渠道代码
     * @return 对应的枚举，如果不存在返回null
     */
    public static SellerTypeEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (SellerTypeEnum channel : values()) {
            if (channel.getCode().equals(code)) {
                return channel;
            }
        }
        return null;
    }

    // 是否是自营
    public static boolean isPlatform(String code) {
        return Objects.equals(code, PLATFORM.code);
    }

    // 是否是第三方
    public static boolean isThirdParty(String code) {
        return Objects.equals(code, THIRD_PARTY.code);
    }
}

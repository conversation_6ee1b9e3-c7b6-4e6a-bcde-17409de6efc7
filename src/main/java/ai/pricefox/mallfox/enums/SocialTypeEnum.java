package ai.pricefox.mallfox.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 社交平台类型枚举
 */
@Getter
@AllArgsConstructor
public enum SocialTypeEnum {

    GOOGLE(1, "Google"),
    FACEBOOK(2, "Facebook"),
    TWITTER(3, "Twitter"),
    LINKEDIN(4, "LinkedIn"),
    GITHUB(5, "GitHub"),
    APPLE(6, "Apple"),
    MICROSOFT(7, "Microsoft"),
    AMAZON(8, "Amazon");

    /**
     * 类型编码
     */
    private final Integer code;
    
    /**
     * 类型名称
     */
    private final String name;
    
    /**
     * 根据编码获取枚举
     */
    public static SocialTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (SocialTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
} 
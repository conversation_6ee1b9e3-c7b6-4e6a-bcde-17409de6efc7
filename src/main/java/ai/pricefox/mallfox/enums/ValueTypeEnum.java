package ai.pricefox.mallfox.enums;

import lombok.Getter;

/**
 * 值类型枚举
 */
@Getter
public enum ValueTypeEnum {
    BRAND("brand", "品牌"),
    COLOR("color", "颜色"),
    STORAGE("storage", "存储"),
    MODEL("model", "型号"),
    CATEGORY("category", "品类"),
    CONDITION("condition","商品条件"),
    SERVICE_PROVIDER("service_provider", "服务商");

    private final String code;
    private final String description;

    ValueTypeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 根据code获取枚举值
     *
     * @param code 代码
     * @return 枚举值
     */
    public static ValueTypeEnum fromCode(String code) {
        for (ValueTypeEnum type : ValueTypeEnum.values()) {
            if (type.getCode().equalsIgnoreCase(code)) {
                return type;
            }
        }
        return null;
    }

    @Override
    public String toString() {
        return code;
    }
}

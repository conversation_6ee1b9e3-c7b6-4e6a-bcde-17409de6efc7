package ai.pricefox.mallfox.event;

import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * 标准化处理数据事件
 */
@Getter
public class StandardDataEvent extends ApplicationEvent {
    /**
     * 原始数据Json
     * 需要包含以下字段：
     * sourcePlatform：平台类型  ai.pricefox.mallfox.model.enums.ProductPlatformEnum
     * dataChannel : 数据渠道  ai.pricefox.mallfox.model.enums.DataChannelEnum
     * selfOperated: 是否自营 true  false
     */
    private final String originalData;

    public StandardDataEvent(Object source, String originalData) {
        super(source);
        this.originalData = originalData;
    }
}

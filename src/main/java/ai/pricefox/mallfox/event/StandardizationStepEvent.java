package ai.pricefox.mallfox.event;

import ai.pricefox.mallfox.enums.StandardizationStepEnum;
import ai.pricefox.mallfox.model.dto.DynamicStandardProduct;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * 标准化步骤事件
 * 用于标准化流程中传递基础数据
 * 每个步骤可独立发布和处理
 */
@Getter
@EqualsAndHashCode(callSuper = true)
public class StandardizationStepEvent extends ApplicationEvent {
    /**
     * 当前步骤标识
     */
    private final StandardizationStepEnum currentStep;
    /**
     * 过程数据
     */
    private final DynamicStandardProduct dynamicStandardProduct;

    /**
     * 创建标准化步骤事件
     *
     * @param source                 事件源
     * @param currentStep            当前步骤标识
     * @param dynamicStandardProduct 动态标准产品
     */
    public StandardizationStepEvent(Object source, StandardizationStepEnum currentStep, DynamicStandardProduct dynamicStandardProduct) {
        super(source);
        this.currentStep = currentStep;
        this.dynamicStandardProduct = dynamicStandardProduct;
    }

}
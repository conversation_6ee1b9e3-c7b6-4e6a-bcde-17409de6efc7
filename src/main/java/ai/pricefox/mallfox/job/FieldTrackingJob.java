package ai.pricefox.mallfox.job;

import ai.pricefox.mallfox.config.FieldTrackingConfig;
import ai.pricefox.mallfox.service.tracking.FieldTrackingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;


/**
 * 字段追踪定时任务
 * 
 * <AUTHOR>
 * @since 2025-01-14
 */
@Slf4j
@Component
public class FieldTrackingJob {

    @Autowired
    private FieldTrackingService fieldTrackingService;

    @Autowired
    private FieldTrackingConfig config;


    /**
     * 定时处理字段追踪事件队列
     * 每5秒执行一次（可通过配置调整）
     */
    @Scheduled(fixedDelayString = "${field.tracking.process-interval:5000}")
    public void processFieldTrackingEvents() {
        if (!config.getEnabled() || !config.getAsyncEnabled()) {
            return;
        }

        try {
            fieldTrackingService.processPendingEvents();
        } catch (Exception e) {
            log.error("定时处理字段追踪事件失败", e);
        }
    }

//    /**
//     * 定时清理过期的字段追踪数据
//     * 每天凌晨2点执行
//     */
//    @Scheduled(cron = "0 0 2 * * ?")
//    public void cleanupExpiredTrackingData() {
//        if (!config.getEnabled()) {
//            return;
//        }
//
//        try {
//            log.info("开始清理过期的字段追踪数据");
//            // 这里可以实现清理逻辑，比如删除30天前的追踪数据
//            // fieldTrackingService.cleanupExpiredData(30);
//            log.info("字段追踪数据清理完成");
//        } catch (Exception e) {
//            log.error("清理过期字段追踪数据失败", e);
//        }
//    }

    /**
     * 定时输出字段追踪统计信息
     * 每小时执行一次
     */
    @Scheduled(cron = "0 0 * * * ?")
    public void reportTrackingStatistics() {
        if (!config.getEnabled()) {
            return;
        }

        try {
            String queueStatus = fieldTrackingService.getQueueStatus();
            log.info("字段追踪统计 - {}", queueStatus);
        } catch (Exception e) {
            log.debug("获取字段追踪统计信息失败", e);
        }
    }
}

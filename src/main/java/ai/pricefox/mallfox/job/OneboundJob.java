package ai.pricefox.mallfox.job;

import ai.pricefox.mallfox.domain.product.Product;
import ai.pricefox.mallfox.job.amazon.AmazonApiService;
import ai.pricefox.mallfox.job.amazon.AmazonProductDetail;
import ai.pricefox.mallfox.job.amazon.AmazonSearchResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class OneboundJob {

    @Autowired
    private AmazonApiService amazonApiService;

    /**
     * 对接api万邦https://open.onebound.cn api获取亚马逊商品列表和详情
     * 亚马逊查询
     */

    @Scheduled(cron = "0 0 8 * * ? ")
    public void oneboundProductListJob() {
        try {
            log.info("对接api万邦https://open.onebound.cn api获取亚马逊商品列表和详情");
            //字符串list
            String[] queryList = {"apple iphone", "samsung phone"};
            for (String query : queryList) {
                for (int page = 1; page <= 10; page++) {
                    AmazonSearchResponse amazonSearchResponse = amazonApiService.getAmazonProducts(query, page);
                    if (amazonSearchResponse.getItems() == null || amazonSearchResponse.getItems().getItem().size() == 0) {
                        break;
                    }
                    log.info("获取到{}页{}条数据", page, amazonSearchResponse.getItems().getItem().size());
                    //保存数据
                    for (AmazonSearchResponse.ItemSearch item : amazonSearchResponse.getItems().getItem()) {
                        log.info("保存数据{}", item);
                        try {
                            //保存数据
                            itemProduct(item.getNum_iid());
                        } catch (Exception e) {
                            log.error("保存数据失败:{}", item, e);
                        }

                    }
                }
            }
        } catch (Exception e) {
            log.error("心跳上报失败", e);
        }
    }

    private void itemProduct(String numIid) {
        AmazonProductDetail product = amazonApiService.getProductDetail(numIid);

    }


}

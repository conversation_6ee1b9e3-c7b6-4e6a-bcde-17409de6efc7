package ai.pricefox.mallfox.job;

import ai.pricefox.mallfox.service.supplement.SalesDataSupplementService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 销量数据补充定时任务
 * 每月1号执行，补充缺失的sales_last30_days数据
 * 
 * <AUTHOR>
 * @since 2025-01-14
 */
@Slf4j
@Component
public class SalesDataSupplementJob {

    @Autowired
    private SalesDataSupplementService salesDataSupplementService;

    @Value("${sales.supplement.enabled:true}")
    private Boolean enabled;

    /**
     * 定时补充销量数据
     * 每月1号凌晨1点执行
     */
    @Scheduled(cron = "0 0 1 1 * ?")
    public void supplementSalesData() {
        if (!enabled) {
            log.info("销量数据补充定时任务已禁用");
            return;
        }

        String currentMonth = salesDataSupplementService.getCurrentMonth();
        log.info("开始执行销量数据补充定时任务，当前月份: {}", currentMonth);

        try {
            // 执行月度销量数据补充
            salesDataSupplementService.executeMonthlySupplementTask(currentMonth);
            
            // 输出统计信息
            String statistics = salesDataSupplementService.getSupplementStatistics(currentMonth);
            log.info("销量数据补充定时任务执行完成 - {}", statistics);
            
        } catch (Exception e) {
            log.error("销量数据补充定时任务执行失败，月份: {}", currentMonth, e);
        }
    }

    /**
     * 每小时输出销量补充统计信息
     */
    @Scheduled(cron = "0 30 * * * ?")
    public void reportSupplementStatistics() {
        if (!enabled) {
            return;
        }

        try {
            String currentMonth = salesDataSupplementService.getCurrentMonth();
            String statistics = salesDataSupplementService.getSupplementStatistics(currentMonth);
            log.info("销量补充统计 - {}", statistics);
        } catch (Exception e) {
            log.debug("获取销量补充统计信息失败", e);
        }
    }
}
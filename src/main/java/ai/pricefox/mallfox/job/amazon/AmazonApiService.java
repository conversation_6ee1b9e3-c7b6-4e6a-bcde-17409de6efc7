package ai.pricefox.mallfox.job.amazon;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import org.springframework.http.ResponseEntity;
import org.springframework.web.util.UriComponentsBuilder;

@Component
public class AmazonApiService {

    private String apiKey = "t3386046727";
    private String apiSecret = "6727a83c";
    @Autowired
    private RestTemplate restTemplate;
//    private final String URL = "https://api-gw.onebound.cn/amazon/item_search/?key=t3386046727&%20&q=apple%20iphone&start_price=&end_price=&page=&cat=&discount_only=&sort=&page_size=&seller_info=&nick=&ppath=&&lang=zh-CN&secret=6727a83c";

    @Retryable(
            value = {RuntimeException.class, Exception.class}, // 指定需要重试的异常类型
            maxAttempts = 3,                    // 最大重试次数（默认3次）
            backoff = @Backoff(delay = 1000)    // 重试间隔（毫秒）
    )
    public AmazonSearchResponse getAmazonProducts(String query, int page) {
        String url = "https://api-gw.onebound.cn/amazon/item_search/?key=" + apiKey + "&secret=" + apiSecret + "&q=&start_price=&end_price=&page=" + page + "&cat=7072561011&discount_only=&sort=&page_size=&seller_info=&nick=&ppath=&&lang=en_US&cache=no";
//        String url = "https://api-gw.onebound.cn/amazon/item_search/?key=" + apiKey + "&secret=" + apiSecret + "&q=" + query + "&start_price=&end_price=&page=" + page + "&cat=&discount_only=&sort=&page_size=&seller_info=&nick=&ppath=&&lang=en_US";
        ResponseEntity<AmazonSearchResponse> response = restTemplate.getForEntity(url, AmazonSearchResponse.class);
//        if (!response.getBody().getError_code().equals("200")) {
//            throw new RuntimeException("请求失败");
//        }
        return response.getBody();
    }

    @Retryable(
            value = {RuntimeException.class, Exception.class}, // 指定需要重试的异常类型
            maxAttempts = 3,                    // 最大重试次数（默认3次）
            backoff = @Backoff(delay = 1000)    // 重试间隔（毫秒）
    )
    public AmazonProductDetail getProductDetail(String productId) {
        String url = UriComponentsBuilder.fromHttpUrl("https://api-gw.onebound.cn/amazon/item_get/")
                .queryParam("key", apiKey)
                .queryParam("num_iid", productId)
                .queryParam("domain", "com")
                .queryParam("cache", "no")
                .queryParam("lang", "zh-CN")
                .queryParam("secret", apiSecret)
                .toUriString();

        AmazonProductResponse response = restTemplate.getForObject(url, AmazonProductResponse.class);

        if (response == null || response.getItem() == null) {
            throw new RuntimeException("Failed to fetch product details");
        }

        return mapToProductDetail(response.getItem());
    }

    private AmazonProductDetail mapToProductDetail(Item item) {
        AmazonProductDetail detail = new AmazonProductDetail();

        // 基本字段映射
        detail.setBrand(item.getBrand());
        detail.setProductName(item.getTitle());
        detail.setProductId(item.getNum_iid());
        detail.setPrice(item.getPrice());
//        detail.setOriginalPrice(item.getOrginal_price());
        detail.setSales(item.getSales());
        detail.setRating(item.getStar());
        detail.setProductUrl(item.getDetail_url());
        detail.setStockQuantity(item.getNum());
        detail.setInStock("99".equals(item.getNum()) ? "有库存" : "无库存");

        // 从props中提取其他字段
        for (Property prop : item.getProps()) {
            switch (prop.getName()) {
                case "Model Name":
                    detail.setModel(prop.getValue());
                    break;
                case "Memory Storage Capacity":
                    detail.setSpecification(prop.getValue());
                    break;
                case "Operating System":
                    detail.setSeries(prop.getValue());
                    break;
                // 可以根据需要添加更多字段映射
            }
        }
        // 处理SKU信息
        if (item.getSkus() != null && !item.getSkus().isEmpty()) {
            // 这里可以处理多SKU的情况
            // 示例代码只取第一个SKU
            Sku firstSku = item.getSkus().get(0);
            detail.setSkuId(firstSku.getSku_id());
        }

        // 商品条件(从标题中判断)
        if (item.getTitle() != null) {
            if (item.getTitle().contains("Renewed")) {
                detail.setCondition("refurbished");
            } else if (item.getTitle().contains("Open Box")) {
                detail.setCondition("with open box");
            } else {
                detail.setCondition("new");
            }
        }

        return detail;
    }
}

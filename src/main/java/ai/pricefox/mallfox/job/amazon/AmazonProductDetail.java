package ai.pricefox.mallfox.job.amazon;

import lombok.Data;

@Data
public class AmazonProductDetail {
    // 品牌
    private String brand;
    // 商品名称
    private String productName;
    // 型号
    private String model;
    // 颜色
    private String color;
    // 规格
    private String specification;
    // 系列
    private String series;
    // 一级类目
    private String primaryCategory;
    // 三级类目
    private String tertiaryCategory;
    // 商品条件
    private String condition;
    // 价格
    private Double price;
    // 卖家
    private String seller;
    // 销量
    private Long sales;
    // 评价数量
    private Long reviewCount;
    // 评分
    private String rating;
    // 商品链接
    private String productUrl;
    // 备注
    private String remarks;
    // 商品ID
    private String productId;
    // 库存数量
    private String stockQuantity;
    // 是否有库存
    private String inStock;
    // UPC code
    private String upcCode;
    // SKU ID
    private String skuId;
}
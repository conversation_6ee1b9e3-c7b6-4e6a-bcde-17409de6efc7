package ai.pricefox.mallfox.job.amazon;

import lombok.Data;

import java.util.List;

@Data
public class AmazonSearchResponse {
    private ItemSearchs items;
    private String error_code;
    private String reason;
    // getters and setters

    @Data
    public static class ItemSearchs {
        private String _ddf;
        private List<ItemSearch> item;
        private String page;
        private int page_size;
        private String pagecount;
        private String q;
        private String real_total_results;
        private String total_results;

        // getters and setters
    }

    @Data
    public static class ItemSearch {
        private String detail_url;
        private String num_iid;
        private String pic_url;
        private String price;
        private String promotion_price;
        private String reviews;
        private int sales;
        private String stars;
        private String title;

        // getters and setters
    }
}
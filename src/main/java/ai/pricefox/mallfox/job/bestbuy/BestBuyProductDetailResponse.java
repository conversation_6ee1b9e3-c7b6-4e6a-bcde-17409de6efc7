package ai.pricefox.mallfox.job.bestbuy;

import ai.pricefox.mallfox.enums.DataChannelEnum;
import ai.pricefox.mallfox.enums.ProductPlatformEnum;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * BestBuy API 产品详情响应实体
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class BestBuyProductDetailResponse {

    /**
     * SKU
     */
    @JsonProperty("sku")
    private String sku;

    private ProductPlatformEnum sourcePlatform = ProductPlatformEnum.BESTBUY;
    private DataChannelEnum dataChannel = DataChannelEnum.API;
    private Boolean selfOperated = true;

    /**
     * 产品名称
     */
    @JsonProperty("name")
    private String name;

    /**
     * 产品类型
     */
    @JsonProperty("type")
    private String type;

    /**
     * 开始日期
     */
    @JsonProperty("startDate")
    private String startDate;

    /**
     * 新产品标识
     */
    @JsonProperty("new")
    private Boolean isNew;

    /**
     * 活跃状态
     */
    @JsonProperty("active")
    private Boolean active;

    /**
     * 低价保证
     */
    @JsonProperty("lowPriceGuarantee")
    private Boolean lowPriceGuarantee;

    /**
     * 活跃更新日期
     */
    @JsonProperty("activeUpdateDate")
    private String activeUpdateDate;

    /**
     * 常规价格
     */
    @JsonProperty("regularPrice")
    
    private String regularPrice;

    /**
     * 销售价格
     */
    @JsonProperty("salePrice")
    
    private String salePrice;

    /**
     * 清仓标识
     */
    @JsonProperty("clearance")
    private Boolean clearance;

    /**
     * 数字交付标识
     */
    @JsonProperty("digitalDelivery")
    private Boolean digitalDelivery;

    /**
     * 扩展保修标识
     */
    @JsonProperty("extendedWarranty")
    private Boolean extendedWarranty;

    /**
     * 免费配送标识
     */
    @JsonProperty("freeShipping")
    private Boolean freeShipping;

    /**
     * 免费配送符合条件标识
     */
    @JsonProperty("freeShippingEligible")
    private Boolean freeShippingEligible;

    /**
     * 安装标识
     */
    @JsonProperty("installation")
    private Boolean installation;

    /**
     * 店内可用性
     */
    @JsonProperty("inStoreAvailability")
    private Boolean inStoreAvailability;

    /**
     * 店内可用性文本
     */
    @JsonProperty("inStoreAvailabilityText")
    private String inStoreAvailabilityText;

    /**
     * 店内可用性更新日期
     */
    @JsonProperty("inStoreAvailabilityUpdateDate")
    private String inStoreAvailabilityUpdateDate;

    /**
     * 商品更新日期
     */
    @JsonProperty("itemUpdateDate")
    private String itemUpdateDate;

    /**
     * 在线可用性
     */
    @JsonProperty("onlineAvailability")
    private Boolean onlineAvailability;

    /**
     * 在线可用性文本
     */
    @JsonProperty("onlineAvailabilityText")
    private String onlineAvailabilityText;

    /**
     * 在线可用性更新日期
     */
    @JsonProperty("onlineAvailabilityUpdateDate")
    private String onlineAvailabilityUpdateDate;

    /**
     * 发布日期
     */
    @JsonProperty("releaseDate")
    private String releaseDate;

    /**
     * 销售排名
     */
    @JsonProperty("salesRankShortTerm")
    private Integer salesRankShortTerm;

    /**
     * 长期销售排名
     */
    @JsonProperty("salesRankMediumTerm")
    private Integer salesRankMediumTerm;

    /**
     * 长期销售排名
     */
    @JsonProperty("salesRankLongTerm")
    private Integer salesRankLongTerm;

    /**
     * 特价标识
     */
    @JsonProperty("specialOrder")
    private Boolean specialOrder;

    /**
     * 短描述
     */
    @JsonProperty("shortDescription")
    private String shortDescription;

    /**
     * 长描述
     */
    @JsonProperty("longDescription")
    private String longDescription;

    /**
     * 类别路径
     */
    @JsonProperty("categoryPath")
    private List<CategoryPath> categoryPath;

    /**
     * 客户评价平均分
     */
    @JsonProperty("customerReviewAverage")
    private String customerReviewAverage;

    /**
     * 客户评价数量
     */
    @JsonProperty("customerReviewCount")
    private Integer customerReviewCount;

    /**
     * 制造商
     */
    @JsonProperty("manufacturer")
    private String manufacturer;

    /**
     * 型号
     */
    @JsonProperty("modelNumber")
    private String modelNumber;

    /**
     * 图片
     */
    @JsonProperty("image")
    private String image;

    /**
     * 大图片
     */
    @JsonProperty("largeFrontImage")
    private String largeFrontImage;

    /**
     * 缩略图
     */
    @JsonProperty("thumbnailImage")
    private String thumbnailImage;

    /**
     * 大缩略图
     */
    @JsonProperty("largeImage")
    private String largeImage;

    /**
     * 替代图片
     */
    @JsonProperty("alternateViewsImage")
    private String alternateViewsImage;

    /**
     * 角度图片
     */
    @JsonProperty("angleImage")
    private String angleImage;

    /**
     * 背面图片
     */
    @JsonProperty("backViewImage")
    private String backViewImage;

    /**
     * 能源指南
     */
    @JsonProperty("energyGuide")
    private String energyGuide;

    /**
     * 左侧图片
     */
    @JsonProperty("leftViewImage")
    private String leftViewImage;

    /**
     * 配件图片
     */
    @JsonProperty("accessoriesImage")
    private String accessoriesImage;

    /**
     * 遥控器图片
     */
    @JsonProperty("remoteControlImage")
    private String remoteControlImage;

    /**
     * 右侧图片
     */
    @JsonProperty("rightViewImage")
    private String rightViewImage;

    /**
     * 顶部图片
     */
    @JsonProperty("topViewImage")
    private String topViewImage;

    /**
     * 详情图片
     */
    @JsonProperty("detailImage")
    private String detailImage;

    /**
     * 特征图片
     */
    @JsonProperty("featuresImage")
    private String featuresImage;

    /**
     * 包含物品图片
     */
    @JsonProperty("includedItemsImage")
    private String includedItemsImage;

    /**
     * 生活方式图片
     */
    @JsonProperty("lifestyleImage")
    private String lifestyleImage;

    /**
     * UPC
     */
    @JsonProperty("upc")
    private String upc;

    /**
     * 颜色
     */
    @JsonProperty("color")
    private String color;

    /**
     * 颜色分类
     */
    @JsonProperty("colorCategory")
    private String colorCategory;

    /**
     * 数量限制
     */
    @JsonProperty("quantityLimit")
    private Integer quantityLimit;

    /**
     * 店内取货
     */
    @JsonProperty("inStorePickup")
    private Boolean inStorePickup;

    /**
     * 家庭配送
     */
    @JsonProperty("homeDelivery")
    private Boolean homeDelivery;

    /**
     * GTIN
     */
    @JsonProperty("gtin")
    private String gtin;

    /**
     * ISBN
     */
    @JsonProperty("isbn")
    private String isbn;

    /**
     * MPN (制造商零件号)
     */
    @JsonProperty("mpn")
    private String mpn;

    /**
     * 产品URL
     */
    @JsonProperty("url")
    private String url;

    /**
     * 旋转URL
     */
    @JsonProperty("spin360Url")
    private String spin360Url;

    /**
     * 移动URL
     */
    @JsonProperty("mobileUrl")
    private String mobileUrl;

    /**
     * 附属品
     */
    @JsonProperty("accessories")
    private List<String> accessories;

    /**
     * 相关产品
     */
    @JsonProperty("relatedProducts")
    private List<String> relatedProducts;

    /**
     * 技术规格
     */
    @JsonProperty("technicalSpecifications")
    private Object technicalSpecifications;

    /**
     * 节省金额
     */
    @JsonProperty("dollarSavings")
    
    private String dollarSavings;

    /**
     * 节省百分比
     */
    @JsonProperty("percentSavings")
    
    private String percentSavings;

    /**
     * 计划价格
     */
    @JsonProperty("priceWithPlan")
    private List<PriceWithPlan> priceWithPlan;

    /**
     * MSRP (建议零售价)
     */
    @JsonProperty("msrp")
    
    private String msrp;

    /**
     * 促销中
     */
    @JsonProperty("onSale")
    private Boolean onSale;

    /**
     * 计划标题
     */
    @JsonProperty("planTitle")
    private String planTitle;

    /**
     * 二手商品
     */
    @JsonProperty("preowned")
    private Boolean preowned;

    /**
     * 会员专享商品
     */
    @JsonProperty("membersOnlyItem")
    private Boolean membersOnlyItem;

    /**
     * 客户最高评价
     */
    @JsonProperty("customerTopRated")
    private Boolean customerTopRated;

    /**
     * 特色产品
     */
    @JsonProperty("features")
    private List<Feature> features;

    /**
     * 包含物品列表
     */
    @JsonProperty("includedItemList")
    private Object includedItemList;

    /**
     * 配送成本
     */
    @JsonProperty("shippingCost")
    
    private String shippingCost;

    /**
     * 配送重量
     */
    @JsonProperty("shippingWeight")
    
    private String shippingWeight;

    /**
     * 保修劳工
     */
    @JsonProperty("warrantyLabor")
    private String warrantyLabor;

    /**
     * 保修零件
     */
    @JsonProperty("warrantyParts")
    private String warrantyParts;

    /**
     * 重量
     */
    @JsonProperty("weight")
    
    private String weight;

    /**
     * 宽度
     */
    @JsonProperty("width")
    
    private String width;

    /**
     * 高度
     */
    @JsonProperty("height")
    
    private String height;

    /**
     * 深度
     */
    @JsonProperty("depth")
    
    private String depth;

    /**
     * 条件
     */
    @JsonProperty("condition")
    private String condition;

    /**
     * 条件ID
     */
    @JsonProperty("conditionId")
    private String conditionId;

    /**
     * 市场
     */
    @JsonProperty("marketplace")
    private Boolean marketplace;

    /**
     * 市场ID
     */
    @JsonProperty("marketplaceId")
    private String marketplaceId;

    /**
     * 类别路径内部类
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class CategoryPath {
        @JsonProperty("id")
        private String id;

        @JsonProperty("name")
        private String name;
    }

    /**
     * 计划价格内部类
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class PriceWithPlan {
        @JsonProperty("planId")
        private String planId;

        @JsonProperty("planTitle")
        private String planTitle;

        @JsonProperty("price")
        private String price;
    }

    /**
     * 特色功能内部类
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Feature {
        @JsonProperty("feature")
        private String feature;

        @JsonProperty("featureTitle")
        private String featureTitle;

        @JsonProperty("featureValue")
        private String featureValue;
    }
}

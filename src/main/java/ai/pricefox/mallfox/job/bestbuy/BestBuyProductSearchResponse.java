package ai.pricefox.mallfox.job.bestbuy;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * BestBuy API 产品搜索响应实体
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class BestBuyProductSearchResponse {

    /**
     * 起始位置
     */
    @JsonProperty("from")
    private Integer from;

    /**
     * 结束位置
     */
    @JsonProperty("to")
    private Integer to;

    /**
     * 当前页
     */
    @JsonProperty("currentPage")
    private Integer currentPage;

    /**
     * 总页数
     */
    @JsonProperty("totalPages")
    private Integer totalPages;

    /**
     * 查询时间
     */
    @JsonProperty("queryTime")
    private String queryTime;

    /**
     * 总数量
     */
    @JsonProperty("total")
    private Integer total;

    /**
     * 部分结果标识
     */
    @JsonProperty("partial")
    private Boolean partial;

    /**
     * 产品列表
     */
    @JsonProperty("products")
    private List<BestBuyProductDetailResponse> products;
}

package ai.pricefox.mallfox.job.bestbuy;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 自定义反序列化器，用于处理带单位的尺寸字符串（如 "0.61 inches"）
 */
public class DimensionDeserializer extends JsonDeserializer<BigDecimal> {
    
    private static final Pattern NUMBER_PATTERN = Pattern.compile("^([0-9]+\\.?[0-9]*)\\s*.*$");
    
    @Override
    public BigDecimal deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
        String value = p.getValueAsString();
        
        if (value == null || value.trim().isEmpty()) {
            return null;
        }
        
        try {
            return new BigDecimal(value);
        } catch (NumberFormatException e) {
            Matcher matcher = NUMBER_PATTERN.matcher(value.trim());
            if (matcher.matches()) {
                return new BigDecimal(matcher.group(1));
            }
            return null;
        }
    }
}
package ai.pricefox.mallfox.job.ebay;

import ai.pricefox.mallfox.config.EbayApiConfig;
import ai.pricefox.mallfox.convert.ebay.EbayConvert;
import ai.pricefox.mallfox.vo.ebay.EbayItemDetailRespVO;
import ai.pricefox.mallfox.vo.ebay.EbaySearchReqVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * eBay API 服务类
 */
@Slf4j
@Component
public class EbayApiService {

    @Autowired
    private EbayApiConfig ebayApiConfig;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private EbayConvert ebayConvert;

    @Autowired
    private EbayOAuthService ebayOAuthService;

    /**
     * 获取 eBay 商品详情
     *
     * @param itemId eBay 商品 ID
     * @return eBay 商品详情
     */
    @Retryable(value = {RuntimeException.class, Exception.class}, maxAttempts = 3, backoff = @Backoff(delay = 1000))
    public EbayItemDetailRespVO getItemDetail(String itemId) {
        // 构建 eBay API URL，格式为: /item/v1|itemId|0

        String url = UriComponentsBuilder.newInstance().scheme("https").host(ebayApiConfig.isSandbox() ? "api.sandbox.ebay.com" : "api.ebay.com").path("/buy/browse/v1/item/{itemId}").buildAndExpand(itemId).toUriString();

        log.info("构建的 eBay API URL: {}", url);

        try {
            // 获取访问令牌并添加认证头
            String accessToken = ebayOAuthService.getAccessToken();

            log.info("访问令牌: {}", accessToken);

            HttpHeaders headers = new HttpHeaders();
            headers.set("Authorization", "Bearer " + accessToken);
            headers.set("Content-Type", "application/json");
            //headers.set("X-EBAY-C-MARKETPLACE-ID", "EBAY_US"); // 默认美国市场
            headers.set("X-EBAY-C-ENDUSERCTX", "contextualLocation=country=US,zip=19406");

            HttpEntity<String> entity = new HttpEntity<>(headers);

            ResponseEntity<EbayItemDetailResponse> response = restTemplate.exchange(url, HttpMethod.GET, entity, EbayItemDetailResponse.class);

            if (response.getBody() == null) {
                throw new RuntimeException("Failed to fetch eBay item details for itemId: " + itemId);
            }

            return ebayConvert.convertItemDetailResponseToRespVO(response.getBody());
        } catch (Exception e) {
            log.error("Error fetching eBay item details for itemId: {}", itemId, e);
            throw new RuntimeException("Failed to fetch eBay item details", e);
        }
    }

    /**
     * 搜索商品（增强版 - 支持完整的 eBay Browse API 搜索功能）
     *
     * @param searchRequest 增强的搜索请求参数
     * @return 搜索结果
     */
    @Retryable(value = {RuntimeException.class, Exception.class}, maxAttempts = 3, backoff = @Backoff(delay = 1000))
    public EbaySearchResponse searchItemsAdvanced(EbaySearchReqVO searchRequest) {
        log.info("开始高级搜索 eBay 商品，关键字: {}", searchRequest.getQuery());

        try {
            // 构建搜索 URL 和参数
            UriComponentsBuilder uriBuilder = UriComponentsBuilder.newInstance().scheme("https").host(ebayApiConfig.isSandbox() ? "api.sandbox.ebay.com" : "api.ebay.com").path("/buy/browse/v1/item_summary/search");

            // 基本搜索参数
            uriBuilder.queryParam("q", searchRequest.getQuery());
            uriBuilder.queryParam("limit", searchRequest.getLimit());
            uriBuilder.queryParam("offset", searchRequest.getOffset());

            // 分类过滤
            if (searchRequest.getCategoryIds() != null && !searchRequest.getCategoryIds().trim().isEmpty()) {
                uriBuilder.queryParam("category_ids", searchRequest.getCategoryIds());
            }

            // 字段组控制 - 默认使用EXTENDED获取详细信息
            String fieldgroups = searchRequest.getFieldgroups();
            if (fieldgroups == null || fieldgroups.trim().isEmpty()) {
                // 默认使用EXTENDED和ADDITIONAL_SELLER_DETAILS获取最详细的信息
                fieldgroups = "EXTENDED,ADDITIONAL_SELLER_DETAILS";
                log.debug("使用默认fieldgroups: {}", fieldgroups);
            }
            uriBuilder.queryParam("fieldgroups", fieldgroups);

            // 构建过滤器字符串
            String filterString = searchRequest.buildFilterString();
            if (!filterString.isEmpty()) {
                uriBuilder.queryParam("filter", filterString);
                log.debug("应用过滤器: {}", filterString);
            }

            // 商品特征过滤
            String aspectFilter = searchRequest.buildAspectFilter();
            if (!aspectFilter.isEmpty()) {
                uriBuilder.queryParam("aspect_filter", aspectFilter);
                log.debug("应用特征过滤器: {}", aspectFilter);
            }

            // 排序选项
            if (searchRequest.getSort() != null && !searchRequest.getSort().trim().isEmpty()) {
                uriBuilder.queryParam("sort", searchRequest.getSort());
            }

            // GTIN 搜索
            if (searchRequest.getGtin() != null && !searchRequest.getGtin().trim().isEmpty()) {
                uriBuilder.queryParam("gtin", searchRequest.getGtin());
            }

            // eBay 产品ID搜索
            if (searchRequest.getEpid() != null && !searchRequest.getEpid().trim().isEmpty()) {
                uriBuilder.queryParam("epid", searchRequest.getEpid());
            }

            // 慈善机构ID
            if (searchRequest.getCharityId() != null && !searchRequest.getCharityId().trim().isEmpty()) {
                uriBuilder.queryParam("charity_id", searchRequest.getCharityId());
            }

            // 兼容性过滤
            if (searchRequest.getCompatibilityFilter() != null && !searchRequest.getCompatibilityFilter().trim().isEmpty()) {
                uriBuilder.queryParam("compatibility_filter", searchRequest.getCompatibilityFilter());
            }

            String finalUrl = uriBuilder.toUriString();
            log.info("构建的增强 eBay 搜索 URL: {}", finalUrl);

            // 获取访问令牌并添加认证头
            String accessToken = ebayOAuthService.getAccessToken();

            HttpHeaders headers = new HttpHeaders();
            headers.set("Authorization", "Bearer " + accessToken);
            headers.set("Content-Type", "application/json");

            // 设置市场和用户上下文
            headers.set("X-EBAY-C-MARKETPLACE-ID", searchRequest.getMarketplaceId());

            // 构建用户上下文（可扩展支持更多地理位置信息）
            StringBuilder contextBuilder = new StringBuilder();
            if (searchRequest.getDeliveryCountry() != null) {
                contextBuilder.append("contextualLocation=country=").append(searchRequest.getDeliveryCountry());
            } else {
                contextBuilder.append("contextualLocation=country=US,zip=19406");
            }
            headers.set("X-EBAY-C-ENDUSERCTX", contextBuilder.toString());

            HttpEntity<String> entity = new HttpEntity<>(headers);

            ResponseEntity<EbaySearchResponse> response = restTemplate.exchange(finalUrl, HttpMethod.GET, entity, EbaySearchResponse.class);

            if (response.getBody() == null) {
                throw new RuntimeException("Failed to search eBay items for advanced query: " + searchRequest.getQuery());
            }

            log.info("高级搜索完成，关键字: {}, 返回结果数: {}", searchRequest.getQuery(), response.getBody().getItemSummaries() != null ? response.getBody().getItemSummaries().size() : 0);

            return response.getBody();

        } catch (Exception e) {
            log.error("高级搜索 eBay 商品失败，关键字: {}", searchRequest.getQuery(), e);
            throw new RuntimeException("Failed to perform advanced eBay search", e);
        }
    }

    /**
     * 获取商品项目组信息
     *
     * @param itemGroupId 商品组 ID
     * @return 商品项目组信息
     */
    @Retryable(value = {Exception.class}, maxAttempts = 3, backoff = @Backoff(delay = 1000))
    public EbayItemGroupResponse getItemsByItemGroup(String itemGroupId) {
        try {
            log.info("开始获取 eBay 商品项目组信息，itemGroupId: {}", itemGroupId);

            String url = UriComponentsBuilder.fromHttpUrl(ebayApiConfig.getBaseUrl() + "/item/get_items_by_item_group").queryParam("item_group_id", itemGroupId).toUriString();

            HttpHeaders headers = new HttpHeaders();
            headers.set("Authorization", "Bearer " + ebayOAuthService.getAccessToken());
            headers.set("Content-Type", "application/json");
            headers.set("X-EBAY-C-MARKETPLACE-ID", "EBAY_US");
            // eBay合作伙伴返佣和运输信息标头示例（此标头可提高预计送达时间信息的准确性）
            // 返佣链接示例：https://www.ebay.com/itm/2021-New-RC-Drone-4k-HD-Wide-Angle-Camera-WIFI-FPV-Drone-Dual-Camera-Quadcopter/
            //2*********5?hash=item4************5&mkevt=1&mkcid=1&mkrid=711-53200-19255-0& campid =1********E&
            // customid =2************F&toolid=10001
//            headers.set("X-EBAY-C-ENDUSERCTX", "affiliateCampaignId=ePNCampaignId,affiliateReferenceId=referenceId," +
//                    "contextualLocation=country%3DUS%2Czip%3D19406");

            HttpEntity<String> entity = new HttpEntity<>(headers);

            log.debug("请求 URL: {}", url);
            ResponseEntity<EbayItemGroupResponse> response = restTemplate.exchange(url, HttpMethod.GET, entity, EbayItemGroupResponse.class);

            log.info("成功获取 eBay 商品项目组信息，itemGroupId: {}, 商品数量: {}", itemGroupId, response.getBody() != null && response.getBody().getItems() != null ? response.getBody().getItems().size() : 0);

            return response.getBody();

        } catch (Exception e) {
            log.error("获取 eBay 商品项目组信息失败，itemGroupId: {}", itemGroupId, e);
            throw new RuntimeException("Failed to fetch eBay item group details", e);
        }
    }

    /**
     * 通过传统ID获取商品详情
     * 对应 eBay Browse API 的 getItemByLegacyId 接口
     *
     * @param legacyItemId                传统商品ID
     * @param legacyVariationId           传统变体ID（可选）
     * @param legacyVariationSku          传统变体SKU（可选）
     * @param fieldgroups                 字段组（可选）
     * @param quantityForShippingEstimate 配送估算数量（可选）
     * @return eBay商品详情响应
     */
    @Retryable(value = {RuntimeException.class, Exception.class}, maxAttempts = 3, backoff = @Backoff(delay = 1000))
    public EbayItemDetailRespVO getItemByLegacyId(String legacyItemId, String legacyVariationId, String legacyVariationSku, String fieldgroups, Integer quantityForShippingEstimate) {
        try {
            log.info("开始通过传统ID获取 eBay 商品详情，legacyItemId: {}", legacyItemId);

            // 构建请求URL
            UriComponentsBuilder uriBuilder = UriComponentsBuilder.fromHttpUrl(ebayApiConfig.getBaseUrl() + "/item/get_item_by_legacy_id").queryParam("legacy_item_id", legacyItemId);

            // 添加可选参数
            if (legacyVariationId != null && !legacyVariationId.trim().isEmpty()) {
                uriBuilder.queryParam("legacy_variation_id", legacyVariationId);
            }

            if (legacyVariationSku != null && !legacyVariationSku.trim().isEmpty()) {
                uriBuilder.queryParam("legacy_variation_sku", legacyVariationSku);
            }

            if (fieldgroups != null && !fieldgroups.trim().isEmpty()) {
                uriBuilder.queryParam("fieldgroups", fieldgroups);
            }

            if (quantityForShippingEstimate != null && quantityForShippingEstimate > 0) {
                uriBuilder.queryParam("quantity_for_shipping_estimate", quantityForShippingEstimate);
            }

            String url = uriBuilder.toUriString();
            log.info("调用 eBay getItemByLegacyId API: {}", url);

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.set("Authorization", "Bearer " + ebayOAuthService.getAccessToken());
            headers.set("Content-Type", "application/json");
            headers.set("X-EBAY-C-MARKETPLACE-ID", "EBAY_US");

            HttpEntity<String> entity = new HttpEntity<>(headers);

            // 发送请求
            ResponseEntity<EbayItemDetailResponse> response = restTemplate.exchange(url, HttpMethod.GET, entity, EbayItemDetailResponse.class);

            if (response.getBody() == null) {
                throw new RuntimeException("Failed to fetch eBay item details for legacyItemId: " + legacyItemId);
            }

            log.info("成功通过传统ID获取 eBay 商品详情，legacyItemId: {}", legacyItemId);

            // 转换为响应VO
            return ebayConvert.convertItemDetailResponseToRespVO(response.getBody());

        } catch (Exception e) {
            log.error("通过传统ID获取 eBay 商品详情失败，legacyItemId: {}", legacyItemId, e);
            throw new RuntimeException("Failed to fetch eBay item details by legacy ID", e);
        }
    }
}

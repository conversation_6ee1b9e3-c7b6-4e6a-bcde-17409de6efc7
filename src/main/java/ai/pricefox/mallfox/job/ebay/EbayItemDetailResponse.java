package ai.pricefox.mallfox.job.ebay;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * eBay 商品详情 API 响应类
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class EbayItemDetailResponse {
    
    private String itemId;
    private String sellerItemRevision;
    private String title;
    private String shortDescription;
    private String description;
    private Price price;
    private String categoryPath;
    private String categoryIdPath;
    private String categoryId;
    private String condition;
    private String conditionId;
    private ItemLocation itemLocation;
    private Image image;
    private String brand;
    private String mpn;
    private Seller seller;
    private List<EstimatedAvailability> estimatedAvailabilities;
    private List<ShippingOption> shippingOptions;
    private ShipToLocations shipToLocations;
    private ReturnTerms returnTerms;
    private List<Tax> taxes;
    private List<LocalizedAspect> localizedAspects;
    private PrimaryProductReviewRating primaryProductReviewRating;
    private Boolean priorityListing;
    private Boolean topRatedBuyingExperience;
    private List<String> buyingOptions;
    private String itemWebUrl;
    private List<PaymentMethod> paymentMethods;
    private Boolean immediatePay;
    private Boolean enabledForGuestCheckout;
    private Boolean eligibleForInlineCheckout;
    private Integer lotSize;
    private String legacyItemId;
    private Boolean adultOnly;

    // 基于实际 API 返回数据补充的顶级字段
    private String color;           // 颜色
    private String material;        // 材质
    private String pattern;         // 图案
    private String sizeType;        // 尺寸类型
    private PrimaryItemGroup primaryItemGroup;  // 主要商品组信息

    // 补充的字段 - 基于实际返回数据
    private List<Image> additionalImages;  // 额外图片列表
    private String itemEndDate;  // 商品结束日期
    private String itemCreationDate;  // 商品创建日期
    private String listingMarketplaceId;  // 市场ID
    private List<AvailableCoupon> availableCoupons;  // 可用优惠券列表
    private String watchCount;  // 关注数量
    private String bidCount;  // 出价次数
    private Price currentBidPrice;  // 当前出价
    private Boolean reserveMet;  // 是否达到保留价
    private String highBidder;  // 最高出价者
    private String itemAffiliateWebUrl;  // 联盟链接
    private List<String> thumbnailImages;  // 缩略图列表

    // 基于eBay官方文档补充的缺失字段
    private String gtin;  // 全球贸易项目编号
    private Product product;  // 产品信息
    private String subtitle;  // 副标题
    private String ageGroup;  // 年龄组
    private String gender;  // 性别
    private String epid;  // eBay产品ID
    private String inferredEpid;  // 推断的eBay产品ID
    private String conditionDescription;  // 状态描述
    private List<ConditionDescriptor> conditionDescriptors;  // 状态描述符
    private String energyEfficiencyClass;  // 能效等级
    private String tyreLabelImageUrl;  // 轮胎标签图片URL
    private ConvertedAmount unitPrice;  // 单价
    private String unitPricingMeasure;  // 单价计量单位
    private ConvertedAmount ecoParticipationFee;  // 环保参与费
    private HazardousMaterialsLabels hazardousMaterialsLabels;  // 危险材料标签
    private List<AddonService> addonServices;  // 附加服务
    private AuthenticityGuaranteeProgram authenticityGuarantee;  // 真品保证
    private AuthenticityVerificationProgram authenticityVerification;  // 真品验证
    private Integer uniqueBidderCount;  // 独特竞拍者数量

    // 营销和促销相关字段
    private MarketingPrice marketingPrice;  // 营销价格
    private List<String> qualifiedPrograms;  // 符合的项目
    private Boolean fastNHandling;  // 快速处理
    private String handlingTime;  // 处理时间
    private String conditionDisplayName;  // 状态显示名称

    // 补充可能缺失的字段
    private String itemGroupId;  // 商品组ID
    private String parentItemId;  // 父商品ID
    private String variationId;  // 变体ID
    private List<ItemVariation> itemVariations;  // 商品变体列表
    private String itemGroupType;  // 商品组类型
    private Boolean hasVariations;  // 是否有变体
    private String defaultVariationId;  // 默认变体ID
    private List<VariationAttribute> variationAttributes;  // 变体属性
    private String itemSpecifics;  // 商品规格
    private List<String> compatibilityProperties;  // 兼容性属性
    private String itemConditionDescription;  // 商品状态详细描述
    private String sellerNotes;  // 卖家备注
    private List<Warning> warnings;  // 警告信息
    private List<String> disclaimers;  // 免责声明

    // 从 LocalizedAspects 中提取的独立字段
    private String aspectColor;  // 颜色
    private String aspectProcessor;  // 处理器
    private String aspectScreenSize;  // 屏幕尺寸
    private String aspectMemoryCardType;  // 内存卡类型
    private String aspectInternetConnectivity;  // 网络连接
    private String aspectLockStatus;  // 锁定状态
    private String aspectModelNumber;  // 型号
    private String aspectSimCardSlot;  // SIM卡槽
    private String aspectColour;  // 颜色（另一个字段）
    private String aspectBrand;  // 品牌
    private String aspectConditionGrade;  // 状态/等级
    private String aspectNetwork;  // 网络
    private String aspectModel;  // 型号
    private String aspectConnectivity;  // 连接性
    private String aspectStyle;  // 样式
    private String aspectFeatures;  // 特性
    private String aspectOperatingSystem;  // 操作系统
    private String aspectStorageCapacity;  // 存储容量
    private String aspectContract;  // 合约
    private String aspectRam;  // 内存

    @Data
    public static class Price {
        private Double value;  // 修复：实际返回数字类型
        private String currency;
    }

    @Data
    public static class ConvertedAmount {
        private String value;
        private String currency;
        private String convertedFromValue;
        private String convertedFromCurrency;
    }

    @Data
    public static class Product {
        private List<AdditionalProductIdentity> additionalProductIdentities;
    }

    @Data
    public static class AdditionalProductIdentity {
        private ProductIdentity productIdentity;
    }

    @Data
    public static class ProductIdentity {
        private String identifierType;
        private String identifierValue;
    }

    @Data
    public static class ConditionDescriptor {
        private String name;
        private List<ConditionDescriptorValue> values;
    }

    @Data
    public static class ConditionDescriptorValue {
        private String content;
        private List<String> additionalInfo;
    }

    @Data
    public static class HazardousMaterialsLabels {
        private String signalWord;
        private String signalWordId;
        private String additionalInformation;
        private List<HazardPictogram> pictograms;
        private List<HazardStatement> statements;
    }

    @Data
    public static class HazardPictogram {
        private String pictogramId;
        private String pictogramDescription;
        private String pictogramUrl;
    }

    @Data
    public static class HazardStatement {
        private String statementId;
        private String statementDescription;
    }

    @Data
    public static class AddonService {
        private String serviceId;
        private String serviceType;
        private String selection;
        private ConvertedAmount serviceFee;
    }

    @Data
    public static class AuthenticityGuaranteeProgram {
        private String description;
        private String termsWebUrl;
    }

    @Data
    public static class AuthenticityVerificationProgram {
        private String description;
        private String termsWebUrl;
    }

    @Data
    public static class AvailableCoupon {
        private String redemptionCode;
        private String discountType;
        private ConvertedAmount discountAmount;
        private String message;
        private String termsWebUrl;
        private CouponConstraint constraint;
    }

    @Data
    public static class CouponConstraint {
        private String expirationDate;
    }

    @Data
    public static class ItemLocation {
        private String city;
        private String stateOrProvince;
        private String postalCode;
        private String country;
    }

    @Data
    public static class Image {
        private String imageUrl;
        private Integer height;
        private Integer width;
    }

    @Data
    public static class Seller {
        private String username;
        private String userId;  // 卖家用户ID（使用ADDITIONAL_SELLER_DETAILS字段组时返回）
        private Double feedbackPercentage;  // 修复：实际返回数字类型
        private Integer feedbackScore;
    }

    @Data
    public static class EstimatedAvailability {
        private List<String> deliveryOptions;
        private String estimatedAvailabilityStatus;
        private Integer estimatedAvailableQuantity;
        private Integer estimatedSoldQuantity;
        private Integer estimatedRemainingQuantity;  // 预计剩余数量
        private Integer availabilityThreshold;  // 库存阈值
        private String availabilityThresholdType;  // 阈值类型
    }

    @Data
    public static class ShippingOption {
        private String shippingServiceCode;
        private String shippingCarrierCode;  // 配送承运商代码
        private String trademarkSymbol;  // 商标符号
        private String type;
        private ConvertedAmount shippingCost;
        private Integer quantityUsedForEstimate;
        private String minEstimatedDeliveryDate;
        private String maxEstimatedDeliveryDate;
        private ConvertedAmount additionalShippingCostPerUnit;
        private String shippingCostType;
        private ShipToLocationUsedForEstimate shipToLocationUsedForEstimate;
    }

    @Data
    public static class ShipToLocationUsedForEstimate {
        private String country;
        private String postalCode;
    }

    @Data
    public static class ShipToLocations {
        private List<Region> regionIncluded;
        private List<Region> regionExcluded;
    }

    @Data
    public static class Region {
        private String regionName;
        private String regionType;
        private String regionId;
    }

    @Data
    public static class ReturnTerms {
        private Boolean returnsAccepted;
        private String refundMethod;
        private String returnMethod;  // 退货方式
        private String returnShippingCostPayer;
        private ReturnPeriod returnPeriod;
    }

    @Data
    public static class ReturnPeriod {
        private Integer value;
        private String unit;
    }

    @Data
    public static class Tax {
        private TaxJurisdiction taxJurisdiction;
        private String taxType;
        private Double taxPercentage;  // 修复：实际返回数字类型
        private Boolean shippingAndHandlingTaxed;
        private Boolean includedInPrice;
        private Boolean ebayCollectAndRemitTax;
    }

    @Data
    public static class TaxJurisdiction {
        private Region region;
        private String taxJurisdictionId;
    }

    @Data
    public static class LocalizedAspect {
        private String type;
        private String name;
        private String value;

        // 补充字段
        private String localizedName;  // 本地化名称
        private List<String> valueList;  // 值列表（用于多值属性）
        private String unit;  // 单位
        private Boolean required;  // 是否必填
        private String displaySequence;  // 显示顺序
    }

    @Data
    public static class PrimaryProductReviewRating {
        private Integer reviewCount;
        private String averageRating;
        private List<RatingHistogram> ratingHistograms;
    }

    @Data
    public static class RatingHistogram {
        private String rating;
        private Integer count;
    }

    @Data
    public static class PaymentMethod {
        private String paymentMethodType;
        private List<PaymentMethodBrand> paymentMethodBrands;
    }

    @Data
    public static class PaymentMethodBrand {
        private String paymentMethodBrandType;
    }

    @Data
    public static class MarketingPrice {
        private Price originalPrice;  // 原价
        private String discountPercentage;  // 折扣百分比
        private Price discountAmount;  // 折扣金额
        private String priceTreatment;  // 价格处理方式
        private String priceDisplayCondition;  // 价格显示条件
    }

    @Data
    public static class ItemVariation {
        private String variationId;  // 变体ID
        private String variationValue;  // 变体值
        private List<VariationAttribute> variationAttributes;  // 变体属性
        private Price variationPrice;  // 变体价格
        private String variationImageUrl;  // 变体图片
        private Integer variationQuantity;  // 变体库存
        private String variationSku;  // 变体SKU
    }

    @Data
    public static class VariationAttribute {
        private String attributeName;  // 属性名称
        private String attributeValue;  // 属性值
        private String attributeType;  // 属性类型
        private String attributeUnit;  // 属性单位
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Warning {
        private String message;  // 警告消息
        private String code;  // 警告代码
        private String category;  // 警告分类
        private String severity;  // 严重程度
        private String domain;  // 域
        private String subdomain;  // 子域
        private String longMessage;  // 详细消息
        private String errorId;  // 错误ID
        private List<String> inputRefIds;  // 输入引用ID
        private List<String> outputRefIds;  // 输出引用ID
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class PrimaryItemGroup {
        private String itemGroupId;  // 商品组ID
        private String itemGroupType;  // 商品组类型
        private String itemGroupHref;  // 商品组链接
        private String itemGroupTitle;  // 商品组标题
        private Image itemGroupImage;  // 商品组主图
        private List<Image> itemGroupAdditionalImages;  // 商品组额外图片
    }
}

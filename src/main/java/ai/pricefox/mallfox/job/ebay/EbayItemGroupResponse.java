package ai.pricefox.mallfox.job.ebay;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.List;

/**
 * eBay 商品项目组 API 响应类
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class EbayItemGroupResponse {

    private List<EbayItemDetailResponse> items;  // 商品列表
    private List<CommonDescription> commonDescriptions;  // 通用描述

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class CommonDescription {
        private String description;  // 描述内容
        private List<String> itemIds;  // 关联的商品ID列表
    }
}

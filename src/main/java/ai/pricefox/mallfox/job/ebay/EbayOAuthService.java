package ai.pricefox.mallfox.job.ebay;

import ai.pricefox.mallfox.config.EbayApiConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.*;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.concurrent.TimeUnit;
import ai.pricefox.mallfox.common.constant.RedisKeyConstants;

/**
 * eBay OAuth 认证服务类
 */
@Slf4j
@Component
public class EbayOAuthService {

    private static final String OAUTH_TOKEN_URL_SANDBOX = "https://api.sandbox.ebay.com/identity/v1/oauth2/token";
    private static final String OAUTH_TOKEN_URL_PRODUCTION = "https://api.ebay.com/identity/v1/oauth2/token";
    private static final String REDIS_TOKEN_KEY = RedisKeyConstants.EBAY_OAUTH_TOKEN;
    private static final String REDIS_REFRESH_TOKEN_KEY = RedisKeyConstants.EBAY_OAUTH_REFRESH_TOKEN;

    // 添加内存缓存和同步锁
    private volatile String memoryToken;
    private volatile long tokenExpiryTime;
    private final Object tokenLock = new Object();

    @Autowired
    private EbayApiConfig ebayApiConfig;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 获取访问令牌（客户端凭据模式）- 优化版本
     *
     * @return 访问令牌
     */
    @Retryable(
            value = {RuntimeException.class, Exception.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000)
    )
    public String getAccessToken() {
        // 1. 先检查内存缓存
        if (memoryToken != null && System.currentTimeMillis() < tokenExpiryTime) {
            log.debug("🚀 从内存缓存中获取到 eBay 访问令牌");
            return memoryToken;
        }

        // 2. 使用同步锁避免并发获取token
        synchronized (tokenLock) {
            // 双重检查，可能其他线程已经获取了新token
            if (memoryToken != null && System.currentTimeMillis() < tokenExpiryTime) {
                log.debug("🚀 从内存缓存中获取到 eBay 访问令牌 (双重检查)");
                return memoryToken;
            }

            // 3. 检查Redis缓存
//            String cachedToken = getCachedToken();
//            if (cachedToken != null) {
//                log.debug("📦 从Redis缓存中获取到 eBay 访问令牌");
//                // 更新内存缓存
//                memoryToken = cachedToken;
//                tokenExpiryTime = System.currentTimeMillis() + (7200 - 60) * 1000; // 提前60秒过期
//                return cachedToken;
//            }

            // 4. 缓存中都没有，智能刷新token
            log.info("🔄 缓存中没有有效token，开始智能刷新");
            return smartRefreshToken();
        }
    }

    /**
     * 智能刷新token：优先使用refresh token，失败后降级到client_credentials
     *
     * @return 新的访问令牌
     */
    private String smartRefreshToken() {
        // 1. 先尝试使用refresh token刷新
        String cachedRefreshToken = getCachedRefreshToken();
        if (cachedRefreshToken != null && !cachedRefreshToken.trim().isEmpty()) {
            log.info("🔄 发现缓存的refresh token，尝试使用refresh token刷新");
            try {
                String newToken = tryRefreshWithRefreshToken(cachedRefreshToken);
                if (newToken != null) {
                    // 更新内存缓存
                    memoryToken = newToken;
                    tokenExpiryTime = System.currentTimeMillis() + (7200 - 60) * 1000; // 提前60秒过期
                    log.info("✅ 成功使用refresh token获取新的access token");
                    return newToken;
                }
            } catch (Exception e) {
                log.warn("⚠️ 使用refresh token刷新失败，将降级到client_credentials模式: {}", e.getMessage());
            }
        } else {
            log.info("📭 没有可用的refresh token，直接使用client_credentials模式");
        }

        // 2. 降级到client_credentials模式
        log.info("🔄 使用client_credentials模式重新获取token");
        return refreshAccessToken();
    }

    /**
     * 尝试使用refresh token刷新，包装异常处理
     *
     * @param refreshToken 刷新令牌
     * @return 新的访问令牌，失败时返回null
     */
    private String tryRefreshWithRefreshToken(String refreshToken) {
        try {
            return refreshTokenWithRefreshToken(refreshToken);
        } catch (Exception e) {
            log.warn("使用refresh token刷新失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 刷新访问令牌（使用client_credentials模式）
     *
     * @return 新的访问令牌
     */
    public String refreshAccessToken() {
        try {
            log.info("开始获取 eBay OAuth 访问令牌");

            // 构建请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
            headers.set("Authorization", "Basic " + generateBasicAuthHeader());

            // 构建请求体
            MultiValueMap<String, String> requestBody = new LinkedMultiValueMap<>();
            requestBody.add("grant_type", "client_credentials");
            requestBody.add("scope", ebayApiConfig.getScope());

            HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(requestBody, headers);

            // 发送请求
            String tokenUrl = ebayApiConfig.isSandbox() ? OAUTH_TOKEN_URL_SANDBOX : OAUTH_TOKEN_URL_PRODUCTION;
            ResponseEntity<EbayOAuthTokenResponse> response = restTemplate.postForEntity(
                    tokenUrl, request, EbayOAuthTokenResponse.class);

            if (response.getBody() == null) {
                throw new RuntimeException("eBay OAuth 响应为空");
            }

            EbayOAuthTokenResponse tokenResponse = response.getBody();

            if (tokenResponse.getError() != null) {
                throw new RuntimeException("eBay OAuth 错误: " + tokenResponse.getError() + 
                        " - " + tokenResponse.getErrorDescription());
            }

            if (tokenResponse.getAccessToken() == null) {
                throw new RuntimeException("eBay OAuth 响应中没有访问令牌");
            }

            // 缓存令牌
            cacheToken(tokenResponse);

            // 更新内存缓存
            memoryToken = tokenResponse.getAccessToken();
            tokenExpiryTime = System.currentTimeMillis() + (tokenResponse.getExpiresIn() - 60) * 1000;

            log.info("✅ 成功获取 eBay OAuth 访问令牌，类型: {}, 过期时间: {} 秒",
                    tokenResponse.getTokenType(), tokenResponse.getExpiresIn());

            return tokenResponse.getAccessToken();

        } catch (Exception e) {
            log.error("获取 eBay OAuth 访问令牌失败", e);
            throw new RuntimeException("获取 eBay OAuth 访问令牌失败: " + e.getMessage(), e);
        }
    }

    /**
     * 使用刷新令牌获取新的访问令牌
     *
     * @param refreshToken 刷新令牌
     * @return 新的访问令牌
     */
    public String refreshTokenWithRefreshToken(String refreshToken) {
        try {
            log.info("使用刷新令牌获取新的 eBay 访问令牌");

            // 构建请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
            headers.set("Authorization", "Basic " + generateBasicAuthHeader());

            // 构建请求体
            MultiValueMap<String, String> requestBody = new LinkedMultiValueMap<>();
            requestBody.add("grant_type", "refresh_token");
            requestBody.add("refresh_token", refreshToken);
            requestBody.add("scope", ebayApiConfig.getScope());

            HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(requestBody, headers);

            // 发送请求
            String tokenUrl = ebayApiConfig.isSandbox() ? OAUTH_TOKEN_URL_SANDBOX : OAUTH_TOKEN_URL_PRODUCTION;
            ResponseEntity<EbayOAuthTokenResponse> response = restTemplate.postForEntity(
                    tokenUrl, request, EbayOAuthTokenResponse.class);

            if (response.getBody() == null) {
                throw new RuntimeException("eBay OAuth 刷新响应为空");
            }

            EbayOAuthTokenResponse tokenResponse = response.getBody();

            if (tokenResponse.getError() != null) {
                throw new RuntimeException("eBay OAuth 刷新错误: " + tokenResponse.getError() + 
                        " - " + tokenResponse.getErrorDescription());
            }

            // 缓存新令牌
            cacheToken(tokenResponse);

            // 更新内存缓存
            memoryToken = tokenResponse.getAccessToken();
            tokenExpiryTime = System.currentTimeMillis() + (tokenResponse.getExpiresIn() - 60) * 1000;

            log.info("✅ 成功使用刷新令牌获取新的 eBay 访问令牌，类型: {}, 过期时间: {} 秒",
                    tokenResponse.getTokenType(), tokenResponse.getExpiresIn());
            return tokenResponse.getAccessToken();

        } catch (Exception e) {
            log.error("使用刷新令牌获取 eBay 访问令牌失败", e);
            throw new RuntimeException("刷新 eBay 访问令牌失败: " + e.getMessage(), e);
        }
    }

    /**
     * 生成 Basic 认证头
     *
     * @return Base64 编码的认证字符串
     */
    private String generateBasicAuthHeader() {
        String credentials =  ebayApiConfig.getCurrentClientId() + ":" + ebayApiConfig.getCurrentClientSecret();
        return Base64.getEncoder().encodeToString(credentials.getBytes(StandardCharsets.UTF_8));
    }

    /**
     * 从缓存中获取令牌
     *
     * @return 缓存的令牌，如果不存在或已过期则返回 null
     */
    private String getCachedToken() {
        try {
            Object token = redisTemplate.opsForValue().get(REDIS_TOKEN_KEY);
            return token != null ? token.toString() : null;
        } catch (Exception e) {
            log.warn("从缓存获取 eBay 令牌失败", e);
            return null;
        }
    }

    /**
     * 缓存令牌
     *
     * @param tokenResponse OAuth 响应
     */
    private void cacheToken(EbayOAuthTokenResponse tokenResponse) {
        try {
            // 缓存访问令牌，提前 60 秒过期以避免边界情况
            int cacheExpiresIn = Math.max(tokenResponse.getExpiresIn() - 60, 60);
            redisTemplate.opsForValue().set(REDIS_TOKEN_KEY, tokenResponse.getAccessToken(), 
                    cacheExpiresIn, TimeUnit.SECONDS);

            // 如果有刷新令牌，也缓存起来
            if (tokenResponse.getRefreshToken() != null) {
                redisTemplate.opsForValue().set(REDIS_REFRESH_TOKEN_KEY, tokenResponse.getRefreshToken(), 
                        30, TimeUnit.DAYS); // 刷新令牌通常有效期较长
            }

            log.debug("eBay 令牌已缓存，过期时间: {} 秒", cacheExpiresIn);
        } catch (Exception e) {
            log.warn("缓存 eBay 令牌失败", e);
        }
    }

    /**
     * 获取缓存的刷新令牌
     *
     * @return 刷新令牌
     */
    public String getCachedRefreshToken() {
        try {
            Object refreshToken = redisTemplate.opsForValue().get(REDIS_REFRESH_TOKEN_KEY);
            return refreshToken != null ? refreshToken.toString() : null;
        } catch (Exception e) {
            log.warn("从缓存获取 eBay 刷新令牌失败", e);
            return null;
        }
    }

    /**
     * 清除缓存的令牌（包括内存缓存和Redis缓存）
     */
    public void clearCachedTokens() {
        try {
            // 清除Redis缓存
            redisTemplate.delete(REDIS_TOKEN_KEY);
            redisTemplate.delete(REDIS_REFRESH_TOKEN_KEY);

            // 清除内存缓存
            synchronized (tokenLock) {
                memoryToken = null;
                tokenExpiryTime = 0;
            }

            log.info("🧹 已清除所有缓存的 eBay 令牌（内存+Redis）");
        } catch (Exception e) {
            log.warn("清除缓存的 eBay 令牌失败", e);
        }
    }

    /**
     * 检查令牌是否有效
     *
     * @return 令牌是否有效
     */
    public boolean isTokenValid() {
        return getCachedToken() != null;
    }
}

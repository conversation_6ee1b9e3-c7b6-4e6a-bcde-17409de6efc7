package ai.pricefox.mallfox.job.ebay;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * eBay OAuth Token 响应类
 */
@Data
public class EbayOAuthTokenResponse {

    /**
     * 访问令牌
     */
    @JsonProperty("access_token")
    private String accessToken;

    /**
     * 令牌类型
     */
    @JsonProperty("token_type")
    private String tokenType;

    /**
     * 过期时间（秒）
     */
    @JsonProperty("expires_in")
    private Integer expiresIn;

    /**
     * 刷新令牌（仅在某些授权类型中返回）
     */
    @JsonProperty("refresh_token")
    private String refreshToken;

    /**
     * 授权范围
     */
    @JsonProperty("scope")
    private String scope;

    /**
     * 错误代码（如果请求失败）
     */
    @JsonProperty("error")
    private String error;

    /**
     * 错误描述（如果请求失败）
     */
    @JsonProperty("error_description")
    private String errorDescription;
}

package ai.pricefox.mallfox.job.ebay;

import ai.pricefox.mallfox.enums.DataChannelEnum;
import ai.pricefox.mallfox.enums.ProductPlatformEnum;
import lombok.Data;

import java.util.List;

/**
 * eBay 搜索响应类
 */
@Data
public class EbaySearchResponse {

    private String href;
    private Integer total;
    private String next;
    private String prev;
    private Integer limit;
    private Integer offset;
    private List<ItemSummary> itemSummaries;
    private List<Warning> warnings;
    
    @Data
    public static class ItemSummary {
        private String itemId;
        private String title;
        private ProductPlatformEnum sourcePlatform = ProductPlatformEnum.BESTBUY;
        private DataChannelEnum dataChannel = DataChannelEnum.API;
        private Boolean selfOperated = true;
        // EXTENDED fieldgroups 新增字段
        private String shortDescription;  // 商品简短描述，包含条件和商品特征信息
        
        private List<String> leafCategoryIds;
        private List<Category> categories;
        private Image image;
        private Price price;
        private String itemHref;
        private Seller seller;
        private MarketingPrice marketingPrice;
        private String condition;
        private String conditionId;
        private List<Image> thumbnailImages;
        private List<ShippingOption> shippingOptions;
        private List<String> buyingOptions;
        private String itemAffiliateWebUrl;
        private String itemWebUrl;
        private ItemLocation itemLocation;
        private List<Image> additionalImages;
        private Boolean adultOnly;
        private String legacyItemId;
        private Boolean availableCoupons;
        private String itemCreationDate;
        private String itemOriginDate;
        private Boolean topRatedBuyingExperience;
        private Boolean priorityListing;
        private String listingMarketplaceId;
        private String itemEndDate;
        private String itemGroupHref;
        private String itemGroupType;
        private String watchCount;
        private String bidCount;
        private Price currentBidPrice;
        private String reserveMet;
        private String highBidder;
        
        // 额外的商品详细信息
        private String unitPrice;                    // 单价信息
        private UnitPricingMeasure unitPricingMeasure; // 单价计量单位
        private List<String> qualifiedPrograms;      // 符合的程序（如快速发货等）
        private Boolean authenticityGuarantee;       // 真品保障
        private Boolean authenticityVerification;    // 真品验证
        private List<AvailableCoupon> availableCouponsList; // 可用优惠券列表
        private String energyEfficiencyClass;        // 能效等级
        private List<Hazmat> hazmatItems;            // 危险品信息
        private String lotSize;                      // 批次大小
        private List<PickupOption> pickupOptions;    // 自提选项
        private ReturnTerms returnTerms;             // 退货条款
        private Boolean fastAndFreeShipping;         // 快速免费配送
        private TaxInfo taxes;                       // 税务信息
        private String estimatedAvailabilities;      // 预计可用性
        private String categoryPath;                 // 分类路径
        private List<Product> products;              // 产品信息
        private CompatibilityMatch compatibilityMatch; // 兼容性匹配
    }
    
    @Data
    public static class Category {
        private String categoryId;
        private String categoryName;
    }
    
    @Data
    public static class Image {
        private String imageUrl;
    }
    
    @Data
    public static class Price {
        private String value;
        private String currency;
    }
    
    @Data
    public static class Seller {
        private String username;
        private String feedbackPercentage;
        private Integer feedbackScore;
        
        // ADDITIONAL_SELLER_DETAILS fieldgroups 新增字段
        private String userId;                    // 卖家用户ID，即使用户名变更也保持一致
        private String sellerAccountType;         // 卖家账户类型
        private Boolean topRatedSeller;          // 是否为顶级卖家
        private String feedbackRatingStar;       // 反馈评级星级
        private Integer positiveFeedbackPercent; // 正面反馈百分比
    }
    
    @Data
    public static class MarketingPrice {
        private Price originalPrice;
        private String discountPercentage;
        private Price discountAmount;
        private String priceTreatment;
    }
    
    @Data
    public static class ShippingOption {
        private String shippingCostType;
        private Price shippingCost;
        private String minEstimatedDeliveryDate;
        private String maxEstimatedDeliveryDate;
        private String guaranteedDelivery;
    }
    
    @Data
    public static class ItemLocation {
        private String postalCode;
        private String country;
        private String city;           // EXTENDED fieldgroups 会返回此字段
        private String stateOrProvince;
        private String countyName;     // 县名
        private String addressLine1;   // 地址行1
        private String addressLine2;   // 地址行2
    }
    
    // 新增的辅助类定义
    
    @Data
    public static class UnitPricingMeasure {
        private String unit;           // 计量单位
        private String unitType;       // 单位类型
    }
    
    @Data
    public static class AvailableCoupon {
        private String constraint;     // 优惠券约束
        private String couponType;     // 优惠券类型
        private String discountAmount; // 折扣金额
        private String discountType;   // 折扣类型
        private String message;        // 优惠券消息
        private String redemptionCode; // 兑换代码
        private String terms;          // 条款
    }
    
    @Data
    public static class Hazmat {
        private String type;           // 危险品类型
        private String description;    // 描述
        private List<String> statements; // 声明
    }
    
    @Data
    public static class ReturnTerms {
        private Boolean returnsAccepted;           // 是否接受退货
        private String returnPeriod;               // 退货期限
        private String returnMethod;               // 退货方式
        private String returnShippingCostPayer;    // 退货运费承担方
        private String restockingFeePercentage;    // 重新上架费用百分比
        private String returnInstructions;         // 退货说明
    }
    
    @Data
    public static class TaxInfo {
        private Boolean includedInPrice;           // 是否包含在价格中
        private String type;                       // 税种
        private String rate;                       // 税率
        private Price amount;                      // 税额
    }
    
    @Data
    public static class Product {
        private String productId;                  // 产品ID
        private String title;                      // 产品标题
        private List<String> aspectGroups;        // 特征组
        private String brand;                      // 品牌
        private String mpn;                        // 制造商部件号
        private String gtin;                       // 全球贸易项目代码
        private List<Image> images;                // 产品图片
    }
    
    @Data
    public static class CompatibilityMatch {
        private String compatibilityStatus;       // 兼容性状态
        private List<String> notes;              // 注释
        private List<String> specifications;     // 规格
    }

    @Data
    public static class Warning {
        private String category;                   // 警告类别
        private String domain;                     // 警告域
        private String errorId;                    // 错误ID
        private String message;                    // 警告消息
        private String longMessage;                // 详细警告消息
        private List<String> inputRefIds;          // 输入引用ID
        private List<String> outputRefIds;         // 输出引用ID
        private String subdomain;                  // 子域
        private List<Parameter> parameters;        // 参数列表
    }

    @Data
    public static class Parameter {
        private String name;                       // 参数名
        private String value;                      // 参数值
    }

    @Data
    public static class PickupOption {
        private String pickupLocationType;         // 自提地点类型
        private String pickupLocationId;           // 自提地点ID
        private String pickupMethod;               // 自提方式
        private String pickupInstructions;         // 自提说明
        private String pickupLocationName;         // 自提地点名称
        private String pickupLocationAddress;      // 自提地点地址
        private String pickupLocationCity;         // 自提地点城市
        private String pickupLocationState;        // 自提地点州/省
        private String pickupLocationCountry;      // 自提地点国家
        private String pickupLocationPostalCode;   // 自提地点邮编
        private String pickupLocationPhone;        // 自提地点电话
        private String pickupLocationHours;        // 自提地点营业时间
        private Boolean pickupLocationAvailable;   // 自提地点是否可用
    }
}

package ai.pricefox.mallfox.job.ebay;

import ai.pricefox.mallfox.vo.ebay.EbaySearchReqVO;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * eBay 搜索工具类
 * 提供搜索参数验证、构建和优化功能
 */
@Slf4j
public class EbaySearchUtil {

    // eBay 分类ID映射 - 常用分类
    public static final Map<String, String> CATEGORY_MAP = new HashMap<>();
    
    // 商品条件映射
    public static final Map<String, Integer> CONDITION_MAP = new HashMap<>();
    
    // 排序选项映射
    public static final Map<String, String> SORT_MAP = new HashMap<>();
    
    // 价格范围验证模式
    private static final Pattern PRICE_PATTERN = Pattern.compile("^\\d+(\\.\\d{1,2})?$");
    
    // GTIN验证模式 (UPC/EAN)
    private static final Pattern GTIN_PATTERN = Pattern.compile("^\\d{8,14}$");
    
    static {
        // 初始化分类映射
        CATEGORY_MAP.put("手机", "9355");
        CATEGORY_MAP.put("笔记本电脑", "175672");
        CATEGORY_MAP.put("台式电脑", "171957");
        CATEGORY_MAP.put("平板电脑", "171485");
        CATEGORY_MAP.put("相机", "625");
        CATEGORY_MAP.put("无人机", "179697");
        CATEGORY_MAP.put("智能手表", "178893");
        CATEGORY_MAP.put("耳机", "15052");
        CATEGORY_MAP.put("游戏机", "139971");
        CATEGORY_MAP.put("电视", "11071");
        
        // 初始化商品条件映射
        CONDITION_MAP.put("全新", 1000);
        CONDITION_MAP.put("翻新", 1500);
        CONDITION_MAP.put("二手", 2000);
        CONDITION_MAP.put("开盒", 2500);
        CONDITION_MAP.put("部件损坏", 3000);
        CONDITION_MAP.put("零件专用", 4000);
        CONDITION_MAP.put("有缺陷", 5000);
        CONDITION_MAP.put("收藏品", 6000);
        
        // 初始化排序映射
        SORT_MAP.put("最佳匹配", "bestMatch");
        SORT_MAP.put("价格升序", "price");
        SORT_MAP.put("价格降序", "-price");
        SORT_MAP.put("距离", "distance");
        SORT_MAP.put("最新上架", "newlyListed");
        SORT_MAP.put("即将结束", "endingSoonest");
    }

    /**
     * 验证搜索请求参数
     * 
     * @param searchRequest 搜索请求对象
     * @return 验证结果信息列表，为空表示验证通过
     */
    public static List<String> validateSearchRequest(EbaySearchReqVO searchRequest) {
        List<String> errors = new ArrayList<>();
        
        if (searchRequest == null) {
            errors.add("搜索请求对象不能为空");
            return errors;
        }
        
        // 验证基本参数
        if (searchRequest.getQuery() == null || searchRequest.getQuery().trim().isEmpty()) {
            errors.add("搜索关键字不能为空");
        } else if (searchRequest.getQuery().length() > 350) {
            errors.add("搜索关键字长度不能超过350个字符");
        }
        
        // 验证分页参数
        if (searchRequest.getLimit() != null && (searchRequest.getLimit() < 1 || searchRequest.getLimit() > 200)) {
            errors.add("返回数量限制必须在1-200之间");
        }
        
        if (searchRequest.getOffset() != null && searchRequest.getOffset() < 0) {
            errors.add("偏移量不能小于0");
        }
        
        // 验证价格范围
        if (searchRequest.getMinPrice() != null && searchRequest.getMinPrice().compareTo(BigDecimal.ZERO) < 0) {
            errors.add("最低价格不能小于0");
        }
        
        if (searchRequest.getMaxPrice() != null && searchRequest.getMaxPrice().compareTo(BigDecimal.ZERO) < 0) {
            errors.add("最高价格不能小于0");
        }
        
        if (searchRequest.getMinPrice() != null && searchRequest.getMaxPrice() != null) {
            if (searchRequest.getMinPrice().compareTo(searchRequest.getMaxPrice()) > 0) {
                errors.add("最低价格不能大于最高价格");
            }
        }
        
        // 验证GTIN格式
        if (searchRequest.getGtin() != null && !searchRequest.getGtin().trim().isEmpty()) {
            if (!GTIN_PATTERN.matcher(searchRequest.getGtin()).matches()) {
                errors.add("GTIN格式无效，必须是8-14位数字");
            }
        }
        
        // 验证货币代码
        if (searchRequest.getCurrency() != null && !searchRequest.getCurrency().matches("^[A-Z]{3}$")) {
            errors.add("货币代码必须是3位大写字母");
        }
        
        // 验证国家代码
        if (searchRequest.getDeliveryCountry() != null && !searchRequest.getDeliveryCountry().matches("^[A-Z]{2}$")) {
            errors.add("配送国家代码必须是2位大写字母");
        }
        
        if (searchRequest.getItemLocationCountry() != null && !searchRequest.getItemLocationCountry().matches("^[A-Z]{2}$")) {
            errors.add("商品位置国家代码必须是2位大写字母");
        }
        
        return errors;
    }

    /**
     * 优化搜索查询字符串
     * 移除无效字符，标准化格式
     * 
     * @param query 原始查询字符串
     * @return 优化后的查询字符串
     */
    public static String optimizeSearchQuery(String query) {
        if (query == null || query.trim().isEmpty()) {
            return query;
        }
        
        // 移除多余空格
        query = query.trim().replaceAll("\\s+", " ");
        
        // 移除特殊字符（保留常用符号）
        query = query.replaceAll("[^\\w\\s\\-+&()\"']", "");
        
        // 智能引号处理 - 为短语搜索添加引号
        if (query.contains(" ") && !query.contains("\"") && query.split(" ").length <= 3) {
            // 对于2-3个单词的查询，可能需要精确匹配
            log.debug("考虑为查询添加引号以进行精确匹配: {}", query);
        }
        
        return query;
    }

    /**
     * 构建智能过滤器
     * 根据查询内容智能添加相关过滤条件
     * 
     * @param searchRequest 搜索请求
     * @return 增强的搜索请求
     */
    public static EbaySearchReqVO enhanceSearchRequest(EbaySearchReqVO searchRequest) {
        if (searchRequest == null || searchRequest.getQuery() == null) {
            return searchRequest;
        }
        
        String query = searchRequest.getQuery().toLowerCase();
        
        // 智能分类识别
        if (searchRequest.getCategoryIds() == null || searchRequest.getCategoryIds().trim().isEmpty()) {
            String categoryId = detectCategory(query);
            if (categoryId != null) {
                searchRequest.setCategoryIds(categoryId);
                log.debug("智能识别分类ID: {} for query: {}", categoryId, query);
            }
        }
        
        // 智能品牌识别
        if (searchRequest.getBrands() == null || searchRequest.getBrands().isEmpty()) {
            List<String> brands = detectBrands(query);
            if (!brands.isEmpty()) {
                searchRequest.setBrands(brands);
                log.debug("智能识别品牌: {} for query: {}", brands, query);
            }
        }
        
        // 智能条件识别
        if (searchRequest.getConditionIds() == null || searchRequest.getConditionIds().isEmpty()) {
            List<Integer> conditions = detectConditions(query);
            if (!conditions.isEmpty()) {
                searchRequest.setConditionIds(conditions);
                log.debug("智能识别商品条件: {} for query: {}", conditions, query);
            }
        }
        
        return searchRequest;
    }

    /**
     * 检测查询中的分类信息
     */
    private static String detectCategory(String query) {
        for (Map.Entry<String, String> entry : CATEGORY_MAP.entrySet()) {
            if (query.contains(entry.getKey().toLowerCase()) || 
                query.contains(entry.getKey().toLowerCase().replace(" ", ""))) {
                return entry.getValue();
            }
        }
        
        // 英文分类检测
        if (query.contains("iphone") || query.contains("phone") || query.contains("smartphone")) {
            return "9355"; // 手机
        }
        if (query.contains("laptop") || query.contains("notebook")) {
            return "175672"; // 笔记本电脑
        }
        if (query.contains("drone")) {
            return "179697"; // 无人机
        }
        if (query.contains("camera")) {
            return "625"; // 相机
        }
        if (query.contains("tablet") || query.contains("ipad")) {
            return "171485"; // 平板电脑
        }
        
        return null;
    }

    /**
     * 检测查询中的品牌信息
     */
    private static List<String> detectBrands(String query) {
        List<String> brands = new ArrayList<>();
        
        String[] commonBrands = {
            "Apple", "Samsung", "Google", "Sony", "LG", "Huawei", "Xiaomi", 
            "OnePlus", "Motorola", "Nokia", "HTC", "Oppo", "Vivo", "Lenovo",
            "Dell", "HP", "Asus", "Acer", "MSI", "Canon", "Nikon", "DJI"
        };
        
        for (String brand : commonBrands) {
            if (query.toLowerCase().contains(brand.toLowerCase())) {
                brands.add(brand);
            }
        }
        
        return brands;
    }

    /**
     * 检测查询中的商品条件信息
     */
    private static List<Integer> detectConditions(String query) {
        List<Integer> conditions = new ArrayList<>();
        
        if (query.contains("new") || query.contains("全新") || query.contains("新品")) {
            conditions.add(1000); // 全新
        }
        if (query.contains("refurbished") || query.contains("翻新")) {
            conditions.add(1500); // 翻新
        }
        if (query.contains("used") || query.contains("二手")) {
            conditions.add(2000); // 二手
        }
        
        return conditions;
    }

    /**
     * 生成搜索建议
     * 根据当前搜索提供优化建议
     */
    public static List<String> generateSearchSuggestions(EbaySearchReqVO searchRequest) {
        List<String> suggestions = new ArrayList<>();
        
        if (searchRequest == null || searchRequest.getQuery() == null) {
            return suggestions;
        }
        
        String query = searchRequest.getQuery().toLowerCase();
        
        // 建议添加价格范围
        if (searchRequest.getMinPrice() == null && searchRequest.getMaxPrice() == null) {
            suggestions.add("考虑添加价格范围过滤以获得更精确的结果");
        }
        
        // 建议添加分类过滤
        if (searchRequest.getCategoryIds() == null || searchRequest.getCategoryIds().trim().isEmpty()) {
            suggestions.add("添加分类过滤可以显著提高搜索准确性");
        }
        
        // 建议使用排序
        if (searchRequest.getSort() == null || searchRequest.getSort().trim().isEmpty()) {
            suggestions.add("使用排序选项可以更好地组织搜索结果");
        }
        
        // 建议使用字段组
        if (searchRequest.getFieldgroups() == null || searchRequest.getFieldgroups().trim().isEmpty()) {
            suggestions.add("使用EXTENDED字段组可以获取更详细的商品信息");
        }
        
        return suggestions;
    }

    /**
     * 构建搜索统计信息
     */
    public static Map<String, Object> buildSearchStats(EbaySearchReqVO searchRequest) {
        Map<String, Object> stats = new HashMap<>();
        
        // 统计过滤器数量
        int filterCount = 0;
        if (searchRequest.getMinPrice() != null || searchRequest.getMaxPrice() != null) filterCount++;
        if (searchRequest.getConditionIds() != null && !searchRequest.getConditionIds().isEmpty()) filterCount++;
        if (searchRequest.getBrands() != null && !searchRequest.getBrands().isEmpty()) filterCount++;
        if (searchRequest.getCategoryIds() != null && !searchRequest.getCategoryIds().trim().isEmpty()) filterCount++;
        if (Boolean.TRUE.equals(searchRequest.getFreeShipping())) filterCount++;
        
        stats.put("filterCount", filterCount);
        stats.put("hasSort", searchRequest.getSort() != null && !searchRequest.getSort().trim().isEmpty());
        stats.put("hasAdvancedFeatures", 
            searchRequest.getGtin() != null || 
            searchRequest.getEpid() != null || 
            searchRequest.getCompatibilityFilter() != null);
        
        return stats;
    }
}
package ai.pricefox.mallfox.job.integration;

import ai.pricefox.mallfox.service.integration.BestBuyDataService;
import ai.pricefox.mallfox.service.integration.EbayDataService;
import ai.pricefox.mallfox.vo.base.CommonResult;
import ai.pricefox.mallfox.vo.ebay.EbaySearchReqVO;
import ai.pricefox.mallfox.vo.ebay.EbaySearchRespVO;
import ai.pricefox.mallfox.vo.bestbuy.BestBuyProductDetailRespVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 市场平台数据同步定时任务
 * 定时从eBay和BestBuy获取增量数据
 *
 * <AUTHOR> Assistant
 * @since 2025-08-03
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class MarketplaceDataSyncJob {

    private final EbayDataService ebayDataService;
    private final BestBuyDataService bestBuyDataService;

    /**
     * eBay数据增量同步定时任务
     * 每天执行一次，每次最多5000次请求
     * 通过服务层的进度管理实现增量查询，避免重复处理已同步数据
     */
    @Scheduled(cron = "0 0 21 * * ?") // 每天凌晨2点执行
    public void syncEbayData() {
        try {
            log.info("开始执行eBay数据增量同步任务");

            // 创建eBay搜索请求
            EbaySearchReqVO searchRequest = new EbaySearchReqVO();
            searchRequest.setQuery(""); // 空查询表示获取所有数据
            searchRequest.setCategoryIds("9355");
            searchRequest.setAspectFilter("categoryId:9355");
            searchRequest.setIncludeSoldItems(false);
            searchRequest.setAvailableOnly(true);
            searchRequest.setExactMatch(true);
            searchRequest.setLimit(200); // 每页200条数据
            searchRequest.setSyncData(false); // 同步数据到产品数据库

            // 调用eBay数据服务执行增量同步
            // 服务层已实现进度管理和增量查询逻辑
            ebayDataService.searchSyncItemsAdvanced(searchRequest);
        } catch (Exception e) {
            log.error("执行eBay数据增量同步任务时发生异常", e);
        }
    }

    /**
     * BestBuy数据增量同步定时任务
     * 每天执行一次
     * 通过服务层的进度管理实现增量查询，避免重复处理已同步数据
     */
    @Scheduled(cron = "0 30 21 * * ?") // 每天凌晨5点30分执行
    public void syncBestBuyData() {
        try {
            log.info("开始执行BestBuy数据增量同步任务");

            // 关键字列表，可以根据实际业务需要进行调整
            String[] keywords = {"cellphone","phone"};

            for (String keyword : keywords) {
                try {
                    log.info("开始同步BestBuy关键词 '{}' 的数据", keyword);

                    // 调用BestBuy数据服务执行增量同步
                    // 服务层已实现进度管理和增量查询逻辑
                    bestBuyDataService.searchProducts(keyword, 100, null); // 每次查询100条数据
                } catch (Exception e) {
                    log.error("同步BestBuy关键词 '{}' 数据时发生异常", keyword, e);
                }
            }

            log.info("BestBuy数据增量同步任务执行完成");
        } catch (Exception e) {
            log.error("执行BestBuy数据增量同步任务时发生异常", e);
        }
    }
}
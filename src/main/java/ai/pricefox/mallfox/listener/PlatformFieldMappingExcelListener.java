package ai.pricefox.mallfox.listener;

import ai.pricefox.mallfox.model.excel.PlatformFieldMappingExcelEntity;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

/**
 * 平台字段映射Excel读取监听器
 * 用于EasyExcel读取Excel文件时的数据处理
 *
 * <AUTHOR>
 * @since 2025-07-26
 */
@Slf4j
public class PlatformFieldMappingExcelListener extends AnalysisEventListener<PlatformFieldMappingExcelEntity> {

    /**
     * 存储读取的数据
     */
    private final List<PlatformFieldMappingExcelEntity> dataList = new ArrayList<>();

    /**
     * 每解析一行都会回调此方法
     */
    @Override
    public void invoke(PlatformFieldMappingExcelEntity data, AnalysisContext context) {
        log.debug("解析到一条数据：{}", data);
        dataList.add(data);
    }

    /**
     * 所有数据解析完成了都会来调用
     */
    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        log.info("Excel数据解析完成，共解析{}条数据", dataList.size());
    }

    /**
     * 获取解析的数据
     */
    public List<PlatformFieldMappingExcelEntity> getDataList() {
        return dataList;
    }

    /**
     * 解析异常处理
     */
    @Override
    public void onException(Exception exception, AnalysisContext context) throws Exception {
        log.error("Excel解析异常，行号：{}，异常信息：{}", context.readRowHolder().getRowIndex(), exception.getMessage());
        super.onException(exception, context);
    }
}
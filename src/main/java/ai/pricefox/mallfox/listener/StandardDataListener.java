package ai.pricefox.mallfox.listener;

import ai.pricefox.mallfox.enums.DataChannelEnum;
import ai.pricefox.mallfox.enums.ProductPlatformEnum;
import ai.pricefox.mallfox.event.StandardDataEvent;
import ai.pricefox.mallfox.model.dto.DynamicStandardProduct;
import ai.pricefox.mallfox.service.mongo.RawDataService;
import ai.pricefox.mallfox.service.rules.DataStandardizationService;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.lang.reflect.Type;
import java.util.Map;

/**
 * 标准化数据监听器
 * 通过统一事件处理机制优化代码重复性，支持异常处理、日志增强等优化特性
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class StandardDataListener {
    private final RawDataService rawDataService;
    private final DataStandardizationService dataStandardizationService;

    /**
     * 标准化数据监听器
     */
    @Async
    @EventListener
    public void handleDataListener(StandardDataEvent event) {
        log.info("[数据保存监听器] 收到监听，准备存入数据");
        Gson gson = new Gson();
        Type stringObjectMap = new TypeToken<Map<String, Object>>() {
        }.getType();
        Map<String, Object> originalDataMap = gson.fromJson(event.getOriginalData(), stringObjectMap);
        // mongoDB保存原始数据
        String sourcePlatform = originalDataMap.get("sourcePlatform").toString();
        String dataChannel = originalDataMap.get("dataChannel").toString();
        Object spu = null;
        Object sku = null;

        switch (ProductPlatformEnum.valueOf(sourcePlatform)) {
            case AMAZON:
                spu = originalDataMap.get("platformSpuId");
                sku = originalDataMap.get("asin");
                break;
            case EBAY:
                spu = originalDataMap.get("itemId");
                sku = originalDataMap.get("itemId");
                break;
            case BESTBUY:
                sku = originalDataMap.get("sku");
                break;
            case WALMART:
                break;
            default:
                break;
        }
        if (sku == null) {
            log.error("[数据保存监听器] 未找到sku属性值");
            return;
        }
        String productIdentifier = dataStandardizationService.generateProductIdentifier(spu == null ? null : spu.toString(), sku.toString(), dataChannel, sourcePlatform);
        if (productIdentifier == null) {
            log.error("[分步标准化] 为能生成产品唯一标识符，sourcePlatform:{},dataChannel:{}", sourcePlatform, dataChannel);
            return;
        }
        DynamicStandardProduct dynamicStandardProduct = new DynamicStandardProduct();
        String standardSku = rawDataService.saveRawData(sourcePlatform, dataChannel, spu == null ? null : spu.toString(), sku.toString(), gson.toJson(originalDataMap), productIdentifier, (Boolean) originalDataMap.get("selfOperated"));
        dynamicStandardProduct.setStandardSku(standardSku);
        dynamicStandardProduct.setProductIdentifier(productIdentifier);
        dynamicStandardProduct.setOriginData(originalDataMap);
        dynamicStandardProduct.setDataChannel(dataChannel);
        dynamicStandardProduct.setPlatformName(sourcePlatform);
        dynamicStandardProduct.setSpu(spu == null ? null : spu.toString());
        dynamicStandardProduct.setSku(sku.toString());
        dataStandardizationService.startStepByStepStandardizationWithJson(dynamicStandardProduct);

        log.info("[数据保存监听器] 数据保存完毕");
    }

}

package ai.pricefox.mallfox.listener;

import ai.pricefox.mallfox.domain.standard.mongo.RawData;
import ai.pricefox.mallfox.enums.ProductExamineStatusEnum;
import ai.pricefox.mallfox.service.mongo.RawDataService;
import ai.pricefox.mallfox.enums.StandardizationStepEnum;
import ai.pricefox.mallfox.event.StandardizationStepEvent;
import ai.pricefox.mallfox.model.dto.DynamicStandardProduct;
import ai.pricefox.mallfox.service.rules.DataStandardizationService;
import ai.pricefox.mallfox.domain.standard.StandardProductMargeData;
import ai.pricefox.mallfox.mapper.standard.StandardProductMargeDataMapper;
import ai.pricefox.mallfox.enums.DataChannelEnum;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Criteria;
import com.google.gson.Gson;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.*;

/**
 * 标准化步骤监听器
 * 通过统一事件处理机制优化代码重复性，支持异常处理、日志增强等优化特性
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class StandardizationStepListener {

    private final DataStandardizationService dataStandardizationService;
    private final ApplicationEventPublisher eventPublisher;

    /**
     * 处理数据合并步骤事件
     * 包含特殊处理逻辑：字段配置查找和源数据保存
     *
     * @param event 标准化步骤事件
     */
    @EventListener
    public void handleMargeDataStep(StandardizationStepEvent event) {
        DynamicStandardProduct dynamicStandardProduct = event.getDynamicStandardProduct();
        try {
            if (event.getCurrentStep() != StandardizationStepEnum.MARGE_DATA) {
                return;
            }

            log.debug("[标准化步骤] 数据合并步骤前的数据: {}", dynamicStandardProduct.getOriginData());
            StandardProductMargeData standardProductMargeData = dataStandardizationService.margeData(dynamicStandardProduct);
            log.debug("[标准化步骤] 合并spu后数据: {}", standardProductMargeData);
            dataStandardizationService.margeAttribute(dynamicStandardProduct,standardProductMargeData);
            // 保存处理结果并记录状态
            dataStandardizationService.saveProcessedData(dynamicStandardProduct, StandardizationStepEnum.MARGE_DATA);
            dataStandardizationService.recordStepCompletion(StandardizationStepEnum.MARGE_DATA, dynamicStandardProduct, true);

        } catch (Exception e) {
            log.error("[标准化步骤] 数据合并步骤发生异常，skuId={}, platformCode={}", dynamicStandardProduct.getSku(), dynamicStandardProduct.getPlatformCode(),e);
            dataStandardizationService.recordStepCompletion(StandardizationStepEnum.MARGE_DATA, null, false);
        }
    }

    /**
     * 统一处理标准化步骤事件
     * 通过步骤枚举与步骤名称的映射关系处理重复性代码
     *
     * @param event 标准化步骤事件
     */
    @EventListener
    public void handleStandardizationStep(StandardizationStepEvent event) {
        DynamicStandardProduct processedData = new DynamicStandardProduct();
        Gson gson = new Gson();
        try {
            // 检查是否为通用处理步骤
            if (!event.getCurrentStep().equals(StandardizationStepEnum.OTHER_STANDARDIZATION)) {
                return;
            }

            String stepName = event.getCurrentStep().getDescription();
            log.info("[标准化步骤] 开始处理{}步骤: skuId={}, platformCode={}", stepName, processedData.getSku(), processedData.getPlatformCode());
            log.debug("[标准化步骤] {}步骤前的数据: {}", stepName, gson.toJson(event.getDynamicStandardProduct().getCurrentData()));

            // 执行标准化规则
            processedData = dataStandardizationService.executeStandardizationStepWithJson(event.getCurrentStep(), event.getDynamicStandardProduct());
            log.debug("[标准化步骤] {}步骤后的数据: {}", stepName, gson.toJson(processedData.getCurrentData()));
            log.info("[标准化步骤] {}步骤处理完成: skuId={}, platformCode={}", stepName, processedData.getProductIdentifier(), processedData.getPlatformCode());

            // 保存处理结果
            dataStandardizationService.saveProcessedData(processedData, event.getCurrentStep());

            // 记录处理状态
            dataStandardizationService.recordStepCompletion(event.getCurrentStep(), processedData, true);

        } catch (Exception e) {
            log.error("[标准化步骤] 处理标准化步骤发生异常，step={}, skuId={}, platformCode={}", event.getCurrentStep(), processedData.getProductIdentifier(), processedData.getPlatformCode(), e);
            dataStandardizationService.recordStepCompletion(event.getCurrentStep(), null, false);
        }
    }

    /**
     * 处理最终标准化步骤事件
     *
     * @param event 标准化步骤事件
     */
    @EventListener
    public void handleFinalStandardizationStep(StandardizationStepEvent event) {
        DynamicStandardProduct processedData = event.getDynamicStandardProduct();
        try {
            if (event.getCurrentStep() != StandardizationStepEnum.FINAL_STANDARDIZATION) {
                return;
            }

            log.info("[标准化步骤] 开始处理最终标准化步骤: skuId={}, platformCode={}", processedData.getSku(), processedData.getPlatformCode());
            log.debug("[标准化步骤] 最终标准化步骤前的数据: {}", event.getDynamicStandardProduct().getCurrentData());

            // 执行最终标准化规则组
            processedData = dataStandardizationService.executeStandardizationStepWithJson(StandardizationStepEnum.FINAL_STANDARDIZATION, event.getDynamicStandardProduct());

            log.debug("[标准化步骤] 最终标准化步骤后的数据: {}", processedData.getCurrentData());
            log.info("[标准化步骤] 最终标准化步骤处理完成: skuId={}, platformCode={}", processedData.getSku(), processedData.getPlatformCode());

            // 保存处理结果
            dataStandardizationService.saveProcessedData(processedData, StandardizationStepEnum.FINAL_STANDARDIZATION);

            // 发布标准化完成事件
            eventPublisher.publishEvent(new StandardizationStepEvent(this, StandardizationStepEnum.FINAL_STANDARDIZATION, processedData));
            // 记录处理状态
            dataStandardizationService.recordStepCompletion(StandardizationStepEnum.FINAL_STANDARDIZATION, processedData, true);

        } catch (Exception e) {
            log.error("[标准化步骤] 最终标准化步骤发生异常，skuId={}, platformCode={}", processedData.getSku(), processedData.getPlatformCode(), e);
            dataStandardizationService.recordStepCompletion(StandardizationStepEnum.FINAL_STANDARDIZATION, null, false);
        }
    }
}

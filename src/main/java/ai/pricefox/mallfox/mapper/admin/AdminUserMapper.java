package ai.pricefox.mallfox.mapper.admin;

import ai.pricefox.mallfox.domain.admin.AdminUser;
import ai.pricefox.mallfox.vo.admin.AdminUserPageReqVO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【admin_user(后台用户表)】的数据库操作Mapper
 * @Entity ai.pricefox.mallfox.domain.AdminUser
 */
@Mapper
public interface AdminUserMapper extends BaseMapper<AdminUser> {

    /**
     * 分页查询后台用户
     *
     * @param reqVO 分页查询请求
     * @return 分页结果
     */
    default Page<AdminUser> selectAdminUserPage(AdminUserPageReqVO reqVO) {
        Page<AdminUser> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapper<AdminUser> queryWrapper = new LambdaQueryWrapper<>();

        if (StringUtils.hasText(reqVO.getUsername())) {
            queryWrapper.like(AdminUser::getUsername, reqVO.getUsername());
        }
        if (StringUtils.hasText(reqVO.getEmail())) {
            queryWrapper.like(AdminUser::getEmail, reqVO.getEmail());
        }
        if (StringUtils.hasText(reqVO.getPhone())) {
            queryWrapper.like(AdminUser::getPhone, reqVO.getPhone());
        }
        if (StringUtils.hasText(reqVO.getNickname())) {
            queryWrapper.like(AdminUser::getNickname, reqVO.getNickname());
        }
        if (reqVO.getSex() != null) {
            queryWrapper.eq(AdminUser::getSex, reqVO.getSex());
        }
        if (reqVO.getStatus() != null) {
            queryWrapper.eq(AdminUser::getStatus, reqVO.getStatus());
        }

        queryWrapper.orderByDesc(AdminUser::getCreateTime);
        return this.selectPage(page, queryWrapper);
    }

    /**
     * 根据用户名查询后台用户
     *
     * @param username 用户名
     * @return 用户对象
     */
    default AdminUser selectAdminUserByUsername(String username) {
        LambdaQueryWrapper<AdminUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AdminUser::getUsername, username);
        return this.selectOne(queryWrapper);
    }

    /**
     * 根据邮箱查询后台用户
     *
     * @param email 邮箱
     * @return 用户对象
     */
    default AdminUser selectAdminUserByEmail(String email) {
        LambdaQueryWrapper<AdminUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AdminUser::getEmail, email);
        return this.selectOne(queryWrapper);
    }

    /**
     * 根据手机号查询后台用户
     *
     * @param phone 手机号
     * @return 用户对象
     */
    default AdminUser selectAdminUserByPhone(String phone) {
        LambdaQueryWrapper<AdminUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AdminUser::getPhone, phone);
        return this.selectOne(queryWrapper);
    }

    /**
     * 根据访问令牌查询后台用户
     *
     * @param accessToken 访问令牌
     * @return 用户对象
     */
    default AdminUser selectAdminUserByAccessToken(String accessToken) {
        LambdaQueryWrapper<AdminUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AdminUser::getAccessToken, accessToken);
        return this.selectOne(queryWrapper);
    }

    /**
     * 根据刷新令牌查询后台用户
     *
     * @param refreshToken 刷新令牌
     * @return 用户对象
     */
    default AdminUser selectAdminUserByRefreshToken(String refreshToken) {
        LambdaQueryWrapper<AdminUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AdminUser::getRefreshToken, refreshToken);
        return this.selectOne(queryWrapper);
    }

    /**
     * 插入后台用户
     *
     * @param adminUser 用户对象
     * @return 插入结果
     */
    default boolean insertAdminUser(AdminUser adminUser) {
        return this.insert(adminUser) > 0;
    }

    /**
     * 更新后台用户
     *
     * @param adminUser 用户对象
     * @return 更新结果
     */
    default boolean updateAdminUserById(AdminUser adminUser) {
        return this.updateById(adminUser) > 0;
    }

    /**
     * 根据ID删除后台用户
     *
     * @param id 用户ID
     * @return 删除结果
     */
    default boolean deleteAdminUserById(Long id) {
        return this.deleteById(id) > 0;
    }

    /**
     * 根据ID查询后台用户
     *
     * @param id 用户ID
     * @return 用户对象
     */
    default AdminUser selectAdminUserById(Long id) {
        return this.selectById(id);
    }

    /**
     * 查询所有后台用户
     *
     * @return 用户列表
     */
    default List<AdminUser> selectAllAdminUsers() {
        LambdaQueryWrapper<AdminUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AdminUser::getStatus, 1); // 只查询启用的用户
        queryWrapper.orderByDesc(AdminUser::getCreateTime);
        return this.selectList(queryWrapper);
    }

    /**
     * 清除后台用户Token
     *
     * @param userId 用户ID
     * @return 更新结果
     */
    default boolean clearAdminUserToken(Long userId) {
        AdminUser adminUser = new AdminUser();
        adminUser.setId(userId);
        adminUser.setAccessToken(null);
        adminUser.setRefreshToken(null);
        adminUser.setTokenExpireTime(null);
        adminUser.setRefreshTokenExpireTime(null);
        return this.updateById(adminUser) > 0;
    }
}

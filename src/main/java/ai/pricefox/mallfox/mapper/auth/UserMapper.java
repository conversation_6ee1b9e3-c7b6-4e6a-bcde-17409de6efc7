package ai.pricefox.mallfox.mapper.auth;

import ai.pricefox.mallfox.domain.auth.User;
import ai.pricefox.mallfox.vo.user.UserPageReqVO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.util.StringUtils;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【user(用户表)】的数据库操作Mapper
* @createLocalDateTime 2025-05-18 12:14:02
* @Entity ai.pricefox.mallfox.domain.User
*/
@Mapper
public interface UserMapper extends BaseMapper<User> {

    /**
     * 分页查询用户
     *
     * @param reqVO 分页查询请求
     * @return 分页结果
     */
    default Page<User> selectUserPage(UserPageReqVO reqVO) {
        Page<User> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();

        if (StringUtils.hasText(reqVO.getUsername())) {
            queryWrapper.like(User::getUsername, reqVO.getUsername());
        }
        if (StringUtils.hasText(reqVO.getEmail())) {
            queryWrapper.like(User::getEmail, reqVO.getEmail());
        }
        if (StringUtils.hasText(reqVO.getPhone())) {
            queryWrapper.like(User::getPhone, reqVO.getPhone());
        }
        if (StringUtils.hasText(reqVO.getNickname())) {
            queryWrapper.like(User::getUsername, reqVO.getNickname());
        }
        if (reqVO.getSex() != null) {
            queryWrapper.eq(User::getSex, reqVO.getSex());
        }
        if (reqVO.getStatus() != null) {
            queryWrapper.eq(User::getStatus, reqVO.getStatus());
        }

        return this.selectPage(page, queryWrapper);
    }

    /**
     * 根据用户名查询用户
     *
     * @param username 用户名
     * @return 用户对象
     */
    default User selectUserByUsername(String username) {
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(User::getUsername, username);
        return this.selectOne(queryWrapper);
    }

    /**
     * 根据邮箱查询用户
     *
     * @param email 邮箱
     * @return 用户对象
     */
    default User selectUserByEmail(String email) {
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(User::getEmail, email);
        return this.selectOne(queryWrapper);
    }

    /**
     * 根据手机号查询用户
     *
     * @param phone 手机号
     * @return 用户对象
     */
    default User selectUserByPhone(String phone) {
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(User::getPhone, phone);
        return this.selectOne(queryWrapper);
    }

    /**
     * 插入用户
     *
     * @param user 用户对象
     * @return 插入结果
     */
    default boolean insertUser(User user) {
        return this.insert(user) > 0;
    }

    /**
     * 更新用户
     *
     * @param user 用户对象
     * @return 更新结果
     */
    default boolean updateUserById(User user) {
        return this.updateById(user) > 0;
    }

    /**
     * 根据ID删除用户
     *
     * @param id 用户ID
     * @return 删除结果
     */
    default boolean deleteUserById(Long id) {
        return this.deleteById(id) > 0;
    }

    /**
     * 根据ID查询用户
     *
     * @param id 用户ID
     * @return 用户对象
     */
    default User selectUserById(Long id) {
        return this.selectById(id);
    }

    /**
     * 查询所有用户
     *
     * @return 用户列表
     */
    default List<User> selectAllUsers() {
        return this.selectList(null);
    }

    /**
     * 根据访问令牌查询用户
     *
     * @param accessToken 访问令牌
     * @return 用户对象
     */
    default User selectUserByAccessToken(String accessToken) {
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(User::getAccessToken, accessToken);
        return this.selectOne(queryWrapper);
    }

    /**
     * 根据刷新令牌查询用户
     *
     * @param refreshToken 刷新令牌
     * @return 用户对象
     */
    default User selectUserByRefreshToken(String refreshToken) {
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(User::getRefreshToken, refreshToken);
        return this.selectOne(queryWrapper);
    }

    /**
     * 清除用户Token
     *
     * @param userId 用户ID
     * @return 更新结果
     */
    default boolean clearUserToken(Long userId) {
        User user = new User();
        user.setId(userId);
        user.setAccessToken(null);
        user.setRefreshToken(null);
        user.setTokenExpireTime(null);
        user.setRefreshTokenExpireTime(null);
        return this.updateById(user) > 0;
    }

}





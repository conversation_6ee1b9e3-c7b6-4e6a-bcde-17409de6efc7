package ai.pricefox.mallfox.mapper.auth;

import ai.pricefox.mallfox.domain.auth.UserSocialAccount;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 用户社交账号关联表Mapper
 */
@Mapper
public interface UserSocialAccountMapper extends BaseMapper<UserSocialAccount> {

    /**
     * 根据社交平台类型和社交ID查询用户社交账号
     *
     * @param socialType 社交平台类型
     * @param socialId 社交平台ID
     * @return 用户社交账号
     */
    default UserSocialAccount selectBySocialTypeAndSocialId(Integer socialType, String socialId) {
        LambdaQueryWrapper<UserSocialAccount> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserSocialAccount::getSocialType, socialType)
                .eq(UserSocialAccount::getSocialId, socialId);
        return this.selectOne(queryWrapper);
    }

    /**
     * 根据用户ID查询该用户所有社交账号
     *
     * @param userId 用户ID
     * @return 社交账号列表
     */
    default List<UserSocialAccount> selectByUserId(Long userId) {
        LambdaQueryWrapper<UserSocialAccount> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserSocialAccount::getUserId, userId);
        return this.selectList(queryWrapper);
    }

    /**
     * 根据用户ID和社交平台类型查询
     *
     * @param userId 用户ID
     * @param socialType 社交平台类型
     * @return 用户社交账号
     */
    default UserSocialAccount selectByUserIdAndSocialType(Long userId, Integer socialType) {
        LambdaQueryWrapper<UserSocialAccount> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserSocialAccount::getUserId, userId)
                .eq(UserSocialAccount::getSocialType, socialType);
        return this.selectOne(queryWrapper);
    }

    /**
     * 插入用户社交账号
     *
     * @param userSocialAccount 用户社交账号
     * @return 影响行数
     */
    default boolean insertUserSocialAccount(UserSocialAccount userSocialAccount) {
        return this.insert(userSocialAccount) > 0;
    }

    /**
     * 更新用户社交账号
     *
     * @param userSocialAccount 用户社交账号
     * @return 影响行数
     */
    default boolean updateUserSocialAccount(UserSocialAccount userSocialAccount) {
        return this.updateById(userSocialAccount) > 0;
    }

    /**
     * 删除用户社交账号
     *
     * @param id 社交账号ID
     * @return 影响行数
     */
    default boolean deleteUserSocialAccount(Long id) {
        return this.deleteById(id) > 0;
    }
} 
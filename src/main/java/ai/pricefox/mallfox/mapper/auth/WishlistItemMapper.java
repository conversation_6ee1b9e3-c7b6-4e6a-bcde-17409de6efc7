package ai.pricefox.mallfox.mapper.auth;

import ai.pricefox.mallfox.domain.auth.WishlistItem;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 用户商品收藏表 Mapper
 * <AUTHOR>
 * @description 针对表【wishlist_item(用户商品收藏表)】的数据库操作Mapper
 */
@Mapper
public interface WishlistItemMapper extends BaseMapper<WishlistItem> {

    /**
     * 根据用户ID和SKU ID列表查询收藏记录
     * 
     * @param userId 用户ID
     * @param skuIds SKU ID列表
     * @return 收藏记录列表
     */
    @Select("<script>" +
            "SELECT * FROM wishlist_item " +
            "WHERE user_id = #{userId} AND sku_id IN " +
            "<foreach collection='skuIds' item='skuId' open='(' separator=',' close=')'>" +
            "  #{skuId}" +
            "</foreach>" +
            "</script>")
    List<WishlistItem> selectByUserIdAndSkuIds(@Param("userId") Long userId, @Param("skuIds") List<String> skuIds);

    /**
     * 检查用户是否收藏了指定商品
     * 
     * @param userId 用户ID
     * @param skuId SKU ID
     * @return 收藏记录
     */
    @Select("SELECT * FROM wishlist_item WHERE user_id = #{userId} AND sku_id = #{skuId} LIMIT 1")
    WishlistItem selectByUserIdAndSkuId(@Param("userId") Long userId, @Param("skuId") String skuId);
}

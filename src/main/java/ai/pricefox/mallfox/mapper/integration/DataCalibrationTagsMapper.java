package ai.pricefox.mallfox.mapper.integration;

import ai.pricefox.mallfox.domain.integration.DataCalibrationTags;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/6/25
 * @desc 数据校准标记表的数据库操作Mapper
 */
@Mapper
public interface DataCalibrationTagsMapper extends BaseMapper<DataCalibrationTags> {
    
    /**
     * 批量插入或更新数据校准标记
     * @param list 数据校准标记列表
     * @return 影响的行数
     */
    int batchInsertOrUpdate(@Param("list") List<DataCalibrationTags> list);
}

package ai.pricefox.mallfox.mapper.product;

import ai.pricefox.mallfox.domain.product.ChannelOffers;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.math.BigDecimal;
import java.util.List;

@Mapper
public interface ChannelOffersMapper extends BaseMapper<ChannelOffers> {

    /**
     * 查找比当前价格更低的所有有效报价，并按价格升序排列
     * @param skuCode 商品编码
     * @param currentPriceInUsd 当前价格
     * @return 有效报价列表
     */
    List<ChannelOffers> findLowerPriceOffers(String skuCode, BigDecimal currentPriceInUsd);

}
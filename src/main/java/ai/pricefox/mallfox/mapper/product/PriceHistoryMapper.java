package ai.pricefox.mallfox.mapper.product;

import ai.pricefox.mallfox.domain.product.PriceHistory;
import ai.pricefox.mallfox.model.response.PriceHistoryResponseDTO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.time.LocalDate;
import java.util.List;

@Mapper
public interface PriceHistoryMapper extends BaseMapper<PriceHistory> {


        /**
         * 查询用于绘制图表的数据点列表。
         * 如果查询多个平台，此方法会对每日的价格进行聚合（最低价取MIN，平均价取AVG）。
         *
         * @param skuCode       要查询的商品SKU CODE。
         * @param startDate   查询周期的开始日期。
         * @param endDate     查询周期的结束日期。
         * @param platforms   要筛选的平台列表。如果列表只包含"ALL"，则查询全平台聚合数据。
         * @return 价格历史数据点列表。
         */
        List<PriceHistoryResponseDTO.DataPoint> findDataPoints(
                @Param("skuCode") String skuCode,
                @Param("startDate") LocalDate startDate,
                @Param("endDate") LocalDate endDate,
                @Param("platforms") List<String> platforms
        );

        /**
         * 在指定的时间和平台范围内，查找价格最低的那一个历史记录点。
         *
         * @param skuCode       商品SKU CODE。
         * @param startDate   开始日期。
         * @param endDate     结束日期。
         * @param platforms   平台列表。
         * @return 包含最低价格及其日期的PricePoint对象。
         */
        PriceHistoryResponseDTO.PricePoint findLowestPriceInPeriod(
                @Param("skuCode") String skuCode,
                @Param("startDate") LocalDate startDate,
                @Param("endDate") LocalDate endDate,
                @Param("platforms") List<String> platforms
        );

        /**
         * 查询最新的价格信息。
         * 通常是查询最近一天的历史记录。在真实系统中，也可以直接查询product_sku表的实时最低价。
         *
         * @param skuCode     商品SKU CODE。
         * @param platforms 平台列表。
         * @return 包含最新价格及其来源的PricePoint对象。
         */
        PriceHistoryResponseDTO.PricePoint findLatestPrice(
                @Param("skuCode") String skuCode,
                @Param("platforms") List<String> platforms
        );
    }
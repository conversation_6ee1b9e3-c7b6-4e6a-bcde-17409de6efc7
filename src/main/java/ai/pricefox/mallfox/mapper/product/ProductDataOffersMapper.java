package ai.pricefox.mallfox.mapper.product;

import ai.pricefox.mallfox.domain.product.ProductDataOffers;
import ai.pricefox.mallfox.domain.product.ProductDataSimplify;
import ai.pricefox.mallfox.model.response.SpuGroupViewResponse;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;
import java.util.Map;

/**
* <AUTHOR>
* @description 针对表【product_data_offers(多平台商品高频表)】的数据库操作Mapper
* @createDate 2025-06-15 19:12:11
* @Entity ai.pricefox.mallfox.domain.ProductDataOffers
*/
public interface ProductDataOffersMapper extends BaseMapper<ProductDataOffers> {

    /**
     * 根据UPC码查询商品记录
     * @param upcCode UPC编码
     * @return 匹配的记录列表
     */
    @Select("SELECT * FROM product_data_offers WHERE " +
            "upc_code = #{upcCode} AND upc_code IS NOT NULL AND upc_code != '' " +
            "and REPLACE(LOWER(upc_code), ' ', '') not in ('n/a', 'doesnotapply', 'string', 'na', 'nichtzutreffend', '不适用')  LIMIT 1")
    ProductDataOffers selectByUpcCode(@Param("upcCode") String upcCode);

    /**
     * 分页查询upc_code="Does not apply"的记录（忽略大小写和空格）
     * @param page 分页参数
     * @param sourcePlatform 数据来源平台，可为空
     * @return 匹配的记录列表
     */
    @Select("SELECT * FROM product_data_offers WHERE " +
            "REPLACE(LOWER(upc_code), ' ', '') = 'doesnotapply' " +
            "ORDER BY id")
    Page<ProductDataOffers> selectUpcCodeDoesNotApplyRecords(
            Page<ProductDataOffers> page,
            @Param("sourcePlatform") String sourcePlatform
    );

    /**
     * 批量更新spuid
     * @param ids 记录ID列表
     * @param newSpuId 新的spuid
     * @return 更新的记录数
     */
    @Update("<script>" +
            "UPDATE product_data_offers SET spu_id = #{newSpuId} WHERE id IN " +
            "<foreach collection='ids' item='id' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach>" +
            "</script>")
    int updateSpuIdByIds(@Param("ids") List<Long> ids, @Param("newSpuId") String newSpuId);

    /**
     * 获取spuid下的sku
     * @param spuIds
     * @return 匹配的记录
     */
    @Select( "<script> SELECT *  FROM product_data_offers " +
            "WHERE spu_id IN " +
            "<foreach collection='spuIds' item='id' open='(' separator=',' close=')'>" +
                "#{id}" +
            "</foreach>"  +
            "</script>")
    List<ProductDataOffers> selectSkuCountBySpuIds(@Param("spuIds") List<String> spuIds);
}





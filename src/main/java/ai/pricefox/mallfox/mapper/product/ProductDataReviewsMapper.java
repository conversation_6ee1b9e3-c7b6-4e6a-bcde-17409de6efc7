package ai.pricefox.mallfox.mapper.product;

import ai.pricefox.mallfox.domain.product.ProductDataReviews;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;

/**
* <AUTHOR>
* @description 针对表【product_data_reviews(多平台商品评论数据表)】的数据库操作Mapper
* @createDate 2025-06-15 19:12:11
* @Entity ai.pricefox.mallfox.domain.ProductDataReviews
*/
public interface ProductDataReviewsMapper extends BaseMapper<ProductDataReviews> {

    /**
     * 根据skuId和spuId查询指定时间范围内的评论数量
     * @param skuId SKU ID
     * @param spuId SPU ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 评论数量
     */
    @Select("SELECT COUNT(*) FROM product_data_reviews WHERE " +
            "(#{skuId} IS NOT NULL AND sku_id = #{skuId}) AND " +
            "(#{spuId} IS NOT NULL AND spu_id = #{spuId}) AND " +
            "review_time >= #{startTime} AND review_time <= #{endTime}")
    Integer countReviewsBySkuSpuAndTimeRange(@Param("skuId") String skuId,
                                           @Param("spuId") String spuId,
                                           @Param("startTime") LocalDateTime startTime,
                                           @Param("endTime") LocalDateTime endTime);

    /**
     * 根据skuId、spuId和sourcePlatform查询指定时间范围内的评论数量
     * @param skuId SKU ID
     * @param spuId SPU ID
     * @param sourcePlatform 来源平台
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 评论数量
     */
    @Select("SELECT COUNT(*) FROM product_data_reviews WHERE " +
            "(#{skuId} IS NOT NULL AND sku_id = #{skuId}) AND " +
            "(#{spuId} IS NOT NULL AND spu_id = #{spuId}) AND " +
            "(#{sourcePlatform} IS NOT NULL AND source_platform = #{sourcePlatform}) AND " +
            "review_time >= #{startTime} AND review_time <= #{endTime}")
    Integer countReviewsBySkuSpuPlatformAndTimeRange(@Param("skuId") String skuId,
                                                    @Param("spuId") String spuId,
                                                    @Param("sourcePlatform") String sourcePlatform,
                                                    @Param("startTime") LocalDateTime startTime,
                                                    @Param("endTime") LocalDateTime endTime);

}





package ai.pricefox.mallfox.mapper.product;

import ai.pricefox.mallfox.domain.product.ProductDataSimplify;
import ai.pricefox.mallfox.model.dto.ProductModelMergeDTO;
import ai.pricefox.mallfox.model.dto.ProductModelProcessDTO;
import ai.pricefox.mallfox.model.dto.ProductModelUpcDTO;
import ai.pricefox.mallfox.model.dto.ProductSkuMergeDTO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【product_data_simplify(商品低频数据表)】的数据库操作Mapper
* @createDate 2025-06-15 19:12:11
* @Entity ai.pricefox.mallfox.domain.ProductDataSimplify
*/
public interface ProductDataSimplifyMapper extends BaseMapper<ProductDataSimplify> {

    Page<ProductDataSimplify> pageSelectProductData(Page page, ProductDataSimplify dataSimplify);

    /**
     * 根据数据渠道查询不重复的model列表
     * @param dataChannel 数据渠道 ({@link ai.pricefox.mallfox.enums.DataChannelEnum#CRAWLER}: 爬虫, {@link ai.pricefox.mallfox.enums.DataChannelEnum#API}: API)
     * @return 不重复的model列表
     */
    List<String> selectDistinctModelsByDataChannel(@Param("dataChannel") Integer dataChannel);

    /**
     * 分页查询需要处理型号的商品数据（关联品牌信息）
     * @param page 分页参数
     * @param sourcePlatform 数据来源平台，可为空
     * @param onlyWithBrand 是否只查询有品牌信息的数据
     * @return 商品型号处理数据列表
     */
    Page<ProductModelProcessDTO> selectProductModelProcessData(
            Page<ProductModelProcessDTO> page,
            @Param("sourcePlatform") String sourcePlatform,
            @Param("onlyWithBrand") Boolean onlyWithBrand
    );

    /**
     * 分页查询需要合并的商品型号数据
     * @param page 分页参数
     * @param sourcePlatform 数据来源平台，可为空
     * @param targetModel 指定要合并的型号，可为空
     * @return 商品型号合并数据列表
     */
    Page<ProductModelMergeDTO> selectProductModelMergeData(
            Page<ProductModelMergeDTO> page,
            @Param("sourcePlatform") String sourcePlatform,
            @Param("targetModel") String targetModel
    );

    /**
     * 分页查询需要合并的商品SKU数据
     * @param page 分页参数
     * @param sourcePlatform 数据来源平台，可为空
     * @param targetSpuId 指定要合并的SPU ID，可为空
     * @return 商品SKU合并数据列表
     */
    Page<ProductSkuMergeDTO> selectProductSkuMergeData(
            Page<ProductSkuMergeDTO> page,
            @Param("sourcePlatform") String sourcePlatform,
            @Param("targetSpuId") String targetSpuId
    );

    /**
     * 查询所有不重复的SPU ID列表
     * @param sourcePlatform 数据来源平台，可为空
     * @return 不重复的SPU ID列表
     */
    List<String> selectDistinctSpuIds(@Param("sourcePlatform") String sourcePlatform);

    /**
     * 根据SPU ID查询UPC信息
     * @param spuIds SPU ID列表
     * @return UPC信息列表
     */
    List<ProductModelUpcDTO> selectUpcInfoBySpuIds(@Param("spuIds") List<String> spuIds);

    /**
     * 根据标准化型号查询SPU记录（忽略大小写和空格）
     * @param normalizedModel 标准化后的型号（去除空格并转小写）
     * @return 匹配的记录列表
     */
    List<ProductDataSimplify> selectByNormalizedModel(@Param("normalizedModel") String normalizedModel);

    /**
     * 根据标准化字段查询SKU记录（忽略大小写和空格）
     * @param normalizedModel 标准化后的型号
     * @param normalizedColor 标准化后的颜色
     * @param normalizedStorage 标准化后的存储
     * @param normalizedServiceProvider 标准化后的服务提供商
     * @param normalizedCondition 标准化后的状态
     * @return 匹配的记录列表
     */
    List<ProductDataSimplify> selectByNormalizedFields(
            @Param("normalizedModel") String normalizedModel,
            @Param("normalizedColor") String normalizedColor,
            @Param("normalizedStorage") String normalizedStorage,
            @Param("normalizedServiceProvider") String normalizedServiceProvider,
            @Param("normalizedCondition") String normalizedCondition
    );

    /**
     * 根据skuId和spuId查询评论数量
     * @param skuId SKU ID
     * @param spuId SPU ID
     * @return 评论数量
     */
    @Select("SELECT review_number FROM product_data_simplify WHERE " +
            "(#{skuId} IS NOT NULL AND sku_id = #{skuId}) and " +
            "(#{spuId} IS NOT NULL AND spu_id = #{spuId}) " +
            "ORDER BY update_time DESC LIMIT 1")
    Integer selectReviewNumberBySkuAndSpu(@Param("skuId") String skuId, @Param("spuId") String spuId);

    /**
     * 根据skuId和spuId查询信息
     * @param skuId SKU ID
     * @param spuId SPU ID
     * @return 信息
     */
    @Select("SELECT * FROM product_data_simplify WHERE " +
            "(#{skuId} IS NOT NULL AND sku_id = #{skuId}) and " +
            "(#{spuId} IS NOT NULL AND spu_id = #{spuId}) " +
            "ORDER BY update_time DESC LIMIT 1")
    ProductDataSimplify selectOneBySkuAndSpu(@Param("skuId") String skuId, @Param("spuId") String spuId);

    /**
     * 根据skuId列表批量更新spuid
     * @param skuIds SKU ID列表
     * @param newSpuId 新的spuid
     * @return 更新的记录数
     */
    @Update("<script>" +
            "UPDATE product_data_simplify SET spu_id = #{newSpuId} WHERE sku_id IN " +
            "<foreach collection='skuIds' item='skuId' open='(' separator=',' close=')'>" +
            "#{skuId}" +
            "</foreach>" +
            "</script>")
    int updateSpuIdBySkuIds(@Param("skuIds") List<String> skuIds, @Param("newSpuId") String newSpuId);

}





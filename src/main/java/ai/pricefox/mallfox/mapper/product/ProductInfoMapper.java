package ai.pricefox.mallfox.mapper.product;


import ai.pricefox.mallfox.domain.product.ProductInfo;
import ai.pricefox.mallfox.model.response.ProductCardDTO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @desc 商品信息Mapper
 * @since 2025/7/7
 */
@Mapper
public interface ProductInfoMapper extends BaseMapper<ProductInfo> {

    /**
     * 根据不同排序规则获取商品卡片列表
     * @param brandName 如果是按标题筛选，则传入标题名
     * @param orderByClause 动态生成的ORDER BY子句
     * @param limit 获取数量
     * @return 商品卡片DTO列表
     */
    List<ProductCardDTO> findProductCardList(
            @Param("brandName") String brandName,
            @Param("orderByClause") String orderByClause,
            @Param("keyword") String keyword,
            @Param("limit") int limit
    );
}
package ai.pricefox.mallfox.mapper.product;

import ai.pricefox.mallfox.domain.product.ProductInventoryHistory;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 库存历史记录Mapper
 * 
 * <AUTHOR>
 * @since 2025-01-20
 */
@Mapper
public interface ProductInventoryHistoryMapper extends BaseMapper<ProductInventoryHistory> {
    
    /**
     * 根据SKU ID查询库存历史（按时间倒序）
     */
    @Select("SELECT * FROM product_inventory_history WHERE sku_id = #{skuId} ORDER BY record_time DESC")
    List<ProductInventoryHistory> selectBySkuId(@Param("skuId") String skuId);
    
    /**
     * 根据SPU ID查询库存历史（按时间倒序）
     */
    @Select("SELECT * FROM product_inventory_history WHERE spu_id = #{spuId} ORDER BY record_time DESC")
    List<ProductInventoryHistory> selectBySpuId(@Param("spuId") String spuId);
    
    /**
     * 查询指定offer的最新库存历史
     */
    @Select("SELECT * FROM product_inventory_history WHERE offer_id = #{offerId} ORDER BY record_time DESC LIMIT 1")
    ProductInventoryHistory selectLatestByOfferId(@Param("offerId") Long offerId);
    
    /**
     * 根据平台和变化类型查询历史记录
     */
    @Select("SELECT * FROM product_inventory_history WHERE source_platform = #{sourcePlatform} AND change_type = #{changeType} ORDER BY record_time DESC LIMIT #{limit}")
    List<ProductInventoryHistory> selectByPlatformAndChangeType(@Param("sourcePlatform") String sourcePlatform, 
                                                               @Param("changeType") Integer changeType,
                                                               @Param("limit") Integer limit);
    
    /**
     * 统计指定SKU的库存变化次数
     */
    @Select("SELECT COUNT(*) FROM product_inventory_history WHERE sku_id = #{skuId} AND change_type = 2")
    Integer countInventoryChanges(@Param("skuId") String skuId);
    
    /**
     * 查询指定SKU在指定天数内的库存历史
     */
    @Select("SELECT * FROM product_inventory_history WHERE sku_id = #{skuId} AND record_time >= DATE_SUB(NOW(), INTERVAL #{days} DAY) ORDER BY record_time DESC")
    List<ProductInventoryHistory> selectBySkuIdAndDays(@Param("skuId") String skuId, @Param("days") Integer days);
}
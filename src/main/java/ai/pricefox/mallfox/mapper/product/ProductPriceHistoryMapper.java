package ai.pricefox.mallfox.mapper.product;

import ai.pricefox.mallfox.domain.product.ProductPriceHistory;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 价格历史记录Mapper
 * 
 * <AUTHOR>
 * @since 2025-07-12
 */
@Mapper
public interface ProductPriceHistoryMapper extends BaseMapper<ProductPriceHistory> {
    
    /**
     * 根据SKU ID查询价格历史（按时间倒序）
     */
    @Select("SELECT * FROM product_price_history WHERE sku_id = #{skuId} ORDER BY record_time DESC")
    List<ProductPriceHistory> selectBySkuId(@Param("skuId") String skuId);
    
    /**
     * 根据SPU ID查询价格历史（按时间倒序）
     */
    @Select("SELECT * FROM product_price_history WHERE spu_id = #{spuId} ORDER BY record_time DESC")
    List<ProductPriceHistory> selectBySpuId(@Param("spuId") String spuId);

    /**
     * 根据SPU IDS查询每个SPU ID价格历史数量
     */
    @Select("SELECT spu_id, COUNT(*) AS count FROM product_price_history WHERE spu_id IN " +
            "(SELECT spu_id FROM product_data_offers WHERE spu_id IN (#{spuIds}))")
    Map<String, Integer> selectCountBySpuIds(@Param("spuIds") List<String> spuIds);

    @Select("SELECT sku_id, COUNT(*) AS count FROM product_price_history WHERE sku_id IN " +
            "(SELECT sku_id FROM product_data_offers WHERE sku_id IN (#{skuIds}))")
    Map<String, Integer> selectCountBySkuIds(@Param("skuIds") List<String> skuIds);

    /**
     * 根据时间范围查询价格历史
     */
    @Select("SELECT * FROM product_price_history WHERE record_time >= #{startTime} AND record_time <= #{endTime} ORDER BY record_time DESC")
    List<ProductPriceHistory> selectByTimeRange(@Param("startTime") LocalDateTime startTime, 
                                               @Param("endTime") LocalDateTime endTime);
    
    /**
     * 查询指定offer的最新价格历史
     */
    @Select("SELECT * FROM product_price_history WHERE offer_id = #{offerId} ORDER BY record_time DESC LIMIT 1")
    ProductPriceHistory selectLatestByOfferId(@Param("offerId") Long offerId);
    
    /**
     * 根据平台和变化类型查询历史记录
     */
    @Select("SELECT * FROM product_price_history WHERE source_platform = #{sourcePlatform} AND change_type = #{changeType} ORDER BY record_time DESC LIMIT #{limit}")
    List<ProductPriceHistory> selectByPlatformAndChangeType(@Param("sourcePlatform") String sourcePlatform, 
                                                           @Param("changeType") Integer changeType,
                                                           @Param("limit") Integer limit);
    
    /**
     * 统计指定SKU的价格变化次数
     */
    @Select("SELECT COUNT(*) FROM product_price_history WHERE sku_id = #{skuId} AND change_type = 2")
    Integer countPriceChanges(@Param("skuId") String skuId);
    
    /**
     * 查询指定SKU在指定天数内的价格历史
     */
    @Select("SELECT * FROM product_price_history WHERE sku_id = #{skuId} AND record_time >= DATE_SUB(NOW(), INTERVAL #{days} DAY) ORDER BY record_time DESC")
    List<ProductPriceHistory> selectBySkuIdAndDays(@Param("skuId") String skuId, @Param("days") Integer days);
    
    /**
     * 查询价格变化最大的记录（按变化金额绝对值排序）
     */
    @Select("SELECT * FROM product_price_history WHERE price_change_amount IS NOT NULL ORDER BY ABS(price_change_amount) DESC LIMIT #{limit}")
    List<ProductPriceHistory> selectTopPriceChanges(@Param("limit") Integer limit);
}
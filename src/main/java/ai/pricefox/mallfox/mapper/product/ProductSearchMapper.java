package ai.pricefox.mallfox.mapper.product;

import ai.pricefox.mallfox.model.param.ProductSearchRequest;
import ai.pricefox.mallfox.model.response.ProductSearchItemDTO;
import ai.pricefox.mallfox.model.response.FilterOption;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @desc 商品搜索
 * @since 2025/7/8
 */
@Mapper
public interface ProductSearchMapper {

    /**
     * 搜索商品
     */
    IPage<ProductSearchItemDTO> searchProducts(Page<ProductSearchRequest> page, @Param("request") ProductSearchRequest request);

    /**
     * 获取可用品牌选项
     */
    List<FilterOption> getAvailableBrands(@Param("request") ProductSearchRequest request);

    /**
     * 获取价格统计信息
     */
    Map<String, Object> getPriceStatistics(@Param("request") ProductSearchRequest request);

    /**
     * 获取评分分布
     */
//    List<FilterOption> getRatingDistribution(@Param("request") ProductSearchRequest request);

    /**
     * 获取可用存储容量选项
     */
    List<FilterOption> getAvailableStorageOptions(@Param("request") ProductSearchRequest request);

    /**
     * 获取可用颜色选项
     */
    List<FilterOption> getAvailableColorOptions(@Param("request") ProductSearchRequest request);

    /**
     * 获取可用屏幕尺寸选项
     */
    List<FilterOption> getAvailableScreenSizeOptions(@Param("request") ProductSearchRequest request);

    /**
     * 获取可用RAM选项
     */
    List<FilterOption> getAvailableRamOptions(@Param("request") ProductSearchRequest request);

    /**
     * 获取可用电池容量选项
     */
    List<FilterOption> getAvailableBatteryOptions(@Param("request") ProductSearchRequest request);
}

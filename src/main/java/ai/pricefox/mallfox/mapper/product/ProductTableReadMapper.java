package ai.pricefox.mallfox.mapper.product;

import ai.pricefox.mallfox.domain.product.ProductTableRead;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @desc 商品表记录已读状态Mapper
 * @since 2025/7/18
 */
@Mapper
public interface ProductTableReadMapper extends BaseMapper<ProductTableRead> {

    /**
     * 批量插入或更新已读状态
     */
    int batchInsertOrUpdate(@Param("list") List<ProductTableRead> list);

    /**
     * 批量查询已读状态
     */
    List<ProductTableRead> selectByTargetTableAndIds(@Param("targetTable") String targetTable, @Param("targetIds") List<Long> targetIds);
}
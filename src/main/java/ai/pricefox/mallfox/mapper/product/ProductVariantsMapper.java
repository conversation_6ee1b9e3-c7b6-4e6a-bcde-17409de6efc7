package ai.pricefox.mallfox.mapper.product;

import ai.pricefox.mallfox.domain.product.ProductVariants;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.math.BigDecimal;
import java.util.List;

/**
 * 商品变体信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-22
 */
@Mapper
public interface ProductVariantsMapper extends BaseMapper<ProductVariants> {

    /**
     * 根据SKU编码查询变体信息
     *
     * @param skuCode SKU编码
     * @return 变体信息
     */
    default ProductVariants findBySkuCode(String skuCode) {
        LambdaQueryWrapper<ProductVariants> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProductVariants::getSkuCode, skuCode)
               .last("LIMIT 1");
        return selectOne(wrapper);
    }

    /**
     * 根据SPU编码查询所有变体信息
     *
     * @param spuCode SPU编码
     * @return 变体信息列表
     */
    default List<ProductVariants> findBySpuCode(String spuCode) {
        LambdaQueryWrapper<ProductVariants> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProductVariants::getSpuCode, spuCode)
               .orderByAsc(ProductVariants::getSort);
        return selectList(wrapper);
    }
}
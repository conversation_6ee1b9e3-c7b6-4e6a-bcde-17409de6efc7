package ai.pricefox.mallfox.mapper.product;


import ai.pricefox.mallfox.model.param.ProductDataSearchRequest;
import ai.pricefox.mallfox.model.response.ProductDataViewResponse;
import ai.pricefox.mallfox.model.response.SkuMasterViewDTO;
import ai.pricefox.mallfox.model.response.SpuGroupViewResponse;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ProductViewMapper {

    /**
     * 分页查询作为“主视图”的Amazon商品报价列表。
     */
    IPage<SkuMasterViewDTO> selectAmazonMasterSkus(Page<ProductDataSearchRequest> page, @Param("request") ProductDataSearchRequest request);

    /**
     * 分页查询 product_data_simplify 表中的 SPU 列表
     */
    IPage<SpuGroupViewResponse> selectSpuList(Page<ProductDataSearchRequest> page, @Param("request") ProductDataSearchRequest request);

    /**
     * 根据 SPU ID 列表查询对应的 SKU 列表
     */
    List<SkuMasterViewDTO> selectSkuListBySpuIds(@Param("spuIds") List<String> spuIds);

    /**
     * 分页查询指定 SPU ID 下的 SKU 列表
     */
    IPage<SkuMasterViewDTO> selectSkuListBySpuIdWithPage(Page<ProductDataSearchRequest> page, @Param("spuId") String spuId);

    /**
     * 根据自建的SKU ID，查询该SKU在所有平台上的报价详情列表。
     */
    List<ProductDataViewResponse> selectAllPlatformOffers(@Param("skuId") String skuId);

    /**
     * 根据自建的SKU IDS，查询该SKU在所有平台上的报价详情列表。
     */
    List<ProductDataViewResponse> selectAllPlatformOffersBySkuIds(@Param("skuIds") List<String> skuIds);
}

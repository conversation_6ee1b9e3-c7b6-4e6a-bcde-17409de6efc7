package ai.pricefox.mallfox.mapper.product;

import ai.pricefox.mallfox.domain.product.StandardProduct;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 标准商品信息Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Mapper
public interface StandardProductMapper extends BaseMapper<StandardProduct> {

    /**
     * 根据SPU编码查询标准商品信息
     *
     * @param spuCode SPU编码
     * @return 标准商品信息
     */
    default StandardProduct findBySpuCode(String spuCode) {
        LambdaQueryWrapper<StandardProduct> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StandardProduct::getSpuCode, spuCode)
               .last("LIMIT 1");
        return selectOne(wrapper);
    }
}
package ai.pricefox.mallfox.mapper.standard;

import ai.pricefox.mallfox.domain.standard.StandardAttribute;
import ai.pricefox.mallfox.domain.standard.StandardCategory;
import ai.pricefox.mallfox.utils.AdminTokenUtil;
import ai.pricefox.mallfox.vo.attribute.AttributeListReqVO;
import ai.pricefox.mallfox.vo.attribute.AttributePageReqVO;
import ai.pricefox.mallfox.vo.attribute.SpecPageReqVO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import cn.hutool.core.util.StrUtil;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 标准属性库Mapper接口
 */
@Mapper
public interface StandardAttributeMapper extends BaseMapper<StandardAttribute> {

    /**
     * 根据分类编码统计属性数量
     */
    default long countByCategoryCode(String categoryCode) {
        LambdaQueryWrapper<StandardAttribute> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StandardAttribute::getStandardCategoryCode, categoryCode)
               .eq(StandardAttribute::getDeleted, false);
        return selectCount(wrapper);
    }

    /**
     * 根据分类编码查询属性列表
     */
    default List<StandardAttribute> selectByCategoryCode(String categoryCode) {
        LambdaQueryWrapper<StandardAttribute> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StandardAttribute::getStandardCategoryCode, categoryCode)
               .eq(StandardAttribute::getDeleted, false);
        return selectList(wrapper);
    }

    /**
     * 根据属性编码查询属性
     */
    default StandardAttribute selectByAttributeCode(String attributeCode) {
        LambdaQueryWrapper<StandardAttribute> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StandardAttribute::getAttributeCode, attributeCode)
               .eq(StandardAttribute::getDeleted, false);
        return selectOne(wrapper);
    }

    /**
     * 分页查询属性列表
     */
    default Page<StandardAttribute> selectAttributePageList(Page<StandardAttribute> page, AttributePageReqVO reqVO) {
        LambdaQueryWrapper<StandardAttribute> wrapper = new LambdaQueryWrapper<>();

        if (StrUtil.isNotBlank(reqVO.getStandardCategoryNameEn())) {
            wrapper.like(StandardAttribute::getStandardCategoryNameEn, reqVO.getStandardCategoryNameEn());
        }
        if (StrUtil.isNotBlank(reqVO.getAttributeCode())) {
            wrapper.like(StandardAttribute::getAttributeCode, reqVO.getAttributeCode());
        }
        if (StrUtil.isNotBlank(reqVO.getAttributeNameEn())) {
            wrapper.like(StandardAttribute::getAttributeNameEn, reqVO.getAttributeNameEn());
        }
        if (StrUtil.isNotBlank(reqVO.getAttributeNameCn())) {
            wrapper.like(StandardAttribute::getAttributeNameCn, reqVO.getAttributeNameCn());
        }

        wrapper.eq(StandardAttribute::getDeleted, false)
               .orderByDesc(StandardAttribute::getCreateDate)
               .orderByAsc(StandardAttribute::getStandardCategoryNameEn);

        return selectPage(page, wrapper);
    }

    /**
     * 查询导出数据列表
     */
    default List<StandardAttribute> selectAttributeExportList(AttributePageReqVO reqVO) {
        LambdaQueryWrapper<StandardAttribute> wrapper = new LambdaQueryWrapper<>();

        if (StrUtil.isNotBlank(reqVO.getStandardCategoryNameEn())) {
            wrapper.like(StandardAttribute::getStandardCategoryNameEn, reqVO.getStandardCategoryNameEn());
        }
        if (StrUtil.isNotBlank(reqVO.getAttributeCode())) {
            wrapper.like(StandardAttribute::getAttributeCode, reqVO.getAttributeCode());
        }
        if (StrUtil.isNotBlank(reqVO.getAttributeNameEn())) {
            wrapper.like(StandardAttribute::getAttributeNameEn, reqVO.getAttributeNameEn());
        }
        if (StrUtil.isNotBlank(reqVO.getAttributeNameCn())) {
            wrapper.like(StandardAttribute::getAttributeNameCn, reqVO.getAttributeNameCn());
        }

        wrapper.eq(StandardAttribute::getDeleted, false)
               .orderByDesc(StandardAttribute::getCreateDate)
               .orderByAsc(StandardAttribute::getStandardCategoryNameEn);

        return selectList(wrapper);
    }

    /**
     * 检查属性是否有映射关系
     *
     * @param attributeCode 属性编码
     * @return 映射关系数量
     */
    long countAttributeMappings(@Param("attributeCode") String attributeCode);

    /**
     * 根据分类编码查询分类信息
     */
    default StandardCategory findCategoryByCode(String categoryCode) {
        // 这个方法需要在StandardCategoryMapper中实现，这里只是占位
        return null;
    }

    /**
     * 检查属性编码是否存在
     */
    default boolean existsByAttributeCode(String attributeCode) {
        LambdaQueryWrapper<StandardAttribute> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StandardAttribute::getAttributeCode, attributeCode)
               .eq(StandardAttribute::getDeleted, false);
        return selectCount(wrapper) > 0;
    }

    /**
     * 根据属性名称和分类编码查询属性
     */
    default StandardAttribute selectByNameAndCategoryCode(String attributeNameEn, String categoryCode) {
        LambdaQueryWrapper<StandardAttribute> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StandardAttribute::getAttributeNameEn, attributeNameEn)
               .eq(StandardAttribute::getStandardCategoryCode, categoryCode)
               .eq(StandardAttribute::getDeleted, false);
        return selectOne(wrapper);
    }

    /**
     * 批量检查属性映射关系
     */
    default List<Long> selectIdsWithMappings(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return new ArrayList<>();
        }

        List<Long> idsWithMappings = new ArrayList<>();
        for (Long id : ids) {
            StandardAttribute attribute = selectById(id);
            if (attribute != null && !Boolean.TRUE.equals(attribute.getDeleted())) {
                long mappingCount = countAttributeMappings(attribute.getAttributeCode());
                if (mappingCount > 0) {
                    idsWithMappings.add(id);
                }
            }
        }
        return idsWithMappings;
    }

    /**
     * 批量逻辑删除属性
     */
    default int batchLogicalDelete(List<Long> ids, String updateUsername) {
        if (ids == null || ids.isEmpty()) {
            return 0;
        }

        int count = 0;
        for (Long id : ids) {
            StandardAttribute attribute = selectById(id);
            if (attribute != null && !Boolean.TRUE.equals(attribute.getDeleted())) {
                attribute.setDeleted(true);
                attribute.setUpdateDate(LocalDateTime.now());
                attribute.setUpdateUsername(updateUsername);
                count += updateById(attribute);
            }
        }
        return count;
    }

    /**
     * 查询最大属性编码
     */
    default String selectMaxAttributeCode() {
        LambdaQueryWrapper<StandardAttribute> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(StandardAttribute::getAttributeCode)
               .like(StandardAttribute::getAttributeCode, "ATTR")
               .orderByDesc(StandardAttribute::getAttributeCode)
               .last("LIMIT 1");
        StandardAttribute attribute = selectOne(wrapper);
        return attribute != null ? attribute.getAttributeCode() : null;
    }

    /**
     * 更新规格状态
     */
    default int updateSpecStatus(Long id, boolean isSpec) {
        StandardAttribute attribute = selectById(id);
        if (attribute == null || Boolean.TRUE.equals(attribute.getDeleted())) {
            return 0;
        }

        attribute.setIsSpec(isSpec);
        attribute.setUpdateDate(LocalDateTime.now());
        attribute.setUpdateUsername(AdminTokenUtil.getCurrentUsername());

        return updateById(attribute);
    }

    /**
     * 分页查询规格列表
     */
    default Page<StandardAttribute> selectSpecPageList(Page<StandardAttribute> page, SpecPageReqVO reqVO) {
        LambdaQueryWrapper<StandardAttribute> wrapper = new LambdaQueryWrapper<>();

        // 只查询规格数据
        wrapper.eq(StandardAttribute::getIsSpec, true);

        if (StrUtil.isNotBlank(reqVO.getStandardCategoryNameEn())) {
            wrapper.like(StandardAttribute::getStandardCategoryNameEn, reqVO.getStandardCategoryNameEn());
        }
        if (StrUtil.isNotBlank(reqVO.getAttributeCode())) {
            wrapper.like(StandardAttribute::getAttributeCode, reqVO.getAttributeCode());
        }
        if (StrUtil.isNotBlank(reqVO.getAttributeNameEn())) {
            wrapper.like(StandardAttribute::getAttributeNameEn, reqVO.getAttributeNameEn());
        }
        if (StrUtil.isNotBlank(reqVO.getAttributeNameCn())) {
            wrapper.like(StandardAttribute::getAttributeNameCn, reqVO.getAttributeNameCn());
        }

        wrapper.eq(StandardAttribute::getDeleted, false)
               .orderByDesc(StandardAttribute::getCreateDate)
               .orderByAsc(StandardAttribute::getStandardCategoryNameEn);

        return selectPage(page, wrapper);
    }

    /**
     * 查询属性列表
     */
    default List<StandardAttribute> selectAttributeList(AttributeListReqVO reqVO) {
        LambdaQueryWrapper<StandardAttribute> wrapper = new LambdaQueryWrapper<>();

        if (StrUtil.isNotBlank(reqVO.getAttributeCode())) {
            wrapper.eq(StandardAttribute::getAttributeCode, reqVO.getAttributeCode());
        }
        if (StrUtil.isNotBlank(reqVO.getAttributeNameEn())) {
            wrapper.like(StandardAttribute::getAttributeNameEn, reqVO.getAttributeNameEn());
        }

        wrapper.eq(StandardAttribute::getDeleted, false)
               .orderByDesc(StandardAttribute::getCreateDate)
               .orderByAsc(StandardAttribute::getStandardCategoryNameEn);

        return selectList(wrapper);
    }
}

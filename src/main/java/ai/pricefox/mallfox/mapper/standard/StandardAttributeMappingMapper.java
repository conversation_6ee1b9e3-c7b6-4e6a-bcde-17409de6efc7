package ai.pricefox.mallfox.mapper.standard;

import ai.pricefox.mallfox.domain.standard.StandardAttributeMapping;
import ai.pricefox.mallfox.model.param.AttributeMappingQueryRequest;
import ai.pricefox.mallfox.model.response.AttributeMappingResponse;
import ai.pricefox.mallfox.model.response.AttributeMappingDetailResponse;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 标准属性映射Mapper
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
@Mapper
public interface StandardAttributeMappingMapper extends BaseMapper<StandardAttributeMapping> {

    int deleteByPrimaryKey(Long id);

    int insert(StandardAttributeMapping record);

    int insertSelective(StandardAttributeMapping record);

    StandardAttributeMapping selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(StandardAttributeMapping record);

    int updateByPrimaryKey(StandardAttributeMapping record);

    /**
     * 分页查询属性映射列表（关联标准属性和类目表）
     *
     * @param page 分页参数
     * @param queryRequest 查询条件
     * @return 属性映射响应列表
     */
    Page<AttributeMappingResponse> selectAttributeMappingPage(Page<AttributeMappingResponse> page,
                                                            @Param("request") AttributeMappingQueryRequest queryRequest);

    /**
     * 查询属性映射列表（不分页，用于导出）
     *
     * @param queryRequest 查询条件
     * @return 属性映射响应列表
     */
    List<AttributeMappingResponse> selectAttributeMappingList(@Param("request") AttributeMappingQueryRequest queryRequest);

    /**
     * 根据ID查询属性映射详情
     *
     * @param id 属性映射ID
     * @return 属性映射详情
     */
    AttributeMappingDetailResponse selectAttributeMappingDetailById(@Param("id") Long id);

    /**
     * 根据属性编码统计映射数量
     */
    default long countByAttributeCode(String attributeCode) {
        LambdaQueryWrapper<StandardAttributeMapping> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StandardAttributeMapping::getAttributeCode, attributeCode)
                .eq(StandardAttributeMapping::getDeleted, false);
        return selectCount(wrapper);
    }

    /**
     * 根据属性编码查询映射列表
     */
    default List<StandardAttributeMapping> selectByAttributeCode(String attributeCode) {
        LambdaQueryWrapper<StandardAttributeMapping> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StandardAttributeMapping::getAttributeCode, attributeCode)
                .eq(StandardAttributeMapping::getDeleted, false);
        return selectList(wrapper);
    }
}
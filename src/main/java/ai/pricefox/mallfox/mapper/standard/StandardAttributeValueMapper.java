package ai.pricefox.mallfox.mapper.standard;

import ai.pricefox.mallfox.domain.standard.StandardAttributeValue;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 标准属性值Mapper接口
 */
@Mapper
public interface StandardAttributeValueMapper extends BaseMapper<StandardAttributeValue> {

    /**
     * 根据属性编码统计属性值数量
     */
    default long countByAttributeCode(String attributeCode) {
        LambdaQueryWrapper<StandardAttributeValue> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StandardAttributeValue::getAttributeCode, attributeCode)
               .eq(StandardAttributeValue::getDeleted, false);
        return selectCount(wrapper);
    }

    /**
     * 根据属性编码查询属性值列表
     */
    default List<StandardAttributeValue> selectByAttributeCode(String attributeCode) {
        LambdaQueryWrapper<StandardAttributeValue> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StandardAttributeValue::getAttributeCode, attributeCode)
               .eq(StandardAttributeValue::getDeleted, false)
                .orderByDesc(StandardAttributeValue::getCreateDate);
        return selectList(wrapper);
    }

    /**
     * 根据属性值编码查询属性值
     */
    default StandardAttributeValue selectByValueCode(String valueCode) {
        LambdaQueryWrapper<StandardAttributeValue> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StandardAttributeValue::getValueCode, valueCode)
               .eq(StandardAttributeValue::getDeleted, false);
        return selectOne(wrapper);
    }

    /**
     * 根据属性编码和属性值名称查询属性值
     */
    default StandardAttributeValue selectByAttributeCodeAndValueName(String attributeCode, String valueEn) {
        LambdaQueryWrapper<StandardAttributeValue> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StandardAttributeValue::getAttributeCode, attributeCode)
               .eq(StandardAttributeValue::getValueEn, valueEn)
               .eq(StandardAttributeValue::getDeleted, false);
        return selectOne(wrapper);
    }

    /**
     * 检查属性值编码是否存在
     */
    default boolean existsByValueCode(String valueCode) {
        LambdaQueryWrapper<StandardAttributeValue> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StandardAttributeValue::getValueCode, valueCode)
               .eq(StandardAttributeValue::getDeleted, false);
        return selectCount(wrapper) > 0;
    }

    /**
     * 批量检查属性值映射关系
     */
    default List<Long> selectIdsWithMappings(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return new ArrayList<>();
        }

        List<Long> idsWithMappings = new ArrayList<>();
        for (Long id : ids) {
            StandardAttributeValue attributeValue = selectById(id);
            if (attributeValue != null && !Boolean.TRUE.equals(attributeValue.getDeleted())) {
                if (hasValueMapping(attributeValue.getValueCode())) {
                    idsWithMappings.add(id);
                }
            }
        }
        return idsWithMappings;
    }

    /**
     * 批量逻辑删除属性值
     */
    default int batchLogicalDelete(List<Long> ids, String updateUsername) {
        if (ids == null || ids.isEmpty()) {
            return 0;
        }

        int count = 0;
        for (Long id : ids) {
            StandardAttributeValue attributeValue = selectById(id);
            if (attributeValue != null && !Boolean.TRUE.equals(attributeValue.getDeleted())) {
                attributeValue.setDeleted(true);
                attributeValue.setUpdateDate(LocalDateTime.now());
                attributeValue.setUpdateUsername(updateUsername);
                count += updateById(attributeValue);
            }
        }
        return count;
    }

    /**
     * 根据属性编码查询属性值（用于导出）
     */
    default List<StandardAttributeValue> selectByAttributeCodeForExport(String attributeCode) {
        LambdaQueryWrapper<StandardAttributeValue> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StandardAttributeValue::getAttributeCode, attributeCode)
               .eq(StandardAttributeValue::getDeleted, false)
               .orderByDesc(StandardAttributeValue::getCreateDate);
        return selectList(wrapper);
    }

    /**
     * 检查属性值是否有映射关系
     *
     * @param valueCode 属性值编码
     * @return 是否有映射关系
     */
    boolean hasValueMapping(@Param("valueCode") String valueCode);

    /**
     * 查询最大属性值编码
     */
    default String selectMaxValueCode() {
        LambdaQueryWrapper<StandardAttributeValue> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(StandardAttributeValue::getValueCode)
               .like(StandardAttributeValue::getValueCode, "ATVL")
               .orderByDesc(StandardAttributeValue::getValueCode)
               .last("LIMIT 1");
        StandardAttributeValue attributeValue = selectOne(wrapper);
        return attributeValue != null ? attributeValue.getValueCode() : null;
    }
}

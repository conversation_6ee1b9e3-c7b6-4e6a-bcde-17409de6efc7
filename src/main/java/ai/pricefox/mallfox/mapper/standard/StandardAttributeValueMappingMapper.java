package ai.pricefox.mallfox.mapper.standard;

import ai.pricefox.mallfox.domain.standard.StandardAttributeValueMapping;
import ai.pricefox.mallfox.model.excel.AttributeValueMappingExportExcelEntity;
import ai.pricefox.mallfox.model.param.AttributeValueMappingQueryRequest;
import ai.pricefox.mallfox.model.response.AttributeValueMappingDetailResponse;
import ai.pricefox.mallfox.model.response.AttributeValueMappingPageInfo;
import ai.pricefox.mallfox.model.response.AttributeValueMappingResponse;
import ai.pricefox.mallfox.model.response.AttributeValueOption;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 标准属性值映射Mapper
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
@Mapper
public interface StandardAttributeValueMappingMapper extends BaseMapper<StandardAttributeValueMapping> {

    int deleteByPrimaryKey(Long id);

    int insert(StandardAttributeValueMapping record);

    int insertSelective(StandardAttributeValueMapping record);

    StandardAttributeValueMapping selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(StandardAttributeValueMapping record);

    int updateByPrimaryKey(StandardAttributeValueMapping record);

    /**
     * 根据属性编码统计属性值映射数量
     */
    default long countByAttributeCode(String attributeCode) {
        LambdaQueryWrapper<StandardAttributeValueMapping> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StandardAttributeValueMapping::getAttributeCode, attributeCode)
                .eq(StandardAttributeValueMapping::getDeleted, false);
        return selectCount(wrapper);
    }

    /**
     * 根据属性编码查询属性值映射列表
     */
    default List<StandardAttributeValueMapping> selectByAttributeCode(String attributeCode) {
        LambdaQueryWrapper<StandardAttributeValueMapping> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StandardAttributeValueMapping::getAttributeCode, attributeCode)
                .eq(StandardAttributeValueMapping::getDeleted, false);
        return selectList(wrapper);
    }


    /**
     * 根据属性映射ID获取页面基础信息
     *
     * @param attributeId 属性映射ID
     * @return 页面基础信息
     */
    AttributeValueMappingPageInfo selectPageInfoByAttributeId(@Param("attributeId") Long attributeId);

    /**
     * 分页查询属性值映射列表
     *
     * @param page 分页参数
     * @param queryRequest 查询条件
     * @return 属性值映射列表
     */
    Page<AttributeValueMappingResponse> selectAttributeValueMappingPage(Page<AttributeValueMappingResponse> page,
                                                                       @Param("request") AttributeValueMappingQueryRequest queryRequest);
    /**
     * 获取属性值选项
     *
     * @param attributeCode 属性编码
     * @param valueName 属性值名称
     * @return 属性值选项列表
     */
    List<AttributeValueOption> selectAttributeValueOptions(@Param("attributeCode") String attributeCode,
                                                          @Param("valueName") String valueName);

    /**
     * 获取属性值映射详情
     *
     * @param valueCode 属性值编码
     * @return 属性值映射详情
     */
    AttributeValueMappingDetailResponse selectAttributeValueMappingDetail(@Param("valueCode") String valueCode);

    /**
     * 根据属性值编码删除映射
     *
     * @param valueCode 属性值编码
     * @return 删除数量
     */
    int deleteByValueCode(@Param("valueCode") String valueCode);

    /**
     * 查询导出数据
     *
     * @param queryRequest 查询条件
     * @return 导出数据列表
     */
    List<AttributeValueMappingExportExcelEntity> selectExportData(@Param("request") AttributeValueMappingQueryRequest queryRequest);
}

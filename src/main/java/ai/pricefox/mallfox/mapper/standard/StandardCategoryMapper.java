package ai.pricefox.mallfox.mapper.standard;

import ai.pricefox.mallfox.domain.standard.StandardCategory;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.util.StringUtils;
import cn.hutool.core.util.StrUtil;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import java.util.List;

/**
 * 标准品类库Mapper接口
 */
@Mapper
public interface StandardCategoryMapper extends BaseMapper<StandardCategory> {

    /**
     * 检查是否有子分类
     */
    default long countChildrenByParentId(Long parentId) {
        LambdaQueryWrapper<StandardCategory> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StandardCategory::getParent, parentId);
        return selectCount(wrapper);
    }

    /**
     * 根据英文名称检查重复
     */
    default StandardCategory findByCategoryNameEn(String categoryNameEn) {
        LambdaQueryWrapper<StandardCategory> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StandardCategory::getCategoryNameEn, categoryNameEn);
        return selectOne(wrapper);
    }

    /**
     * 根据编码检查重复
     */
    default StandardCategory findByCategoryCode(String categoryCode) {
        LambdaQueryWrapper<StandardCategory> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StandardCategory::getCategoryCode, categoryCode)
               .eq(StandardCategory::getDeleted, false);
        return selectOne(wrapper);
    }

    /**
     * 根据编码查询激活的分类
     */
    default StandardCategory findActiveCategoryByCode(String categoryCode) {
        LambdaQueryWrapper<StandardCategory> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StandardCategory::getCategoryCode, categoryCode)
               .eq(StandardCategory::getIsActive, true)
               .eq(StandardCategory::getDeleted, false);
        return selectOne(wrapper);
    }

    /**
     * 获取树形结构数据（按英文名称排序）
     */
    default List<StandardCategory> selectTreeList(String categoryCode, String categoryNameEn, String categoryNameCn) {
        LambdaQueryWrapper<StandardCategory> wrapper = new LambdaQueryWrapper<>();

        // 条件筛选
        if (StringUtils.hasText(categoryCode)) {
            wrapper.eq(StandardCategory::getCategoryCode, categoryCode);
        }
        if (StringUtils.hasText(categoryNameEn)) {
            wrapper.like(StandardCategory::getCategoryNameEn, categoryNameEn);
        }
        if (StringUtils.hasText(categoryNameCn)) {
            wrapper.like(StandardCategory::getCategoryNameCn, categoryNameCn);
        }

        // 排序：先按level，再按英文名称
        wrapper.orderByAsc(StandardCategory::getLevel)
               .orderByAsc(StandardCategory::getCategoryNameEn);

        return selectList(wrapper);
    }

    /**
     * 分页查询列表
     */
    default IPage<StandardCategory> selectPageList(Page<StandardCategory> page, String categoryCode, String categoryNameEn, String categoryNameCn) {
        LambdaQueryWrapper<StandardCategory> wrapper = new LambdaQueryWrapper<>();

        if (StringUtils.hasText(categoryCode)) {
            wrapper.eq(StandardCategory::getCategoryCode, categoryCode);
        }
        if (StringUtils.hasText(categoryNameEn)) {
            wrapper.like(StandardCategory::getCategoryNameEn, categoryNameEn);
        }
        if (StringUtils.hasText(categoryNameCn)) {
            wrapper.like(StandardCategory::getCategoryNameCn, categoryNameCn);
        }

        wrapper.orderByAsc(StandardCategory::getLevel)
               .orderByAsc(StandardCategory::getCategoryNameEn);

        return selectPage(page, wrapper);
    }

    /**
     * 根据分类编码构建类目路径
     * 格式：/Level1/Level2/Level3
     */
    default String buildCategoryPath(String categoryCode) {
        if (StrUtil.isBlank(categoryCode)) {
            return "";
        }

        // 1. 根据编码查找分类
        StandardCategory category = findByCategoryCode(categoryCode);
        if (category == null) {
            return "";
        }

        // 2. 构建路径
        List<String> pathParts = new ArrayList<>();
        StandardCategory current = category;

        // 3. 向上递归查找父级分类
        while (current != null) {
            pathParts.addFirst(current.getCategoryNameEn()); // 插入到开头

            // 查找父级分类
            if (current.getParent() != null && current.getParent() != 0) {
                current = selectById(current.getParent());
            } else {
                break;
            }
        }

        // 4. 构建路径字符串
        return "/" + String.join("/", pathParts);
    }

    /**
     * 批量构建类目路径
     */
    default Map<String, String> buildCategoryPaths(List<String> categoryCodes) {
        Map<String, String> pathMap = new HashMap<>();
        if (categoryCodes == null || categoryCodes.isEmpty()) {
            return pathMap;
        }

        for (String categoryCode : categoryCodes) {
            String path = buildCategoryPath(categoryCode);
            pathMap.put(categoryCode, path);
        }

        return pathMap;
    }

    /**
     * 查询最大分类编码
     */
    default String selectMaxCategoryCode() {
        LambdaQueryWrapper<StandardCategory> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(StandardCategory::getCategoryCode)
               .like(StandardCategory::getCategoryCode, "CATG")
               .orderByDesc(StandardCategory::getCategoryCode)
               .last("LIMIT 1");
        StandardCategory category = selectOne(wrapper);
        return category != null ? category.getCategoryCode() : null;
    }

}





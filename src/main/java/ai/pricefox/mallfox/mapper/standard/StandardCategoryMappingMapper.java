package ai.pricefox.mallfox.mapper.standard;

import ai.pricefox.mallfox.domain.standard.StandardCategory;
import ai.pricefox.mallfox.domain.standard.StandardCategoryMapping;
import ai.pricefox.mallfox.model.response.CategoryMappingResponse;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 平台与标准品类映射规则Mapper接口
 */
@Mapper
public interface StandardCategoryMappingMapper extends BaseMapper<StandardCategoryMapping> {

    /**
     * 根据标准分类编码统计映射数量
     */
    default long countByStandardCategoryCode(String standardCategoryCode) {
        LambdaQueryWrapper<StandardCategoryMapping> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StandardCategoryMapping::getStandardCategoryCode, standardCategoryCode);
        return selectCount(wrapper);
    }

    /**
     * 根据标准分类编码查询映射列表
     */
    default List<StandardCategoryMapping> selectByStandardCategoryCode(String standardCategoryCode) {
        LambdaQueryWrapper<StandardCategoryMapping> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StandardCategoryMapping::getStandardCategoryCode, standardCategoryCode);
        return selectList(wrapper);
    }

    /**
     * 查询类目映射数据用于导出
     *
     * @param categoryCode 类目编码
     * @param categoryName 类目名称
     * @param categoryNameCn 类目中文名称
     * @return 类目映射列表
     */
    List<CategoryMappingResponse> selectCategoryMappingForExport(
            @Param("categoryCode") String categoryCode,
            @Param("categoryName") String categoryName,
            @Param("categoryNameCn") String categoryNameCn);

}

package ai.pricefox.mallfox.mapper.standard;

import ai.pricefox.mallfox.domain.standard.StandardProductMargeData;
import ai.pricefox.mallfox.model.param.ProductDataSearchRequest;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper
public interface StandardProductMargeDataMapper extends BaseMapper<StandardProductMargeData> {
    /**
     * 根据productIdentifier查询标准商品合并数据
     *
     * @param productIdentifier 产品标识符
     * @return 标准商品合并数据
     */
    StandardProductMargeData selectByProductIdentifier(@Param("productIdentifier") String productIdentifier);

    /**
     * 根据动态表头分页查询商品数据
     *
     * @param page 分页参数
     * @param request 查询条件
     * @return 分页结果
     */
    Page<StandardProductMargeData> selectPage(@Param("page") Page<StandardProductMargeData> page, @Param("request") ProductDataSearchRequest request);
}
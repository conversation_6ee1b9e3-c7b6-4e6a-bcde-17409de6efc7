package ai.pricefox.mallfox.mapper.supplement;

import ai.pricefox.mallfox.domain.supplement.SalesDataSupplementRecord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 销量数据补充记录Mapper
 * 
 * <AUTHOR>
 * @since 2025-01-14
 */
@Mapper
public interface SalesDataSupplementRecordMapper extends BaseMapper<SalesDataSupplementRecord> {

    /**
     * 根据月份查询所有补充记录的offer ID
     * 
     * @param supplementMonth 补充月份
     * @return offer ID列表
     */
    @Select("SELECT offer_id FROM sales_data_supplement_record WHERE supplement_month = #{supplementMonth}")
    List<Long> selectOfferIdsByMonth(@Param("supplementMonth") String supplementMonth);

    /**
     * 根据月份查询所有补充记录
     * 
     * @param supplementMonth 补充月份
     * @return 补充记录列表
     */
    @Select("SELECT * FROM sales_data_supplement_record WHERE supplement_month = #{supplementMonth}")
    List<SalesDataSupplementRecord> selectRecordsByMonth(@Param("supplementMonth") String supplementMonth);

    /**
     * 查询所有不重复的offer ID (用于获取所有历史补充过的记录)
     * 
     * @return 不重复的offer ID列表
     */
    @Select("SELECT DISTINCT offer_id FROM sales_data_supplement_record")
    List<Long> selectAllDistinctOfferIds();
}
package ai.pricefox.mallfox.mapper.system;

import ai.pricefox.mallfox.domain.system.SysDict;
import ai.pricefox.mallfox.vo.dict.SysDictPageReqVO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * <p>
 * 字典表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
@Mapper
public interface SysDictMapper extends BaseMapper<SysDict> {

    /**
     * 分页查询字典
     *
     * @param reqVO 分页查询请求
     * @return 分页结果
     */
    default Page<SysDict> selectDictPage(SysDictPageReqVO reqVO) {
        Page<SysDict> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapper<SysDict> queryWrapper = new LambdaQueryWrapper<>();

        if (StringUtils.hasText(reqVO.getType()) || StringUtils.hasText(reqVO.getDescription())) {
            if (StringUtils.hasText(reqVO.getType())) {
                queryWrapper.like(SysDict::getType, reqVO.getType());
            }
            if (StringUtils.hasText(reqVO.getDescription())) {
                queryWrapper.or().like(SysDict::getDescription, reqVO.getDescription());
            }
        }

        return this.selectPage(page, queryWrapper);
    }

    /**
     * 根据类型查询字典
     *
     * @param type 字典类型
     * @return 字典
     */
    default SysDict selectDictByType(String type) {
        LambdaQueryWrapper<SysDict> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysDict::getType, type);
        return this.selectOne(queryWrapper);
    }

    /**
     * 插入字典
     *
     * @param sysDict 字典对象
     * @return 插入结果
     */
    default boolean insertDict(SysDict sysDict) {
        return this.insert(sysDict) > 0;
    }

    /**
     * 更新字典
     *
     * @param sysDict 字典对象
     * @return 更新结果
     */
    default boolean updateDictById(SysDict sysDict) {
        return this.updateById(sysDict) > 0;
    }

    /**
     * 根据ID删除字典
     *
     * @param id 字典ID
     * @return 删除结果
     */
    default boolean deleteDictById(Long id) {
        return this.deleteById(id) > 0;
    }

    /**
     * 根据ID查询字典
     *
     * @param id 字典ID
     * @return 字典对象
     */
    default SysDict selectDictById(Long id) {
        return this.selectById(id);
    }

    /**
     * 查询所有字典
     *
     * @return 字典列表
     */
    default List<SysDict> selectAllDicts() {
        return this.selectList(null);
    }

}

package ai.pricefox.mallfox.mapper.system;

import ai.pricefox.mallfox.domain.system.SysDictValue;
import ai.pricefox.mallfox.vo.dict.SysDictValuePageReqVO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * 字典项
 *
 * <AUTHOR>
@Mapper
public interface SysDictValueMapper extends BaseMapper<SysDictValue> {

    /**
     * 分页查询字典项
     *
     * @param reqVO 分页查询请求
     * @return 分页结果
     */
    default Page<SysDictValue> selectDictValuePage(SysDictValuePageReqVO reqVO) {
        Page<SysDictValue> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapper<SysDictValue> queryWrapper = new LambdaQueryWrapper<>();

        if (reqVO.getDictId() != null) {
            queryWrapper.eq(SysDictValue::getDictId, reqVO.getDictId());
        }
        if (StringUtils.hasText(reqVO.getValue())) {
            queryWrapper.like(SysDictValue::getValue, reqVO.getValue());
        }
        if (StringUtils.hasText(reqVO.getLabel())) {
            queryWrapper.like(SysDictValue::getLabel, reqVO.getLabel());
        }
        if (StringUtils.hasText(reqVO.getType())) {
            queryWrapper.eq(SysDictValue::getType, reqVO.getType());
        }
        if (StringUtils.hasText(reqVO.getDescription())) {
            queryWrapper.like(SysDictValue::getDescription, reqVO.getDescription());
        }

        return this.selectPage(page, queryWrapper);
    }

    /**
     * 根据类型查询字典项列表
     *
     * @param type 字典类型
     * @return 字典项列表
     */
    default List<SysDictValue> selectDictValuesByType(String type) {
        LambdaQueryWrapper<SysDictValue> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysDictValue::getType, type)
                .orderByAsc(SysDictValue::getSort);
        return this.selectList(queryWrapper);
    }

    /**
     * 根据类型查询单个字典项（按排序倒序取第一个）
     *
     * @param type 字典类型
     * @return 字典项
     */
    default SysDictValue selectDictValueByType(String type) {
        LambdaQueryWrapper<SysDictValue> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysDictValue::getType, type)
                .orderByDesc(SysDictValue::getSort)
                .last("limit 1");
        return this.selectOne(queryWrapper);
    }

    /**
     * 根据字典ID删除字典项
     *
     * @param dictId 字典ID
     * @return 删除数量
     */
    default int deleteDictValuesByDictId(Long dictId) {
        LambdaQueryWrapper<SysDictValue> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysDictValue::getDictId, dictId);
        return this.delete(queryWrapper);
    }

    /**
     * 插入字典项
     *
     * @param sysDictValue 字典项对象
     * @return 插入结果
     */
    default boolean insertDictValue(SysDictValue sysDictValue) {
        return this.insert(sysDictValue) > 0;
    }

    /**
     * 更新字典项
     *
     * @param sysDictValue 字典项对象
     * @return 更新结果
     */
    default boolean updateDictValueById(SysDictValue sysDictValue) {
        return this.updateById(sysDictValue) > 0;
    }

    /**
     * 根据ID删除字典项
     *
     * @param id 字典项ID
     * @return 删除结果
     */
    default boolean deleteDictValueById(Long id) {
        return this.deleteById(id) > 0;
    }

    /**
     * 根据ID查询字典项
     *
     * @param id 字典项ID
     * @return 字典项对象
     */
    default SysDictValue selectDictValueById(Long id) {
        return this.selectById(id);
    }

    /**
     * 查询所有字典项
     *
     * @return 字典项列表
     */
    default List<SysDictValue> selectAllDictValues() {
        return this.selectList(null);
    }

    /**
     * 根据字典ID更新字典项类型
     *
     * @param dictId 字典ID
     * @param type 新的类型
     * @return 更新数量
     */
    default int updateDictValueTypeByDictId(Long dictId, String type) {
        SysDictValue updateEntity = new SysDictValue();
        updateEntity.setType(type);

        LambdaQueryWrapper<SysDictValue> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysDictValue::getDictId, dictId);

        return this.update(updateEntity, queryWrapper);
    }

}

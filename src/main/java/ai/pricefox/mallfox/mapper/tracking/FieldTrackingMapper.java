package ai.pricefox.mallfox.mapper.tracking;

import ai.pricefox.mallfox.domain.tracking.ProductFieldSourceTracking;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 字段追踪数据访问层
 * 
 * <AUTHOR>
 * @since 2025-01-14
 */
@Mapper
public interface FieldTrackingMapper extends BaseMapper<ProductFieldSourceTracking> {

    /**
     * 批量插入或更新字段追踪记录（JSON格式）
     *
     * @param trackingList 追踪记录列表
     * @return 影响行数
     */
    int batchInsertOrUpdate(@Param("list") List<ProductFieldSourceTracking> trackingList);

    /**
     * 查询指定记录的字段来源信息（从JSON解析）
     *
     * @param tableName 表名
     * @param recordId 记录ID
     * @return 包含field_sources的Map
     */
    Map<String, Object> selectFieldSources(@Param("tableName") String tableName, @Param("recordId") Long recordId);

    /**
     * 查询指定字段的来源信息（从JSON提取）
     *
     * @param tableName 表名
     * @param recordId 记录ID
     * @param fieldName 字段名
     * @return 字段来源信息
     */
    Map<String, Object> selectFieldSource(@Param("tableName") String tableName,
                                         @Param("recordId") Long recordId,
                                         @Param("fieldName") String fieldName);

    /**
     * 更新JSON中的特定字段来源信息
     *
     * @param tableName 表名
     * @param recordId 记录ID
     * @param fieldName 字段名
     * @param dataSource 数据来源
     * @param sourcePlatform 来源平台
     * @param oldValue 旧值
     * @param newValue 新值
     * @return 影响行数
     */
    int updateFieldSource(@Param("tableName") String tableName,
                         @Param("recordId") Long recordId,
                         @Param("fieldName") String fieldName,
                         @Param("dataSource") Integer dataSource,
                         @Param("sourcePlatform") String sourcePlatform,
                         @Param("oldValue") String oldValue,
                         @Param("newValue") String newValue);

    /**
     * 更新或插入字段追踪记录
     *
     * @param tracking 追踪记录
     * @return 影响行数
     */
    int insertOrUpdate(ProductFieldSourceTracking tracking);

    /**
     * 删除指定记录的所有字段追踪信息
     *
     * @param tableName 表名
     * @param recordId 记录ID
     * @return 删除数量
     */
    int deleteByRecord(@Param("tableName") String tableName, @Param("recordId") Long recordId);

    /**
     * 删除过期的字段追踪数据
     *
     * @param daysToKeep 保留天数
     * @return 删除数量
     */
    int deleteExpiredData(@Param("daysToKeep") int daysToKeep);

    /**
     * 统计字段追踪记录数量
     *
     * @param tableName 表名（可选）
     * @return 记录数量
     */
    long countTrackingRecords(@Param("tableName") String tableName);

    /**
     * 统计指定表和记录ID的字段追踪记录数量
     *
     * @param tableName 表名
     * @param recordId 记录ID
     * @return 记录数量
     */
    long countSpecificRecord(@Param("tableName") String tableName, @Param("recordId") Long recordId);
}

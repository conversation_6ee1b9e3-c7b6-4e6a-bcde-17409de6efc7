package ai.pricefox.mallfox.model.dto;

import lombok.Data;

/**
 * mongo增量统计返回封装
 */
@Data
public class DailyPlatformGrowthStats {
    private String productPlatform;
    private String dataChannel;
    private int totalCount;
    private int spuCount;
    private int skuCount;

    @Override
    public String toString() {
        return "DailyPlatformGrowthStats{" + "productPlatform='" + productPlatform + '\'' + ", dataChannel='" + dataChannel + '\'' + ", totalCount=" + totalCount + ", spuCount=" + spuCount + ", skuCount=" + skuCount + '}';
    }
}

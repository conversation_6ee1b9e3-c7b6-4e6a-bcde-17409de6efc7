package ai.pricefox.mallfox.model.dto;

import ai.pricefox.mallfox.domain.standard.*;
import ai.pricefox.mallfox.enums.DataChannelEnum;
import ai.pricefox.mallfox.enums.ProductPlatformEnum;
import lombok.Data;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 动态标准产品类
 * 使用Map存储所有标准字段的值，键为StandardField中的field_name_en
 */
@Data
public class DynamicStandardProduct {
    /**
     * 源数据
     */
    private Map<String, Object> originData = new HashMap<>();
    /**
     * 当前数据
     */
    private Map<String, Object> currentData = new HashMap<>();
    /**
     * 平台名称
     */
    private String platformName;

    /**
     * 平台代码
     */
    private String platformCode;

    /**
     * 数据来源
     */
    private String dataChannel;

    private String spu;

    private String sku;

    private String standardSku;

    /**
     * 商品标识符
     */
    private String productIdentifier;

    /**
     * 源数据ID
     */
    private String dataId;

    /**
     * 标准属性列表
     */
    private List<StandardAttribute> standardAttributeList;
    /**
     * 标准类别
     */
    private StandardCategory standardCategory;

}

package ai.pricefox.mallfox.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * ebay断点续传得redis存储实体
 */
@Data
public class EbaySearchProgress implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 页码
     */
    private int offset;
    /**
     * 总请求数量
     */
    private int requestCount;
    /**
     * 最后请求时间
     */
    private LocalDate lastRequestDate;
}

package ai.pricefox.mallfox.model.dto;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 字段变更事件
 * 
 * <AUTHOR>
 * @since 2025-01-14
 */
@Data
@AllArgsConstructor
public class FieldChangeEvent {
    /**
     * 表名
     */
    private String tableName;

    /**
     * 记录ID
     */
    private Long recordId;

    /**
     * 字段名
     */
    private String fieldName;

    /**
     * 数据来源
     */
    private Integer dataSource;

    /**
     * 来源平台
     */
    private String sourcePlatform;

    /**
     * 旧值
     */
    private Object oldValue;

    /**
     * 新值
     */
    private Object newValue;

    /**
     * 变更时间
     */
    private LocalDateTime changeTime;

    public FieldChangeEvent(String tableName, Long recordId, String fieldName, 
                           Integer dataSource, Object oldValue, Object newValue) {
        this.tableName = tableName;
        this.recordId = recordId;
        this.fieldName = fieldName;
        this.dataSource = dataSource;
        this.oldValue = oldValue;
        this.newValue = newValue;
        this.changeTime = LocalDateTime.now();
    }

    public FieldChangeEvent(String tableName, Long recordId, String fieldName, 
                           Integer dataSource, String sourcePlatform, Object oldValue, Object newValue) {
        this.tableName = tableName;
        this.recordId = recordId;
        this.fieldName = fieldName;
        this.dataSource = dataSource;
        this.sourcePlatform = sourcePlatform;
        this.oldValue = oldValue;
        this.newValue = newValue;
        this.changeTime = LocalDateTime.now();
    }
}

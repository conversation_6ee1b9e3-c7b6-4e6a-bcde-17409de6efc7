package ai.pricefox.mallfox.model.dto;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.Set;

/**
 * 字段追踪上下文
 * 
 * <AUTHOR>
 * @since 2025-01-14
 */
@Data
@AllArgsConstructor
public class FieldTrackingContext {
    /**
     * 表名
     */
    private String tableName;

    /**
     * 记录ID
     */
    private Long recordId;

    /**
     * 数据来源
     */
    private Integer dataSource;

    /**
     * 来源平台
     */
    private String sourcePlatform;

    /**
     * 包含的字段（为空表示包含所有字段）
     */
    private Set<String> includeFields;

    /**
     * 排除的字段
     */
    private Set<String> excludeFields;

    /**
     * 创建ProductDataOffers的追踪上下文
     */
    public static FieldTrackingContext forOffers(Long recordId, Integer dataSource) {
        return new FieldTrackingContext("product_data_offers", recordId, dataSource, null, null, null);
    }

    /**
     * 创建ProductDataOffers的追踪上下文（带平台信息）
     */
    public static FieldTrackingContext forOffers(Long recordId, Integer dataSource, String sourcePlatform) {
        return new FieldTrackingContext("product_data_offers", recordId, dataSource, sourcePlatform, null, null);
    }

    /**
     * 创建ProductDataSimplify的追踪上下文
     */
    public static FieldTrackingContext forSimplify(Long recordId, Integer dataSource) {
        return new FieldTrackingContext("product_data_simplify", recordId, dataSource, null, null, null);
    }

    /**
     * 创建ProductDataSimplify的追踪上下文（带平台信息）
     */
    public static FieldTrackingContext forSimplify(Long recordId, Integer dataSource, String sourcePlatform) {
        return new FieldTrackingContext("product_data_simplify", recordId, dataSource, sourcePlatform, null, null);
    }

    /**
     * 判断是否应该追踪该字段
     */
    public boolean shouldTrackField(String fieldName) {
        if (excludeFields != null && excludeFields.contains(fieldName)) {
            return false;
        }
        if (includeFields != null && !includeFields.isEmpty()) {
            return includeFields.contains(fieldName);
        }
        return true;
    }
}

package ai.pricefox.mallfox.model.dto;

import lombok.Data;

import java.util.Date;

/**
 * 平台字段映射Excel导入DTO
 * 用于Excel文件数据读取和转换
 *
 * <AUTHOR>
 * @since 2025-07-26
 */
@Data
public class PlatformFieldMappingExcelDTO {

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 创建人
     */
    private String createUsername;

    /**
     * 平台编码
     */
    private String platformCode;

    /**
     * 字段编码
     */
    private String standardFieldCode;

    /**
     * 转换的逻辑关系
     */
    private String transformLogic;

    /**
     * 自定义规则
     */
    private String customScript;

    /**
     * 字段名称-英文
     */
    private String sourceFieldName;
}
package ai.pricefox.mallfox.model.dto;


import lombok.Data;

/**
 * <AUTHOR>
 * @desc 插件匹配结果
 * @since 2025/7/31
 */
@Data
public class PluginMatchResult {
    private boolean success;
    private String skuCode;
    private String spuCode;

    public PluginMatchResult success(String skuCode, String spuCode) {
        PluginMatchResult r = new PluginMatchResult();
        r.setSuccess(true);
        r.setSkuCode(skuCode);
        r.setSpuCode(spuCode);
        return r;
    }

    public PluginMatchResult failure() {
        PluginMatchResult r = new PluginMatchResult();
        r.setSuccess(false);
        return r;
    }
}
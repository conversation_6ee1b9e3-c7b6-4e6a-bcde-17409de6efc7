package ai.pricefox.mallfox.model.dto;


import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @desc 商品数据评论DTO
 * @since 2025/6/23
 */
@Data
public class ProductDataReviewDTO {

    /**
     * 数据来源的电商平台。
     * 必填。例如: "Amazon", "eBay"。
     */
    @NotBlank(message = "商品来源平台不能为空")
    private String sourcePlatform;

    /**
     * 评论所属商品在来源平台的SKU ID。
     * 必填。用于反查我们自建的 sku_id。
     */
    @NotBlank(message = "商品来源的SKU-ID不能为空")
    private String platformSkuId;

    /**
     * 评论在来源平台的唯一ID。
     * 必填。用于判断评论是否已存在。
     */
    @NotBlank(message = "评论的唯一ID不能为空")
    private String platformReviewId;

    /**
     * 评论用户的名称。
     */
    private String reviewUserName;

    /**
     * 评论的标题。
     */
    private String reviewTitle;

    /**
     * 评论的正文内容。
     */
    private String reviewContent;

    /**
     * 评论的星级 (1-5)。
     */
    private Integer reviewScore;

    /**
     * 评论在来源平台的发布时间。
     */
    private LocalDateTime reviewTime;

    /**
     * 认为此评论有帮助的用户数量。
     */
    private Integer isHelpfulOrNot;

    /**
     * 评论附带的图片URL列表 (多个用逗号分隔)。
     */
    private String reviewImageUrl;

}

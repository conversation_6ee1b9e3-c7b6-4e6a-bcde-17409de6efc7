package ai.pricefox.mallfox.model.dto;

import lombok.Data;

/**
 * 商品型号合并数据传输对象
 *
 * <AUTHOR>
 * @since 2025-01-09
 */
@Data
public class ProductModelMergeDTO {

    /**
     * 商品简化数据ID
     */
    private Long id;

    /**
     * SKU ID
     */
    private String skuId;

    /**
     * SPU ID
     */
    private String spuId;

    /**
     * 数据来源平台
     */
    private String sourcePlatform;

    /**
     * 商品型号
     */
    private String model;

    /**
     * 标准化后的型号（用于匹配）
     */
    private String normalizedModel;

    /**
     * 是否为Amazon数据
     */
    private Boolean isAmazon;

    /**
     * 是否为基准数据
     */
    private Boolean isBase;

    /**
     * 原始SKU ID（更新前）
     */
    private String originalSkuId;

    /**
     * 原始SPU ID（更新前）
     */
    private String originalSpuId;
}

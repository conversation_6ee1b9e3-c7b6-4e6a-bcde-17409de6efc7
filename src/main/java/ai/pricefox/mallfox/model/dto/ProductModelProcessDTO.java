package ai.pricefox.mallfox.model.dto;

import lombok.Data;

/**
 * 商品型号处理数据传输对象
 *
 * <AUTHOR>
 * @since 2025-01-08
 */
@Data
public class ProductModelProcessDTO {

    /**
     * 商品简化数据ID
     */
    private Long id;

    /**
     * SKU ID
     */
    private String skuId;

    /**
     * 原始型号
     */
    private String originalModel;

    /**
     * 品牌名称
     */
    private String brand;

    /**
     * 处理后的型号
     */
    private String processedModel;

    /**
     * 是否需要更新
     */
    private Boolean needUpdate;

    /**
     * 处理状态：SUCCESS, SKIPPED, FAILED
     */
    private String status;

    /**
     * 处理说明
     */
    private String remark;
}

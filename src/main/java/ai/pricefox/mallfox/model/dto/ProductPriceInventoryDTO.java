package ai.pricefox.mallfox.model.dto;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 商品价格和库存更新DTO
 * 
 * <AUTHOR>
 * @since 2025年07月23日
 */
@Data
public class ProductPriceInventoryDTO {
    
    /**
     * 商品来源平台(必填)
     */
    @NotBlank(message = "商品来源平台不能为空")
    private String sourcePlatform;

    /**
     * 平台SPU ID(必填)
     */
    @NotBlank(message = "平台商品的SPU-ID不能为空")
    private String platformSpuId;

    /**
     * 平台SKU ID(必填)
     */
    @NotBlank(message = "平台商品的SKU-ID不能为空")
    private String platformSkuId;

    /**
     * 当前价格
     */
    private BigDecimal price;

    /**
     * 列表价
     */
    private BigDecimal listPrice;

    /**
     * 折扣
     */
    private BigDecimal discount;

    /**
     * 库存
     */
    private String inventory;

    /**
     * 价格更新时间
     */
    private LocalDateTime priceUpdateTime;

    /**
     * 库存更新时间
     */
    private LocalDateTime inventoryUpdateTime;

    /**
     * 数据渠道：1-爬虫 2-API
     */
    private Integer dataChannel = 1;

    /**
     * 强制更新标志：1-强制更新 0-按策略更新 默认0
     */
    private Integer forceUpdate = 0;
}
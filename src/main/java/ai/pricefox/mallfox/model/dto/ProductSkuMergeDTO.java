package ai.pricefox.mallfox.model.dto;

import lombok.Data;

/**
 * 商品SKU合并数据传输对象
 *
 * <AUTHOR>
 * @since 2025-01-09
 */
@Data
public class ProductSkuMergeDTO {

    /**
     * 商品简化数据ID
     */
    private Long id;

    /**
     * SKU ID
     */
    private String skuId;

    /**
     * SPU ID
     */
    private String spuId;

    /**
     * 数据来源平台
     */
    private String sourcePlatform;

    /**
     * 颜色
     */
    private String color;

    /**
     * 存储容量
     */
    private String storage;

    /**
     * 标准化后的存储容量（用于匹配）
     */
    private String normalizedStorage;

    /**
     * 商品状态
     */
    private String conditionNew;

    /**
     * 服务提供商
     */
    private String serviceProvider;

    /**
     * 是否为Amazon数据
     */
    private Boolean isAmazon;

    /**
     * 是否为基准数据
     */
    private Boolean isBase;

    /**
     * 原始SKU ID（更新前）
     */
    private String originalSkuId;

    /**
     * SKU组合键（用于分组）
     */
    private String skuGroupKey;
}

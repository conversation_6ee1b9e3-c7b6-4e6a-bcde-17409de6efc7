package ai.pricefox.mallfox.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * 销量数据手动补充请求参数
 * 
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
@Schema(description = "销量数据手动补充请求参数")
public class SalesDataManualSupplementRequest {

    /**
     * 来源平台
     */
    @NotBlank(message = "来源平台不能为空")
    @Schema(description = "来源平台", example = "amazon", required = true)
    private String sourcePlatform;

    /**
     * 开始时间
     */
    @NotNull(message = "开始时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "开始时间", example = "2025-01-01 00:00:00", required = true)
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @NotNull(message = "结束时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "结束时间", example = "2025-01-10 23:59:59", required = true)
    private LocalDateTime endTime;
}

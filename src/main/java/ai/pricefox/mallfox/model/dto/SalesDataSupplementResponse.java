package ai.pricefox.mallfox.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 销量数据补充响应结果
 * 
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
@Schema(description = "销量数据补充响应结果")
public class SalesDataSupplementResponse {

    /**
     * 处理的offer总数
     */
    @Schema(description = "处理的offer总数")
    private Integer totalProcessed;

    /**
     * 成功补充的offer数量
     */
    @Schema(description = "成功补充的offer数量")
    private Integer successCount;

    /**
     * 失败的offer数量
     */
    @Schema(description = "失败的offer数量")
    private Integer failureCount;

    /**
     * 跳过的offer数量（没有有效评论数据）
     */
    @Schema(description = "跳过的offer数量")
    private Integer skippedCount;

    /**
     * 执行时间（毫秒）
     */
    @Schema(description = "执行时间（毫秒）")
    private Long executionTimeMs;

    /**
     * 详细信息
     */
    @Schema(description = "详细信息")
    private String message;

    public static SalesDataSupplementResponse success(Integer totalProcessed, Integer successCount, 
                                                    Integer failureCount, Integer skippedCount, 
                                                    Long executionTimeMs, String message) {
        SalesDataSupplementResponse response = new SalesDataSupplementResponse();
        response.setTotalProcessed(totalProcessed);
        response.setSuccessCount(successCount);
        response.setFailureCount(failureCount);
        response.setSkippedCount(skippedCount);
        response.setExecutionTimeMs(executionTimeMs);
        response.setMessage(message);
        return response;
    }
}

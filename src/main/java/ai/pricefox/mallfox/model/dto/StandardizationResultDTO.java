package ai.pricefox.mallfox.model.dto;

import ai.pricefox.mallfox.enums.DataChannelEnum;
import ai.pricefox.mallfox.enums.FieldMappingStatusEnum;
import ai.pricefox.mallfox.enums.ProductPlatformEnum;
import lombok.Data;

import java.util.Map;

/**
 * 标准化结果DTO
 * 用于封装标准化处理后的结果数据和状态信息
 */
@Data
public class StandardizationResultDTO {
    /**
     * 平台名称
     */
    private ProductPlatformEnum platformName;

    /**
     * 平台代码
     */
    private String platformCode;

    /**
     * 数据来源
     */
    private DataChannelEnum sourceType;

    /**
     * 商品标识符
     */
    private String productIdentifier;

    /**
     * 源数据ID
     */
    private String dataId;
    /**
     * 源数据 (JSON格式)
     */
    private Map<String, Object> originalDataMap;

    /**
     * 标准化后的数据
     */
    private Map<String, Object> standardizedData;

    /**
     * 字段映射状态
     */
    private Map<String, FieldMappingStatusEnum> fieldMappingStatus;

    /**
     * 所有标准字段
     */
    private Map<String, String> allStandardFields;

    /**
     * 匹配字段数量
     */
    private int matchedFieldsCount;

    /**
     * 未匹配字段数量
     */
    private int unmatchedFieldsCount;

    /**
     * 空字段数量
     */
    private int emptyFieldsCount;

    /**
     * 总字段数量
     */
    private int totalFieldsCount;

    /**
     * 检查是否存在某个字段
     *
     * @param fieldName StandardField.field_name_en
     * @return 是否存在
     */
    public boolean hasField(String fieldName) {
        return originalDataMap.containsKey(fieldName);
    }
    /**
     * 获取字符串类型的字段值
     *
     * @param fieldName StandardField.field_name_en
     * @return 字符串字段值
     */
    public String getStringField(String fieldName) {
        Object value = originalDataMap.get(fieldName);
        return value != null ? value.toString() : null;
    }
    /**
     * 检查字段是否为空
     *
     * @param fieldName StandardField.field_name_en
     * @return 字段是否为空
     */
    public boolean isFieldEmpty(String fieldName) {
        Object value = originalDataMap.get(fieldName);
        if (value == null) {
            return true;
        }
        if (value instanceof String) {
            return ((String) value).trim().isEmpty();
        }
        return false;
    }

    /**
     * 检查是否存在指定字段且不为空
     *
     * @param fieldName 字段名
     * @return 是否存在且不为空
     */
    public boolean hasFieldWithValue(String fieldName) {
        return hasField(fieldName) && !isFieldEmpty(fieldName);
    }

}
package ai.pricefox.mallfox.model.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 属性导出Excel实体类
 * 用于EasyExcel导出属性数据
 */
@Data
public class AttributeExportExcelEntity {

    @ExcelProperty(value = "类目编码", index = 0)
    @ColumnWidth(20)
    private String categoryCode;

    @ExcelProperty(value = "类目名称", index = 1)
    @ColumnWidth(15)
    private String categoryNameEn;

    @ExcelProperty(value = "类目CN", index = 2)
    @ColumnWidth(10)
    private String categoryNameCn;

    @ExcelProperty(value = "类目路径", index = 3)
    @ColumnWidth(40)
    private String categoryPath;

    @ExcelProperty(value = "属性名称", index = 4)
    @ColumnWidth(20)
    private String attributeNameEn;

    @ExcelProperty(value = "属性CN", index = 5)
    @ColumnWidth(20)
    private String attributeNameCn;

    @ExcelProperty(value = "属性编码", index = 6)
    @ColumnWidth(20)
    private String attributeCode;

    @ExcelProperty(value = "属性值名称", index = 7)
    @ColumnWidth(20)
    private String valueEn;

    @ExcelProperty(value = "属性值CN", index = 8)
    @ColumnWidth(20)
    private String valueCn;

    @ExcelProperty(value = "属性值编码", index = 9)
    @ColumnWidth(20)
    private String valueCode;

    @ExcelProperty(value = "创建时间", index = 10)
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @ColumnWidth(30)
    private LocalDateTime createDate;
}

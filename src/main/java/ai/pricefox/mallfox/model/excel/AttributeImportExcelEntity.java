package ai.pricefox.mallfox.model.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * 属性导入Excel实体类
 * 用于EasyExcel读取Excel文件数据
 */
@Data
public class AttributeImportExcelEntity {

    @ExcelProperty(value = "类目编码", index = 0)
    private String standardCategoryCode;

    @ExcelProperty(value = "属性名称", index = 1)
    private String attributeNameEn;

    @ExcelProperty(value = "属性CN", index = 2)
    private String attributeNameCn;

    @ExcelProperty(value = "属性编码", index = 3)
    private String attributeCode;

    @ExcelProperty(value = "属性值名称", index = 4)
    private String valueEn;

    @ExcelProperty(value = "属性值CN", index = 5)
    private String valueCn;

    @ExcelProperty(value = "属性值编码", index = 6)
    private String valueCode;
}

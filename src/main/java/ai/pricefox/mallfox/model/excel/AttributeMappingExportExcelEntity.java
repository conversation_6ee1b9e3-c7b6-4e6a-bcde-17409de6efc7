package ai.pricefox.mallfox.model.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * 属性映射导出Excel实体
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
@Data
public class AttributeMappingExportExcelEntity {

    @ExcelProperty(value = "类目编码", index = 0)
    private String categoryCode;

    @ExcelProperty(value = "类目名称", index = 1)
    private String categoryName;

    @ExcelProperty(value = "类目CN", index = 2)
    private String categoryNameCn;

    @ExcelProperty(value = "类目路径", index = 3)
    private String categoryPath;

    @ExcelProperty(value = "属性编码", index = 4)
    private String attributeCode;

    @ExcelProperty(value = "属性CN", index = 5)
    private String attributeNameCn;

    @ExcelProperty(value = "属性名称", index = 6)
    private String attributeNameEn;

    @ExcelProperty(value = "映射属性名称", index = 7)
    private String mappingName;
}
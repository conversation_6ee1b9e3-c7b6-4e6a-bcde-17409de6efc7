package ai.pricefox.mallfox.model.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

/**
 * 属性模板Excel实体类
 * 用于EasyExcel生成模板文件
 */
@Data
public class AttributeTemplateExcelEntity {

    @ExcelProperty(value = "类目编码", index = 0)
    @ColumnWidth(15)
    private String standardCategoryCode;

    @ExcelProperty(value = "属性名称", index = 1)
    @ColumnWidth(20)
    private String attributeNameEn;

    @ExcelProperty(value = "属性CN", index = 2)
    @ColumnWidth(15)
    private String attributeNameCn;

    @ExcelProperty(value = "属性编码", index = 3)
    @ColumnWidth(15)
    private String attributeCode;

    @ExcelProperty(value = "属性值名称", index = 4)
    @ColumnWidth(20)
    private String valueEn;

    @ExcelProperty(value = "属性值CN", index = 5)
    @ColumnWidth(15)
    private String valueCn;

    @ExcelProperty(value = "属性值编码", index = 6)
    @ColumnWidth(15)
    private String valueCode;
}

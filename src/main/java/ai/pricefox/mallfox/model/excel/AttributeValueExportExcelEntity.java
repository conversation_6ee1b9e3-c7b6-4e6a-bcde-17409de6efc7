package ai.pricefox.mallfox.model.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

/**
 * 属性值导出Excel实体类
 * 用于EasyExcel导出属性值数据
 */
@Data
public class AttributeValueExportExcelEntity {

    @ExcelProperty(value = "属性名称", index = 0)
    @ColumnWidth(20)
    private String attributeNameEn;

    @ExcelProperty(value = "属性CN", index = 1)
    @ColumnWidth(15)
    private String attributeNameCn;

    @ExcelProperty(value = "属性编码", index = 2)
    @ColumnWidth(15)
    private String attributeCode;

    @ExcelProperty(value = "属性值名称", index = 3)
    @ColumnWidth(20)
    private String valueEn;

    @ExcelProperty(value = "属性值CN", index = 4)
    @ColumnWidth(15)
    private String valueCn;

    @ExcelProperty(value = "属性值编码", index = 5)
    @ColumnWidth(15)
    private String valueCode;
}

package ai.pricefox.mallfox.model.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 分类导出Excel实体类
 * 用于EasyExcel导出分类数据
 */
@Data
public class CategoryExportExcelEntity {

    @ExcelProperty(value = "分类ID", index = 0)
    private Long id;

    @ExcelProperty(value = "品类编码", index = 1)
    private String categoryCode;

    @ExcelProperty(value = "品类英文名称", index = 2)
    private String categoryNameEn;

    @ExcelProperty(value = "品类中文名称", index = 3)
    private String categoryNameCn;

    @ExcelProperty(value = "级别", index = 4)
    private Integer level;

    @ExcelProperty(value = "分类图标", index = 5)
    private String iconUrl;

    @ExcelProperty(value = "排序", index = 6)
    private Integer sort;

    @ExcelProperty(value = "是否激活", index = 7)
    private String isActiveText;

    @ExcelProperty(value = "父级ID", index = 8)
    private Long parent;

    @ExcelProperty(value = "创建时间", index = 9)
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @ColumnWidth(20)
    private LocalDateTime createDate;

    @ExcelProperty(value = "更新时间", index = 10)
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @ColumnWidth(20)
    private LocalDateTime updateDate;

    @ExcelProperty(value = "创建人", index = 11)
    private String createUsername;

    @ExcelProperty(value = "更新人", index = 12)
    private String updateUsername;
}

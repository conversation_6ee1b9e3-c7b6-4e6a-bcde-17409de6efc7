package ai.pricefox.mallfox.model.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * 类目映射导出Excel实体
 */
@Data
public class CategoryMappingExportExcelEntity {

    @ExcelProperty(value = "类目名称", index = 0)
    private String categoryName;

    @ExcelProperty(value = "类目CN", index = 1)
    private String categoryNameCn;

    @ExcelProperty(value = "类目编码", index = 2)
    private String categoryCode;

    // 动态平台映射列，根据实际平台数量动态生成
    // 例如：映射类目名称-天猫、映射类目名称-京东等
}
package ai.pricefox.mallfox.model.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * 类目映射导入Excel实体
 */
@Data
public class CategoryMappingImportExcelEntity {

    @ExcelProperty(value = "类目编码", index = 0)
    private String categoryCode;

    @ExcelProperty(value = "映射平台编码", index = 1)
    private String platformCode;

    @ExcelProperty(value = "映射类目名称", index = 2)
    private String mappingCategoryName;
}
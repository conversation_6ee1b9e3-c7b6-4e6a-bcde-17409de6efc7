package ai.pricefox.mallfox.model.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * 平台类目导出Excel实体
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
@Data
public class PlatformCategoryExportExcelEntity {

    @ExcelProperty(value = "类目名称", index = 0)
    private String platformCategoryName;

    @ExcelProperty(value = "类目CN", index = 1)
    private String platformCategoryNameCn;

    @ExcelProperty(value = "类目级别", index = 2)
    private Integer platformLevel;

    @ExcelProperty(value = "父类目名称", index = 3)
    private String parentCategoryName;
}
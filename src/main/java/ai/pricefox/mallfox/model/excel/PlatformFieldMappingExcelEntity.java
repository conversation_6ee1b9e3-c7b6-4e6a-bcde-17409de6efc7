package ai.pricefox.mallfox.model.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import lombok.Data;

import java.util.Date;

/**
 * 平台字段映射Excel读取实体类
 * 用于EasyExcel读取Excel文件数据
 *
 * <AUTHOR>
 * @since 2025-07-26
 */
@Data
public class PlatformFieldMappingExcelEntity {

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间", index = 0)
    @DateTimeFormat("yyyy/MM/dd HH:mm:ss")
    private Date createDate;

    /**
     * 创建人
     */
    @ExcelProperty(value = "创建人", index = 1)
    private String createUsername;

    /**
     * 平台编码
     */
    @ExcelProperty(value = "平台编码", index = 2)
    private String platformCode;

    /**
     * 字段编码
     */
    @ExcelProperty(value = "字段编码", index = 3)
    private String standardFieldCode;

    /**
     * 转换的逻辑关系
     */
    @ExcelProperty(value = "转换的逻辑关系", index = 4)
    private String transformLogic;

    /**
     * 自定义规则
     */
    @ExcelProperty(value = "自定义规则", index = 5)
    private String customScript;

    /**
     * 字段名称-英文
     */
    @ExcelProperty(value = "字段名称-英文", index = 6)
    private String sourceFieldName;

    /**
     * 网页字段位置
     */
    @ExcelProperty(value = "网页字段位置", index = 7)
    private String positionInfo;
}
package ai.pricefox.mallfox.model.param;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 属性映射创建请求
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
@Data
@Schema(description = "属性映射创建请求")
public class AttributeMappingCreateRequest {

    @Schema(description = "标准属性编码", example = "ATTR001", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "标准属性编码不能为空")
    private String attributeCode;

    @Schema(description = "映射属性名称", example = "颜色", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "映射属性名称不能为空")
    private String mappingName;
}
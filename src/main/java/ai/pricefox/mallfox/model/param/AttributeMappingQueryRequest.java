package ai.pricefox.mallfox.model.param;

import ai.pricefox.mallfox.vo.base.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 属性映射查询请求
 *
 * <AUTHOR>
 * @date 2025-08-02
 */
@Data
@Schema(description = "属性映射查询请求")
public class AttributeMappingQueryRequest extends PageParam {

    @Schema(description = "类目名称列表", example = "[\"Electronics\", \"Cell Phones\"]")
    private List<String> categoryNames;

    @Schema(description = "类目CN", example = "电子产品")
    private String categoryNameCn;

    @Schema(description = "属性编码", example = "ATTR001")
    private String attributeCode;

    @Schema(description = "属性名称", example = "Brand")
    private String attributeName;

    @Schema(description = "属性CN", example = "品牌")
    private String attributeNameCn;

}

package ai.pricefox.mallfox.model.param;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 属性映射更新请求
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
@Data
@Schema(description = "属性映射更新请求")
public class AttributeMappingUpdateRequest {

    @Schema(description = "主键ID", example = "1", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "ID不能为空")
    private Long id;

    @Schema(description = "标准属性编码", example = "ATTR001", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "标准属性编码不能为空")
    private String attributeCode;

    @Schema(description = "映射属性名称", example = "颜色", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "映射属性名称不能为空")
    private String mappingName;
}
package ai.pricefox.mallfox.model.param;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 属性值映射创建请求
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
@Data
@Schema(description = "属性值映射创建请求")
public class AttributeValueMappingCreateRequest {

    @Schema(description = "属性映射ID", example = "1", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "属性映射ID不能为空")
    private Long attributeId;

    @Schema(description = "属性值编码", example = "VALUE001", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "属性值编码不能为空")
    private String valueCode;
}
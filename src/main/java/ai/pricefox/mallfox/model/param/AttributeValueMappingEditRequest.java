package ai.pricefox.mallfox.model.param;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * 属性值映射编辑请求
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
@Data
@Schema(description = "属性值映射编辑请求")
public class AttributeValueMappingEditRequest {

    @Schema(description = "属性值编码", example = "VALUE001", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "属性值编码不能为空")
    private String valueCode;

    @Schema(description = "映射属性值名称列表", example = "['Apple Inc.', 'APPLE']")
    private List<String> mappingValueNames;
}
package ai.pricefox.mallfox.model.param;

import ai.pricefox.mallfox.vo.base.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 属性值映射查询请求
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
@Data
@Schema(description = "属性值映射查询请求")
public class AttributeValueMappingQueryRequest extends PageParam {

    @Schema(description = "属性映射ID", example = "1", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "属性映射ID不能为空")
    private Long attributeId;

    @Schema(description = "属性值编码", example = "VALUE001")
    private String valueCode;

    @Schema(description = "属性值名称", example = "Apple")
    private String valueName;

    @Schema(description = "属性值CN", example = "苹果")
    private String valueNameCn;
}
package ai.pricefox.mallfox.model.param;


import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @desc 数据标记
 * @since 2025/6/25
 */
@Data
public class CalibrationTagRequest {

    /**
     * 被标记的表名: "offers" 或 "simplify" 填sku选offers spu选simplify
     */
    @NotBlank(message = "被标记的唯一标识不能为空")
    private String targetTable;

    /**
     * 被标记的记录ID offerId
     */
    @NotNull(message = "被标记的记录ID不能为空")
    private Long targetId;

    /**
     * 被标记的字段名
     */
    @NotBlank(message = "被标记的字段名不能为空")
    private String fieldName;

    /**
     * 标记状态 (1: 缺失, 2: 错误)
     */
    @NotNull(message = "标记状态不能为空")
    private Integer tagStatus;

    /**
     * 备注
     */
    private String remark;
}

package ai.pricefox.mallfox.model.param;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * 类目映射创建请求
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
@Data
@Schema(description = "类目映射创建请求")
public class CategoryMappingCreateRequest {

    @Schema(description = "标准类目ID", example = "1", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "标准类目ID不能为空")
    private Long standardCategoryId;

    @Schema(description = "平台映射关系列表")
    private List<PlatformMappingItem> platformMappings;

    @Data
    @Schema(description = "平台映射项")
    public static class PlatformMappingItem {
        
        @Schema(description = "平台编码", example = "TMALL")
        private String platformCode;
        
        @Schema(description = "平台类目ID", example = "123")
        private Long platformCategoryId;
        
        @Schema(description = "平台类目名称", example = "电子产品")
        private String platformCategoryName;
    }
}
package ai.pricefox.mallfox.model.param;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * 类目映射更新请求
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
@Data
@Schema(description = "类目映射更新请求")
public class CategoryMappingUpdateRequest {

    @Schema(description = "映射ID", example = "1", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "映射ID不能为空")
    private Long id;

    @Schema(description = "标准类目ID", example = "1", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "标准类目ID不能为空")
    private Long standardCategoryId;

    @Schema(description = "平台映射关系列表")
    private List<CategoryMappingCreateRequest.PlatformMappingItem> platformMappings;
}
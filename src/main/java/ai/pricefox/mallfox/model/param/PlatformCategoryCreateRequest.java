package ai.pricefox.mallfox.model.param;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 平台类目创建请求VO
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
@Data
@Schema(description = "平台类目创建请求")
public class PlatformCategoryCreateRequest {

    @Schema(description = "平台编码", example = "TMALL")
    @NotBlank(message = "平台编码不能为空")
    private String platformCode;

    @Schema(description = "平台类目名称", example = "Electronics")
    @NotBlank(message = "平台类目名称不能为空")
    private String platformCategoryName;

    @Schema(description = "平台类目中文名称", example = "电子产品")
    private String platformCategoryNameCn;

    @Schema(description = "父类目ID", example = "0")
    private Long platformParentCategoryId = 0L;

    @Schema(description = "平台类目级别", example = "1")
    @NotNull(message = "平台类目级别不能为空")
    private Integer platformLevel;
}
package ai.pricefox.mallfox.model.param;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;


/**
 * 平台类目更新请求VO
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
@Data
@Schema(description = "平台类目更新请求")
public class PlatformCategoryUpdateRequest {

    @Schema(description = "ID", example = "1")
    @NotNull(message = "ID不能为空")
    private Long id;

    @Schema(description = "平台类目名称", example = "Electronics")
    private String platformCategoryName;

    @Schema(description = "平台类目中文名称", example = "电子产品")
    private String platformCategoryNameCn;

    @Schema(description = "父类目ID", example = "0")
    private Long platformParentCategoryId;

    @Schema(description = "平台类目级别", example = "1")
    private Integer platformLevel;
}
package ai.pricefox.mallfox.model.param;


import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @desc 商品插件请求参数
 * @since 2025/7/31
 */
@Data
@Schema(description = "插件匹配与比价请求")
public class PluginMatchRequest {

    /**
     * 来源平台 Amazon ebay bestbuy
     */
    @Schema(description = "来源平台", required = true)
    @NotBlank(message = "来源平台不能为空")
    private String sourcePlatform;

    private String platformCode;

    /**
     * 平台商品ID (ASIN, ItemNo，skuId)
     */
    @Schema(description = "平台商品ID", required = true)
    @NotBlank(message = "平台商品ID不能为空")
    private String productId;

    /**
     * 平台商品链接
     */
    @Schema(description = "平台商品链接")
    private String itemUrl;

    /**
     * 商品标题
     */
    @Schema(description = "商品原始标题")
    private String title;

    /**
     * 品牌
     */
    @Schema(description = "商品原始品牌")
    private String brand;

    /**
     * 商品型号
     */
    @Schema(description = "商品原始型号")
    private String model;

    /**
     * 颜色
     */
    @Schema(description = "商品原始颜色")
    private String color;

    /**
     * 存储容量
     */
    @Schema(description = "商品原始存储容量")
    private String storage;

    /**
     * 商品状态
     */
    @Schema(description = "商品原始状态")
    private String condition;

    /**
     * 服务提供商
     */
    @Schema(description = "商品原始服务提供商")
    private String serviceProvider;

    /**
     * 用户在页面上看到的当前价格
     */
    @Schema(description = "当前页面价格", required = true)
    @NotNull(message = "当前页面价格不能为空")
    private BigDecimal currentPrice;

    /**
     * 货币单位 usd or $ eur or €
     */
    @Schema(description = "货币单位")
    private String currency;
}

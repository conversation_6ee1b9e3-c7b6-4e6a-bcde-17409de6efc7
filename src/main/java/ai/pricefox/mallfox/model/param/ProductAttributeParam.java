package ai.pricefox.mallfox.model.param;

import ai.pricefox.mallfox.domain.product.ProductAttributeValue;
import lombok.Data;

import java.util.List;

/**
 * 商品属性表
 * @TableName product_attribute
 */
@Data
public class ProductAttributeParam {
    /**
     * 属性ID
     */
    private Long id;

    /**
     * 所属分类ID
     */
    private Long categoryId;

    /**
     * 属性名称
     */
    private String name;

    /**
     * 输入类型(1:手动输入 2:单选 3:多选)
     */
    private Integer inputType;

    /**
     * 可选值列表(JSON数组)
     */
    private List<ProductAttributeValue> values;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 是否用于筛选(0:否 1:是)
     */
    private Integer isFilter;

    /**
     * 是否必填(0:否 1:是)
     */
    private Integer isRequired;



}
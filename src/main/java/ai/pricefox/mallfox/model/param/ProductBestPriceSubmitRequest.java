package ai.pricefox.mallfox.model.param;


import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @desc 商品最优价格请求
 * @since 2025/7/30
 */
@Data
public class ProductBestPriceSubmitRequest {

    /**
     * sku编码
     */
    @NotBlank(message = "sku编码不能为空")
    private String skuCode;

    /**
     * 最优价格
     */
    @NotBlank(message = "最优价格不能为空")
    @Min(value = 0, message = "最优价格必须大于等于0")
    private BigDecimal price;

    /**
     * 商品链接
     */
    @NotBlank(message = "商品链接不能为空")
    private String itemUrl;

    /**
     * 最优价格平台
     */
    private String optional;

    /**
     * 用户邮箱
     */
    private String userEmail;

    /**
     * 提交用户
     */
    @NotNull(message = "提交用户不能为空")
    private Long submitUser;
}

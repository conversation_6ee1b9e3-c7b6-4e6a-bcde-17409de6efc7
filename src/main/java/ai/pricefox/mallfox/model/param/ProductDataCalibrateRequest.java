package ai.pricefox.mallfox.model.param;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @desc 商品数据校准
 * @since 2025/6/24
 */
@Data
public class ProductDataCalibrateRequest {

    /**
     * 需要校准的SKU ID列表
     */
    @NotEmpty(message = "需要校准的SKU ID列表不能为空")
    private List<String> skuIdList;

    /**
     * 1 标记缺失 2 标记错误
     */
    @NotNull(message = "标记操作不能为空")
    private Integer mark;

}

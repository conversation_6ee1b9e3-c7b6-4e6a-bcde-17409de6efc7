package ai.pricefox.mallfox.model.param;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 多平台商品评论数据表
 * @TableName product_data_reviews
 */
@Data
public class ProductDataReviewsParam {

    /**
     * 评论来源平台 (例如: amazon, bestbuy)
     */
    @NotBlank(message = "sourcePlatform不能为空")
    @Size(max = 50, message = "sourcePlatform长度不能超过50个字符")
    private String sourcePlatform;

    /**
     * 来源平台的SPU/ID/ASIN
     */
    @NotBlank(message = "platformSpuId不能为空")
    @Size(max = 255, message = "platformSpuId长度不能超过255个字符")
    private String platformSpuId;

    /**
     * 来源平台的SKU组合ID
     */
    @NotBlank(message = "platformSkuId不能为空")
    @Size(max = 255, message = "platformSpuId长度不能超过255个字符")
    private String platformSkuId;

    /**
     * 评论星级 (1-5)
     */
    @Min(value = 1, message = "评分最小为1")
    @Max(value = 5, message = "评分最大为5")
    private Integer reviewScore;

    /**
     * 评论用户名称
     */
    @Size(max = 255, message = "reviewUserName长度不能超过255个字符")
    private String reviewUserName;

    /**
     * 评论标题
     */
    @Size(max = 512, message = "reviewTitle长度不能超过512个字符")
    private String reviewTitle;

    /**
     * 评论正文内容
     */
    private String reviewContent;

    /**
     * 评论发布时间
     */
    private LocalDateTime reviewTime;

    /**
     * 用户认为有帮助
     */
    private Integer isHelpfulOrNot;

    /**
     * 评论附带的图片URL列表 (多个用逗号分隔)
     */
    private String reviewImageUrl;
}
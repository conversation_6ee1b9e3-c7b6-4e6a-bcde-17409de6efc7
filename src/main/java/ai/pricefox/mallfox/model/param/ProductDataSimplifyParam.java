package ai.pricefox.mallfox.model.param;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import jakarta.validation.constraints.*;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 商品低频数据表
 * @TableName product_data_simplify
 */
@Data
public class ProductDataSimplifyParam {

    /**
     * 数据的主要来源平台
     */
    @NotBlank(message = "sourcePlatform不能为空")
    @Size(max = 50, message = "sourcePlatform长度不能超过50个字符")
    private String sourcePlatform;

    /**
     * 来源平台的SPU/ID/ASIN
     */
    @NotBlank(message = "platformSpuId不能为空")
    @Size(max = 255, message = "platformSpuId长度不能超过255个字符")
    private String platformSpuId;

    /**
     * 来源平台的SKU编码
     */
    @Size(max = 255, message = "platformSkuId长度不能超过255个字符")
    @NotBlank(message = "platformSkuId不能为空")
    private String platformSkuId;

    /**
     * 商品型号
     */
    @Size(max = 100, message = "model长度不能超过100个字符")
    private String model;

    /**
     * 型号年份
     */
    @Size(max = 10, message = "modelYear必须是有效的年份")
    private String modelYear;

    /**
     * 颜色
     */
    @Size(max = 50, message = "color长度不能超过50个字符")
    private String color;

    /**
     * 内存/存储容量
     */
    @Size(max = 50, message = "storage长度不能超过50个字符")
    private String storage;

    /**
     * 商品主图URL列表逗号分隔 (url1", "url2")
     */
    private String productMainImageUrls;

    /**
     * 规格颜色图片URL
     */
    private String productSpecColorUrl;

    /**
     * 已安装内存RAM大小
     */
    @Size(max = 50, message = "ramMemoryInstalledSize长度不能超过50个字符")
    private String ramMemoryInstalledSize;

    /**
     * 操作系统
     */
    @Size(max = 100, message = "operatingSystem长度不能超过100个字符")
    private String operatingSystem;

    /**
     * 处理器
     */
    @Size(max = 100, message = "processor长度不能超过100个字符")
    private String processor;

    /**
     * 蜂窝技术
     */
    @Size(max = 50, message = "cellularTechnology长度不能超过50个字符")
    private String cellularTechnology;

    /**
     * 屏幕尺寸
     */
    @Pattern(regexp = "^\\d+(\\.\\d+)?\\s*(inches|inch|\")$", message = "screenSize格式不正确，例如: 6.1 inches")
    private String screenSize;

    /**
     * 分辨率
     */
    @Pattern(regexp = "^\\d+[x×]\\d+$", message = "resolution格式不正确，例如: 1920x1080")
    private String resolution;

    /**
     * 刷新率
     */
    @Pattern(regexp = "^\\d+\\s*Hz$", message = "refreshRate格式不正确，例如: 60Hz")
    private String refreshRate;

    /**
     * 显示类型
     */
    @Size(max = 100, message = "displayType长度不能超过100个字符")
    private String displayType;

    /**
     * 电池电量
     */
    @Pattern(regexp = "^\\d+\\s*(mAh|Ah)$", message = "batteryPower格式不正确，例如: 4000mAh")
    private String batteryPower;

    /**
     * 平均通话时长
     */
    @Pattern(regexp = "^\\d+\\s*(hours|hrs|h)$", message = "averageTalkTime格式不正确，例如: 24 hours")
    private String averageTalkTime;

    /**
     * 电池充电时长
     */
    @Pattern(regexp = "^\\d+\\s*(hours|hrs|h)$", message = "batteryChargeTime格式不正确，例如: 2 hours")
    private String batteryChargeTime;

    /**
     * 前置摄像头分辨率
     */
    @Pattern(regexp = "^\\d+[MPmp]|\\d+\\.\\d+[MPmp]$", message = "frontPhotoSensorResolution格式不正确，例如: 12MP")
    private String frontPhotoSensorResolution;

    /**
     * 后置摄像头分辨率
     */
    @Pattern(regexp = "^\\d+[MPmp]|\\d+\\.\\d+[MPmp]$", message = "rearFacingCameraPhotoSensorResolution格式不正确，例如: 12MP")
    private String rearFacingCameraPhotoSensorResolution;

    /**
     * 后置摄像头数量
     */
    @Min(value = 0, message = "numberOfRearFacingCameras不能小于0")
    @Max(value = 10, message = "numberOfRearFacingCameras不能大于10")
    private Integer numberOfRearFacingCameras;

    /**
     * 有效视频分辨率
     */
    @Pattern(regexp = "^\\d+[pPkK]$", message = "effectiveVideoResolution格式不正确，例如: 4K或1080p")
    private String effectiveVideoResolution;

    /**
     * 视频捕捉帧率
     */
    @Pattern(regexp = "^\\d+[fF][pP][sS]$", message = "videoCaptureResolution格式不正确，例如: 60fps")
    private String videoCaptureResolution;

    /**
     * SIM卡卡槽类型
     */
    @Size(max = 100, message = "sImCardSlotCount长度不能超过100个字符")
    private String simCardSlotCount;

    /**
     * 连接器类型
     */
    @Size(max = 100, message = "connectorType长度不能超过100个字符")
    private String connectorType;

    /**
     * 防水性能
     */
    @Pattern(regexp = "^IP\\d{2}|Waterproof$", message = "waterResistance格式不正确，例如: IP68")
    private String waterResistance;

    /**
     * 手机尺寸
     */
    @Pattern(regexp = "^\\d+(\\.\\d+)?×\\d+(\\.\\d+)?×\\d+(\\.\\d+)?\\s*(mm|cm|in)$", message = "dimensions格式不正确，例如: 150.9×75.7×8.3mm")
    private String dimensions;

    /**
     * 商品重量
     */
    @Pattern(regexp = "^\\d+(\\.\\d+)?\\s*(g|kg|oz|lb)$", message = "itemWeight格式不正确，例如: 194g")
    private String itemWeight;

    /**
     * 生物识别安全技术
     */
    @Size(max = 255, message = "biometricSecurityFeature长度不能超过255个字符")
    private String biometricSecurityFeature;

    /**
     * 支持的卫星导航系统
     */
    @Size(max = 255, message = "supportedSatelliteNavigationSystem长度不能超过255个字符")
    private String supportedSatelliteNavigationSystem;

    /**
     * 特征/功能列表
     */
    private String features;

    /**
     * 退换货政策
     */
    private String returnPolicy;

    /**
     * 付款方式 如信用卡,分期
     */
    private String paymentInstallment;

    /**
     * 分期信息
     */
    private String installPayment;

    /**
     * 保修说明
     */
    private String warrantyDescription;

    /**
     * 评论数量
     */
    @PositiveOrZero(message = "reviewNumber必须为正数或零")
    private Integer reviewNumber;

    /**
     * 评分
     */
    @DecimalMin(value = "0.0", message = "reviewScore不能小于0")
    @DecimalMax(value = "5.0", message = "reviewScore不能大于5")
    @Digits(integer = 1, fraction = 1, message = "reviewScore格式不正确，应为1位整数和1位小数")
    private BigDecimal reviewScore;

    /**
     * 商品评价五点图分布 ({"5_star": 120, "1_star": 5})
     */
    private String reviewRatingDistribution;

    /**
     * 不同维度评分 ({"camera": 4.5, "battery": 4.2})
     */
    private String reviewDimensionalRatings;

    /**
     * 评价-概览-优缺点 ({"pros": ["{"long baettery:200"}"], "cons": ["{"heavy to hold:102"}"]})
     */
    private String reviewOverviewProsCons;

    /**
     * 评价-1-5星级优缺点
     */
    private String reviewProsConsByStar;
}
package ai.pricefox.mallfox.model.param;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.util.List;

@Data
public class ProductHistoryRequest {

    /**
     * 商品SKU CODE
     */
    @Schema(description = "SKU CODE", example = "1")
    @NotBlank(message = "商品SKU CODE不能为空")
    private String skuCode;

    /**
     * 时间周期。可选值: 1M, 3M, 6M, 1Y, ALL
     */
    @Schema(description = "周期", example = "1")
    private String period;

    /**
     * 要筛选的平台列表。 如果未提供或值为ALL，则返回全平台聚合数据。
     */
    @Schema(description = "要筛选的平台列表", example = "PA0001,PC0001")
    private List<String> platforms;
}

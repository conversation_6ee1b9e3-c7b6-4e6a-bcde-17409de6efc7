package ai.pricefox.mallfox.model.param;


import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

/**
 * <AUTHOR>
 * @desc 商品标记已读请求
 * @since 2025/7/18
 */
@Data
public class ProductMarkReadRequest {

    /**
     * 标记已读的记录表  sku二级填offers  三级平台sku plat-offers   spu填simplify
     */
    @NotEmpty(message = "标记已读的记录表不能为空")
    private String targetTable;

    /**
     * 标记已读的记录 offerId
     */
    @NotBlank(message = "标记已读的记录ID不能为空")
    private Long targetId;

}

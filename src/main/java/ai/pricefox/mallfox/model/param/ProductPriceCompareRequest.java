package ai.pricefox.mallfox.model.param;


import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @desc 商品价格比较
 * @since 2025/7/30
 */
@Data
@Schema(description = "商品价格比较请求参数")
public class ProductPriceCompareRequest {

    /**
     * 必须指定要查询哪个商品的报价。
     */
    @Schema(description = "要查询的自建SKU ID", required = true)
    @NotBlank(message = "SKU Code不能为空")
    private String skuCode;

    // --- 筛选条件 ---
    @Schema(description = "平台编码")
    private String platformCode;

    @Schema(description = "卖家类型列表 (\"platform-平台自营\", \"thirdParty-第三方")
    private String sellerType;

    @Schema(description = "最低商家评分 (4.0)")
    private Double minMerchantRating;

    @Schema(description = "发货时效区间筛选 - 最小天数 (1)")
    private Double minShippingTime;
    
    @Schema(description = "发货时效区间筛选 - 最大天数")
    private Double maxShippingTime;

    @Schema(description = "商品成色列表 ([\"New\", \"Open Box\", \"Refurbished\"])")
    private List<String> conditions;
}

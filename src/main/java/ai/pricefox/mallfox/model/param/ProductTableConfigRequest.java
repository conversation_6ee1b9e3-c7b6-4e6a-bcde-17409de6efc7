package ai.pricefox.mallfox.model.param;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 产品表格配置请求DTO
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "产品表格配置请求")
public class ProductTableConfigRequest {

    @Schema(description = "主键ID")
    private Integer id;

    @Schema(description = "字段")
    private String field;

    @NotNull(message = "类型不能为空")
    @Schema(description = "类型：1-字段 2-配置", required = true)
    private Integer type;

    @Schema(description = "JSON信息")
    private String info;

    @Schema(description = "权重")
    private Integer weight;


    /**
     * 根据字段更新请求
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "根据字段更新请求")
    public static class UpdateByFieldRequest {

//        @Schema(description = "主键ID")
//        private Integer id;

        @Schema(description = "字段", required = true)
        private String field;

        @Schema(description = "类型：1-字段 2-配置")
        private Integer type;

        @Schema(description = "JSON信息")
        private String info;

        @Schema(description = "权重")
        private Integer weight;
    }

    /**
     * 更新权重请求 - 支持拖拽排序和新增配置
     *
     * <p>功能说明：</p>
     * <ul>
     *   <li>当id不为空时：执行拖拽排序，更新指定字段的权重</li>
     *   <li>当id为空时：新增配置，根据位置信息计算权重并插入</li>
     * </ul>
     *
     * <p>通过指定目标位置的左右元素ID来确定新的权重值</p>
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "更新权重请求，支持拖拽排序和新增配置")
    public static class UpdateWeightRequest {

        @NotNull(message = "表格标识不能为空")
        @Schema(description = "表格标识，用于区分不同表格的配置", example = "product_list", required = true)
        private String tag;

        @Schema(description = "配置ID，为空时执行新增操作，不为空时执行更新操作", example = "3")
        private Integer id;

        @Schema(description = "字段")
        private String field;

        @NotNull(message = "类型不能为空")
        @Schema(description = "类型：1-字段 2-配置", required = true)
        private Integer type;

        @Schema(description = "JSON信息")
        private String info;

        @Schema(description = "目标位置左侧元素的ID，为null表示插入到开头", example = "1")
        private Integer leftId;

        @Schema(description = "目标位置右侧元素的ID，为null表示插入到末尾", example = "2")
        private Integer rightId;
    }
}

package ai.pricefox.mallfox.model.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 属性映射批量导入结果响应
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
@Data
@Schema(description = "属性映射批量导入结果响应")
public class AttributeMappingBatchImportResultResponse {

    @Schema(description = "属性映射更新数量")
    private Integer attributeMappingTotal;

    @Schema(description = "属性映射成功数量")
    private Integer attributeMappingSuccess;

    @Schema(description = "属性映射失败数量")
    private Integer attributeMappingFailed;

    @Schema(description = "属性值映射更新数量")
    private Integer attributeValueMappingTotal;

    @Schema(description = "属性值映射成功数量")
    private Integer attributeValueMappingSuccess;

    @Schema(description = "属性值映射失败数量")
    private Integer attributeValueMappingFailed;

    @Schema(description = "失败详情")
    private String failureDetails;
}
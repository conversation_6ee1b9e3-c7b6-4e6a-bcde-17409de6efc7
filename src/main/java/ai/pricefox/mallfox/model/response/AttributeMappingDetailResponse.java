package ai.pricefox.mallfox.model.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 属性映射详情响应
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
@Data
@Schema(description = "属性映射详情响应")
public class AttributeMappingDetailResponse {

    @Schema(description = "主键ID", example = "1")
    private Long id;

    @Schema(description = "属性编码", example = "Raw0000008")
    private String attributeCode;

    @Schema(description = "属性CN", example = "品牌")
    private String attributeNameCn;

    @Schema(description = "属性名称", example = "Brand")
    private String attributeNameEn;

    @Schema(description = "类目编码", example = "CAT001")
    private String categoryCode;

    @Schema(description = "类目名称", example = "Electronics")
    private String categoryName;

    @Schema(description = "类目CN", example = "电子产品")
    private String categoryNameCn;

    @Schema(description = "映射属性名称列表")
    private List<MappingInfo> mappingInfos;

    @Schema(description = "创建时间")
    private LocalDateTime createDate;

    @Schema(description = "更新时间")
    private LocalDateTime updateDate;

    @Schema(description = "创建人", example = "admin")
    private String createUsername;

    @Schema(description = "更新人", example = "admin")
    private String updateUsername;

    /**
     * 映射信息
     */
    @Data
    @Schema(description = "映射信息")
    public static class MappingInfo {
        @Schema(description = "映射ID", example = "1")
        private Long id;

        @Schema(description = "映射名称", example = "Processor type")
        private String mappingName;

        @Schema(description = "创建时间")
        private LocalDateTime createDate;
    }
}
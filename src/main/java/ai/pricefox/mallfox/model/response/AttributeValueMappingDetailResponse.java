package ai.pricefox.mallfox.model.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 属性值映射详情响应
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
@Data
@Schema(description = "属性值映射详情响应")
public class AttributeValueMappingDetailResponse {

    // 属性值信息
    @Schema(description = "属性值编码", example = "VALUE001")
    private String valueCode;

    @Schema(description = "属性值名称", example = "Apple")
    private String valueName;

    @Schema(description = "属性值CN", example = "苹果")
    private String valueNameCn;

    // 归属属性信息
    @Schema(description = "属性编码", example = "ATTR001")
    private String attributeCode;

    @Schema(description = "属性名称", example = "Brand")
    private String attributeName;

    @Schema(description = "属性CN", example = "品牌")
    private String attributeNameCn;

    // 归属类目信息
    @Schema(description = "类目编码", example = "CAT001")
    private String categoryCode;

    @Schema(description = "类目名称", example = "Electronics")
    private String categoryName;

    @Schema(description = "类目CN", example = "电子产品")
    private String categoryNameCn;

    // 映射列表
    @Schema(description = "映射属性值列表")
    private List<MappingValueInfo> mappingValues;

    /**
     * 映射值信息
     */
    @Data
    @Schema(description = "映射值信息")
    public static class MappingValueInfo {
        @Schema(description = "映射ID", example = "1")
        private Long mappingId;

        @Schema(description = "映射属性值名称", example = "Apple Inc.")
        private String mappingValueName;

        @Schema(description = "创建时间")
        private LocalDateTime createDate;
    }
}
package ai.pricefox.mallfox.model.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 属性值映射页面基础信息
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
@Data
@Schema(description = "属性值映射页面基础信息")
public class AttributeValueMappingPageInfo {

    // 属性信息
    @Schema(description = "属性编码", example = "ATTR001")
    private String attributeCode;

    @Schema(description = "属性名称", example = "Brand")
    private String attributeName;

    @Schema(description = "属性CN", example = "品牌")
    private String attributeNameCn;

    // 归属类目信息
    @Schema(description = "类目编码", example = "CAT001")
    private String categoryCode;

    @Schema(description = "类目名称", example = "Electronics")
    private String categoryName;

    @Schema(description = "类目CN", example = "电子产品")
    private String categoryNameCn;
}
package ai.pricefox.mallfox.model.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 属性值选项响应
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
@Data
@Schema(description = "属性值选项响应")
public class AttributeValueOption {

    @Schema(description = "属性值编码", example = "VALUE001")
    private String valueCode;

    @Schema(description = "属性值名称", example = "Apple")
    private String valueName;

    @Schema(description = "属性值CN", example = "苹果")
    private String valueNameCn;

    @Schema(description = "显示文本", example = "VALUE001｜Apple")
    private String displayText;
}
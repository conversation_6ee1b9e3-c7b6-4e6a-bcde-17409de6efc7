package ai.pricefox.mallfox.model.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @desc 可用筛选器
 * @since 2025/7/8
 */
@Data
@Schema(description = "可用筛选器")
public class AvailableFiltersDTO {
    @Schema(description = "品牌筛选器")
    private List<FilterOption> brands;

    @Schema(description = "动态规格筛选器")
    private List<DynamicSpecFilter> specFilters;

    @Schema(description = "价格区间")
    private Map<String, Object> priceRange;

    @Schema(description = "评分选项")
    private List<FilterOption> ratingOptions;
}

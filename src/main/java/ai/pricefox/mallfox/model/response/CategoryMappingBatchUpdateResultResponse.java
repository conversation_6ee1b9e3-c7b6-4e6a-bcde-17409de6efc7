package ai.pricefox.mallfox.model.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 类目映射批量更新结果响应
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
@Data
@Schema(description = "类目映射批量更新结果响应")
public class CategoryMappingBatchUpdateResultResponse {

    @Schema(description = "总数量", example = "100")
    private Integer totalCount;

    @Schema(description = "成功数量", example = "95")
    private Integer successCount;

    @Schema(description = "失败数量", example = "5")
    private Integer failureCount;

    @Schema(description = "是否成功", example = "true")
    private Boolean success;

    @Schema(description = "错误信息")
    private String errorMessage;

    @Schema(description = "详细结果信息", example = "类目映射更新数量100，成功95，失败5")
    private String resultMessage;
}
package ai.pricefox.mallfox.model.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 类目映射响应
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
@Data
@Schema(description = "类目映射响应")
public class CategoryMappingResponse {

    @Schema(description = "映射ID", example = "1")
    private Long id;

    @Schema(description = "标准类目信息")
    private StandardCategoryInfo standardCategory;

    @Schema(description = "平台映射列表")
    private List<PlatformMappingInfo> platformMappings;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Data
    @Schema(description = "标准类目信息")
    public static class StandardCategoryInfo {
        
        @Schema(description = "类目ID", example = "1")
        private Long id;
        
        @Schema(description = "类目编码", example = "CAT001")
        private String categoryCode;
        
        @Schema(description = "类目名称", example = "Electronics")
        private String categoryName;
        
        @Schema(description = "类目中文名称", example = "电子产品")
        private String categoryNameCn;
        
        @Schema(description = "父级类目ID", example = "0")
        private Long parentId;
        
        @Schema(description = "层级", example = "1")
        private Integer level;
        
        @Schema(description = "子级类目列表")
        private List<CategoryMappingResponse> children;
    }

    @Data
    @Schema(description = "平台映射信息")
    public static class PlatformMappingInfo {
        
        @Schema(description = "平台编码", example = "TMALL")
        private String platformCode;
        
        @Schema(description = "平台名称", example = "天猫")
        private String platformName;
        
        @Schema(description = "平台类目ID", example = "123")
        private Long platformCategoryId;
        
        @Schema(description = "平台类目名称", example = "电子产品")
        private String platformCategoryName;
        
        @Schema(description = "平台类目中文名称", example = "电子产品")
        private String platformCategoryNameCn;
    }
}
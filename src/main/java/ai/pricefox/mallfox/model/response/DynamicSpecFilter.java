package ai.pricefox.mallfox.model.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @desc 动态规格筛选器
 * @since 2025/7/8
 */
@Data
@Schema(description = "动态规格筛选器")
public class DynamicSpecFilter {
    @Schema(description = "属性ID")
    private Long attributeId;

    @Schema(description = "属性名称")
    private String attributeName;

    @Schema(description = "属性选项")
    private List<FilterOption> options;
}

package ai.pricefox.mallfox.model.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @desc 专家评论返回
 * @since 2025/6/29
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ExpertReviewResponse {

    /**
     * 评论的唯一ID
     */
    private Integer id;

    /**
     * 评论标题
     */
    private String title;

    /**
     * 商品图片URL
     */
    private String imageUrl;

    /**
     * 评论描述/摘要
     */
    private String description;

    /**
     * 评论者名称
     */
    private String reviewer;

    /**
     * 阅读时间（如：8min read）
     */
    private String readTime;

    /**
     * 发布时间
     */
    private String publishTime;

    /**
     * 商品品牌
     */
    private String brand;

    /**
     * 商品型号
     */
    private String model;

    /**
     * 评论类型（如：In-Depth Review, Quick Review, Comparison等）
     */
    private String reviewType;

    /**
     * 评分（1-5星）
     */
    private Double rating;

    /**
     * 是否推荐
     */
    private Boolean recommended;
}

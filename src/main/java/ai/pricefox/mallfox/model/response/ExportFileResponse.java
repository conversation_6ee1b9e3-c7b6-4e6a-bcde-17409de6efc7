package ai.pricefox.mallfox.model.response;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @desc 文件导出
 * @since 2025/8/4
 */
@Data
@Schema(description = "文件导出响应")
public class ExportFileResponse {

    @Schema(description = "文件URL", example = "https://oss.example.com/exports/01_20.xlsx")
    private String url;

    @Schema(description = "文件名称", example = "01_20.xlsx")
    private String fileName;

    @Schema(description = "错误信息", example = "文件不存在")
    private String message;

    public ExportFileResponse() {
    }

    public ExportFileResponse(String url, String fileName, String message) {
        this.url = url;
        this.fileName = fileName;
        this.message = message;
    }
}

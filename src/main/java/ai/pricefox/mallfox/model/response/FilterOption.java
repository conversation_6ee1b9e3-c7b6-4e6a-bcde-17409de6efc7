package ai.pricefox.mallfox.model.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @desc 筛选选项
 * @since 2025/7/8
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "筛选选项")
public class FilterOption {
    @Schema(description = "选项值")
    private String value;

    @Schema(description = "显示标签")
    private String label;

    @Schema(description = "商品数量")
    private Long count;
}

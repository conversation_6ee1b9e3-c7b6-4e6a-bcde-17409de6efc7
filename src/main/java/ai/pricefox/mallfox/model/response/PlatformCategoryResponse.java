package ai.pricefox.mallfox.model.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 平台类目响应VO
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
@Data
@Schema(description = "平台类目响应")
public class PlatformCategoryResponse {

    @Schema(description = "ID", example = "1")
    private Long id;

    @Schema(description = "平台编码", example = "TMALL")
    private String platformCode;

    @Schema(description = "平台类目名称", example = "Electronics")
    private String platformCategoryName;

    @Schema(description = "平台类目中文名称", example = "电子产品")
    private String platformCategoryNameCn;

    @Schema(description = "父类目ID", example = "0")
    private Long platformParentCategoryId;

    @Schema(description = "平台类目级别", example = "1")
    private Integer platformLevel;

    @Schema(description = "子类目列表")
    private List<PlatformCategoryResponse> children;
}
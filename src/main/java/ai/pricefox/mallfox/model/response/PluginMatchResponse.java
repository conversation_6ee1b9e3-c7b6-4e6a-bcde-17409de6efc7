package ai.pricefox.mallfox.model.response;

import ai.pricefox.mallfox.enums.PluginMatchStatusEnum;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors; // 引入链式调用

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @desc 插件匹配比价返回 (已优化为统一数据结构)
 * @since 2025/7/31
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL) // 关键：为null的字段不序列化，保持JSON干净
public class PluginMatchResponse {

    /**
     * 插件应展示的状态
     *
     * @see PluginMatchStatusEnum
     */
    @Schema(description = "插件状态")
    private String status;

    /**
     * 统一的数据负载结构
     */
    @Schema(description = "数据负载")
    private PluginMatchData data;


    public static PluginMatchResponse lowerPriceFound(PluginMatchData data) {
        PluginMatchResponse res = new PluginMatchResponse();
        res.setStatus(PluginMatchStatusEnum.LOWER_PRICE_FOUND.getValue());
        res.setData(data);
        return res;
    }

    public static PluginMatchResponse bestPriceConfirmed(PluginMatchData data) {
        PluginMatchResponse res = new PluginMatchResponse();
        res.setStatus(PluginMatchStatusEnum.BEST_PRICE_CONFIRMED.getValue());
        res.setData(data);
        return res;
    }

    public static PluginMatchResponse productNotFound(PluginMatchData data) {
        PluginMatchResponse res = new PluginMatchResponse();
        res.setStatus(PluginMatchStatusEnum.PRODUCT_NOT_FOUND.getValue());
        res.setData(data);
        return res;
    }

    /**
     * 包含了所有三种状态可能需要的数据。
     * 在不同状态下，其中的某些字段可能为null。
     */
    @Data
    @Accessors(chain = true)
    @Schema(description = "统一的插件数据负载")
    public static class PluginMatchData {

        @Schema(description = "我们系统内匹配到的SKU Code (状态A, B)")
        private String matchedSkuCode;

        @Schema(description = "原始请求信息，用于状态C回传或调试")
        private RawRequestInfo rawRequest;

        // === 状态 A (LOWER_PRICE_FOUND) 专属字段 ===
        @Schema(description = "全网找到的最佳报价（最低价）")
        private OfferSnippet bestOffer;

        @Schema(description = "其他有竞争力的报价列表")
        private List<OfferSnippet> otherOffers;

        @Schema(description = "节省的金额信息, '$30 less'")
        private String savingsText;

        // === 状态 B (BEST_PRICE_CONFIRMED) 专属字段 ===
        @Schema(description = "承诺的返利金额")
        private BigDecimal cashbackAmount;

        @Schema(description = "当前页面的佣金链接")
        private String commissionUrl;
    }

    /**
     * 用于回传原始请求信息的内部类
     */
    @Data
    @Accessors(chain = true)
    public static class RawRequestInfo {
        private String sourcePlatform;
        private String platformSkuId;
        private String title;
        private String itemUrl;
    }

    /**
     * 用于展示的报价信息简化片段
     */
    @Data
    @Accessors(chain = true)
    public static class OfferSnippet {
        private String platform;
        private BigDecimal price;
        private String currency;
        private String condition;
        private String itemUrl;
    }
}
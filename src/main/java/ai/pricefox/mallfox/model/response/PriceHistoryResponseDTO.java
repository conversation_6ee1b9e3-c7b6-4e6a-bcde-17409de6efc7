package ai.pricefox.mallfox.model.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @description 价格历史API的响应数据传输对象 (DTO)
 * <AUTHOR>
 * @date 2023-10-28
 * @version 1.0
 *
 * 用于封装返回给前端的、用于渲染价格历史图表和相关信息的所有数据。
 */
@Data
@Schema(description = "价格历史响应体")
public class PriceHistoryResponseDTO {

    /**
     * 用于绘制价格历史曲线图的数据点列表。
     * 每个点代表一天的数据。
     */
    @Schema(description = "用于绘制图表的数据点列表")
    private List<DataPoint> dataPoints;

    /**
     * 在当前查询周期内的历史最低价信息。
     * 用于展示 "Lowest price [in period]" 卡片。
     */
    @Schema(description = "当前周期的最低价信息")
    private PricePoint lowestPriceInPeriod;

    /**
     * 商品的最新价格信息。
     * 用于展示 "Lowest price now" 卡片。
     */
    @Schema(description = "当前最新价格信息")
    private PricePoint latestPrice;

    /**
     * @description 图表数据点
     *
     * 代表价格历史图表上的一个坐标点。
     */
    @Data
    @Schema(description = "图表数据点")
    public static class DataPoint {
        /**
         * 日期，格式为 "yyyy-MM-dd"。
         */
        @Schema(description = "日期, 格式: yyyy-MM-dd")
        private String date;

        /**
         * 当天的最低价。
         */
        @Schema(description = "当天的最低价")
        private BigDecimal lowestPrice;

        /**
         * 当天的平均价。
         */
        @Schema(description = "当天的平均价")
        private BigDecimal averagePrice;
    }

    /**
     * @description 价格信息点
     *
     * 用于展示一个特定的价格及其来源或日期信息。
     */
    @Data
    @Schema(description = "价格信息点")
    public static class PricePoint {
        /**
         * 价格数值。
         */
        @Schema(description = "价格")
        private BigDecimal price;

        /**
         * 描述该价格的来源或日期信息。
         * 例如: "12 Feb 2025" 或 "Game.co.uk"。
         */
        @Schema(description = "出现该价格的日期或来源")
        private String sourceInfo;
    }
}

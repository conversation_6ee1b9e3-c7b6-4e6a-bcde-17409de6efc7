package ai.pricefox.mallfox.model.response;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @desc 商品查询返回
 * @since 2025/6/28
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProductPageResponse {

    /**
     * 商品的唯一ID
     */
    private Integer id;

    /**
     * 商品标题
     */
    private String title;

    /**
     * 商品图片URL
     */
    private String imageUrl;

    /**
     * 当前售价
     */
    private double price;

    /**
     * 原始标价 (用于展示划线价)
     */
    private double listPrice;

    /**
     * 折扣信息文本，例如 "-10%"
     */
    private String discount;

    /**
     * 是否被用户收藏 (红心)
     */
    private boolean favorited;

    /**
     * 商品评分 (例如 4.5)
     */
    private double rating;

    /**
     * 分期付款信息文本
     */
    private String installmentInfo;
}

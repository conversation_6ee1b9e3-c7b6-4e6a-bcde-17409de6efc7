package ai.pricefox.mallfox.model.response;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @desc 商品价格比较实体
 * @since 2025/7/30
 */
@Data
@Schema(description = "商品价格比较详情")
public class ProductPriceCompareResponse {

    @Schema(description = "商品售价")
    private BigDecimal itemPrice;

    @Schema(description = "折扣信息")
    private String discount;

    @Schema(description = "pricefox最终价格")
    private BigDecimal finalPrice;

    @Schema(description = "支付与分期方案")
    private String paymentInstallment;

    @Schema(description = "发货时效")
    private String shippingTime;

    @Schema(description = "退货政策")
    private String returnPolicy;

    @Schema(description = "平台名称")
    private String platform;

    @Schema(description = "卖家类型")
    private String sellerType;

    @Schema(description = "商家/卖家名称")
    private String merchant;

    @Schema(description = "商家评分")
    private BigDecimal merchantRating;

    @Schema(description = "商家评分总数")
    private Integer merchantRatingCount;

    @Schema(description = "商品状态")
    private String condition;

    @Schema(description = "跳转到商城的购买链接 (带佣金)")
    private String shopUrl;
}

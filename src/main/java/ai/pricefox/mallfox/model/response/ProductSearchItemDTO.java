package ai.pricefox.mallfox.model.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @desc 商品搜索结果项
 * @since 2025/7/8
 */
@Data
@Schema(description = "商品搜索结果项")
public class ProductSearchItemDTO {

    @Schema(description = "商品SPU Code")
    private String spuCode;

    @Schema(description = "商品SKU Code")
    private String skuCode;

    @Schema(description = "商品标题")
    private String title;

    @Schema(description = "品牌")
    private String brand;

    @Schema(description = "型号")
    private String model;

    @Schema(description = "主图URL")
    private String mainImageUrl;

//    @Schema(description = "商品图片列表")
//    private List<String> imageUrls;

    @Schema(description = "当前最低价格")
    private BigDecimal price;

    @Schema(description = "原价/列表价")
    private BigDecimal listPrice;

    @Schema(description = "折扣百分比")
    private BigDecimal discount;

    @Schema(description = "评分")
    private BigDecimal rating;

    @Schema(description = "评论数量")
    private Integer reviewCount;

    @Schema(description = "颜色")
    private String color;

    @Schema(description = "存储容量")
    private String storage;

    @Schema(description = "屏幕尺寸")
    private String screenSize;

    @Schema(description = "RAM内存")
    private String ramMemory;

    @Schema(description = "电池容量")
    private String batteryCapacity;

    @Schema(description = "操作系统")
    private String operatingSystem;

    @Schema(description = "处理器")
    private String processor;

    @Schema(description = "库存状态")
    private String inventory;

    @Schema(description = "近30天销量")
    private Integer salesLast30Days;

    @Schema(description = "分期付款信息")
    private String installmentInfo;

//    @Schema(description = "商品链接")
//    private String itemUrl;
//
//    @Schema(description = "卖家信息")
//    private String seller;

//    @Schema(description = "商家评分")
//    private BigDecimal merchantRating;

    @Schema(description = "发货时间")
    private String deliveryTime;

    @Schema(description = "是否收藏")
    private Boolean isFavorited = false;

    @Schema(description = "商品规格属性")
    private List<ProductSpecAttribute> specifications;

    @Data
    @Schema(description = "商品规格属性")
    public static class ProductSpecAttribute {
        @Schema(description = "属性名称")
        private String name;

        @Schema(description = "属性值")
        private String value;

        @Schema(description = "属性单位")
        private String unit;
    }
}

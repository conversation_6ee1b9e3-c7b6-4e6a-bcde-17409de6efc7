package ai.pricefox.mallfox.model.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @desc 商品搜索返回
 * @since 2025/7/8
 */
@Data
@Schema(description = "商品搜索结果")
public class ProductSearchResponse {

    @Schema(description = "商品列表")
    private List<ProductSearchItemDTO> products;

    @Schema(description = "总条数")
    private long total;

//    @Schema(description = "搜索关键词")
//    private String keyword;
//
//    @Schema(description = "排序方式")
//    private String sortBy;
//
//    @Schema(description = "可用的筛选器选项及其数量")
//    private AvailableFiltersDTO availableFilters;
//
//    @Schema(description = "搜索统计信息")
//    private SearchStatsDTO searchStats;
}
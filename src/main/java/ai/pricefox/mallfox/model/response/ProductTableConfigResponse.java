package ai.pricefox.mallfox.model.response;

import com.alibaba.fastjson.JSONObject;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 产品表格配置响应DTO
 * 
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "产品表格配置响应")
public class ProductTableConfigResponse {

    @Schema(description = "主键ID")
    private Integer id;

    @Schema(description = "字段")
    private String field;

    @Schema(description = "类型：1-字段 2-配置")
    private Integer type;

    @Schema(description = "类型描述")
    private String typeDesc;

    @Schema(description = "JSON信息")
    private JSONObject info;

    @Schema(description = "权重")
    private String weight;

    @Schema(description = "表格标签")
    private String tag;

}

package ai.pricefox.mallfox.model.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @desc 搜索统计信息
 * @since 2025/7/8
 */
@Data
@Schema(description = "搜索统计信息")
public class SearchStatsDTO {
    @Schema(description = "搜索耗时(毫秒)")
    private Long searchTime;

    @Schema(description = "匹配的品牌数量")
    private Integer brandCount;

    @Schema(description = "价格区间")
    private PriceRangeDTO priceRange;

    @Data
    @Schema(description = "价格区间")
    public static class PriceRangeDTO {
        @Schema(description = "最低价")
        private Double minPrice;

        @Schema(description = "最高价")
        private Double maxPrice;

        @Schema(description = "平均价")
        private Double avgPrice;
    }
}

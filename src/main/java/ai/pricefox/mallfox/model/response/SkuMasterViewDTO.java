package ai.pricefox.mallfox.model.response;


import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR>
 * @desc
 * @since 2025/6/24
 */
@Data
public class SkuMasterViewDTO extends ProductDataViewResponse {
    /**
     * 自建的核心ID
     */
    private String spuId;
    private String skuId;

    /**
     * product_data_simplify表的主键ID
     */
    private Long simplifyId;

//    private List<ProductDataViewResponse> allPlatformOffers;

    /**
     * 数据字段标记列表
     */
//    private Map<String, Integer> calibrationTags;
}

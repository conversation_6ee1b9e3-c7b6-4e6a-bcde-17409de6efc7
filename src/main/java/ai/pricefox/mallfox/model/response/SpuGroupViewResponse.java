package ai.pricefox.mallfox.model.response;

import ai.pricefox.mallfox.serializer.ProductDataSerializer;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Map;

/**
 * <AUTHOR>
 * @desc 商品SPU分组数据返回
 * @since 2025/6/24
 */
@Data
@JsonSerialize(using = ProductDataSerializer.class)
@Schema(description = "商品SPU分组数据返回")
public class SpuGroupViewResponse {


    /**
     * id
     */
    @Schema(description = "id")
    private Long id;

    /**
     * 上级id
     */
    @Schema(description = "上级id")
    private Long pid;

    /**
     * 分组依据：自建的SPU ID
     */
    @Schema(description = "分组依据：自建的SPU ID")
    private String spuId;

    /**
     * 商品详情ID
     */
    @Schema(description = "商品详情ID")
    private Long offerId;

    /**
     * product_data_simplify表的主键ID
     */
    @Schema(description = "product_data_simplify表的主键ID")
    private Long simplifyId;

    /**
     * 商品SKU ID
     */
    @Schema(description = "商品SKU ID")
    private String skuId;

    /**
     * 商品sku数量
     */
    @Schema(description = "商品sku数量")
    private Integer skuCount;

    /**
     * 库存更新时间
     */
    @Schema(description = "库存更新时间")
    private String inventoryUpdateTime;

    /**
     * 过去7天价格更新频次
     */
    @Schema(description = "过去7天价格更新频次")
    private Integer priceUpdateFrequency;

    /**
     * 商品规格
     */
    @Schema(description = "商品规格")
    private String model;

    /**
     * 商品颜色Storage
     */
    @Schema(description = "商品颜色")
    private String color;

    /**
     * 商品存储
     */
    @Schema(description = "商品存储")
    private String storage;

    /**
     * 商品服务商
     */
    @Schema(description = "商品服务商")
    private String serviceProvider;

    /**
     * 商品状态
     */
    @Schema(description = "商品状态")
    private String conditionNew;

    /**
     * 系列商品状态
     */
    @Schema(description = "系列商品状态")
    private String series;

    /**
     * UPC编码
     */
    @Schema(description = "UPC编码")
    private String upcCode;

    /**
     * 商品来源平台
     */
    @Schema(description = "商品来源平台")
    private String sourcePlatform;

    /**
     * 平台SPU ID
     */
    @Schema(description = "平台SPU ID")
    private String platformSpuId;

    /**
     * 平台SKU ID
     */
    @Schema(description = "平台SKU ID")
    private String platformSkuId;

    /**
     * 商品链接地址
     */
    @Schema(description = "商品链接地址")
    private String itemUrl;

    /**
     * 商品标题
     */
    @Schema(description = "商品标题")
    private String title;

    /**
     * 品牌
     */
    @Schema(description = "品牌")
    private String brand;

    /**
     * 当前价格
     */
    @Schema(description = "当前价格")
    private BigDecimal price;

    /**
     * 列表价
     */
    @Schema(description = "列表价")
    private BigDecimal listPrice;

    /**
     * 折扣
     */
    @Schema(description = "折扣")
    private BigDecimal discount;

    /**
     * 库存
     */
    @Schema(description = "库存")
    private String inventory;

    /**
     * 最近30天销量
     */
    @Schema(description = "最近30天销量")
    private String salesLast30Days;

    /**
     * 卖家信息
     */
    @Schema(description = "卖家信息")
    private String seller;

    /**
     * 商家评分
     */
    @Schema(description = "商家评分")
    private BigDecimal merchantRating;

    /**
     * 款式年份
     */
    @Schema(description = "款式年份")
    private String modelYear;

    /**
     * 操作系统
     */
    @Schema(description = "操作系统")
    private String operatingSystem;

    /**
     * 屏幕尺寸
     */
    @Schema(description = "屏幕尺寸")
    private String screenSize;

    /**
     * 商品主图1URL
     */
    @Schema(description = "商品主图1URL")
    private String productMainImageUrls;

    /**
     * 规格对应颜色图片URL
     */
    @Schema(description = "规格对应颜色图片URL")
    private String productSpecColorUrl;

    /**
     * 安装内存大小
     */
    @Schema(description = "安装内存大小")
    private String ramMemoryInstalledSize;

    /**
     * 处理器型号
     */
    @Schema(description = "处理器型号")
    private String processor;

    /**
     * 移动网络技术
     */
    @Schema(description = "移动网络技术")
    private String cellularTechnology;

    /**
     * 分辨率
     */
    @Schema(description = "分辨率")
    private String resolution;

    /**
     * 刷新率
     */
    @Schema(description = "刷新率")
    private String refreshRate;

    /**
     * 显示屏类型
     */
    @Schema(description = "显示屏类型")
    private String displayType;

    /**
     * 电池容量
     */
    @Schema(description = "电池容量")
    private String batteryPower;

    /**
     * 平均通话时间
     */
    @Schema(description = "平均通话时间")
    private String averageTalkTime;

    /**
     * 电池充电时间
     */
    @Schema(description = "电池充电时间")
    private String batteryChargeTime;

    /**
     * 前置摄像头分辨率
     */
    @Schema(description = "前置摄像头分辨率")
    private String frontPhotoSensorResolution;

    /**
     * 后置摄像头分辨率
     */
    @Schema(description = "后置摄像头分辨率")
    private String rearFacingCameraPhotoSensorResolution;

    /**
     * 后置摄像头数量
     */
    @Schema(description = "后置摄像头数量")
    private Integer numberOfRearFacingCameras;

    /**
     * 有效视频分辨率
     */
    @Schema(description = "有效视频分辨率")
    private String effectiveVideoResolution;

    /**
     * 视频捕捉分辨率
     */
    @Schema(description = "视频捕捉分辨率")
    private String videoCaptureResolution;

    /**
     * SIM卡槽数量
     */
    @Schema(description = "SIM卡槽数量")
    private String simCardSlotCount;

    /**
     * 连接器类型
     */
    @Schema(description = "连接器类型")
    private String connectorType;

    /**
     * 防水等级
     */
    @Schema(description = "防水等级")
    private String waterResistance;

    /**
     * 产品尺寸
     */
    @Schema(description = "产品尺寸")
    private String dimensions;

    /**
     * 商品重量
     */
    @Schema(description = "商品重量")
    private String itemWeight;

    /**
     * 生物识别安全功能
     */
    @Schema(description = "生物识别安全功能")
    private String biometricSecurityFeature;

    /**
     * 支持的卫星导航系统
     */
    @Schema(description = "支持的卫星导航系统")
    private String supportedSatelliteNavigationSystem;

    /**
     * 其他特征描述
     */
    @Schema(description = "其他特征描述")
    private String features;

    /**
     * 退货政策
     */
    @Schema(description = "退货政策")
    private String returnPolicy;

    /**
     * 是否支持分期付款
     */
    @Schema(description = "是否支持分期付款")
    private String paymentInstallment;

    /**
     * 安装支付方式
     */
    @Schema(description = "安装支付方式")
    private String installPayment;

    /**
     * 保修说明
     */
    @Schema(description = "保修说明")
    private String warrantyDescription;

    /**
     * 评论数量
     */
    @Schema(description = "评论数量")
    private Integer reviewNumber;

    /**
     * 评论评分
     */
    @Schema(description = "评论评分")
    private BigDecimal reviewScore;

    /**
     * 评分分布
     */
//    private String reviewRatingDistribution;

    /**
     * 维度评分
     */
//    private String reviewDimensionalRatings;

    /**
     * 评论-整体的好坏标签
     */
    @Schema(description = "评论-整体的好坏标签")
    private String reviewOverviewProsCons;

    /**
     * 按星级分类的评价优缺点
     */
//    private String reviewProsConsByStar;

    /**
     * 发货时间
     */
    @Schema(description = "发货时间")
    private String shippingTime;

    /**
     * 价格更新时间
     */
    @Schema(description = "价格更新时间")
    private String priceUpdateTime;

    /**
     * 一级分类
     */
    @Schema(description = "一级分类")
    private String categoryLevel1;

    /**
     * 二级分类
     */
    @Schema(description = "二级分类")
    private String categoryLevel2;

    /**
     * 三级分类
     */
    @Schema(description = "三级分类")
    private String categoryLevel3;

//    /**
//     * 高频商品更新时间
//     */
//    private LocalDateTime offerUpdateTime;
//
//    /**
//     * 基础商品创建时间
//     */
//    private LocalDateTime simplifyCreateTime;

    /**
     * 是否是子集
     */
    @Schema(description = "是否是子集")
    private Boolean hasChildren = true;

    /**
     * 价格区间
     */
    @Schema(description = "价格区间")
    private String priceUSD;

    /**
     * 连接技术
     */
    @Schema(description = "连接技术")
    private String connectivityTech;

    /**
     * 商品主图 N张
     */
    @Schema(description = "商品主图 N张")
    private String productImages;

    /**
     * Review content-重复
     */
    @Schema(description = "Review content-重复")
    private String reviewContentRepeat;

    /**
     * 评论的图片
     */
    @Schema(description = "评论的图片")
    private String reviewImages;

    /**
     * 商品评分后标签
     */
    @Schema(description = "商品评分后标签")
    private String reviewComment;

    /**
     * 每个商品规格的内容
     */
    @Schema(description = "每个商品规格的内容")
    private String colorRepeat;

    /**
     * 每个商品规格的内容
     */
    @Schema(description = "每个商品规格的内容")
    private String serviceProviderRepeat;

    /**
     * 每个商品规格的内容
     */
    @Schema(description = "每个商品规格的内容")
    private String productConditionRepeat;

    /**
     * 每个商品规格的价格
     */
    @Schema(description = "每个商品规格的价格")
    private String SKUPriceUSD;

    /**
     * priceHistory-区间最低价最低价
     */
    @Schema(description = "priceHistory-区间最低价最低价")
    private String priceHistoryLowestPrice;

    /**
     * 价格趋势 最低价
     */
    @Schema(description = "价格趋势 最低价")
    private String priceHistoryLowestPriceRepeat;

    /**
     * 价格趋势 平均成交价
     */
    @Schema(description = "价格趋势 平均成交价")
    private String priceHistoryAveragePrice;

    /**
     * 比价模块 后面标签
     */
    @Schema(description = "比价模块 后面标签")
    private String priceComparisonComment;

    /**
     * 增加最低价按钮
     */
    @Schema(description = "增加最低价按钮")
    private String addToLowestPrice;
    /**
     * 卖家类型
     */
    @Schema(description = "卖家类型")
    private String sellerType;

    /**
     * 卖家名称-重复
     */
    @Schema(description = "卖家名称-重复")
    private String merchant;
    /**
     * Price history-当前最低价
     */
    @Schema(description = "Price history-当前最低价")
    private String priceHistoryLowestPriceNow;

    /**
     * 规格-参数信息
     */
    @Schema(description = "规格-参数信息")
    private String specificationInfo;

    /**
     * 规格-排名
     */
    @Schema(description = "规格-排名")
    private String specificationRanking;

    /*
     * 规格-专家评论
     */
    @Schema(description = "规格-专家评论")
    private String specificationExpertReview;

    /**
     * 规格-AI描述
     */
    @Schema(description = "规格-AI描述")
    private String specificationAiDescription;

    /**
     * 规格-AI案例
     */
    @Schema(description = "规格-AI案例")
    private String specificationAiCase;

    /**
     * 评论-1-5星评分
     */
    @Schema(description = "评论-1-5星评分")
    private String review1To5StarNumbers;

    /**
     * Review-蛛网图的几个二级版块
     */
    @Schema(description = "Review-蛛网图的几个二级版块")
    private String reviewSeveral;

    /**
     * Review-不同星级评分
     */
    @Schema(description = "Review-不同星级评分")
    private String reviewDifferentStarRatings;

    /**
     * Review-不同星级 提及率
     */
    @Schema(description = "Review-不同星级 提及率")
    private String reviewDifferentMentionRates;

    /**
     * Review-二级版块pros cons
     */
    @Schema(description = "Review-二级版块pros cons")
    private String reviewSecondaryDescription;

    /**
     * Review-二级版块 AI description
     */
    @Schema(description = "Review-二级版块 AI description")
    private String reviewSecondaryAiDescription;

    /**
     * 评论-用户晒单
     */
    @Schema(description = "评论-用户晒单")
    private String reviewCustomerImages;

    /**
     * Review-标签
     */
    @Schema(description = "Review-标签")
    private String reviewTags;

    /**
     * 专家测评
     */
    @Schema(description = "专家测评")
    private String expertReview;

    /**
     * Ranking 列表页 -参数
     */
    @Schema(description = "Ranking 列表页 -参数")
    private String rankingListParameters;

    /**
     * Ranking 列表页 -参数打分
     */
    @Schema(description = "Ranking 列表页 -参数打分")
    private String rankingListParametersScore;

    /**
     * Ranking 列表页 -参数排名
     */
    @Schema(description = "Ranking 列表页 -参数排名")
    private String rankingListParametersRanking;

    /**
     * 首页-分类导航-best seller
     */
    @Schema(description = "首页-分类导航-best seller")
    private String homeCategoryNavigationBestSeller;

    /**
     * 首页-分类导航-Trending
     */
    @Schema(description = "首页-分类导航-Trending")
    private String homeCategoryNavigationTrending;

    /**
     * 首页-分类导航-Top rated
     */
    @Schema(description = "首页-分类导航-Top rated")
    private String homeCategoryNavigationTopRated;

    /**
     * 首页-分类导航-new releases
     */
    @Schema(description = "首页-分类导航-new releases")
    private String homeCategoryNavigationNewReleases;

    /**
     * 首页-banner-数字参数
     */
    @Schema(description = "首页-banner-数字参数")
    private String homeBannerNumberParameters;

    /**
     * 插件页-A页面-优惠信息标签
     */
    @Schema(description = "插件页-A页面-优惠信息标签")
    private String pluginPageAPageDiscountInfoTag;

    /**
     * 插件页-A页面-按钮比价店铺数量
     */
    @Schema(description = "插件页-A页面-按钮比价店铺数量")
    private String pluginPageAPagePriceComparisonShopCount;

    /**
     * 插件页-B页面-比价结果文案参数
     */
    @Schema(description = "插件页-B页面-比价结果文案参数")
    private String pluginPageBPagePriceComparisonResultText;

    /**
     * 插件页-B页面-active按钮
     */
    @Schema(description = "插件页-B页面-active按钮")
    private String pluginPageBPageActiveButton;

    /**
     * 插件页-C页面-voting按钮
     */
    @Schema(description = "插件页-C页面-voting按钮")
    private String pluginPageCPageVotingButton;

    /**
     * 插件页-deal页面
     */
    @Schema(description = "插件页-deal页面")
    private String pluginPageDPage;

    /**
     * 商品主图2
     */
    @Schema(description = "商品主图2")
    private String productMainImage2;

    /**
     * 商品主图3
     */
    @Schema(description = "商品主图3")
    private String productMainImage3;

    /**
     * 商品主图4
     */
    @Schema(description = "商品主图4")
    private String productMainImage4;

    /**
     * 商品主图5
     */
    @Schema(description = "商品主图5")
    private String productMainImage5;

    /**
     * 商品主图6
     */
    @Schema(description = "商品主图6")
    private String productMainImage6;

    /**
     * 商品主图7
     */
    @Schema(description = "商品主图7")
    private String productMainImage7;

    /**
     * 商品主图8
     */
    @Schema(description = "商品主图8")
    private String productMainImage8;

    /**
     * 商品主图9
     */
    @Schema(description = "商品主图9")
    private String productMainImage9;

    /**
     * 商品主图10
     */
    @Schema(description = "商品主图10")
    private String productMainImage10;

    /**
     * 每个商品规格的内容存储重复
     */
    @Schema(description = "每个商品规格的内容存储重复")
    private String storageRepeat;

    /**
     * 数据字段标记列表
     */
    @JsonIgnore
    private Map<String, Integer> calibrationTags;

    /**
     * 字段数据来源列表： 数据来源：0-未知 1-爬虫 2-API 3-内部
     */
    @Schema(description = "字段数据来源列表： 数据来源：0-未知 1-爬虫 2-API 3-内部")
    @JsonIgnore
    private Map<String, Integer> fieldSources;

    /**
     * 是否已读
     */
    private Boolean isRead;

}

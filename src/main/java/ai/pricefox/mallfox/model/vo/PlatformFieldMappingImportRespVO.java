package ai.pricefox.mallfox.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 平台字段映射导入响应VO
 *
 * <AUTHOR>
 * @since 2025-07-26
 */
@Data
@Schema(description = "平台字段映射导入响应")
public class PlatformFieldMappingImportRespVO {

    @Schema(description = "导入总数", example = "100")
    private Integer totalCount;

    @Schema(description = "成功导入数量", example = "95")
    private Integer successCount;

    @Schema(description = "失败数量", example = "5")
    private Integer failureCount;

    @Schema(description = "跳过数量", example = "2")
    private Integer skippedCount;

    @Schema(description = "错误信息")
    private String errorMessage;

    @Schema(description = "是否导入成功", example = "true")
    private Boolean success;

    public static PlatformFieldMappingImportRespVO success(int totalCount, int successCount, int failureCount, int skippedCount) {
        PlatformFieldMappingImportRespVO vo = new PlatformFieldMappingImportRespVO();
        vo.setTotalCount(totalCount);
        vo.setSuccessCount(successCount);
        vo.setFailureCount(failureCount);
        vo.setSkippedCount(skippedCount);
        vo.setSuccess(failureCount == 0);
        return vo;
    }

    public static PlatformFieldMappingImportRespVO success(int totalCount, int successCount, int failureCount) {
        return success(totalCount, successCount, failureCount, 0);
    }

    public static PlatformFieldMappingImportRespVO failure(String errorMessage) {
        PlatformFieldMappingImportRespVO vo = new PlatformFieldMappingImportRespVO();
        vo.setSuccess(false);
        vo.setErrorMessage(errorMessage);
        return vo;
    }
}
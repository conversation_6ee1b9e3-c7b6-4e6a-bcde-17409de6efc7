package ai.pricefox.mallfox.model.vo.mapping.platform;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 平台创建请求VO
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
@Data
@Schema(description = "平台创建请求")
public class PlatformCreateReqVO {

    @Schema(description = "平台名称", example = "Amazon")
    @NotBlank(message = "平台名称不能为空")
    @Size(max = 100, message = "平台名称长度不能超过100个字符")
    private String platformName;

    @Schema(description = "平台名称（中文）", example = "亚马逊")
    @NotBlank(message = "平台中文名称不能为空")
    @Size(max = 100, message = "平台中文名称长度不能超过100个字符")
    private String platformNameCn;

    private Boolean enable = true;
}
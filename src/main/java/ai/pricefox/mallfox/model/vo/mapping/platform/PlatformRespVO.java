package ai.pricefox.mallfox.model.vo.mapping.platform;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 平台响应VO
 *
 * <AUTHOR>
 * @date 2025-08-01
 */
@Data
@Schema(description = "平台响应")
public class PlatformRespVO {

    @Schema(description = "平台ID", example = "1")
    private Integer id;

    @Schema(description = "平台编码", example = "ECOM12345678")
    private String platformCode;

    @Schema(description = "平台名称", example = "Amazon")
    private String platformName;

    @Schema(description = "平台名称（中文）", example = "亚马逊")
    private String platformNameCn;

    @Schema(description = "是否启用", example = "true")
    private Boolean enable;

    @Schema(description = "创建时间")
    private Date createDate;

    @Schema(description = "修改时间")
    private Date updateDate;

    @Schema(description = "创建人名称")
    private String createUsername;

    @Schema(description = "更新人名称")
    private String updateUsername;
}
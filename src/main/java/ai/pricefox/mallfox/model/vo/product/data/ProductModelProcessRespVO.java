package ai.pricefox.mallfox.model.vo.product.data;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 商品型号处理响应结果
 *
 * <AUTHOR>
 * @since 2025-01-08
 */
@Schema(description = "商品型号处理响应结果")
@Data
public class ProductModelProcessRespVO {

    @Schema(description = "处理的总记录数", example = "150")
    private Integer totalProcessed;

    @Schema(description = "成功更新的记录数", example = "120")
    private Integer successCount;

    @Schema(description = "跳过的记录数（无对应品牌信息）", example = "20")
    private Integer skippedCount;

    @Schema(description = "失败的记录数", example = "10")
    private Integer failedCount;

    @Schema(description = "处理详情信息", example = "处理完成，共处理150条记录")
    private String message;

    @Schema(description = "是否还有更多数据需要处理", example = "true")
    private Boolean hasMore;
}

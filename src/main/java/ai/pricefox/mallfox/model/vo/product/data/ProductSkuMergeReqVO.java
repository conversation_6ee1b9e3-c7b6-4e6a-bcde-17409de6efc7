package ai.pricefox.mallfox.model.vo.product.data;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;

/**
 * 商品SKU合并请求参数
 *
 * <AUTHOR>
 * @since 2025-01-09
 */
@Schema(description = "商品SKU合并请求参数")
@Data
public class ProductSkuMergeReqVO {

    @Schema(description = "页码，从1开始", example = "1")
    @Min(value = 1, message = "页码必须大于0")
    private Integer pageNo = 1;

    @Schema(description = "每页数量，最大1000", example = "100")
    @Min(value = 1, message = "每页数量必须大于0")
    @Max(value = 10000, message = "每页数量不能超过1000")
    private Integer pageSize = 100;

    @Schema(description = "数据来源平台，为空则处理所有平台", example = "amazon")
    private String sourcePlatform;

    @Schema(description = "是否预览模式，true=只返回合并结果不更新数据库", example = "false")
    private Boolean previewMode = false;

    @Schema(description = "指定要合并的SPU ID，为空则自动查找需要合并的SKU", example = "SPU0000000001")
    private String targetSpuId;
}

package ai.pricefox.mallfox.model.vo.product.data;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 商品SKU合并响应结果
 *
 * <AUTHOR>
 * @since 2025-01-09
 */
@Schema(description = "商品SKU合并响应结果")
@Data
public class ProductSkuMergeRespVO {

    @Schema(description = "处理的SKU组数", example = "15")
    private Integer totalSkuGroups;

    @Schema(description = "成功合并的记录数", example = "120")
    private Integer successCount;

    @Schema(description = "跳过的记录数（无需合并或字段不完整）", example = "20")
    private Integer skippedCount;

    @Schema(description = "失败的记录数", example = "10")
    private Integer failedCount;

    @Schema(description = "处理详情信息", example = "合并完成，共处理15个SKU组，成功合并120条记录")
    private String message;

    @Schema(description = "是否还有更多数据需要处理", example = "true")
    private Boolean hasMore;
}

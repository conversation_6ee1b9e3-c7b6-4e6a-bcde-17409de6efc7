package ai.pricefox.mallfox.model.vo.product.data;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;

/**
 * UPC码修复请求VO
 * 
 * <AUTHOR>
 * @since 2025-01-14
 */
@Data
@Schema(description = "UPC码修复请求参数")
public class UpcCodeFixReqVO {

    @Schema(description = "页码", example = "1")
    @Min(value = 1, message = "页码必须大于0")
    private Integer pageNo = 1;

    @Schema(description = "每页大小", example = "100")
    @Min(value = 1, message = "每页大小必须大于0")
    @Max(value = 2000, message = "每页大小不能超过2000")
    private Integer pageSize = 100;

    @Schema(description = "预览模式", example = "false")
    private Boolean previewMode = false;

    @Schema(description = "数据来源平台", example = "amazon")
    private String sourcePlatform;

    @Schema(description = "强制更新", example = "false")
    private Boolean forceUpdate = false;
}

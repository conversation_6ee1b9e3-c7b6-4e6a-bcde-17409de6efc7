package ai.pricefox.mallfox.model.vo.product.data;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * UPC码修复响应VO
 * 
 * <AUTHOR>
 * @since 2025-01-14
 */
@Data
@Schema(description = "UPC码修复响应结果")
public class UpcCodeFixRespVO {

    @Schema(description = "总处理记录数", example = "150")
    private Integer totalProcessed;

    @Schema(description = "成功更新记录数", example = "120")
    private Integer successCount;

    @Schema(description = "跳过记录数", example = "20")
    private Integer skippedCount;

    @Schema(description = "失败记录数", example = "10")
    private Integer failedCount;

    @Schema(description = "新生成的SPU数量", example = "5")
    private Integer newSpuCount;

    @Schema(description = "重用现有SPU数量", example = "115")
    private Integer reusedSpuCount;

    @Schema(description = "是否还有更多数据", example = "true")
    private Boolean hasMore;

    @Schema(description = "处理消息", example = "修复完成，共处理150条记录")
    private String message;

    @Schema(description = "详细处理信息")
    private List<String> details;

    @Schema(description = "错误信息")
    private List<String> errors;
}

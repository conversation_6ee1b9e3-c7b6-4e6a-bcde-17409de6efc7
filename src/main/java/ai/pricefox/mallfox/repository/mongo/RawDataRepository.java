package ai.pricefox.mallfox.repository.mongo;

import ai.pricefox.mallfox.domain.standard.mongo.RawData;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * 第三方源数据
 * 提供基本的CRUD操作和自定义查询方法
 *
 * <AUTHOR>
 * @date 2025-07-25
 */
@Repository
public interface RawDataRepository extends MongoRepository<RawData, String> {
    /**
     * 根据平台编码查询
     *
     * @param productPlatform 平台编码
     * @return 匹配的记录
     */
    List<RawData> findByProductPlatform(String productPlatform);

    /**
     * 根据商品唯一编码查询
     *
     * @param productIdentifier 商品唯一编码
     * @return 匹配的记录
     */
    List<RawData> findByProductIdentifier(String productIdentifier);

    /**
     * 根据平台编码和商品唯一编码查询
     *
     * @param productPlatform   平台编码
     * @param productIdentifier 商品唯一编码
     * @return 匹配的记录
     */
    List<RawData> findByProductPlatformAndProductIdentifier(String productPlatform, String productIdentifier);

    /**
     * 根据平台编码、SPU和SKU查询
     *
     * @param productPlatform 平台编码
     * @param spu             SPU编码
     * @param sku             SKU编码
     * @return 匹配的记录
     */
    List<RawData> findByProductPlatformAndSpuAndSku(String productPlatform, String spu, String sku);

    /**
     * 根据平台编码和SKU查询
     *
     * @param productPlatform 平台编码
     * @param sku             SKU编码
     * @return 匹配的记录
     */
    List<RawData> findByProductPlatformAndSku(String productPlatform, String sku);
}
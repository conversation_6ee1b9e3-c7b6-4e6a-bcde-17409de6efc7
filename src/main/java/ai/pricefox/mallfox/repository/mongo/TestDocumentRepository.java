package ai.pricefox.mallfox.repository.mongo;

import ai.pricefox.mallfox.domain.mongo.TestDocument;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * MongoDB 测试文档Repository接口
 * 提供基本的CRUD操作和自定义查询方法
 *
 * <AUTHOR>
 * @date 2025-07-25
 */
@Repository
public interface TestDocumentRepository extends MongoRepository<TestDocument, String> {

    /**
     * 根据名称查找文档
     * @param name 文档名称
     * @return 匹配的文档
     */
    Optional<TestDocument> findByName(String name);

    /**
     * 根据状态查找文档列表
     * @param status 文档状态
     * @return 匹配的文档列表
     */
    List<TestDocument> findByStatus(String status);

    /**
     * 根据创建者查找文档列表
     * @param creator 创建者
     * @return 匹配的文档列表
     */
    List<TestDocument> findByCreator(String creator);

    /**
     * 根据名称模糊查询文档
     * @param name 文档名称（支持正则表达式）
     * @return 匹配的文档列表
     */
    @Query("{'name': {$regex: ?0, $options: 'i'}}")
    List<TestDocument> findByNameContaining(String name);

    /**
     * 查找指定时间范围内创建的文档
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 匹配的文档列表
     */
    List<TestDocument> findByCreateTimeBetween(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 根据数值范围查找文档
     * @param minValue 最小值
     * @param maxValue 最大值
     * @return 匹配的文档列表
     */
    List<TestDocument> findByValueBetween(Double minValue, Double maxValue);

    /**
     * 根据标签查找文档（标签包含指定内容）
     * @param tag 标签内容
     * @return 匹配的文档列表
     */
    @Query("{'tags': {$regex: ?0, $options: 'i'}}")
    List<TestDocument> findByTagsContaining(String tag);

    /**
     * 统计指定状态的文档数量
     * @param status 文档状态
     * @return 文档数量
     */
    long countByStatus(String status);

    /**
     * 删除指定状态的所有文档
     * @param status 文档状态
     * @return 删除的文档数量
     */
    long deleteByStatus(String status);
}
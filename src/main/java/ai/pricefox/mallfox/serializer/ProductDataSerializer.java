package ai.pricefox.mallfox.serializer;

import ai.pricefox.mallfox.model.response.ProductDataViewResponse;
import ai.pricefox.mallfox.model.response.SpuGroupViewResponse;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;

import java.io.IOException;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @desc 商品数据校准标记序列化器
 * @since 2025/6/25
 */
public class ProductDataSerializer extends JsonSerializer<Object> {

    private static final ObjectMapper objectMapper;
    private static final DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    static {
        objectMapper = new ObjectMapper();
        objectMapper.registerModule(new JavaTimeModule());
    }

    @Override
    public void serialize(Object value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        if (value instanceof ProductDataViewResponse) {
            gen.writeObject(transformWithCalibrationTags((ProductDataViewResponse) value));
        } else if (value instanceof SpuGroupViewResponse) {
            gen.writeObject(transformSpuGroupWithCalibrationTags((SpuGroupViewResponse) value));
        } else {
            gen.writeObject(value);
        }
    }

    /**
     * 转换ProductDataViewResponse对象，将带有校准标记的字段转为对象格式
     */
    public Map<String, Object> transformWithCalibrationTags(ProductDataViewResponse product) {
        Map<String, Object> result = new HashMap<>();

        // 获取所有字段
        Field[] fields = ProductDataViewResponse.class.getDeclaredFields();

        for (Field field : fields) {
            field.setAccessible(true);
            try {
                String fieldName = field.getName();
                Object fieldValue = field.get(product);

                // 跳过calibrationTags和fieldSources字段本身
                if ("calibrationTags".equals(fieldName) || "fieldSources".equals(fieldName)) {
                    continue;
                }

                // 特殊处理ID相关字段 - 直接返回原始值，不包装成对象
                if ("id".equals(fieldName) || "pid".equals(fieldName) || "spuId".equals(fieldName) ||
                        "offerId".equals(fieldName) || "skuId".equals(fieldName) || "offerUpdateTime".equals(fieldName)
                        || "simplifyCreateTime".equals(fieldName) || "isRead".equals(fieldName)) {
                    result.put(fieldName, fieldValue);
                    continue;
                }
                if ("hasChildren".equals(fieldName)) {
                    result.put(fieldName, fieldValue);
                    continue;
                }


                // 处理LocalDateTime类型
                if (fieldValue instanceof LocalDateTime) {
                    fieldValue = ((LocalDateTime) fieldValue).format(dateTimeFormatter);
                }

                // 如果字段值为null，设置默认值
                if (fieldValue == null) {
                    fieldValue = getDefaultValueForType(field.getType());
                }

                // 创建包含value、tag和source的对象
                Map<String, Object> taggedValue = new HashMap<>();
                taggedValue.put("value", fieldValue);

                // 检查该字段是否有校准标记
                if (product.getCalibrationTags() != null && product.getCalibrationTags().containsKey(fieldName)) {
                    Integer tagValue = product.getCalibrationTags().get(fieldName);
                    taggedValue.put("tag", tagValue);
                } else {
                    // 没有标记的字段设置tag为0
                    taggedValue.put("tag", 0);
                }

                // 检查该字段是否有数据来源信息
                if (product.getFieldSources() != null && product.getFieldSources().containsKey(fieldName)) {
                    Integer sourceValue = product.getFieldSources().get(fieldName);
                    taggedValue.put("source", sourceValue);
                } else {
                    // 没有来源信息的字段设置source为0
                    taggedValue.put("source", 0);
                }

                result.put(fieldName, taggedValue);
            } catch (IllegalAccessException e) {
                // 处理异常
                Map<String, Object> taggedValue = new HashMap<>();
                taggedValue.put("value", null);
                taggedValue.put("tag", 0);
                taggedValue.put("source", 0);
                result.put(field.getName(), taggedValue);
            }
        }

        return result;
    }

    /**
     * 转换SpuGroupViewResponse对象，将带有校准标记的字段转为对象格式
     */
    public Map<String, Object> transformSpuGroupWithCalibrationTags(SpuGroupViewResponse spuGroup) {
        Map<String, Object> result = new HashMap<>();

        // 获取所有字段
        Field[] fields = SpuGroupViewResponse.class.getDeclaredFields();

        for (Field field : fields) {
            field.setAccessible(true);
            try {
                String fieldName = field.getName();
                Object fieldValue = field.get(spuGroup);

                // 跳过calibrationTags和fieldSources字段本身
                if ("calibrationTags".equals(fieldName) || "fieldSources".equals(fieldName)) {
                    continue;
                }

                // 特殊处理ID相关字段 - 直接返回原始值，不包装成对象
                if ("id".equals(fieldName) || "pid".equals(fieldName) || "spuId".equals(fieldName) ||
                        "offerId".equals(fieldName) || "skuId".equals(fieldName) || "offerUpdateTime".equals(fieldName)
                        || "simplifyCreateTime".equals(fieldName) || "isRead".equals(fieldName)) {
                    result.put(fieldName, fieldValue);
                    continue;
                }

                if ("hasChildren".equals(fieldName)) {
                    result.put(fieldName, fieldValue);
                    continue;
                }


                // 处理LocalDateTime类型
                if (fieldValue instanceof LocalDateTime) {
                    fieldValue = ((LocalDateTime) fieldValue).format(dateTimeFormatter);
                }

                // 如果字段值为null，设置默认值
                if (fieldValue == null) {
                    fieldValue = getDefaultValueForType(field.getType());
                }

                // 创建包含value、tag和source的对象
                Map<String, Object> taggedValue = new HashMap<>();
                taggedValue.put("value", fieldValue);

                // 检查该字段是否有校准标记
                if (spuGroup.getCalibrationTags() != null && spuGroup.getCalibrationTags().containsKey(fieldName)) {
                    Integer tagValue = spuGroup.getCalibrationTags().get(fieldName);
                    taggedValue.put("tag", tagValue);
                } else {
                    // 没有标记的字段设置tag为0
                    taggedValue.put("tag", 0);
                }

                // 检查该字段是否有数据来源信息
                if (spuGroup.getFieldSources() != null && spuGroup.getFieldSources().containsKey(fieldName)) {
                    Integer sourceValue = spuGroup.getFieldSources().get(fieldName);
                    taggedValue.put("source", sourceValue);
                } else {
                    // 没有来源信息的字段设置source为0
                    taggedValue.put("source", 0);
                }

                result.put(fieldName, taggedValue);
            } catch (IllegalAccessException e) {
                // 处理异常
                Map<String, Object> taggedValue = new HashMap<>();
                taggedValue.put("value", null);
                taggedValue.put("tag", 0);
                taggedValue.put("source", 0);
                result.put(field.getName(), taggedValue);
            }
        }

        return result;
    }

    /**
     * 根据字段类型获取默认值
     */
    private Object getDefaultValueForType(Class<?> fieldType) {
        if (fieldType == String.class) {
            return "";
        } else if (fieldType == Integer.class || fieldType == int.class) {
            return 0;
        } else if (fieldType == Long.class || fieldType == long.class) {
            return 0L;
        } else if (fieldType == BigDecimal.class) {
            return BigDecimal.ZERO;
        } else if (fieldType == Boolean.class || fieldType == boolean.class) {
            return false;
        } else if (fieldType == Double.class || fieldType == double.class) {
            return 0.0;
        } else if (fieldType == Float.class || fieldType == float.class) {
            return 0.0f;
        } else if (fieldType == LocalDateTime.class) {
            return "";
        } else if (List.class.isAssignableFrom(fieldType)) {
            return java.util.Collections.emptyList();
        } else if (Map.class.isAssignableFrom(fieldType)) {
            return java.util.Collections.emptyMap();
        } else {
            // 对于其他类型，返回空字符串
            return "";
        }
    }

}
package ai.pricefox.mallfox.service.admin;

import ai.pricefox.mallfox.domain.admin.AdminUser;
import cn.dev33.satoken.stp.SaTokenInfo;

/**
 * 后台Token管理服务接口
 */
public interface AdminTokenService {

    /**
     * 生成并保存后台用户Token
     *
     * @param userId 用户ID
     * @return Token信息
     */
    SaTokenInfo generateAndSaveToken(Long userId);

    /**
     * 刷新后台用户Token
     *
     * @param userId 用户ID
     * @param refreshToken 刷新令牌
     * @return 新的Token信息
     */
    SaTokenInfo refreshToken(Long userId, String refreshToken);

    /**
     * 验证后台Token有效性
     *
     * @param userId 用户ID
     * @param accessToken 访问令牌
     * @return 是否有效
     */
    boolean validateToken(Long userId, String accessToken);

    /**
     * 删除后台用户Token
     *
     * @param userId 用户ID
     */
    void removeToken(Long userId);

    /**
     * 根据Token获取后台用户信息
     *
     * @param accessToken 访问令牌
     * @return 用户信息
     */
    AdminUser getUserByToken(String accessToken);

    /**
     * 更新后台用户最后登录时间
     *
     * @param userId 用户ID
     * @param loginIp 登录IP
     */
    void updateLastLoginTime(Long userId, String loginIp);

    /**
     * 踢下线指定后台用户
     *
     * @param userId 用户ID
     */
    void kickoutUser(Long userId);

    /**
     * 检查Token是否即将过期并自动续期
     *
     * @param userId 用户ID
     * @param accessToken 访问令牌
     * @return 是否续期成功
     */
    boolean renewTokenIfNeeded(Long userId, String accessToken);

    /**
     * 获取后台用户的完整Token信息
     *
     * @param userId 用户ID
     * @return Token信息，包含accessToken和refreshToken
     */
    AdminUser getUserTokenInfo(Long userId);
}

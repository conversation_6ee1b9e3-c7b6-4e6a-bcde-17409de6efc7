package ai.pricefox.mallfox.service.admin;

import ai.pricefox.mallfox.vo.admin.*;
import ai.pricefox.mallfox.vo.base.CommonResult;
import ai.pricefox.mallfox.vo.base.PageResult;

/**
 * 后台用户服务接口
 */
public interface AdminUserService {

    /**
     * 创建后台用户
     *
     * @param reqVO 创建请求
     * @return 创建结果
     */
    CommonResult<AdminUserRespVO> createAdminUser(AdminUserCreateReqVO reqVO);

    /**
     * 更新后台用户
     *
     * @param reqVO 更新请求
     * @return 更新结果
     */
    CommonResult<AdminUserRespVO> updateAdminUser(AdminUserUpdateReqVO reqVO);

    /**
     * 根据ID获取后台用户
     *
     * @param id 用户ID
     * @return 用户信息
     */
    CommonResult<AdminUserRespVO> getAdminUserById(Long id);

    /**
     * 分页查询后台用户
     *
     * @param reqVO 分页查询请求
     * @return 分页结果
     */
    CommonResult<PageResult<AdminUserRespVO>> getAdminUserPage(AdminUserPageReqVO reqVO);

    /**
     * 删除后台用户
     *
     * @param id 用户ID
     * @return 删除结果
     */
    CommonResult<Boolean> deleteAdminUser(Long id);

    /**
     * 根据用户名获取后台用户
     *
     * @param username 用户名
     * @return 用户信息
     */
    CommonResult<AdminUserRespVO> getAdminUserByUsername(String username);

    /**
     * 后台用户登录
     *
     * @param reqVO 登录请求
     * @param loginIp 登录IP
     * @return 登录结果
     */
    CommonResult<AdminAuthRespVO> login(AdminLoginReqVO reqVO, String loginIp);

    /**
     * 后台用户登出
     *
     * @return 登出结果
     */
    CommonResult<String> logout();

    /**
     * 刷新后台用户Token
     *
     * @param refreshToken 刷新令牌
     * @return 新的Token信息
     */
    CommonResult<AdminAuthRespVO> refreshToken(String refreshToken);

    /**
     * 获取当前后台用户信息
     *
     * @return 用户信息
     */
    CommonResult<AdminAuthRespVO> getCurrentAdminUser();
}

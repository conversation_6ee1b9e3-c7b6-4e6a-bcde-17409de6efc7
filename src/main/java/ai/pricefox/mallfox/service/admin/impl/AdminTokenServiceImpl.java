package ai.pricefox.mallfox.service.admin.impl;

import ai.pricefox.mallfox.common.exception.enums.ErrorCodeConstants;
import ai.pricefox.mallfox.domain.admin.AdminUser;
import ai.pricefox.mallfox.mapper.admin.AdminUserMapper;
import ai.pricefox.mallfox.service.admin.AdminTokenService;
import cn.dev33.satoken.stp.SaTokenInfo;
import cn.dev33.satoken.stp.StpUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import ai.pricefox.mallfox.common.constant.RedisKeyConstants;

import static ai.pricefox.mallfox.common.exception.util.ServiceExceptionUtil.exception;

/**
 * 后台Token管理服务实现
 */
@Slf4j
@Service
@AllArgsConstructor
public class AdminTokenServiceImpl implements AdminTokenService {

    private final AdminUserMapper adminUserMapper;
    private final RedisTemplate<String, Object> redisTemplate;

    // Redis Key 前缀（后台用户专用）(使用统一常量)
    private static final String ADMIN_TOKEN_PREFIX = RedisKeyConstants.ADMIN_TOKEN;
    private static final String ADMIN_USER_TOKEN_PREFIX = RedisKeyConstants.ADMIN_ID_TOKEN;
    private static final String ADMIN_REFRESH_TOKEN_PREFIX = RedisKeyConstants.ADMIN_REFRESH_TOKEN;

    // Token 有效期配置（秒）(使用统一常量)
    private static final long ACCESS_TOKEN_EXPIRE = RedisKeyConstants.ADMIN_TOKEN_TIMEOUT;
    private static final long REFRESH_TOKEN_EXPIRE = RedisKeyConstants.USER_REFRESH_TOKEN_TIMEOUT;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SaTokenInfo generateAndSaveToken(Long userId) {
        // 获取用户信息
        AdminUser adminUser = adminUserMapper.selectAdminUserById(userId);
        if (adminUser == null) {
            throw exception(ErrorCodeConstants.USER_NOT_EXIST);
        }

        // 生成新的Token（后台用户专用前缀）
        String accessToken = generateAdminAccessToken();
        String refreshToken = generateAdminRefreshToken();
        
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime accessTokenExpire = now.plusSeconds(ACCESS_TOKEN_EXPIRE);
        LocalDateTime refreshTokenExpire = now.plusSeconds(REFRESH_TOKEN_EXPIRE);

        // 更新数据库中的Token信息
        adminUser.setAccessToken(accessToken);
        adminUser.setRefreshToken(refreshToken);
        adminUser.setTokenExpireTime(accessTokenExpire);
        adminUser.setRefreshTokenExpireTime(refreshTokenExpire);
        adminUser.setLastLoginTime(now);
        adminUser.setUpdateTime(now);
        
        boolean updated = adminUserMapper.updateAdminUserById(adminUser);
        log.info("更新后台用户Token到数据库: userId={}, updated={}, accessToken={}, refreshToken={}", 
                userId, updated, accessToken, refreshToken);

        // 保存到Redis缓存
        saveTokenToRedis(userId, accessToken, refreshToken, accessTokenExpire, refreshTokenExpire);

        // 构建SaTokenInfo
        SaTokenInfo tokenInfo = new SaTokenInfo();
        tokenInfo.setTokenName("Authorization");
        tokenInfo.setTokenValue(accessToken);
        tokenInfo.setIsLogin(true);
        tokenInfo.setLoginId(userId);
        tokenInfo.setLoginType("admin-login");
        tokenInfo.setTokenTimeout(ACCESS_TOKEN_EXPIRE);
        tokenInfo.setSessionTimeout(ACCESS_TOKEN_EXPIRE);
        tokenInfo.setTokenSessionTimeout(ACCESS_TOKEN_EXPIRE);
        tokenInfo.setTokenActiveTimeout(-1L);

        log.info("为后台用户 {} 生成新Token: accessToken={}, refreshToken={}", userId, accessToken, refreshToken);
        return tokenInfo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SaTokenInfo refreshToken(Long userId, String refreshToken) {
        // 验证刷新令牌
        AdminUser adminUser = adminUserMapper.selectAdminUserById(userId);
        if (adminUser == null || !refreshToken.equals(adminUser.getRefreshToken())) {
            throw exception(ErrorCodeConstants.AUTH_TOKEN_INVALID, "刷新令牌无效");
        }

        // 检查刷新令牌是否过期
        if (adminUser.getRefreshTokenExpireTime().isBefore(LocalDateTime.now())) {
            throw exception(ErrorCodeConstants.AUTH_TOKEN_EXPIRED, "刷新令牌已过期");
        }

        // 生成新的访问令牌
        return generateAndSaveToken(userId);
    }

    @Override
    public boolean validateToken(Long userId, String accessToken) {
        if (!StringUtils.hasText(accessToken)) {
            return false;
        }

        // 先从Redis缓存中查找
        String cacheKey = ADMIN_TOKEN_PREFIX + accessToken;
        Object cachedUserId = redisTemplate.opsForValue().get(cacheKey);
        if (cachedUserId != null && userId.equals(cachedUserId)) {
            return true;
        }

        // 从数据库中验证
        AdminUser adminUser = adminUserMapper.selectAdminUserById(userId);
        if (adminUser == null || !accessToken.equals(adminUser.getAccessToken())) {
            return false;
        }

        // 检查Token是否过期
        if (adminUser.getTokenExpireTime().isBefore(LocalDateTime.now())) {
            return false;
        }

        // 更新Redis缓存
        long ttl = java.time.Duration.between(LocalDateTime.now(), adminUser.getTokenExpireTime()).getSeconds();
        if (ttl > 0) {
            redisTemplate.opsForValue().set(cacheKey, userId, ttl, TimeUnit.SECONDS);
        }

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeToken(Long userId) {
        // 从数据库中清除Token
        AdminUser adminUser = adminUserMapper.selectAdminUserById(userId);
        if (adminUser != null) {
            String oldAccessToken = adminUser.getAccessToken();
            String oldRefreshToken = adminUser.getRefreshToken();
            
            adminUser.setAccessToken(null);
            adminUser.setRefreshToken(null);
            adminUser.setTokenExpireTime(null);
            adminUser.setRefreshTokenExpireTime(null);
            adminUser.setUpdateTime(LocalDateTime.now());
            
            adminUserMapper.updateAdminUserById(adminUser);

            // 从Redis中删除缓存
            if (StringUtils.hasText(oldAccessToken)) {
                redisTemplate.delete(ADMIN_TOKEN_PREFIX + oldAccessToken);
                redisTemplate.delete(ADMIN_USER_TOKEN_PREFIX + userId);
                redisTemplate.delete(ADMIN_REFRESH_TOKEN_PREFIX + oldRefreshToken);
            }
        }

        log.info("清除后台用户 {} 的Token", userId);
    }

    @Override
    public AdminUser getUserByToken(String accessToken) {
        if (!StringUtils.hasText(accessToken)) {
            return null;
        }

        // 先从Redis缓存中查找用户ID
        String cacheKey = ADMIN_TOKEN_PREFIX + accessToken;
        Object cachedUserId = redisTemplate.opsForValue().get(cacheKey);
        
        if (cachedUserId != null) {
            Long userId = Long.valueOf(cachedUserId.toString());
            return adminUserMapper.selectAdminUserById(userId);
        }

        // 从数据库中查找
        AdminUser adminUser = adminUserMapper.selectAdminUserByAccessToken(accessToken);
        if (adminUser != null && adminUser.getTokenExpireTime().isAfter(LocalDateTime.now())) {
            // 更新Redis缓存
            long ttl = java.time.Duration.between(LocalDateTime.now(), adminUser.getTokenExpireTime()).getSeconds();
            if (ttl > 0) {
                redisTemplate.opsForValue().set(cacheKey, adminUser.getId(), ttl, TimeUnit.SECONDS);
            }
            return adminUser;
        }

        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateLastLoginTime(Long userId, String loginIp) {
        AdminUser adminUser = new AdminUser();
        adminUser.setId(userId);
        adminUser.setLastLoginTime(LocalDateTime.now());
        adminUser.setLastLoginIp(loginIp);
        adminUser.setUpdateTime(LocalDateTime.now());
        adminUserMapper.updateAdminUserById(adminUser);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void kickoutUser(Long userId) {
        removeToken(userId);
        // 同时清除SaToken的会话
        StpUtil.kickout(userId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean renewTokenIfNeeded(Long userId, String accessToken) {
        AdminUser adminUser = adminUserMapper.selectAdminUserById(userId);
        if (adminUser == null || !accessToken.equals(adminUser.getAccessToken())) {
            return false;
        }

        // 检查Token是否在30分钟内过期
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime expireTime = adminUser.getTokenExpireTime();
        long minutesUntilExpire = java.time.Duration.between(now, expireTime).toMinutes();

        if (minutesUntilExpire <= 30 && minutesUntilExpire > 0) {
            // 续期Token
            LocalDateTime newExpireTime = now.plusSeconds(ACCESS_TOKEN_EXPIRE);
            adminUser.setTokenExpireTime(newExpireTime);
            adminUser.setUpdateTime(now);
            adminUserMapper.updateAdminUserById(adminUser);

            // 更新Redis缓存
            String cacheKey = ADMIN_TOKEN_PREFIX + accessToken;
            redisTemplate.opsForValue().set(cacheKey, userId, ACCESS_TOKEN_EXPIRE, TimeUnit.SECONDS);

            log.info("为后台用户 {} 续期Token", userId);
            return true;
        }

        return false;
    }

    @Override
    public AdminUser getUserTokenInfo(Long userId) {
        return adminUserMapper.selectAdminUserById(userId);
    }

    /**
     * 生成后台访问令牌
     */
    private String generateAdminAccessToken() {
        return "ADMIN_AT_" + UUID.randomUUID().toString().replace("-", "");
    }

    /**
     * 生成后台刷新令牌
     */
    private String generateAdminRefreshToken() {
        return "ADMIN_RT_" + UUID.randomUUID().toString().replace("-", "");
    }

    /**
     * 保存Token到Redis
     */
    private void saveTokenToRedis(Long userId, String accessToken, String refreshToken, 
                                 LocalDateTime accessTokenExpire, LocalDateTime refreshTokenExpire) {
        LocalDateTime now = LocalDateTime.now();
        
        // 保存访问令牌
        long accessTtl = java.time.Duration.between(now, accessTokenExpire).getSeconds();
        if (accessTtl > 0) {
            redisTemplate.opsForValue().set(ADMIN_TOKEN_PREFIX + accessToken, userId, accessTtl, TimeUnit.SECONDS);
            redisTemplate.opsForValue().set(ADMIN_USER_TOKEN_PREFIX + userId, accessToken, accessTtl, TimeUnit.SECONDS);
        }

        // 保存刷新令牌
        long refreshTtl = java.time.Duration.between(now, refreshTokenExpire).getSeconds();
        if (refreshTtl > 0) {
            redisTemplate.opsForValue().set(ADMIN_REFRESH_TOKEN_PREFIX + refreshToken, userId, refreshTtl, TimeUnit.SECONDS);
        }
    }
}

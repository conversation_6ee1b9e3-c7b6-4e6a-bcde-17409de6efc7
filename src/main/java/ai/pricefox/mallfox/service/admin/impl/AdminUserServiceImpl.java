package ai.pricefox.mallfox.service.admin.impl;

import ai.pricefox.mallfox.common.exception.enums.ErrorCodeConstants;
import ai.pricefox.mallfox.domain.admin.AdminUser;
import ai.pricefox.mallfox.mapper.admin.AdminUserMapper;
import ai.pricefox.mallfox.service.admin.AdminTokenService;
import ai.pricefox.mallfox.service.admin.AdminUserService;
import ai.pricefox.mallfox.service.auth.PasswordService;
import ai.pricefox.mallfox.service.auth.VerifyCodeService;
import ai.pricefox.mallfox.utils.AdminTokenUtil;
import ai.pricefox.mallfox.vo.admin.*;
import ai.pricefox.mallfox.vo.base.CommonResult;
import ai.pricefox.mallfox.vo.base.PageResult;
import cn.dev33.satoken.stp.SaTokenInfo;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

import static ai.pricefox.mallfox.common.exception.util.ServiceExceptionUtil.exception;

/**
 * 后台用户服务实现
 */
@Slf4j
@Service
@AllArgsConstructor
public class AdminUserServiceImpl implements AdminUserService {

    private final AdminUserMapper adminUserMapper;
    private final AdminTokenService adminTokenService;
    private final PasswordService passwordService;
    private final VerifyCodeService verifyCodeService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<AdminUserRespVO> createAdminUser(AdminUserCreateReqVO reqVO) {
        // 检查用户名是否已存在
        AdminUser existingUser = adminUserMapper.selectAdminUserByUsername(reqVO.getUsername());
        if (existingUser != null) {
            throw exception(ErrorCodeConstants.AUTH_USER_ALREADY_EXISTS, "用户名已存在");
        }

        // 检查邮箱是否已存在
        if (StringUtils.hasText(reqVO.getEmail())) {
            existingUser = adminUserMapper.selectAdminUserByEmail(reqVO.getEmail());
            if (existingUser != null) {
                throw exception(ErrorCodeConstants.AUTH_EMAIL_ALREADY_EXISTS, "邮箱已存在");
            }
        }

        // 检查手机号是否已存在
        if (StringUtils.hasText(reqVO.getPhone())) {
            existingUser = adminUserMapper.selectAdminUserByPhone(reqVO.getPhone());
            if (existingUser != null) {
                throw exception(ErrorCodeConstants.AUTH_PHONE_ALREADY_EXISTS, "手机号已存在");
            }
        }

        // 创建用户
        AdminUser adminUser = new AdminUser();
        BeanUtils.copyProperties(reqVO, adminUser);
        // 密码加密
        adminUser.setPassword(passwordService.encryptPassword(reqVO.getPassword()));
        adminUser.setCreateTime(LocalDateTime.now());
        adminUser.setUpdateTime(LocalDateTime.now());
        
        // 设置默认值
        if (adminUser.getStatus() == null) {
            adminUser.setStatus(1); // 默认启用
        }
        if (adminUser.getSex() == null) {
            adminUser.setSex(0); // 默认未知
        }

        boolean inserted = adminUserMapper.insertAdminUser(adminUser);
        if (!inserted) {
            throw exception(ErrorCodeConstants.AUTH_REGISTER_FAIL, "创建用户失败");
        }

        // 转换为响应VO
        AdminUserRespVO respVO = new AdminUserRespVO();
        BeanUtils.copyProperties(adminUser, respVO);

        return CommonResult.success(respVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<AdminUserRespVO> updateAdminUser(AdminUserUpdateReqVO reqVO) {
        // 检查用户是否存在
        AdminUser adminUser = adminUserMapper.selectAdminUserById(reqVO.getId());
        if (adminUser == null) {
            throw exception(ErrorCodeConstants.USER_NOT_EXIST);
        }

        // 检查用户名是否被其他用户使用
        AdminUser existingUser = adminUserMapper.selectAdminUserByUsername(reqVO.getUsername());
        if (existingUser != null && !existingUser.getId().equals(reqVO.getId())) {
            throw exception(ErrorCodeConstants.AUTH_USER_ALREADY_EXISTS, "用户名已被其他用户使用");
        }

        // 检查邮箱是否被其他用户使用
        if (StringUtils.hasText(reqVO.getEmail())) {
            existingUser = adminUserMapper.selectAdminUserByEmail(reqVO.getEmail());
            if (existingUser != null && !existingUser.getId().equals(reqVO.getId())) {
                throw exception(ErrorCodeConstants.AUTH_EMAIL_ALREADY_EXISTS, "邮箱已被其他用户使用");
            }
        }

        // 检查手机号是否被其他用户使用
        if (StringUtils.hasText(reqVO.getPhone())) {
            existingUser = adminUserMapper.selectAdminUserByPhone(reqVO.getPhone());
            if (existingUser != null && !existingUser.getId().equals(reqVO.getId())) {
                throw exception(ErrorCodeConstants.AUTH_PHONE_ALREADY_EXISTS, "手机号已被其他用户使用");
            }
        }

        // 更新用户信息
        BeanUtils.copyProperties(reqVO, adminUser);
        adminUser.setUpdateTime(LocalDateTime.now());

        // 如果密码为空，则不更新密码；如果有密码，则加密后更新
        if (!StringUtils.hasText(reqVO.getPassword())) {
            adminUser.setPassword(null);
        } else {
            adminUser.setPassword(passwordService.encryptPassword(reqVO.getPassword()));
        }

        boolean updated = adminUserMapper.updateAdminUserById(adminUser);
        if (!updated) {
            throw exception(ErrorCodeConstants.AUTH_REGISTER_FAIL, "更新用户失败");
        }

        // 重新查询用户信息
        adminUser = adminUserMapper.selectAdminUserById(reqVO.getId());
        
        // 转换为响应VO
        AdminUserRespVO respVO = new AdminUserRespVO();
        BeanUtils.copyProperties(adminUser, respVO);

        return CommonResult.success(respVO);
    }

    @Override
    public CommonResult<AdminUserRespVO> getAdminUserById(Long id) {
        AdminUser adminUser = adminUserMapper.selectAdminUserById(id);
        if (adminUser == null) {
            throw exception(ErrorCodeConstants.USER_NOT_EXIST);
        }

        AdminUserRespVO respVO = new AdminUserRespVO();
        BeanUtils.copyProperties(adminUser, respVO);

        return CommonResult.success(respVO);
    }

    @Override
    public CommonResult<PageResult<AdminUserRespVO>> getAdminUserPage(AdminUserPageReqVO reqVO) {
        Page<AdminUser> page = adminUserMapper.selectAdminUserPage(reqVO);
        
        List<AdminUserRespVO> list = page.getRecords().stream()
                .map(adminUser -> {
                    AdminUserRespVO respVO = new AdminUserRespVO();
                    BeanUtils.copyProperties(adminUser, respVO);
                    return respVO;
                })
                .collect(Collectors.toList());

        PageResult<AdminUserRespVO> pageResult = new PageResult<>();
        pageResult.setList(list);
        pageResult.setTotal(page.getTotal());

        return CommonResult.success(pageResult);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<Boolean> deleteAdminUser(Long id) {
        // 检查用户是否存在
        AdminUser adminUser = adminUserMapper.selectAdminUserById(id);
        if (adminUser == null) {
            throw exception(ErrorCodeConstants.USER_NOT_EXIST);
        }

        // 清除用户的Token
        adminTokenService.removeToken(id);

        // 删除用户
        boolean deleted = adminUserMapper.deleteAdminUserById(id);
        if (!deleted) {
            throw exception(ErrorCodeConstants.AUTH_REGISTER_FAIL, "删除用户失败");
        }

        return CommonResult.success(true);
    }

    @Override
    public CommonResult<AdminUserRespVO> getAdminUserByUsername(String username) {
        AdminUser adminUser = adminUserMapper.selectAdminUserByUsername(username);
        if (adminUser == null) {
            throw exception(ErrorCodeConstants.USER_NOT_EXIST);
        }

        AdminUserRespVO respVO = new AdminUserRespVO();
        BeanUtils.copyProperties(adminUser, respVO);

        return CommonResult.success(respVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<AdminAuthRespVO> login(AdminLoginReqVO reqVO, String loginIp) {
        AdminUser adminUser = null;

        // 根据登录类型进行验证
        switch (reqVO.getLoginType()) {
            case USERNAME_PASSWORD:
                adminUser = validateUsernamePassword(reqVO.getUsername(), reqVO.getPassword());
                break;
            case PHONE_PASSWORD:
                adminUser = validatePhonePassword(reqVO.getPhone(), reqVO.getPassword());
                break;
            case EMAIL_PASSWORD:
                adminUser = validateEmailPassword(reqVO.getEmail(), reqVO.getPassword());
                break;
            case PHONE_CODE:
                adminUser = validatePhoneCode(reqVO.getPhone(), reqVO.getVerifyCode());
                break;
            case EMAIL_CODE:
                adminUser = validateEmailCode(reqVO.getEmail(), reqVO.getVerifyCode());
                break;
            default:
                throw exception(ErrorCodeConstants.AUTH_LOGIN_FAIL_UNKNOWN);
        }

        // 检查用户状态
        if (adminUser.getStatus() != 1) {
            throw exception(ErrorCodeConstants.AUTH_LOGIN_USER_DISABLED);
        }

        // 执行登录，生成并保存Token
        SaTokenInfo tokenInfo = adminTokenService.generateAndSaveToken(adminUser.getId());

        // 更新最后登录时间和IP
        adminTokenService.updateLastLoginTime(adminUser.getId(), loginIp);

        // 获取完整的Token信息
        AdminUser tokenUser = adminTokenService.getUserTokenInfo(adminUser.getId());

        // 构建响应
        AdminAuthRespVO respVO = new AdminAuthRespVO();
        respVO.setAccessToken(tokenInfo.getTokenValue());
        respVO.setRefreshToken(tokenUser.getRefreshToken());
        respVO.setTokenType("Bearer");
        respVO.setExpiresIn(tokenInfo.getTokenTimeout());
        respVO.setUserId(adminUser.getId());
        respVO.setUsername(adminUser.getUsername());
        respVO.setNickname(adminUser.getNickname());
        respVO.setAvatar(adminUser.getAvatar());

        return CommonResult.success(respVO);
    }

    @Override
    public CommonResult<String> logout() {
        // 使用AdminTokenUtil进行登出，会清除数据库和Redis中的Token
        AdminTokenUtil.logout();
        return CommonResult.success("登出成功");
    }

    @Override
    public CommonResult<AdminAuthRespVO> refreshToken(String refreshToken) {
        // 根据刷新令牌查找用户
        AdminUser adminUser = adminUserMapper.selectAdminUserByRefreshToken(refreshToken);
        if (adminUser == null) {
            throw exception(ErrorCodeConstants.AUTH_TOKEN_NOT_FOUND, "刷新令牌不存在");
        }

        // 检查刷新令牌是否过期
        if (adminUser.getRefreshTokenExpireTime().isBefore(LocalDateTime.now())) {
            throw exception(ErrorCodeConstants.AUTH_TOKEN_EXPIRED, "刷新令牌已过期");
        }

        // 生成新的Token
        SaTokenInfo tokenInfo = adminTokenService.refreshToken(adminUser.getId(), refreshToken);

        // 获取更新后的用户信息（包含新的refreshToken）
        AdminUser updatedUser = adminTokenService.getUserTokenInfo(adminUser.getId());

        // 构建响应
        AdminAuthRespVO respVO = new AdminAuthRespVO();
        respVO.setAccessToken(tokenInfo.getTokenValue());
        respVO.setRefreshToken(updatedUser.getRefreshToken());
        respVO.setTokenType("Bearer");
        respVO.setExpiresIn(tokenInfo.getTokenTimeout());
        respVO.setUserId(adminUser.getId());
        respVO.setUsername(adminUser.getUsername());
        respVO.setNickname(adminUser.getNickname());
        respVO.setAvatar(adminUser.getAvatar());

        return CommonResult.success(respVO);
    }

    @Override
    public CommonResult<AdminAuthRespVO> getCurrentAdminUser() {
        // 获取当前用户ID
        Long userId = AdminTokenUtil.getCurrentUserIdOrNull();
        if (userId == null) {
            throw exception(ErrorCodeConstants.AUTH_TOKEN_INVALID, "用户未登录");
        }
        
        // 查询用户信息
        AdminUser adminUser = adminUserMapper.selectAdminUserById(userId);
        if (adminUser == null) {
            throw exception(ErrorCodeConstants.USER_NOT_EXIST);
        }

        // 构建响应（使用现有的Token信息）
        AdminAuthRespVO respVO = new AdminAuthRespVO();
        respVO.setAccessToken(adminUser.getAccessToken());
        respVO.setRefreshToken(adminUser.getRefreshToken());
        respVO.setTokenType("Bearer");
        
        // 计算剩余过期时间
        if (adminUser.getTokenExpireTime() != null) {
            long expiresIn = java.time.Duration.between(LocalDateTime.now(), adminUser.getTokenExpireTime()).getSeconds();
            respVO.setExpiresIn(Math.max(0, expiresIn));
        }
        
        respVO.setUserId(adminUser.getId());
        respVO.setUsername(adminUser.getUsername());
        respVO.setNickname(adminUser.getNickname());
        respVO.setAvatar(adminUser.getAvatar());

        return CommonResult.success(respVO);
    }

    // ========== 私有方法 ==========

    /**
     * 验证用户名密码
     */
    private AdminUser validateUsernamePassword(String username, String password) {
        if (!StringUtils.hasText(username) || !StringUtils.hasText(password)) {
            throw exception(ErrorCodeConstants.AUTH_LOGIN_BAD_CREDENTIALS);
        }

        AdminUser adminUser = adminUserMapper.selectAdminUserByUsername(username);
        if (adminUser == null || !passwordService.verifyPassword(password, adminUser.getPassword())) {
            throw exception(ErrorCodeConstants.AUTH_LOGIN_BAD_CREDENTIALS);
        }

        return adminUser;
    }

    /**
     * 验证手机号密码
     */
    private AdminUser validatePhonePassword(String phone, String password) {
        if (!StringUtils.hasText(phone) || !StringUtils.hasText(password)) {
            throw exception(ErrorCodeConstants.AUTH_LOGIN_BAD_CREDENTIALS);
        }

        AdminUser adminUser = adminUserMapper.selectAdminUserByPhone(phone);
        if (adminUser == null) {
            throw exception(ErrorCodeConstants.AUTH_MOBILE_NOT_EXISTS);
        }

        if (!passwordService.verifyPassword(password, adminUser.getPassword())) {
            throw exception(ErrorCodeConstants.AUTH_LOGIN_BAD_CREDENTIALS);
        }

        return adminUser;
    }

    /**
     * 验证邮箱密码
     */
    private AdminUser validateEmailPassword(String email, String password) {
        if (!StringUtils.hasText(email) || !StringUtils.hasText(password)) {
            throw exception(ErrorCodeConstants.AUTH_LOGIN_BAD_CREDENTIALS);
        }

        AdminUser adminUser = adminUserMapper.selectAdminUserByEmail(email);
        if (adminUser == null) {
            throw exception(ErrorCodeConstants.AUTH_EMAIL_NOT_EXISTS);
        }

        if (!passwordService.verifyPassword(password, adminUser.getPassword())) {
            throw exception(ErrorCodeConstants.AUTH_LOGIN_BAD_CREDENTIALS);
        }

        return adminUser;
    }

    /**
     * 验证手机号验证码
     */
    private AdminUser validatePhoneCode(String phone, String verifyCode) {
        if (!StringUtils.hasText(phone) || !StringUtils.hasText(verifyCode)) {
            throw exception(ErrorCodeConstants.AUTH_LOGIN_BAD_CREDENTIALS);
        }

        AdminUser adminUser = adminUserMapper.selectAdminUserByPhone(phone);
        if (adminUser == null) {
            throw exception(ErrorCodeConstants.AUTH_MOBILE_NOT_EXISTS);
        }

        // 验证验证码（预留接口）
        validateVerifyCode(verifyCode, "PHONE", phone, null);

        return adminUser;
    }

    /**
     * 验证邮箱验证码
     */
    private AdminUser validateEmailCode(String email, String verifyCode) {
        if (!StringUtils.hasText(email) || !StringUtils.hasText(verifyCode)) {
            throw exception(ErrorCodeConstants.AUTH_LOGIN_BAD_CREDENTIALS);
        }

        AdminUser adminUser = adminUserMapper.selectAdminUserByEmail(email);
        if (adminUser == null) {
            throw exception(ErrorCodeConstants.AUTH_EMAIL_NOT_EXISTS);
        }

        // 验证验证码（预留接口）
        validateVerifyCode(verifyCode, "EMAIL", null, email);

        return adminUser;
    }

    /**
     * 验证验证码
     */
    private void validateVerifyCode(String code, String type, String phone, String email) {
        boolean isValid = false;
        String businessType = "LOGIN"; // 默认业务类型

        if ("PHONE".equals(type)) {
            isValid = verifyCodeService.verifySmsCode(phone, code, businessType);
            if (!isValid) {
                throw exception(ErrorCodeConstants.AUTH_MOBILE_CODE_NOT_CORRECT);
            }
        } else if ("EMAIL".equals(type)) {
            isValid = verifyCodeService.verifyEmailCode(email, code, businessType);
            if (!isValid) {
                throw exception(ErrorCodeConstants.AUTH_EMAIL_CODE_NOT_CORRECT);
            }
        }
    }
}

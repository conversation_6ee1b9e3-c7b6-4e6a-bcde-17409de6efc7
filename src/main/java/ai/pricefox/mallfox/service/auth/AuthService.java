package ai.pricefox.mallfox.service.auth;

import ai.pricefox.mallfox.vo.auth.*;
import ai.pricefox.mallfox.vo.base.CommonResult;

/**
 * 认证服务接口
 */
public interface AuthService {

    /**
     * 用户注册
     *
     * @param reqVO 注册请求
     * @return 注册结果
     */
    CommonResult<AuthRespVO> register(RegisterReqVO reqVO);

    /**
     * 用户登录
     *
     * @param reqVO 登录请求
     * @return 登录结果
     */
    CommonResult<AuthRespVO> login(LoginReqVO reqVO);

    /**
     * 用户登出
     *
     * @return 登出结果
     */
    CommonResult<String> logout();

    /**
     * 发送验证码
     *
     * @param reqVO 发送验证码请求
     * @return 发送结果
     */
    CommonResult<String> sendCode(SendCodeReqVO reqVO);

    /**
     * 发送重置密码链接
     *
     * @param reqVO 发送重置密码链接请求
     * @return 发送结果
     */
    CommonResult<String> sendResetLink(SendResetLinkReqVO reqVO);

    /**
     * 重置密码
     *
     * @param reqVO 重置密码请求
     * @return 重置结果
     */
    CommonResult<String> resetPassword(ResetPasswordReqVO reqVO);

    /**
     * 查询登录状态
     *
     * @return 登录状态
     */
    CommonResult<String> getLoginStatus();

    /**
     * 获取当前用户信息
     *
     * @return 用户信息
     */
    CommonResult<AuthRespVO> getCurrentUser();

    /**
     * 刷新Token
     *
     * @param refreshToken 刷新令牌
     * @return 新的Token信息
     */
    CommonResult<AuthRespVO> refreshToken(String refreshToken);
}

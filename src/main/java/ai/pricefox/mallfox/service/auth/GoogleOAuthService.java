package ai.pricefox.mallfox.service.auth;

import ai.pricefox.mallfox.vo.auth.AuthRespVO;
import ai.pricefox.mallfox.vo.base.CommonResult;

/**
 * Google OAuth服务接口
 */
public interface GoogleOAuthService {

    /**
     * 获取Google登录URL
     *
     * @return Google授权URL
     */
    String getAuthorizationUrl();

    /**
     * 处理Google OAuth回调
     *
     * @param code 授权码
     * @return 登录结果
     */
    CommonResult<AuthRespVO> handleOAuthCallback(String code);
} 
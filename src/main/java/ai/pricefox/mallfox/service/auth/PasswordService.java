package ai.pricefox.mallfox.service.auth;

/**
 * 密码处理服务接口
 */
public interface PasswordService {

    /**
     * 加密密码
     *
     * @param plainPassword 明文密码
     * @return 加密后的密码
     */
    String encryptPassword(String plainPassword);

    /**
     * 验证密码
     *
     * @param plainPassword 明文密码
     * @param encryptedPassword 加密后的密码
     * @return 是否匹配
     */
    boolean verifyPassword(String plainPassword, String encryptedPassword);

    /**
     * 检查密码强度
     *
     * @param password 密码
     * @return 是否符合要求
     */
    boolean isValidPassword(String password);

    /**
     * 安全地加密密码（避免重复加密）
     *
     * @param password 密码
     * @return 加密后的密码
     */
    String encryptPasswordSafely(String password);
}

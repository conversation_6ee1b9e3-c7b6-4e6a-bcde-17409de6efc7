package ai.pricefox.mallfox.service.auth;

import ai.pricefox.mallfox.domain.auth.User;
import ai.pricefox.mallfox.domain.auth.UserSocialAccount;
import ai.pricefox.mallfox.vo.auth.AuthRespVO;
import ai.pricefox.mallfox.vo.base.CommonResult;

import java.util.List;
import java.util.Map;

/**
 * 社交账号服务接口
 */
public interface SocialAccountService {

    /**
     * 处理社交登录（登录或注册）
     *
     * @param socialType 社交平台类型（1:Google, 2:Facebook, 3:Twitter, 4:LinkedIn, 5:GitHub, 6:Apple, 7:Microsoft, 8:Amazon）
     * @param socialId 社交平台用户唯一标识
     * @param userInfo 用户信息
     * @return 登录结果
     */
    CommonResult<AuthRespVO> processSocialLogin(Integer socialType, String socialId, Map<String, Object> userInfo);

    /**
     * 绑定社交账号到现有用户
     *
     * @param userId 用户ID
     * @param socialType 社交平台类型（1:Google, 2:Facebook, 3:Twitter, 4:LinkedIn, 5:GitHub, 6:Apple, 7:Microsoft, 8:Amazon）
     * @param socialId 社交平台用户唯一标识
     * @param userInfo 用户信息
     * @return 绑定结果
     */
    CommonResult<String> bindSocialAccount(Long userId, Integer socialType, String socialId, Map<String, Object> userInfo);

    /**
     * 解绑社交账号
     *
     * @param userId 用户ID
     * @param socialType 社交平台类型（1:Google, 2:Facebook, 3:Twitter, 4:LinkedIn, 5:GitHub, 6:Apple, 7:Microsoft, 8:Amazon）
     * @return 解绑结果
     */
    CommonResult<String> unbindSocialAccount(Long userId, Integer socialType);

    /**
     * 查询用户绑定的社交账号列表
     *
     * @param userId 用户ID
     * @return 社交账号列表
     */
    List<UserSocialAccount> getUserSocialAccounts(Long userId);

    /**
     * 根据社交平台类型和ID查找用户
     *
     * @param socialType 社交平台类型（1:Google, 2:Facebook, 3:Twitter, 4:LinkedIn, 5:GitHub, 6:Apple, 7:Microsoft, 8:Amazon）
     * @param socialId 社交平台用户唯一标识
     * @return 用户对象，不存在则返回null
     */
    User findUserBySocialAccount(Integer socialType, String socialId);

    /**
     * 更新社交账号信息
     *
     * @param socialAccount 社交账号信息
     * @return 更新结果
     */
    boolean updateSocialAccount(UserSocialAccount socialAccount);
} 
package ai.pricefox.mallfox.service.auth;

import ai.pricefox.mallfox.domain.auth.User;
import cn.dev33.satoken.stp.SaTokenInfo;

/**
 * Token管理服务接口
 */
public interface TokenService {

    /**
     * 生成并保存用户Token
     *
     * @param userId 用户ID
     * @return Token信息
     */
    SaTokenInfo generateAndSaveToken(Long userId);

    /**
     * 刷新用户Token
     *
     * @param userId 用户ID
     * @param refreshToken 刷新令牌
     * @return 新的Token信息
     */
    SaTokenInfo refreshToken(Long userId, String refreshToken);

    /**
     * 验证Token有效性
     *
     * @param userId 用户ID
     * @param accessToken 访问令牌
     * @return 是否有效
     */
    boolean validateToken(Long userId, String accessToken);

    /**
     * 删除用户Token
     *
     * @param userId 用户ID
     */
    void removeToken(Long userId);

    /**
     * 根据Token获取用户信息
     *
     * @param accessToken 访问令牌
     * @return 用户信息
     */
    User getUserByToken(String accessToken);

    /**
     * 更新用户最后登录时间
     *
     * @param userId 用户ID
     */
    void updateLastLoginTime(Long userId);

    /**
     * 踢下线指定用户
     *
     * @param userId 用户ID
     */
    void kickoutUser(Long userId);

    /**
     * 检查Token是否即将过期并自动续期
     *
     * @param userId 用户ID
     * @param accessToken 访问令牌
     * @return 是否续期成功
     */
    boolean renewTokenIfNeeded(Long userId, String accessToken);

    /**
     * 获取用户的完整Token信息
     *
     * @param userId 用户ID
     * @return Token信息，包含accessToken和refreshToken
     */
    User getUserTokenInfo(Long userId);
}

package ai.pricefox.mallfox.service.auth;

import ai.pricefox.mallfox.vo.base.CommonResult;
import ai.pricefox.mallfox.vo.base.PageResult;
import ai.pricefox.mallfox.vo.user.*;

/**
* <AUTHOR>
* @description 针对表【user(用户表)】的数据库操作Service
* @createDate 2025-05-18 12:14:02
*/
public interface UserService {

    /**
     * 创建用户
     *
     * @param reqVO 创建请求
     * @return 创建结果
     */
    CommonResult<UserRespVO> createUser(UserCreateReqVO reqVO);

    /**
     * 更新用户
     *
     * @param reqVO 更新请求
     * @return 更新结果
     */
    CommonResult<UserRespVO> updateUser(UserUpdateReqVO reqVO);

    /**
     * 根据ID获取用户
     *
     * @param id 用户ID
     * @return 用户信息
     */
    CommonResult<UserRespVO> getUserById(Long id);

    /**
     * 分页查询用户
     *
     * @param reqVO 分页查询请求
     * @return 分页结果
     */
    CommonResult<PageResult<UserRespVO>> getUserPage(UserPageReqVO reqVO);

    /**
     * 删除用户
     *
     * @param id 用户ID
     * @return 删除结果
     */
    CommonResult<Boolean> deleteUser(Long id);

    /**
     * 根据用户名获取用户
     *
     * @param username 用户名
     * @return 用户信息
     */
    CommonResult<UserRespVO> getUserByUsername(String username);

}

package ai.pricefox.mallfox.service.auth;

/**
 * 验证码服务接口
 */
public interface VerifyCodeService {

    /**
     * 发送邮箱验证码
     *
     * @param email 邮箱地址
     * @param businessType 业务类型
     * @return 是否发送成功
     */
    boolean sendEmailCode(String email, String businessType);

    /**
     * 发送短信验证码
     *
     * @param phone 手机号
     * @param businessType 业务类型
     * @return 是否发送成功
     */
    boolean sendSmsCode(String phone, String businessType);

    /**
     * 验证邮箱验证码
     *
     * @param email 邮箱地址
     * @param code 验证码
     * @param businessType 业务类型
     * @return 是否验证成功
     */
    boolean verifyEmailCode(String email, String code, String businessType);

    /**
     * 验证短信验证码
     *
     * @param phone 手机号
     * @param code 验证码
     * @param businessType 业务类型
     * @return 是否验证成功
     */
    boolean verifySmsCode(String phone, String code, String businessType);

    /**
     * 检查发送频率限制
     *
     * @param target 目标（邮箱或手机号）
     * @param type 类型（EMAIL或SMS）
     * @return 是否可以发送
     */
    boolean checkSendFrequency(String target, String type);

    /**
     * 清除验证码
     *
     * @param target 目标（邮箱或手机号）
     * @param type 类型（EMAIL或SMS）
     * @param businessType 业务类型
     */
    void clearCode(String target, String type, String businessType);
}

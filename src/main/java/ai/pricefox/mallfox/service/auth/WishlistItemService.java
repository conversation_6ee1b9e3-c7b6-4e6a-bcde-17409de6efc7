package ai.pricefox.mallfox.service.auth;

import ai.pricefox.mallfox.domain.auth.WishlistItem;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Set;

/**
 * 用户商品收藏表 Service
 * <AUTHOR>
 * @description 针对表【wishlist_item(用户商品收藏表)】的数据库操作Service
 */
public interface WishlistItemService extends IService<WishlistItem> {

    /**
     * 根据用户ID和SKU ID列表查询收藏的SKU ID集合
     * 
     * @param userId 用户ID
     * @param skuIds SKU ID列表
     * @return 已收藏的SKU ID集合
     */
    Set<String> getFavoritedSkuIds(Long userId, List<String> skuIds);

    /**
     * 检查用户是否收藏了指定商品
     * 
     * @param userId 用户ID
     * @param skuId SKU ID
     * @return 是否收藏
     */
    boolean isFavorited(Long userId, String skuId);
}

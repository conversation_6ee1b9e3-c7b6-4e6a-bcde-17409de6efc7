package ai.pricefox.mallfox.service.auth.impl;

import ai.pricefox.mallfox.common.exception.enums.ErrorCodeConstants;
import ai.pricefox.mallfox.domain.auth.User;
import ai.pricefox.mallfox.mapper.auth.UserMapper;
import ai.pricefox.mallfox.service.auth.AuthService;
import ai.pricefox.mallfox.service.integration.EmailService;
import ai.pricefox.mallfox.service.auth.PasswordService;
import ai.pricefox.mallfox.service.auth.TokenService;
import ai.pricefox.mallfox.service.auth.VerifyCodeService;
import ai.pricefox.mallfox.utils.TokenUtil;
import ai.pricefox.mallfox.vo.auth.*;
import ai.pricefox.mallfox.vo.base.CommonResult;
import cn.dev33.satoken.stp.SaTokenInfo;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.UUID;

import static ai.pricefox.mallfox.common.exception.util.ServiceExceptionUtil.exception;

/**
 * 认证服务实现
 */
@Slf4j
@Service
@AllArgsConstructor
public class AuthServiceImpl implements AuthService {

    private final UserMapper userMapper;
    private final TokenService tokenService;
    private final PasswordService passwordService;
    private final EmailService emailService;
    private final VerifyCodeService verifyCodeService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<AuthRespVO> register(RegisterReqVO reqVO) {
        // 验证验证码（预留接口）
        validateVerifyCode(reqVO.getVerifyCode(), reqVO.getRegisterType(), 
                          reqVO.getPhone(), reqVO.getEmail());

        // 检查用户是否已存在
        if (reqVO.getRegisterType() == RegisterReqVO.RegisterType.PHONE) {
            User existingUser = userMapper.selectUserByPhone(reqVO.getPhone());
            if (existingUser != null) {
                throw exception(ErrorCodeConstants.AUTH_PHONE_ALREADY_EXISTS);
            }
        } else if (reqVO.getRegisterType() == RegisterReqVO.RegisterType.EMAIL) {
            User existingUser = userMapper.selectUserByEmail(reqVO.getEmail());
            if (existingUser != null) {
                throw exception(ErrorCodeConstants.AUTH_EMAIL_ALREADY_EXISTS);
            }
        }

        // 创建用户
        User user = new User();
        user.setNickname(reqVO.getNickname());
        user.setPassword(passwordService.encryptPassword(reqVO.getPassword())); // 密码加密存储
        user.setPhone(reqVO.getPhone());
        user.setEmail(reqVO.getEmail());
        user.setStatus(1); // 默认启用
        user.setCreateTime(LocalDateTime.now());
        user.setUpdateTime(LocalDateTime.now());

        // 生成用户名
        if (reqVO.getRegisterType() == RegisterReqVO.RegisterType.PHONE) {
            user.setUsername("user_" + reqVO.getPhone());
        } else {
            user.setUsername("user_" + reqVO.getEmail().split("@")[0]);
        }

        boolean inserted = userMapper.insertUser(user);
        if (!inserted) {
            throw exception(ErrorCodeConstants.AUTH_REGISTER_FAIL);
        }

        // 发送欢迎邮件
        if (StringUtils.hasText(user.getEmail())) {
            try {
                emailService.sendWelcomeEmail(user.getEmail(), user.getUsername());
            } catch (Exception e) {
                log.warn("发送欢迎邮件失败: email={}, username={}", user.getEmail(), user.getUsername(), e);
            }
        }

        // 自动登录，生成并保存Token
        SaTokenInfo tokenInfo = tokenService.generateAndSaveToken(user.getId());

        // 获取完整的Token信息
        User tokenUser = tokenService.getUserTokenInfo(user.getId());

        // 构建响应
        AuthRespVO respVO = new AuthRespVO();
        respVO.setAccessToken(tokenInfo.getTokenValue());
        respVO.setRefreshToken(tokenUser.getRefreshToken());
        respVO.setTokenType("Bearer");
        respVO.setExpiresIn(tokenInfo.getTokenTimeout());
        respVO.setUserId(user.getId());
        respVO.setUsername(user.getUsername());
        respVO.setNickname(user.getNickname());
        respVO.setAvatar(user.getAvatar());

        return CommonResult.success(respVO);
    }

    @Override
    public CommonResult<AuthRespVO> login(LoginReqVO reqVO) {
        User user = null;

        // 根据登录类型进行验证
        switch (reqVO.getLoginType()) {
            case USERNAME_PASSWORD:
                user = validateUsernamePassword(reqVO.getUsername(), reqVO.getPassword());
                break;
            case PHONE_PASSWORD:
                user = validatePhonePassword(reqVO.getPhone(), reqVO.getPassword());
                break;
            case EMAIL_PASSWORD:
                user = validateEmailPassword(reqVO.getEmail(), reqVO.getPassword());
                break;
            case PHONE_CODE:
                user = validatePhoneCode(reqVO.getPhone(), reqVO.getVerifyCode());
                break;
            default:
                throw exception(ErrorCodeConstants.AUTH_LOGIN_FAIL_UNKNOWN);
        }

        // 检查用户状态
        if (user.getStatus() != 1) {
            throw exception(ErrorCodeConstants.AUTH_LOGIN_USER_DISABLED);
        }

        // 执行登录，生成并保存Token
        SaTokenInfo tokenInfo = tokenService.generateAndSaveToken(user.getId());

        // 获取完整的Token信息
        User tokenUser = tokenService.getUserTokenInfo(user.getId());

        // 构建响应
        AuthRespVO respVO = new AuthRespVO();
        respVO.setAccessToken(tokenInfo.getTokenValue());
        respVO.setRefreshToken(tokenUser.getRefreshToken());
        respVO.setTokenType("Bearer");
        respVO.setExpiresIn(tokenInfo.getTokenTimeout());
        respVO.setUserId(user.getId());
        respVO.setUsername(user.getUsername());
        respVO.setNickname(user.getNickname());
        respVO.setAvatar(user.getAvatar());

        return CommonResult.success(respVO);
    }

    @Override
    public CommonResult<String> logout() {
        // 使用TokenUtil进行登出，会清除数据库和Redis中的Token
        TokenUtil.logout();
        return CommonResult.success("登出成功");
    }

    @Override
    public CommonResult<String> sendCode(SendCodeReqVO reqVO) {
        // 验证发送频率（预留）
        validateSendFrequency(reqVO);

        // 根据验证码类型发送
        if (reqVO.getCodeType() == SendCodeReqVO.CodeType.SMS) {
            // 发送短信验证码（预留接口）
            sendSmsCode(reqVO.getPhone(), reqVO.getBusinessType());
            return CommonResult.success("短信验证码发送成功");
        } else if (reqVO.getCodeType() == SendCodeReqVO.CodeType.EMAIL) {
            // 发送邮箱验证码（预留接口）
            sendEmailCode(reqVO.getEmail(), reqVO.getBusinessType());
            return CommonResult.success("邮箱验证码发送成功");
        }

        return CommonResult.success("验证码发送成功");
    }

    @Override
    public CommonResult<String> sendResetLink(SendResetLinkReqVO reqVO) {
        // 检查邮箱是否存在
        User user = userMapper.selectUserByEmail(reqVO.getEmail());
        if (user == null) {
            throw exception(ErrorCodeConstants.AUTH_EMAIL_NOT_EXISTS);
        }

        // 生成重置令牌
        String resetToken = generateResetToken();
        
        // 发送重置密码邮件（预留接口）
        sendResetPasswordEmail(reqVO.getEmail(), resetToken);

        return CommonResult.success("重置密码链接已发送到您的邮箱");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<String> resetPassword(ResetPasswordReqVO reqVO) {
        // 验证重置令牌（预留接口）
        validateResetToken(reqVO.getResetToken(), reqVO.getEmail());

        // 查找用户
        User user = userMapper.selectUserByEmail(reqVO.getEmail());
        if (user == null) {
            throw exception(ErrorCodeConstants.AUTH_EMAIL_NOT_EXISTS);
        }

        // 更新密码
        user.setPassword(passwordService.encryptPassword(reqVO.getNewPassword())); // 密码加密存储
        user.setUpdateTime(LocalDateTime.now());
        userMapper.updateUserById(user);

        // 清除重置令牌（预留接口）
        clearResetToken(reqVO.getResetToken());

        return CommonResult.success("密码重置成功");
    }

    @Override
    public CommonResult<String> getLoginStatus() {
        String status = "当前会话是否登录：" + TokenUtil.isLogin();
        if (TokenUtil.isLogin()) {
            status += "，用户ID：" + TokenUtil.getCurrentUserId();
        }
        return CommonResult.success(status);
    }

    @Override
    public CommonResult<AuthRespVO> getCurrentUser() {
        // 获取当前用户ID
        Long userId = TokenUtil.getCurrentUserIdOrNull();
        if (userId == null) {
            throw exception(ErrorCodeConstants.AUTH_TOKEN_INVALID, "用户未登录");
        }

        // 查询用户信息
        User user = userMapper.selectUserById(userId);
        if (user == null) {
            throw exception(ErrorCodeConstants.USER_NOT_EXIST);
        }

        // 构建响应（使用现有的Token信息）
        AuthRespVO respVO = new AuthRespVO();
        respVO.setAccessToken(user.getAccessToken());
        respVO.setRefreshToken(user.getRefreshToken());
        respVO.setTokenType("Bearer");

        // 计算剩余过期时间
        if (user.getTokenExpireTime() != null) {
            long expiresIn = java.time.Duration.between(LocalDateTime.now(), user.getTokenExpireTime()).getSeconds();
            respVO.setExpiresIn(Math.max(0, expiresIn));
        }

        respVO.setUserId(user.getId());
        respVO.setUsername(user.getUsername());
        respVO.setNickname(user.getNickname());
        respVO.setAvatar(user.getAvatar());

        return CommonResult.success(respVO);
    }

    @Override
    public CommonResult<AuthRespVO> refreshToken(String refreshToken) {
        // 根据刷新令牌查找用户
        User user = userMapper.selectUserByRefreshToken(refreshToken);
        if (user == null) {
            throw exception(ErrorCodeConstants.AUTH_TOKEN_NOT_FOUND, "刷新令牌不存在");
        }

        // 检查刷新令牌是否过期
        if (user.getRefreshTokenExpireTime().isBefore(LocalDateTime.now())) {
            throw exception(ErrorCodeConstants.AUTH_TOKEN_EXPIRED, "刷新令牌已过期");
        }

        // 生成新的Token
        SaTokenInfo tokenInfo = tokenService.refreshToken(user.getId(), refreshToken);

        // 获取更新后的用户信息（包含新的refreshToken）
        User updatedUser = tokenService.getUserTokenInfo(user.getId());

        // 构建响应
        AuthRespVO respVO = new AuthRespVO();
        respVO.setAccessToken(tokenInfo.getTokenValue());
        respVO.setRefreshToken(updatedUser.getRefreshToken()); // 返回新的刷新令牌
        respVO.setTokenType("Bearer");
        respVO.setExpiresIn(tokenInfo.getTokenTimeout());
        respVO.setUserId(user.getId());
        respVO.setUsername(user.getUsername());
        respVO.setNickname(user.getNickname());
        respVO.setAvatar(user.getAvatar());

        return CommonResult.success(respVO);
    }

    // ========== 私有方法 ==========

    /**
     * 验证用户名密码
     */
    private User validateUsernamePassword(String username, String password) {
        if (!StringUtils.hasText(username) || !StringUtils.hasText(password)) {
            throw exception(ErrorCodeConstants.AUTH_LOGIN_BAD_CREDENTIALS);
        }

        User user = userMapper.selectUserByUsername(username);
        if (user == null || !passwordService.verifyPassword(password, user.getPassword())) {
            throw exception(ErrorCodeConstants.AUTH_LOGIN_BAD_CREDENTIALS);
        }

        return user;
    }

    /**
     * 验证手机号密码
     */
    private User validatePhonePassword(String phone, String password) {
        if (!StringUtils.hasText(phone) || !StringUtils.hasText(password)) {
            throw exception(ErrorCodeConstants.AUTH_LOGIN_BAD_CREDENTIALS);
        }

        User user = userMapper.selectUserByPhone(phone);
        if (user == null) {
            throw exception(ErrorCodeConstants.AUTH_MOBILE_NOT_EXISTS);
        }

        if (!passwordService.verifyPassword(password, user.getPassword())) {
            throw exception(ErrorCodeConstants.AUTH_LOGIN_BAD_CREDENTIALS);
        }

        return user;
    }

    /**
     * 验证邮箱密码
     */
    private User validateEmailPassword(String email, String password) {
        if (!StringUtils.hasText(email) || !StringUtils.hasText(password)) {
            throw exception(ErrorCodeConstants.AUTH_LOGIN_BAD_CREDENTIALS);
        }

        User user = userMapper.selectUserByEmail(email);
        if (user == null) {
            throw exception(ErrorCodeConstants.AUTH_EMAIL_NOT_EXISTS);
        }

        if (!passwordService.verifyPassword(password, user.getPassword())) {
            throw exception(ErrorCodeConstants.AUTH_LOGIN_BAD_CREDENTIALS);
        }

        return user;
    }

    /**
     * 验证手机号验证码
     */
    private User validatePhoneCode(String phone, String verifyCode) {
        if (!StringUtils.hasText(phone) || !StringUtils.hasText(verifyCode)) {
            throw exception(ErrorCodeConstants.AUTH_LOGIN_BAD_CREDENTIALS);
        }

        User user = userMapper.selectUserByPhone(phone);
        if (user == null) {
            throw exception(ErrorCodeConstants.AUTH_MOBILE_NOT_EXISTS);
        }

        // 验证验证码（预留接口）
        validateVerifyCode(verifyCode, RegisterReqVO.RegisterType.PHONE, phone, null);

        return user;
    }

    /**
     * 验证验证码（预留接口）
     */
    private void validateVerifyCode(String code, RegisterReqVO.RegisterType type, String phone, String email) {
        // TODO: 实现验证码验证逻辑
        log.info("验证验证码: code={}, type={}, phone={}, email={}", code, type, phone, email);
        
        // 临时验证逻辑，实际应用中需要从缓存或数据库中验证
        if (!"123456".equals(code)) {
            if (type == RegisterReqVO.RegisterType.PHONE) {
                throw exception(ErrorCodeConstants.AUTH_MOBILE_CODE_NOT_CORRECT);
            } else {
                throw exception(ErrorCodeConstants.AUTH_EMAIL_CODE_NOT_CORRECT);
            }
        }
    }

    /**
     * 验证发送频率
     */
    private void validateSendFrequency(SendCodeReqVO reqVO) {
        String target = reqVO.getCodeType() == SendCodeReqVO.CodeType.EMAIL ? reqVO.getEmail() : reqVO.getPhone();
        String type = reqVO.getCodeType() == SendCodeReqVO.CodeType.EMAIL ? "EMAIL" : "SMS";

        if (!verifyCodeService.checkSendFrequency(target, type)) {
            throw exception(ErrorCodeConstants.AUTH_CODE_SEND_TOO_FAST, "验证码发送过于频繁，请稍后再试");
        }
    }

    /**
     * 发送短信验证码
     */
    private void sendSmsCode(String phone, SendCodeReqVO.BusinessType businessType) {
        boolean success = verifyCodeService.sendSmsCode(phone, businessType.name());
        if (!success) {
            throw exception(ErrorCodeConstants.AUTH_MOBILE_CODE_SEND_FAIL, "短信验证码发送失败");
        }
    }

    /**
     * 发送邮箱验证码
     */
    private void sendEmailCode(String email, SendCodeReqVO.BusinessType businessType) {
        boolean success = verifyCodeService.sendEmailCode(email, businessType.name());
        if (!success) {
            throw exception(ErrorCodeConstants.AUTH_EMAIL_CODE_SEND_FAIL, "邮箱验证码发送失败");
        }
    }

    /**
     * 生成重置令牌
     */
    private String generateResetToken() {
        return emailService.generateResetToken();
    }

    /**
     * 发送重置密码邮件
     */
    private void sendResetPasswordEmail(String email, String resetToken) {
        String resetUrl = "https://www.pricefox.ai/reset-password?token=" + resetToken;
        boolean success = emailService.sendResetPasswordEmail(email, resetToken, resetUrl);
        if (!success) {
            throw exception(ErrorCodeConstants.AUTH_EMAIL_SEND_FAIL, "重置密码邮件发送失败");
        }
    }

    /**
     * 验证重置令牌（预留接口）
     */
    private void validateResetToken(String resetToken, String email) {
        // TODO: 实现重置令牌验证逻辑
        log.info("验证重置令牌: resetToken={}, email={}", resetToken, email);
        
        // 临时验证逻辑
        if (!StringUtils.hasText(resetToken)) {
            throw exception(ErrorCodeConstants.AUTH_RESET_TOKEN_INVALID);
        }
    }

    /**
     * 清除重置令牌（预留接口）
     */
    private void clearResetToken(String resetToken) {
        // TODO: 实现清除重置令牌逻辑
        log.info("清除重置令牌: resetToken={}", resetToken);
    }
}

package ai.pricefox.mallfox.service.auth.impl;

import ai.pricefox.mallfox.common.exception.enums.ErrorCodeConstants;
import ai.pricefox.mallfox.config.GoogleOAuthConfig;
import ai.pricefox.mallfox.enums.SocialTypeEnum;
import ai.pricefox.mallfox.service.auth.GoogleOAuthService;
import ai.pricefox.mallfox.service.auth.SocialAccountService;
import ai.pricefox.mallfox.vo.auth.AuthRespVO;
import ai.pricefox.mallfox.vo.base.CommonResult;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

import static ai.pricefox.mallfox.common.exception.util.ServiceExceptionUtil.exception;

@Slf4j
@Service
@RequiredArgsConstructor
public class GoogleOAuthServiceImpl implements GoogleOAuthService {

    private final GoogleOAuthConfig googleOAuthConfig;
    private final SocialAccountService socialAccountService;
    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;

    @Override
    public String getAuthorizationUrl() {
        return String.format(
                "https://accounts.google.com/o/oauth2/auth?client_id=%s&redirect_uri=%s&response_type=code&scope=email profile&access_type=offline",
                googleOAuthConfig.getClientId(),
                googleOAuthConfig.getRedirectUri()
        );
    }

    @Override
    public CommonResult<AuthRespVO> handleOAuthCallback(String code) {
        try {
            log.info("===== 开始处理Google OAuth回调 =====");
            log.info("授权码: {}", code);
            log.info("重定向URI: {}", googleOAuthConfig.getRedirectUri());
            
            // 步骤1: 使用code换取访问令牌
            String accessToken = getAccessToken(code);
            if (accessToken == null) {
                throw exception(ErrorCodeConstants.AUTH_LOGIN_FAIL_UNKNOWN, "Google授权失败");
            }
            
            log.info("成功获取访问令牌: {}", accessToken.substring(0, Math.min(10, accessToken.length())) + "...");

            // 步骤2: 使用访问令牌获取用户信息
            JsonNode userInfoNode = getUserInfo(accessToken);
            if (userInfoNode == null) {
                throw exception(ErrorCodeConstants.AUTH_LOGIN_FAIL_UNKNOWN, "获取Google用户信息失败");
            }
            
            log.info("成功获取用户信息");

            String googleId = userInfoNode.get("id").asText();
            log.info("Google用户ID: {}", googleId);
            
            // 构建用户信息映射
            Map<String, Object> userInfo = new HashMap<>();
            userInfo.put("id", googleId);
            userInfo.put("name", userInfoNode.has("name") ? userInfoNode.get("name").asText() : null);
            userInfo.put("email", userInfoNode.has("email") ? userInfoNode.get("email").asText() : null);
            userInfo.put("picture", userInfoNode.has("picture") ? userInfoNode.get("picture").asText() : null);
            userInfo.put("locale", userInfoNode.has("locale") ? userInfoNode.get("locale").asText() : null);
            userInfo.put("verified_email", userInfoNode.has("verified_email") ? userInfoNode.get("verified_email").asBoolean() : false);
            userInfo.put("access_token", accessToken);
            
            log.info("用户信息映射: {}", userInfo);

            // 调用通用社交账号服务处理登录逻辑
            log.info("调用社交账号服务处理登录");
            CommonResult<AuthRespVO> result = socialAccountService.processSocialLogin(SocialTypeEnum.GOOGLE.getCode(), googleId, userInfo);
            log.info("社交账号登录处理结果: {}", result.getCode());
            
            return result;
        } catch (Exception e) {
            log.error("Google OAuth处理异常", e);
            throw exception(ErrorCodeConstants.AUTH_LOGIN_FAIL_UNKNOWN, "Google登录失败: " + e.getMessage());
        }
    }

    private String getAccessToken(String code) {
        try {
            log.info("===== 开始获取Google访问令牌 =====");
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

            MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
            params.add("code", code);
            params.add("client_id", googleOAuthConfig.getClientId());
            params.add("client_secret", googleOAuthConfig.getClientSecret());
            params.add("redirect_uri", googleOAuthConfig.getRedirectUri());
            params.add("grant_type", "authorization_code");

            HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(params, headers);
            log.info("请求URL: https://oauth2.googleapis.com/token");
            log.info("请求参数: client_id={}, redirect_uri={}, grant_type=authorization_code", 
                    googleOAuthConfig.getClientId(), googleOAuthConfig.getRedirectUri());
                    
            ResponseEntity<String> response = restTemplate.postForEntity(
                    "https://oauth2.googleapis.com/token",
                    request,
                    String.class
            );
            
            log.info("收到响应状态码: {}", response.getStatusCode());
            
            if (response.getStatusCode() == HttpStatus.OK) {
                log.info("响应内容: {}", response.getBody());
                JsonNode jsonResponse = objectMapper.readTree(response.getBody());
                return jsonResponse.get("access_token").asText();
            } else {
                log.error("获取Google访问令牌失败: {}", response.getBody());
                return null;
            }
        } catch (Exception e) {
            log.error("获取Google访问令牌过程异常", e);
            return null;
        }
    }

    private JsonNode getUserInfo(String accessToken) {
        try {
            log.info("===== 开始获取Google用户信息 =====");
            HttpHeaders headers = new HttpHeaders();
            headers.setBearerAuth(accessToken);
            HttpEntity<String> entity = new HttpEntity<>(headers);
            
            log.info("请求URL: https://www.googleapis.com/oauth2/v2/userinfo");

            ResponseEntity<String> response = restTemplate.exchange(
                    "https://www.googleapis.com/oauth2/v2/userinfo",
                    HttpMethod.GET,
                    entity,
                    String.class
            );
            
            log.info("收到响应状态码: {}", response.getStatusCode());

            if (response.getStatusCode() == HttpStatus.OK) {
                log.info("响应内容: {}", response.getBody());
                return objectMapper.readTree(response.getBody());
            } else {
                log.error("获取Google用户信息失败: {}", response.getBody());
                return null;
            }
        } catch (Exception e) {
            log.error("获取Google用户信息过程异常", e);
            return null;
        }
    }
} 
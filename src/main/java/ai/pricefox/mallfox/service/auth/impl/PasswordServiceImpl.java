package ai.pricefox.mallfox.service.auth.impl;

import ai.pricefox.mallfox.config.PasswordEncryptionConfig;
import ai.pricefox.mallfox.service.auth.PasswordService;
import ai.pricefox.mallfox.utils.AESUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * 密码处理服务实现
 */
@Slf4j
@Service
@AllArgsConstructor
public class PasswordServiceImpl implements PasswordService {

    private final PasswordEncryptionConfig passwordConfig;

    @Override
    public String encryptPassword(String plainPassword) {
        if (!StringUtils.hasText(plainPassword)) {
            return plainPassword;
        }

        if (!passwordConfig.isEnableEncryption()) {
            if (passwordConfig.isLogEncryption()) {
                log.debug("密码加密已禁用，返回原密码");
            }
            return plainPassword;
        }

        try {
            String encrypted = AESUtil.encrypt(plainPassword, passwordConfig.getSecretKey());
            if (passwordConfig.isLogEncryption()) {
                log.debug("密码加密成功: {} -> {}", plainPassword, encrypted);
            }
            return encrypted;
        } catch (Exception e) {
            log.error("密码加密失败: {}", plainPassword, e);
            throw new RuntimeException("密码加密失败", e);
        }
    }

    @Override
    public boolean verifyPassword(String plainPassword, String encryptedPassword) {
        if (!StringUtils.hasText(plainPassword) || !StringUtils.hasText(encryptedPassword)) {
            return false;
        }

        if (!passwordConfig.isEnableEncryption()) {
            // 如果加密已禁用，直接比较明文
            return plainPassword.equals(encryptedPassword);
        }

        try {
            boolean result = AESUtil.verifyPassword(plainPassword, encryptedPassword);
            if (passwordConfig.isLogEncryption()) {
                log.debug("密码验证结果: {} vs {} = {}", plainPassword, encryptedPassword, result);
            }
            return result;
        } catch (Exception e) {
            log.error("密码验证失败: plainPassword={}, encryptedPassword={}", plainPassword, encryptedPassword, e);
            return false;
        }
    }

    @Override
    public boolean isValidPassword(String password) {
        if (!StringUtils.hasText(password)) {
            return false;
        }

        // 检查长度
        if (password.length() < passwordConfig.getMinLength() || 
            password.length() > passwordConfig.getMaxLength()) {
            return false;
        }

        // 可以添加更多密码强度检查规则
        // 例如：必须包含数字、字母、特殊字符等

        return true;
    }

    @Override
    public String encryptPasswordSafely(String password) {
        if (!StringUtils.hasText(password)) {
            return password;
        }

        if (!passwordConfig.isEnableEncryption()) {
            return password;
        }

        // 使用AESUtil的安全加密方法
        return AESUtil.encryptPasswordSafely(password);
    }
}

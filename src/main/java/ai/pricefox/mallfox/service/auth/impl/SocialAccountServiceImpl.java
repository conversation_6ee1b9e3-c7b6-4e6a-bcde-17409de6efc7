package ai.pricefox.mallfox.service.auth.impl;

import ai.pricefox.mallfox.common.exception.enums.ErrorCodeConstants;
import ai.pricefox.mallfox.domain.auth.User;
import ai.pricefox.mallfox.domain.auth.UserSocialAccount;
import ai.pricefox.mallfox.enums.SocialTypeEnum;
import ai.pricefox.mallfox.mapper.auth.UserMapper;
import ai.pricefox.mallfox.mapper.auth.UserSocialAccountMapper;
import ai.pricefox.mallfox.service.auth.PasswordService;
import ai.pricefox.mallfox.service.auth.SocialAccountService;
import ai.pricefox.mallfox.service.auth.TokenService;
import ai.pricefox.mallfox.utils.TokenUtil;
import ai.pricefox.mallfox.vo.auth.AuthRespVO;
import ai.pricefox.mallfox.vo.base.CommonResult;
import cn.dev33.satoken.stp.SaTokenInfo;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import static ai.pricefox.mallfox.common.exception.util.ServiceExceptionUtil.exception;

/**
 * 社交账号服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SocialAccountServiceImpl implements SocialAccountService {

    private final UserSocialAccountMapper userSocialAccountMapper;
    private final UserMapper userMapper;
    private final TokenService tokenService;
    private final PasswordService passwordService;
    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<AuthRespVO> processSocialLogin(Integer socialType, String socialId, Map<String, Object> userInfo) {
        try {
            // 根据社交平台类型和ID查询社交账号关联信息
            UserSocialAccount socialAccount = userSocialAccountMapper.selectBySocialTypeAndSocialId(socialType, socialId);

            // 获取社交平台账号信息
            String socialName = userInfo.get("name") != null ? userInfo.get("name").toString() : null;
            String socialEmail = userInfo.get("email") != null ? userInfo.get("email").toString() : null;
            String socialAvatar = userInfo.get("picture") != null ? userInfo.get("picture").toString() : null;
            String accessToken = userInfo.get("access_token") != null ? userInfo.get("access_token").toString() : null;

            User user;
            if (socialAccount == null) {
                // 社交账号不存在，先尝试通过邮箱查找用户
                if (socialEmail != null) {
                    user = userMapper.selectUserByEmail(socialEmail);
                    if (user != null) {
                        // 找到邮箱对应的用户，创建社交账号关联
                        socialAccount = createSocialAccount(user.getId(), socialType, socialId, socialName, socialEmail, socialAvatar, accessToken, userInfo);
                    } else {
                        // 未找到邮箱对应的用户，创建新用户并关联社交账号
                        user = createUser(socialName, socialEmail, socialAvatar);
                        socialAccount = createSocialAccount(user.getId(), socialType, socialId, socialName, socialEmail, socialAvatar, accessToken, userInfo);
                    }
                } else {
                    // 没有邮箱，直接创建新用户并关联社交账号
                    user = createUser(socialName, null, socialAvatar);
                    socialAccount = createSocialAccount(user.getId(), socialType, socialId, socialName, null, socialAvatar, accessToken, userInfo);
                }
            } else {
                // 社交账号已存在，查询关联的用户
                user = userMapper.selectUserById(socialAccount.getUserId());
                if (user == null) {
                    // 关联的用户不存在，可能被删除了，需要重新创建用户
                    user = createUser(socialName, socialEmail, socialAvatar);
                    socialAccount.setUserId(user.getId());
                    userSocialAccountMapper.updateUserSocialAccount(socialAccount);
                }

                // 更新社交账号信息
                updateSocialAccountInfo(socialAccount, socialName, socialEmail, socialAvatar, accessToken);
            }

            // 执行登录，生成并保存Token
            SaTokenInfo tokenInfo = tokenService.generateAndSaveToken(user.getId());

            // 获取完整的Token信息
            User tokenUser = tokenService.getUserTokenInfo(user.getId());

            // 构建响应
            AuthRespVO respVO = new AuthRespVO();
            respVO.setAccessToken(tokenInfo.getTokenValue());
            respVO.setRefreshToken(tokenUser.getRefreshToken());
            respVO.setTokenType("Bearer");
            respVO.setExpiresIn(tokenInfo.getTokenTimeout());
            respVO.setUserId(user.getId());
            respVO.setUsername(user.getUsername());
            respVO.setNickname(user.getNickname());
            respVO.setAvatar(user.getAvatar());

            return CommonResult.success(respVO);
        } catch (Exception e) {
            log.error("社交登录处理异常", e);
            throw exception(ErrorCodeConstants.AUTH_LOGIN_FAIL_UNKNOWN, "社交登录失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<String> bindSocialAccount(Long userId, Integer socialType, String socialId, Map<String, Object> userInfo) {
        // 验证用户是否存在
        User user = userMapper.selectUserById(userId);
        if (user == null) {
            throw exception(ErrorCodeConstants.USER_NOT_EXIST);
        }

        // 检查社交账号是否已被绑定
        UserSocialAccount existingAccount = userSocialAccountMapper.selectBySocialTypeAndSocialId(socialType, socialId);
        if (existingAccount != null) {
            if (!existingAccount.getUserId().equals(userId)) {
                throw exception(ErrorCodeConstants.AUTH_THIRD_LOGIN_BIND_EXIST, "该社交账号已被其他用户绑定");
            }
            return CommonResult.success("该社交账号已绑定到当前用户");
        }

        // 获取社交平台账号信息
        String socialName = userInfo.get("name") != null ? userInfo.get("name").toString() : null;
        String socialEmail = userInfo.get("email") != null ? userInfo.get("email").toString() : null;
        String socialAvatar = userInfo.get("picture") != null ? userInfo.get("picture").toString() : null;
        String accessToken = userInfo.get("access_token") != null ? userInfo.get("access_token").toString() : null;

        // 创建社交账号关联
        createSocialAccount(userId, socialType, socialId, socialName, socialEmail, socialAvatar, accessToken, userInfo);

        // 获取社交平台名称
        SocialTypeEnum socialTypeEnum = SocialTypeEnum.getByCode(socialType);
        String socialTypeName = socialTypeEnum != null ? socialTypeEnum.getName() : "未知平台";

        return CommonResult.success("成功绑定" + socialTypeName + "账号");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<String> unbindSocialAccount(Long userId, Integer socialType) {
        // 验证用户是否存在
        User user = userMapper.selectUserById(userId);
        if (user == null) {
            throw exception(ErrorCodeConstants.USER_NOT_EXIST);
        }

        // 检查是否绑定了该社交账号
        UserSocialAccount socialAccount = userSocialAccountMapper.selectByUserIdAndSocialType(userId, socialType);
        if (socialAccount == null) {
            throw exception(ErrorCodeConstants.AUTH_THIRD_LOGIN_NOT_BIND, "未绑定该社交账号");
        }

        // 检查是否为唯一登录方式
        // 如果用户没有设置密码且只有这一个社交账号，则不允许解绑
        boolean hasPassword = user.getPassword() != null && !user.getPassword().isEmpty();
        List<UserSocialAccount> accounts = userSocialAccountMapper.selectByUserId(userId);
        if (!hasPassword && accounts.size() == 1) {
            throw exception(ErrorCodeConstants.AUTH_THIRD_LOGIN_UNBIND_FAIL, "无法解绑唯一登录方式，请先设置密码");
        }

        // 解绑社交账号
        userSocialAccountMapper.deleteUserSocialAccount(socialAccount.getId());

        // 获取社交平台名称
        SocialTypeEnum socialTypeEnum = SocialTypeEnum.getByCode(socialType);
        String socialTypeName = socialTypeEnum != null ? socialTypeEnum.getName() : "未知平台";

        return CommonResult.success("成功解绑" + socialTypeName + "账号");
    }

    @Override
    public List<UserSocialAccount> getUserSocialAccounts(Long userId) {
        return userSocialAccountMapper.selectByUserId(userId);
    }

    @Override
    public User findUserBySocialAccount(Integer socialType, String socialId) {
        UserSocialAccount socialAccount = userSocialAccountMapper.selectBySocialTypeAndSocialId(socialType, socialId);
        if (socialAccount == null) {
            return null;
        }
        return userMapper.selectUserById(socialAccount.getUserId());
    }

    @Override
    public boolean updateSocialAccount(UserSocialAccount socialAccount) {
        return userSocialAccountMapper.updateUserSocialAccount(socialAccount);
    }

    /**
     * 创建用户
     *
     * @param nickname 昵称
     * @param email 邮箱
     * @param avatar 头像
     * @return 用户对象
     */
    private User createUser(String nickname, String email, String avatar) {
        User user = new User();
        user.setUsername("social_" + UUID.randomUUID().toString().substring(0, 8));
        user.setNickname(nickname != null ? nickname : "用户" + UUID.randomUUID().toString().substring(0, 5));
        user.setEmail(email);
        user.setAvatar(avatar);
        user.setStatus(1); // 启用状态
        // 随机8位密码
        user.setPassword(passwordService.encryptPassword(UUID.randomUUID().toString().substring(0, 8)));
        user.setCreateTime(LocalDateTime.now());
        user.setUpdateTime(LocalDateTime.now());

        boolean inserted = userMapper.insertUser(user);
        if (!inserted) {
            throw exception(ErrorCodeConstants.AUTH_REGISTER_FAIL, "创建用户失败");
        }
        return user;
    }

    /**
     * 创建社交账号关联
     *
     * @param userId 用户ID
     * @param socialType 社交平台类型
     * @param socialId 社交平台用户唯一标识
     * @param socialName 社交平台用户名
     * @param socialEmail 社交平台邮箱
     * @param socialAvatar 社交平台头像
     * @param accessToken 访问令牌
     * @param extraData 额外数据
     * @return 社交账号关联对象
     */
    private UserSocialAccount createSocialAccount(Long userId, Integer socialType, String socialId, 
                                               String socialName, String socialEmail, String socialAvatar, 
                                               String accessToken, Map<String, Object> extraData) {
        UserSocialAccount socialAccount = new UserSocialAccount();
        socialAccount.setUserId(userId);
        socialAccount.setSocialType(socialType);
        socialAccount.setSocialId(socialId);
        socialAccount.setSocialName(socialName);
        socialAccount.setSocialEmail(socialEmail);
        socialAccount.setSocialAvatar(socialAvatar);
        socialAccount.setAccessToken(accessToken);
        
        try {
            if (extraData != null) {
                socialAccount.setExtraData(objectMapper.writeValueAsString(extraData));
            }
        } catch (Exception e) {
            log.error("序列化社交账号额外数据异常", e);
        }
        
        socialAccount.setCreateTime(LocalDateTime.now());
        socialAccount.setUpdateTime(LocalDateTime.now());
        
        boolean inserted = userSocialAccountMapper.insertUserSocialAccount(socialAccount);
        if (!inserted) {
            throw exception(ErrorCodeConstants.AUTH_THIRD_LOGIN_BIND_FAIL, "创建社交账号关联失败");
        }
        return socialAccount;
    }

    /**
     * 更新社交账号信息
     *
     * @param socialAccount 社交账号对象
     * @param socialName 社交平台用户名
     * @param socialEmail 社交平台邮箱
     * @param socialAvatar 社交平台头像
     * @param accessToken 访问令牌
     */
    private void updateSocialAccountInfo(UserSocialAccount socialAccount, String socialName, 
                                       String socialEmail, String socialAvatar, String accessToken) {
        boolean updated = false;
        
        if (socialName != null && !socialName.equals(socialAccount.getSocialName())) {
            socialAccount.setSocialName(socialName);
            updated = true;
        }
        
        if (socialEmail != null && !socialEmail.equals(socialAccount.getSocialEmail())) {
            socialAccount.setSocialEmail(socialEmail);
            updated = true;
        }
        
        if (socialAvatar != null && !socialAvatar.equals(socialAccount.getSocialAvatar())) {
            socialAccount.setSocialAvatar(socialAvatar);
            updated = true;
        }
        
        if (accessToken != null && !accessToken.equals(socialAccount.getAccessToken())) {
            socialAccount.setAccessToken(accessToken);
            updated = true;
        }
        
        if (updated) {
            socialAccount.setUpdateTime(LocalDateTime.now());
            userSocialAccountMapper.updateUserSocialAccount(socialAccount);
        }
    }
} 
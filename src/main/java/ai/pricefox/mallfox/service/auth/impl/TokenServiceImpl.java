package ai.pricefox.mallfox.service.auth.impl;

import ai.pricefox.mallfox.common.exception.enums.ErrorCodeConstants;
import ai.pricefox.mallfox.domain.auth.User;
import ai.pricefox.mallfox.mapper.auth.UserMapper;
import ai.pricefox.mallfox.service.auth.TokenService;
import cn.dev33.satoken.stp.SaTokenInfo;
import cn.dev33.satoken.stp.StpUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import ai.pricefox.mallfox.common.constant.RedisKeyConstants;

import static ai.pricefox.mallfox.common.exception.util.ServiceExceptionUtil.exception;

/**
 * Token管理服务实现
 */
@Slf4j
@Service
@AllArgsConstructor
public class TokenServiceImpl implements TokenService {

    private final UserMapper userMapper;
    private final RedisTemplate<String, Object> redisTemplate;

    // Redis Key 前缀 (使用统一常量)
    private static final String TOKEN_PREFIX = RedisKeyConstants.USER_TOKEN;
    private static final String USER_TOKEN_PREFIX = RedisKeyConstants.USER_ID_TOKEN;
    private static final String REFRESH_TOKEN_PREFIX = RedisKeyConstants.USER_REFRESH_TOKEN;

    // Token 有效期配置（秒）(使用统一常量)
    private static final long ACCESS_TOKEN_EXPIRE = RedisKeyConstants.USER_TOKEN_TIMEOUT;
    private static final long REFRESH_TOKEN_EXPIRE = RedisKeyConstants.USER_REFRESH_TOKEN_TIMEOUT;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SaTokenInfo generateAndSaveToken(Long userId) {
        // 获取用户信息
        User user = userMapper.selectUserById(userId);
        if (user == null) {
            throw exception(ErrorCodeConstants.USER_NOT_EXIST);
        }

        // 生成新的Token
        String accessToken = generateAccessToken();
        String refreshToken = generateRefreshToken();
        
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime accessTokenExpire = now.plusSeconds(ACCESS_TOKEN_EXPIRE);
        LocalDateTime refreshTokenExpire = now.plusSeconds(REFRESH_TOKEN_EXPIRE);

        // 更新数据库中的Token信息
        user.setAccessToken(accessToken);
        user.setRefreshToken(refreshToken);
        user.setTokenExpireTime(accessTokenExpire);
        user.setRefreshTokenExpireTime(refreshTokenExpire);
        user.setLastLoginTime(now);
        user.setUpdateTime(now);

        boolean updated = userMapper.updateUserById(user);
        log.info("更新用户Token到数据库: userId={}, updated={}, refreshToken={}", userId, updated, refreshToken);

        // 保存到Redis缓存
        saveTokenToRedis(userId, accessToken, refreshToken, accessTokenExpire, refreshTokenExpire);

        // 构建SaTokenInfo
        SaTokenInfo tokenInfo = new SaTokenInfo();
        tokenInfo.setTokenName("Authorization");
        tokenInfo.setTokenValue(accessToken);
        tokenInfo.setIsLogin(true);
        tokenInfo.setLoginId(userId);
        tokenInfo.setLoginType("login");
        tokenInfo.setTokenTimeout(ACCESS_TOKEN_EXPIRE);
        tokenInfo.setSessionTimeout(ACCESS_TOKEN_EXPIRE);
        tokenInfo.setTokenSessionTimeout(ACCESS_TOKEN_EXPIRE);
        tokenInfo.setTokenActiveTimeout(-1L);

        log.info("为用户 {} 生成新Token: accessToken={}, refreshToken={}", userId, accessToken, refreshToken);
        return tokenInfo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SaTokenInfo refreshToken(Long userId, String refreshToken) {
        // 验证刷新令牌
        User user = userMapper.selectUserById(userId);
        if (user == null || !refreshToken.equals(user.getRefreshToken())) {
            throw exception(ErrorCodeConstants.AUTH_TOKEN_INVALID, "刷新令牌无效");
        }

        // 检查刷新令牌是否过期
        if (user.getRefreshTokenExpireTime().isBefore(LocalDateTime.now())) {
            throw exception(ErrorCodeConstants.AUTH_TOKEN_EXPIRED, "刷新令牌已过期");
        }

        // 生成新的访问令牌
        return generateAndSaveToken(userId);
    }

    @Override
    public boolean validateToken(Long userId, String accessToken) {
        if (!StringUtils.hasText(accessToken)) {
            return false;
        }

        // 先从Redis缓存中查找
        String cacheKey = TOKEN_PREFIX + accessToken;
        Object cachedUserId = redisTemplate.opsForValue().get(cacheKey);
        if (cachedUserId != null && userId.equals(cachedUserId)) {
            return true;
        }

        // 从数据库中验证
        User user = userMapper.selectUserById(userId);
        if (user == null || !accessToken.equals(user.getAccessToken())) {
            return false;
        }

        // 检查Token是否过期
        if (user.getTokenExpireTime().isBefore(LocalDateTime.now())) {
            return false;
        }

        // 更新Redis缓存
        long ttl = java.time.Duration.between(LocalDateTime.now(), user.getTokenExpireTime()).getSeconds();
        if (ttl > 0) {
            redisTemplate.opsForValue().set(cacheKey, userId, ttl, TimeUnit.SECONDS);
        }

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeToken(Long userId) {
        // 从数据库中清除Token
        User user = userMapper.selectUserById(userId);
        if (user != null) {
            String oldAccessToken = user.getAccessToken();
            String oldRefreshToken = user.getRefreshToken();
            
            user.setAccessToken(null);
            user.setRefreshToken(null);
            user.setTokenExpireTime(null);
            user.setRefreshTokenExpireTime(null);
            user.setUpdateTime(LocalDateTime.now());
            
            userMapper.updateUserById(user);

            // 从Redis中删除缓存
            if (StringUtils.hasText(oldAccessToken)) {
                redisTemplate.delete(TOKEN_PREFIX + oldAccessToken);
                redisTemplate.delete(USER_TOKEN_PREFIX + userId);
                redisTemplate.delete(REFRESH_TOKEN_PREFIX + oldRefreshToken);
            }
        }

        log.info("清除用户 {} 的Token", userId);
    }

    @Override
    public User getUserByToken(String accessToken) {
        if (!StringUtils.hasText(accessToken)) {
            return null;
        }

        // 先从Redis缓存中查找用户ID
        String cacheKey = TOKEN_PREFIX + accessToken;
        Object cachedUserId = redisTemplate.opsForValue().get(cacheKey);
        
        if (cachedUserId != null) {
            Long userId = Long.valueOf(cachedUserId.toString());
            return userMapper.selectUserById(userId);
        }

        // 从数据库中查找
        User user = userMapper.selectUserByAccessToken(accessToken);
        if (user != null && user.getTokenExpireTime().isAfter(LocalDateTime.now())) {
            // 更新Redis缓存
            long ttl = java.time.Duration.between(LocalDateTime.now(), user.getTokenExpireTime()).getSeconds();
            if (ttl > 0) {
                redisTemplate.opsForValue().set(cacheKey, user.getId(), ttl, TimeUnit.SECONDS);
            }
            return user;
        }

        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateLastLoginTime(Long userId) {
        User user = new User();
        user.setId(userId);
        user.setLastLoginTime(LocalDateTime.now());
        user.setUpdateTime(LocalDateTime.now());
        userMapper.updateUserById(user);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void kickoutUser(Long userId) {
        removeToken(userId);
        // 同时清除SaToken的会话
        StpUtil.kickout(userId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean renewTokenIfNeeded(Long userId, String accessToken) {
        User user = userMapper.selectUserById(userId);
        if (user == null || !accessToken.equals(user.getAccessToken())) {
            return false;
        }

        // 检查Token是否在30分钟内过期
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime expireTime = user.getTokenExpireTime();
        long minutesUntilExpire = java.time.Duration.between(now, expireTime).toMinutes();

        if (minutesUntilExpire <= 30 && minutesUntilExpire > 0) {
            // 续期Token
            LocalDateTime newExpireTime = now.plusSeconds(ACCESS_TOKEN_EXPIRE);
            user.setTokenExpireTime(newExpireTime);
            user.setUpdateTime(now);
            userMapper.updateUserById(user);

            // 更新Redis缓存
            String cacheKey = TOKEN_PREFIX + accessToken;
            redisTemplate.opsForValue().set(cacheKey, userId, ACCESS_TOKEN_EXPIRE, TimeUnit.SECONDS);

            log.info("为用户 {} 续期Token", userId);
            return true;
        }

        return false;
    }

    @Override
    public User getUserTokenInfo(Long userId) {
        return userMapper.selectUserById(userId);
    }

    /**
     * 生成访问令牌
     */
    private String generateAccessToken() {
        return "AT_" + UUID.randomUUID().toString().replace("-", "");
    }

    /**
     * 生成刷新令牌
     */
    private String generateRefreshToken() {
        return "RT_" + UUID.randomUUID().toString().replace("-", "");
    }

    /**
     * 保存Token到Redis
     */
    private void saveTokenToRedis(Long userId, String accessToken, String refreshToken, 
                                 LocalDateTime accessTokenExpire, LocalDateTime refreshTokenExpire) {
        LocalDateTime now = LocalDateTime.now();
        
        // 保存访问令牌
        long accessTtl = java.time.Duration.between(now, accessTokenExpire).getSeconds();
        if (accessTtl > 0) {
            redisTemplate.opsForValue().set(TOKEN_PREFIX + accessToken, userId, accessTtl, TimeUnit.SECONDS);
            redisTemplate.opsForValue().set(USER_TOKEN_PREFIX + userId, accessToken, accessTtl, TimeUnit.SECONDS);
        }

        // 保存刷新令牌
        long refreshTtl = java.time.Duration.between(now, refreshTokenExpire).getSeconds();
        if (refreshTtl > 0) {
            redisTemplate.opsForValue().set(REFRESH_TOKEN_PREFIX + refreshToken, userId, refreshTtl, TimeUnit.SECONDS);
        }
    }
}

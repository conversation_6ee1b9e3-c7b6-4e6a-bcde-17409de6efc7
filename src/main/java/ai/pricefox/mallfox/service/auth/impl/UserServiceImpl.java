package ai.pricefox.mallfox.service.auth.impl;

import ai.pricefox.mallfox.common.exception.enums.ErrorCodeConstants;
import ai.pricefox.mallfox.domain.auth.User;
import ai.pricefox.mallfox.mapper.auth.UserMapper;
import ai.pricefox.mallfox.service.auth.PasswordService;
import ai.pricefox.mallfox.service.auth.UserService;
import ai.pricefox.mallfox.vo.base.CommonResult;
import ai.pricefox.mallfox.vo.base.PageResult;
import ai.pricefox.mallfox.vo.user.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.AllArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import static ai.pricefox.mallfox.common.exception.util.ServiceExceptionUtil.exception;

/**
* <AUTHOR>
* @description 针对表【user(用户表)】的数据库操作Service实现
* @createDate 2025-05-18 12:14:02
*/
@Service
@AllArgsConstructor
public class UserServiceImpl implements UserService {

    private final UserMapper userMapper;
    private final PasswordService passwordService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<UserRespVO> createUser(UserCreateReqVO reqVO) {
        // 检查用户名是否已存在
        User existingUser = userMapper.selectUserByUsername(reqVO.getUsername());
        if (existingUser != null) {
            throw exception(ErrorCodeConstants.USER_NOT_EXIST, "用户名已存在");
        }

        // 检查邮箱是否已存在
        if (reqVO.getEmail() != null) {
            existingUser = userMapper.selectUserByEmail(reqVO.getEmail());
            if (existingUser != null) {
                throw exception(ErrorCodeConstants.USER_NOT_EXIST, "邮箱已存在");
            }
        }

        // 检查手机号是否已存在
        if (reqVO.getPhone() != null) {
            existingUser = userMapper.selectUserByPhone(reqVO.getPhone());
            if (existingUser != null) {
                throw exception(ErrorCodeConstants.USER_NOT_EXIST, "手机号已存在");
            }
        }

        User user = new User();
        BeanUtils.copyProperties(reqVO, user);
        // 密码加密
        if (StringUtils.hasText(reqVO.getPassword())) {
            user.setPassword(passwordService.encryptPassword(reqVO.getPassword()));
        }
        // 使用 Mapper 层的插入方法
        userMapper.insertUser(user);

        UserRespVO respVO = new UserRespVO();
        BeanUtils.copyProperties(user, respVO);
        return CommonResult.success(respVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<UserRespVO> updateUser(UserUpdateReqVO reqVO) {
        // 检查用户是否存在
        User existingUser = userMapper.selectUserById(reqVO.getId());
        if (existingUser == null) {
            throw exception(ErrorCodeConstants.USER_NOT_EXIST);
        }

        // 检查用户名是否被其他用户使用
        User userByUsername = userMapper.selectUserByUsername(reqVO.getUsername());
        if (userByUsername != null && !userByUsername.getId().equals(reqVO.getId())) {
            throw exception(ErrorCodeConstants.USER_NOT_EXIST, "用户名已被其他用户使用");
        }

        // 检查邮箱是否被其他用户使用
        if (reqVO.getEmail() != null) {
            User userByEmail = userMapper.selectUserByEmail(reqVO.getEmail());
            if (userByEmail != null && !userByEmail.getId().equals(reqVO.getId())) {
                throw exception(ErrorCodeConstants.USER_NOT_EXIST, "邮箱已被其他用户使用");
            }
        }

        // 检查手机号是否被其他用户使用
        if (reqVO.getPhone() != null) {
            User userByPhone = userMapper.selectUserByPhone(reqVO.getPhone());
            if (userByPhone != null && !userByPhone.getId().equals(reqVO.getId())) {
                throw exception(ErrorCodeConstants.USER_NOT_EXIST, "手机号已被其他用户使用");
            }
        }

        User user = new User();
        BeanUtils.copyProperties(reqVO, user);
        // 密码加密（如果有密码更新）
        if (StringUtils.hasText(reqVO.getPassword())) {
            user.setPassword(passwordService.encryptPassword(reqVO.getPassword()));
        } else {
            // 如果密码为空，保持原密码不变
            user.setPassword(null);
        }
        // 使用 Mapper 层的更新方法
        userMapper.updateUserById(user);

        UserRespVO respVO = new UserRespVO();
        BeanUtils.copyProperties(user, respVO);
        return CommonResult.success(respVO);
    }

    @Override
    public CommonResult<UserRespVO> getUserById(Long id) {
        // 使用 Mapper 层的查询方法
        User user = userMapper.selectUserById(id);
        if (user == null) {
            throw exception(ErrorCodeConstants.USER_NOT_EXIST);
        }

        UserRespVO respVO = new UserRespVO();
        BeanUtils.copyProperties(user, respVO);
        return CommonResult.success(respVO);
    }

    @Override
    public CommonResult<PageResult<UserRespVO>> getUserPage(UserPageReqVO reqVO) {
        // 使用 Mapper 层的分页查询方法
        Page<User> userPage = userMapper.selectUserPage(reqVO);

        // 转换为响应VO
        PageResult<UserRespVO> pageResult = new PageResult<>();
        pageResult.setTotal(userPage.getTotal());
        pageResult.setList(userPage.getRecords().stream().map(user -> {
            UserRespVO respVO = new UserRespVO();
            BeanUtils.copyProperties(user, respVO);
            return respVO;
        }).toList());

        return CommonResult.success(pageResult);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<Boolean> deleteUser(Long id) {
        // 检查用户是否存在
        User user = userMapper.selectUserById(id);
        if (user == null) {
            throw exception(ErrorCodeConstants.USER_NOT_EXIST);
        }

        // 使用 Mapper 层的删除方法
        boolean deleted = userMapper.deleteUserById(id);
        return CommonResult.success(deleted);
    }

    @Override
    public CommonResult<UserRespVO> getUserByUsername(String username) {
        // 使用 Mapper 层的查询方法
        User user = userMapper.selectUserByUsername(username);
        if (user == null) {
            throw exception(ErrorCodeConstants.USER_NOT_EXIST);
        }

        UserRespVO respVO = new UserRespVO();
        BeanUtils.copyProperties(user, respVO);
        return CommonResult.success(respVO);
    }

}





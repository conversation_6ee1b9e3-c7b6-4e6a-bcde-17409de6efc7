package ai.pricefox.mallfox.service.auth.impl;

import ai.pricefox.mallfox.service.integration.EmailService;
import ai.pricefox.mallfox.service.auth.VerifyCodeService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.concurrent.TimeUnit;
import ai.pricefox.mallfox.common.constant.RedisKeyConstants;

/**
 * 验证码服务实现
 */
@Slf4j
@Service
@AllArgsConstructor
public class VerifyCodeServiceImpl implements VerifyCodeService {

    private final EmailService emailService;
    private final RedisTemplate<String, Object> redisTemplate;

    // Redis Key 前缀 (使用统一常量)
    private static final String EMAIL_CODE_PREFIX = RedisKeyConstants.EMAIL_CODE;
    private static final String SMS_CODE_PREFIX = RedisKeyConstants.SMS_CODE;
    private static final String SEND_FREQUENCY_PREFIX = RedisKeyConstants.SEND_FREQUENCY;

    // 验证码有效期（秒）(使用统一常量)
    private static final long CODE_EXPIRE_TIME = RedisKeyConstants.VERIFY_CODE_TIMEOUT;

    // 发送频率限制（秒）(使用统一常量)
    private static final long SEND_FREQUENCY_LIMIT = RedisKeyConstants.SEND_FREQUENCY_TIMEOUT;

    @Override
    public boolean sendEmailCode(String email, String businessType) {
        if (!emailService.isValidEmail(email)) {
            log.error("邮箱格式无效: {}", email);
            return false;
        }

        // 检查发送频率
        if (!checkSendFrequency(email, "EMAIL")) {
            log.warn("邮箱验证码发送过于频繁: {}", email);
            return false;
        }

        try {
            // 生成验证码
            String code = emailService.generateVerifyCode();
            
            // 发送邮件
            boolean sent = emailService.sendVerifyCode(email, code, businessType);
            if (sent) {
                // 保存验证码到Redis
                String key = EMAIL_CODE_PREFIX + businessType + ":" + email;
                redisTemplate.opsForValue().set(key, code, CODE_EXPIRE_TIME, TimeUnit.SECONDS);
                
                // 记录发送时间
                String freqKey = SEND_FREQUENCY_PREFIX + "EMAIL:" + email;
                redisTemplate.opsForValue().set(freqKey, System.currentTimeMillis(), 
                    SEND_FREQUENCY_LIMIT, TimeUnit.SECONDS);
                
                log.info("邮箱验证码发送成功: email={}, businessType={}", email, businessType);
                return true;
            }
        } catch (Exception e) {
            log.error("发送邮箱验证码失败: email={}, businessType={}", email, businessType, e);
        }
        
        return false;
    }

    @Override
    public boolean sendSmsCode(String phone, String businessType) {
        // TODO: 实现短信发送功能
        log.info("短信验证码发送（暂未实现）: phone={}, businessType={}", phone, businessType);
        
        // 临时实现：生成验证码并保存到Redis
        try {
            String code = emailService.generateVerifyCode();
            String key = SMS_CODE_PREFIX + businessType + ":" + phone;
            redisTemplate.opsForValue().set(key, code, CODE_EXPIRE_TIME, TimeUnit.SECONDS);
            
            log.info("短信验证码已生成（测试模式）: phone={}, code={}, businessType={}", phone, code, businessType);
            return true;
        } catch (Exception e) {
            log.error("生成短信验证码失败: phone={}, businessType={}", phone, businessType, e);
            return false;
        }
    }

    @Override
    public boolean verifyEmailCode(String email, String code, String businessType) {
        if (!StringUtils.hasText(email) || !StringUtils.hasText(code)) {
            return false;
        }

        try {
            String key = EMAIL_CODE_PREFIX + businessType + ":" + email;
            Object storedCode = redisTemplate.opsForValue().get(key);
            
            if (storedCode != null && code.equals(storedCode.toString())) {
                // 验证成功，删除验证码
                redisTemplate.delete(key);
                log.info("邮箱验证码验证成功: email={}, businessType={}", email, businessType);
                return true;
            } else {
                log.warn("邮箱验证码验证失败: email={}, code={}, businessType={}", email, code, businessType);
                return false;
            }
        } catch (Exception e) {
            log.error("邮箱验证码验证异常: email={}, code={}, businessType={}", email, code, businessType, e);
            return false;
        }
    }

    @Override
    public boolean verifySmsCode(String phone, String code, String businessType) {
        if (!StringUtils.hasText(phone) || !StringUtils.hasText(code)) {
            return false;
        }

        try {
            String key = SMS_CODE_PREFIX + businessType + ":" + phone;
            Object storedCode = redisTemplate.opsForValue().get(key);
            
            if (storedCode != null && code.equals(storedCode.toString())) {
                // 验证成功，删除验证码
                redisTemplate.delete(key);
                log.info("短信验证码验证成功: phone={}, businessType={}", phone, businessType);
                return true;
            } else {
                log.warn("短信验证码验证失败: phone={}, code={}, businessType={}", phone, code, businessType);
                return false;
            }
        } catch (Exception e) {
            log.error("短信验证码验证异常: phone={}, code={}, businessType={}", phone, code, businessType, e);
            return false;
        }
    }

    @Override
    public boolean checkSendFrequency(String target, String type) {
        try {
            String freqKey = SEND_FREQUENCY_PREFIX + type + ":" + target;
            Object lastSendTime = redisTemplate.opsForValue().get(freqKey);
            
            if (lastSendTime == null) {
                return true; // 没有发送记录，可以发送
            }
            
            long lastTime = Long.parseLong(lastSendTime.toString());
            long currentTime = System.currentTimeMillis();
            long timeDiff = (currentTime - lastTime) / 1000; // 转换为秒
            
            return timeDiff >= SEND_FREQUENCY_LIMIT;
        } catch (Exception e) {
            log.error("检查发送频率失败: target={}, type={}", target, type, e);
            return true; // 异常情况下允许发送
        }
    }

    @Override
    public void clearCode(String target, String type, String businessType) {
        try {
            String prefix = "EMAIL".equals(type) ? EMAIL_CODE_PREFIX : SMS_CODE_PREFIX;
            String key = prefix + businessType + ":" + target;
            redisTemplate.delete(key);
            log.info("验证码已清除: target={}, type={}, businessType={}", target, type, businessType);
        } catch (Exception e) {
            log.error("清除验证码失败: target={}, type={}, businessType={}", target, type, businessType, e);
        }
    }
}

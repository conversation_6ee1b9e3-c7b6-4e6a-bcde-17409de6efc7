package ai.pricefox.mallfox.service.auth.impl;

import ai.pricefox.mallfox.domain.auth.WishlistItem;
import ai.pricefox.mallfox.mapper.auth.WishlistItemMapper;
import ai.pricefox.mallfox.service.auth.WishlistItemService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 用户商品收藏表 Service实现
 * <AUTHOR>
 * @description 针对表【wishlist_item(用户商品收藏表)】的数据库操作Service实现
 */
@Service
@Slf4j
public class WishlistItemServiceImpl extends ServiceImpl<WishlistItemMapper, WishlistItem>
        implements WishlistItemService {

    @Override
    public Set<String> getFavoritedSkuIds(Long userId, List<String> skuIds) {
        try {
            if (userId == null || skuIds == null || skuIds.isEmpty()) {
                log.debug("用户ID或SKU ID列表为空，返回空集合: userId={}, skuIds={}", userId, skuIds);
                return Set.of();
            }
            
            log.debug("查询用户收藏的SKU: userId={}, skuIds数量={}", userId, skuIds.size());
            List<WishlistItem> wishlistItems = baseMapper.selectByUserIdAndSkuIds(userId, skuIds);
            
            return wishlistItems.stream()
                    .map(WishlistItem::getSkuId)
                    .collect(Collectors.toSet());
        } catch (Exception e) {
            log.error("查询用户收藏SKU失败: userId={}, skuIds={}, error={}", userId, skuIds, e.getMessage(), e);
            // 收藏功能失败不应该影响主要业务，返回空集合
            return Set.of();
        }
    }

    @Override
    public boolean isFavorited(Long userId, String skuId) {
        try {
            if (userId == null || skuId == null) {
                log.debug("用户ID或SKU ID为空，返回未收藏: userId={}, skuId={}", userId, skuId);
                return false;
            }
            
            log.debug("检查用户是否收藏商品: userId={}, skuId={}", userId, skuId);
            WishlistItem wishlistItem = baseMapper.selectByUserIdAndSkuId(userId, skuId);
            return wishlistItem != null;
        } catch (Exception e) {
            log.error("检查用户收藏状态失败: userId={}, skuId={}, error={}", userId, skuId, e.getMessage(), e);
            // 收藏功能失败不应该影响主要业务，返回未收藏
            return false;
        }
    }
}

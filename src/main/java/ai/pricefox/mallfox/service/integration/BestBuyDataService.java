package ai.pricefox.mallfox.service.integration;

import ai.pricefox.mallfox.vo.base.CommonResult;
import ai.pricefox.mallfox.vo.bestbuy.BestBuyProductDetailRespVO;

import java.util.List;

/**
 * BestBuy数据服务接口
 */
public interface BestBuyDataService {

    /**
     * 搜索BestBuy产品
     *
     * @param query 搜索关键字
     * @param limit 返回数量限制
     * @param offset 偏移量
     * @return 搜索结果
     */
    void searchProducts(String query, Integer limit, Integer offset);
}
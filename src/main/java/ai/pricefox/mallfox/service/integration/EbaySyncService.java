package ai.pricefox.mallfox.service.integration;

import ai.pricefox.mallfox.vo.base.CommonResult;

/**
 * eBay 数据同步服务接口
 * 
 * <AUTHOR>
 * @since 2025-01-07
 */
public interface EbaySyncService {

    /**
     * 一键同步爬虫数据
     * 从 product_data_simplify 表中查询 data_channel={@link ai.pricefox.mallfox.enums.DataChannelEnum#CRAWLER} 的所有不重复 model，
     * 然后调用 eBay 搜索 API 进行数据同步
     * 支持断点续传：处理完的model会从缓存中移除，重新启动时从未处理的model继续
     *
     * @return 同步结果统计
     */
    CommonResult<SyncResultVO> oneClickSyncFromApiData();

    /**
     * 清理同步缓存
     * 手动清理待处理model缓存，重置同步状态
     *
     * @return 清理结果
     */
    CommonResult<String> clearSyncCache();

    /**
     * 查看同步缓存状态
     * 查看当前待处理的model数量和列表
     *
     * @return 缓存状态信息
     */
    CommonResult<SyncCacheStatusVO> getSyncCacheStatus();

    /**
     * 从 itemGroupHref URL 中提取 item_group_id
     *
     * @param itemGroupHref 商品组链接
     * @return item_group_id 或 null
     */
    String extractItemGroupIdFromHref(String itemGroupHref);

    /**
     * 清空已同步商品的缓存
     */
    void clearSyncedItemsCache();

    /**
     * 获取已同步商品数量
     *
     * @return 已同步商品数量
     */
    long getSyncedItemsCount();

    /**
     * 检查指定商品是否在缓存中
     *
     * @param itemId 商品ID
     * @return true-在缓存中，false-不在缓存中
     */
    boolean checkItemInCache(String itemId);

    /**
     * 将商品标记为已同步
     *
     * @param itemId 商品ID
     */
    void markItemAsSynced(String itemId);

    /**
     * 同步结果统计 VO
     */
    class SyncResultVO {
        private Integer totalModels;           // 总model数量
        private Integer processedModels;       // 已处理model数量
        private Integer totalItems;            // 总商品数量
        private Integer syncedItems;           // 已同步商品数量
        private Integer itemGroupItems;        // 商品组商品数量
        private Integer failedItems;           // 失败商品数量
        private Integer skippedItems;          // 跳过商品数量（无价格、品牌、model）
        private Integer actualProcessedItems;  // 去重后的实际处理商品数量
        private Integer batchCount;            // 批次处理数量
        private Long totalTimeMs;              // 总耗时（毫秒）
        private String message;                // 结果消息

        // Getters and Setters
        public Integer getTotalModels() { return totalModels; }
        public void setTotalModels(Integer totalModels) { this.totalModels = totalModels; }

        public Integer getProcessedModels() { return processedModels; }
        public void setProcessedModels(Integer processedModels) { this.processedModels = processedModels; }

        public Integer getTotalItems() { return totalItems; }
        public void setTotalItems(Integer totalItems) { this.totalItems = totalItems; }

        public Integer getSyncedItems() { return syncedItems; }
        public void setSyncedItems(Integer syncedItems) { this.syncedItems = syncedItems; }

        public Integer getItemGroupItems() { return itemGroupItems; }
        public void setItemGroupItems(Integer itemGroupItems) { this.itemGroupItems = itemGroupItems; }

        public Integer getFailedItems() { return failedItems; }
        public void setFailedItems(Integer failedItems) { this.failedItems = failedItems; }

        public Integer getSkippedItems() { return skippedItems; }
        public void setSkippedItems(Integer skippedItems) { this.skippedItems = skippedItems; }

        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }

        public Integer getActualProcessedItems() { return actualProcessedItems; }
        public void setActualProcessedItems(Integer actualProcessedItems) { this.actualProcessedItems = actualProcessedItems; }

        public Integer getBatchCount() { return batchCount; }
        public void setBatchCount(Integer batchCount) { this.batchCount = batchCount; }

        public Long getTotalTimeMs() { return totalTimeMs; }
        public void setTotalTimeMs(Long totalTimeMs) { this.totalTimeMs = totalTimeMs; }
    }

    /**
     * 同步缓存状态 VO
     */
    class SyncCacheStatusVO {
        private Integer pendingModelsCount;    // 待处理model数量
        private java.util.List<String> pendingModels;  // 待处理model列表（最多显示前10个）
        private Boolean cacheExists;          // 缓存是否存在
        private String message;               // 状态消息

        // Getters and Setters
        public Integer getPendingModelsCount() { return pendingModelsCount; }
        public void setPendingModelsCount(Integer pendingModelsCount) { this.pendingModelsCount = pendingModelsCount; }

        public java.util.List<String> getPendingModels() { return pendingModels; }
        public void setPendingModels(java.util.List<String> pendingModels) { this.pendingModels = pendingModels; }

        public Boolean getCacheExists() { return cacheExists; }
        public void setCacheExists(Boolean cacheExists) { this.cacheExists = cacheExists; }

        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
    }
}

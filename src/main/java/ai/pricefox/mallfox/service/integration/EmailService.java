package ai.pricefox.mallfox.service.integration;

/**
 * 邮箱服务接口
 */
public interface EmailService {

    /**
     * 发送验证码邮件
     *
     * @param email 邮箱地址
     * @param code 验证码
     * @param businessType 业务类型
     * @return 是否发送成功
     */
    boolean sendVerifyCode(String email, String code, String businessType);

    /**
     * 发送重置密码邮件
     *
     * @param email 邮箱地址
     * @param resetToken 重置令牌
     * @param resetUrl 重置链接
     * @return 是否发送成功
     */
    boolean sendResetPasswordEmail(String email, String resetToken, String resetUrl);

    /**
     * 发送欢迎邮件
     *
     * @param email 邮箱地址
     * @param username 用户名
     * @return 是否发送成功
     */
    boolean sendWelcomeEmail(String email, String username);

    /**
     * 发送通知邮件
     *
     * @param email 邮箱地址
     * @param subject 邮件主题
     * @param content 邮件内容
     * @return 是否发送成功
     */
    boolean sendNotificationEmail(String email, String subject, String content);

    /**
     * 生成验证码
     *
     * @return 6位数字验证码
     */
    String generateVerifyCode();

    /**
     * 生成重置令牌
     *
     * @return 重置令牌
     */
    String generateResetToken();

    /**
     * 验证邮箱格式
     *
     * @param email 邮箱地址
     * @return 是否有效
     */
    boolean isValidEmail(String email);
}

package ai.pricefox.mallfox.service.integration;

import ai.pricefox.mallfox.config.MicrosoftGraphConfig.MicrosoftGraphProperties;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Microsoft Graph 邮件服务
 */
@Slf4j
@Service
public class MicrosoftGraphEmailService {

    private final WebClient microsoftGraphWebClient;
    private final MicrosoftGraphProperties graphProperties;
    private final ObjectMapper objectMapper;

    private String accessToken;
    private long tokenExpiryTime;

    public MicrosoftGraphEmailService(WebClient microsoftGraphWebClient,
                                    MicrosoftGraphProperties graphProperties) {
        this.microsoftGraphWebClient = microsoftGraphWebClient;
        this.graphProperties = graphProperties;
        this.objectMapper = new ObjectMapper();
    }

    /**
     * 获取客户端ID（用于测试接口）
     */
    public String getClientId() {
        return graphProperties.getClientId();
    }

    /**
     * 获取访问令牌
     */
    private String getAccessToken() {
        try {
            // 检查令牌是否过期
            if (accessToken != null && System.currentTimeMillis() < tokenExpiryTime) {
                return accessToken;
            }

            // 获取新的访问令牌
            Map<String, String> tokenRequest = new HashMap<>();
            tokenRequest.put("client_id", graphProperties.getClientId());
            tokenRequest.put("client_secret", graphProperties.getClientSecret());
            tokenRequest.put("scope", "https://graph.microsoft.com/.default");
            tokenRequest.put("grant_type", "client_credentials");

            log.info("正在获取 Microsoft Graph 访问令牌...");
            String response = WebClient.create()
                    .post()
                    .uri("https://login.microsoftonline.com/" + graphProperties.getTenantId() + "/oauth2/v2.0/token")
                    .header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_FORM_URLENCODED_VALUE)
                    .bodyValue(buildFormData(tokenRequest))
                    .retrieve()
                    .bodyToMono(String.class)
                    .block();

            Map<String, Object> tokenResponse = objectMapper.readValue(response, Map.class);

            if (tokenResponse.containsKey("error")) {
                log.error("获取访问令牌失败: {}", tokenResponse.get("error_description"));
                return null;
            }

            accessToken = (String) tokenResponse.get("access_token");
            int expiresIn = (Integer) tokenResponse.get("expires_in");
            tokenExpiryTime = System.currentTimeMillis() + (expiresIn - 60) * 1000L; // 提前60秒过期

            log.info("Microsoft Graph 访问令牌获取成功");
            return accessToken;
        } catch (Exception e) {
            log.error("获取访问令牌失败", e);
            return null;
        }
    }

    /**
     * 构建表单数据
     */
    private String buildFormData(Map<String, String> data) {
        return data.entrySet().stream()
                .map(entry -> entry.getKey() + "=" + entry.getValue())
                .reduce((a, b) -> a + "&" + b)
                .orElse("");
    }

    /**
     * 发送HTML邮件
     */
    public boolean sendHtmlEmail(String to, String subject, String htmlContent) {
        return sendEmail(to, subject, htmlContent, "HTML");
    }

    /**
     * 发送文本邮件
     */
    public boolean sendTextEmail(String to, String subject, String textContent) {
        return sendEmail(to, subject, textContent, "Text");
    }

    /**
     * 发送邮件的通用方法
     */
    private boolean sendEmail(String to, String subject, String content, String contentType) {
        try {
            String token = getAccessToken();
            if (token == null) {
                log.error("无法获取访问令牌，邮件发送失败");
                return false;
            }

            // 构建邮件消息
            Map<String, Object> message = new HashMap<>();
            message.put("subject", subject);

            Map<String, Object> body = new HashMap<>();
            body.put("contentType", contentType);
            body.put("content", content);
            message.put("body", body);

            Map<String, Object> toRecipient = new HashMap<>();
            Map<String, Object> emailAddress = new HashMap<>();
            emailAddress.put("address", to);
            toRecipient.put("emailAddress", emailAddress);
            message.put("toRecipients", List.of(toRecipient));

            Map<String, Object> sendMailRequest = new HashMap<>();
            sendMailRequest.put("message", message);
            sendMailRequest.put("saveToSentItems", true);

            log.info("正在通过 Microsoft Graph API 发送邮件: to={}, subject={}", to, subject);

            // 发送邮件
            String response = microsoftGraphWebClient
                    .post()
                    .uri("/users/" + graphProperties.getSenderEmail() + "/sendMail")
                    .header(HttpHeaders.AUTHORIZATION, "Bearer " + token)
                    .header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                    .bodyValue(sendMailRequest)
                    .retrieve()
                    .onStatus(status -> status.is4xxClientError() || status.is5xxServerError(),
                            clientResponse -> clientResponse.bodyToMono(String.class)
                                    .map(errorBody -> {
                                        log.error("Microsoft Graph API 错误响应: status={}, body={}",
                                                clientResponse.statusCode(), errorBody);
                                        return new RuntimeException("Microsoft Graph API 错误: " +
                                                clientResponse.statusCode() + " - " + errorBody);
                                    }))
                    .bodyToMono(String.class)
                    .block();

            log.info("Microsoft Graph 邮件发送成功: to={}, subject={}", to, subject);
            return true;

        } catch (Exception e) {
            log.error("Microsoft Graph 邮件发送失败: to={}, subject={}", to, subject, e);

            // 如果是权限问题，提供更详细的错误信息
            if (e.getMessage() != null && e.getMessage().contains("403")) {
                log.error("权限错误：请检查 Azure 应用注册是否具有以下权限：");
                log.error("- Mail.Send (应用程序权限)");
                log.error("- User.Read.All (应用程序权限)");
                log.error("并确保管理员已授予同意");
            }

            return false;
        }
    }

    /**
     * 发送给多个收件人
     */
    public boolean sendBatchEmail(List<String> toList, String subject, String htmlContent) {
        try {
            String token = getAccessToken();
            if (token == null) {
                log.error("无法获取访问令牌");
                return false;
            }

            // 构建邮件消息
            Map<String, Object> message = new HashMap<>();
            message.put("subject", subject);

            Map<String, Object> body = new HashMap<>();
            body.put("contentType", "HTML");
            body.put("content", htmlContent);
            message.put("body", body);

            // 设置多个收件人
            List<Map<String, Object>> toRecipients = toList.stream()
                    .map(email -> {
                        Map<String, Object> recipient = new HashMap<>();
                        Map<String, Object> emailAddress = new HashMap<>();
                        emailAddress.put("address", email);
                        recipient.put("emailAddress", emailAddress);
                        return recipient;
                    })
                    .toList();
            message.put("toRecipients", toRecipients);

            Map<String, Object> sendMailRequest = new HashMap<>();
            sendMailRequest.put("message", message);
            sendMailRequest.put("saveToSentItems", true);

            // 发送邮件
            String response = microsoftGraphWebClient
                    .post()
                    .uri("/users/" + graphProperties.getSenderEmail() + "/sendMail")
                    .header(HttpHeaders.AUTHORIZATION, "Bearer " + token)
                    .header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                    .bodyValue(sendMailRequest)
                    .retrieve()
                    .bodyToMono(String.class)
                    .block();

            log.info("Microsoft Graph 批量邮件发送成功: toList={}, subject={}", toList, subject);
            return true;

        } catch (Exception e) {
            log.error("Microsoft Graph 批量邮件发送失败: toList={}, subject={}", toList, subject, e);
            return false;
        }
    }
}

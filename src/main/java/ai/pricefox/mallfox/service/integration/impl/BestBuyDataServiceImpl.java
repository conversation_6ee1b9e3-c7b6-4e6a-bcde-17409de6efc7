package ai.pricefox.mallfox.service.integration.impl;

import ai.pricefox.mallfox.common.constant.RedisKeyConstants;
import ai.pricefox.mallfox.common.util.CacheUtil;
import ai.pricefox.mallfox.convert.bestbuy.BestBuyConvert;
import ai.pricefox.mallfox.event.StandardDataEvent;
import ai.pricefox.mallfox.job.bestbuy.BestBuyApiService;
import ai.pricefox.mallfox.job.bestbuy.BestBuyProductDetailResponse;
import ai.pricefox.mallfox.job.bestbuy.BestBuyProductSearchResponse;
import ai.pricefox.mallfox.service.integration.BestBuyDataService;
import ai.pricefox.mallfox.vo.base.CommonResult;
import ai.pricefox.mallfox.vo.bestbuy.BestBuyProductDetailRespVO;
import com.google.gson.Gson;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeUnit;

/**
 * BestBuy数据服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BestBuyDataServiceImpl implements BestBuyDataService {

    private final BestBuyApiService bestBuyApiService;
    private final CacheUtil cacheUtil;
    private final ApplicationEventPublisher eventPublisher;

    /**
     * 搜索BestBuy产品，支持增量获取数据
     *
     * @param query  搜索关键字
     * @param limit  返回数量限制
     * @param offset 偏移量
     * @return 搜索结果
     */
    @Override
    public void searchProducts(String query, Integer limit, Integer offset) {
        String progressKey = RedisKeyConstants.BESTBUY_PROGRESS_KEY;
        if (query != null) {
            progressKey = progressKey + query.hashCode();
        }

        LocalDate today = LocalDate.now();

        // 获取缓存进度
        BestBuySearchProgress progress = cacheUtil.getCacheObject(progressKey);
        if (progress == null) {
            progress = new BestBuySearchProgress();
            progress.setOffset(0);
            progress.setRequestCount(0);
            progress.setLastRequestDate(today);
        }

        // 重置每日请求计数
        if (!today.equals(progress.getLastRequestDate())) {
            progress.setRequestCount(0);
            progress.setLastRequestDate(today);
        }

        // 检查请求次数限制 (BestBuy API限制)
        // 根据BestBuy API文档，通常有每分钟/每小时请求限制
        if (progress.getRequestCount() >= 50000) { // 假设每日限制5000次
            log.warn("已达每日请求上限，暂停同步");
            return;
        }

        // 如果传入了offset参数，则使用传入的值，否则使用缓存中的值
        int actualOffset = (offset != null) ? offset : progress.getOffset();
        int totalFetched = 0;
        int maxLimit = (limit != null) ? limit : 100; // 默认每次最多获取100条数据

        try {
            log.info("搜索 BestBuy 产品，query: {}, limit: {}, offset: {}", query, limit, actualOffset);

            // 检查服务是否可用
            if (!bestBuyApiService.isServiceAvailable()) {
                return;
            }

            BestBuyProductSearchResponse searchResponse;
            do {
                searchResponse = bestBuyApiService.searchProducts(query, Math.min(maxLimit - totalFetched, 100), actualOffset);

                int currentFetched = searchResponse.getProducts().size();
                totalFetched += currentFetched;

                // 更新进度
                actualOffset += currentFetched;
                progress.setOffset(actualOffset);
                progress.setRequestCount(progress.getRequestCount() + 1);
                cacheUtil.setCacheObject(progressKey, progress, 24L * 60 * 60, TimeUnit.SECONDS); // 缓存24小时
                log.info("成功搜索 BestBuy 产品，query: {}, offset: {}, requestCount: {}, 当前获取: {}, 总计获取: {}", query, progress.getOffset(), progress.getRequestCount(), currentFetched, totalFetched);
                // 发布事件
                Gson gson = new Gson();
                // 批量处理事件，避免一次性发布过多事件
                final int batchSize = 20;
                List<BestBuyProductDetailResponse> products = searchResponse.getProducts();
                products.forEach(item -> {
                    // 添加小延迟，避免事件处理过载
                    try {
                        Thread.sleep(500);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                    }
                    eventPublisher.publishEvent(new StandardDataEvent(this, gson.toJson(item)));
                });

                // 如果当前获取的数据少于100条，说明已经到最后一页
                if (currentFetched < 100) {
                    break;
                }

                // 检查是否达到请求上限
                if (progress.getRequestCount() >= 50000) {
                    log.warn("已达每日请求上限，暂停同步");
                    break;
                }

                // 检查是否已完成所需数量的获取
                if (totalFetched >= maxLimit) {
                    break;
                }

            } while (true);

        } catch (Exception e) {
            log.error("搜索 BestBuy 产品失败，query: {}", query, e);
        }
    }

    /**
     * BestBuy搜索进度数据类
     */
    public static class BestBuySearchProgress implements java.io.Serializable {
        private static final long serialVersionUID = 1L;

        private int offset = 0;
        private int requestCount = 0;
        private LocalDate lastRequestDate;

        public int getOffset() {
            return offset;
        }

        public void setOffset(int offset) {
            this.offset = offset;
        }

        public int getRequestCount() {
            return requestCount;
        }

        public void setRequestCount(int requestCount) {
            this.requestCount = requestCount;
        }

        public LocalDate getLastRequestDate() {
            return lastRequestDate;
        }

        public void setLastRequestDate(LocalDate lastRequestDate) {
            this.lastRequestDate = lastRequestDate;
        }
    }
}
package ai.pricefox.mallfox.service.integration.impl;


import ai.pricefox.mallfox.domain.integration.DataCalibrationTags;
import ai.pricefox.mallfox.enums.ProductDataMarkEnum;
import ai.pricefox.mallfox.mapper.integration.DataCalibrationTagsMapper;
import ai.pricefox.mallfox.model.param.CalibrationTagRequest;
import ai.pricefox.mallfox.utils.AdminTokenUtil;
import ai.pricefox.mallfox.utils.TokenUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @desc 数据标记服务
 * @since 2025/6/25
 */
@Service
public class CalibrationService {

    @Autowired
    private DataCalibrationTagsMapper dataCalibrationTagsMapper;


    public List<DataCalibrationTags> getTagsByOfferId(String tableName, List<Long> cacheMissOfferIds) {
        LambdaQueryWrapper<DataCalibrationTags> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DataCalibrationTags::getTargetTable, tableName)
                .in(DataCalibrationTags::getTargetId, cacheMissOfferIds)
                .in(DataCalibrationTags::getTagStatus, ProductDataMarkEnum.getErrorMarkCode());
        return dataCalibrationTagsMapper.selectList(queryWrapper);
    }

    @Transactional
    public void batchSaveOrUpdateTags(List<CalibrationTagRequest> dtoList) {
        List<DataCalibrationTags> tagsList = dtoList.stream()
                .map(dto -> {
                    DataCalibrationTags tag = new DataCalibrationTags();
                    BeanUtils.copyProperties(dto, tag);
                    tag.setOperatorId(AdminTokenUtil.getCurrentUserId());
                    return tag;
                }).collect(Collectors.toList());
        dataCalibrationTagsMapper.batchInsertOrUpdate(tagsList);
    }

}

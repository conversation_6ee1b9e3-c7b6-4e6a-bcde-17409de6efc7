package ai.pricefox.mallfox.service.integration.impl;

import ai.pricefox.mallfox.common.constant.RedisKeyConstants;
import ai.pricefox.mallfox.common.exception.ErrorCode;
import ai.pricefox.mallfox.common.exception.enums.ErrorCodeConstants;
import ai.pricefox.mallfox.common.exception.util.ServiceExceptionUtil;
import ai.pricefox.mallfox.common.util.CacheUtil;
import ai.pricefox.mallfox.convert.ebay.EbayConvert;
import ai.pricefox.mallfox.convert.ebay.EbayToProductDataConverter;
import ai.pricefox.mallfox.enums.DataChannelEnum;
import ai.pricefox.mallfox.enums.ProductPlatformEnum;
import ai.pricefox.mallfox.event.StandardDataEvent;
import ai.pricefox.mallfox.job.ebay.EbayApiService;
import ai.pricefox.mallfox.job.ebay.EbaySearchResponse;
import ai.pricefox.mallfox.model.dto.EbaySearchProgress;
import ai.pricefox.mallfox.model.dto.ProductDataDTO;
import ai.pricefox.mallfox.service.integration.EbayDataService;
import ai.pricefox.mallfox.service.integration.EbaySyncService;
import ai.pricefox.mallfox.service.product.UnifiedProductDataService;
import ai.pricefox.mallfox.vo.base.CommonResult;
import ai.pricefox.mallfox.vo.ebay.EbayItemDetailRespVO;
import ai.pricefox.mallfox.vo.ebay.EbaySearchReqVO;
import ai.pricefox.mallfox.vo.ebay.EbaySearchRespVO;
import com.google.gson.Gson;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * eBay数据服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class EbayDataServiceImpl implements EbayDataService {

    private final EbayApiService ebayApiService;
    private final CacheUtil cacheUtil;
    private final EbayToProductDataConverter ebayToProductDataConverter;
    private final UnifiedProductDataService unifiedProductDataService;
    private final EbayConvert ebayConvert;
    private final EbaySyncService ebaySyncService;
    private final ApplicationEventPublisher eventPublisher;

    /**
     * 高级搜索eBay商品并同步数据
     * 支持增量获取数据，已获取过的数据不会重复获取
     *
     * @param searchRequest 搜索请求参数
     * @return 搜索结果
     */
    @Override
    public void searchSyncItemsAdvanced(EbaySearchReqVO searchRequest) {
        String progressKey = RedisKeyConstants.EBAY_PROGRESS_KEY + searchRequest.hashCode();
        LocalDate today = LocalDate.now();

        // 获取缓存进度
        EbaySearchProgress progress = cacheUtil.getCacheObject(progressKey);
        if (progress == null) {
            progress = new EbaySearchProgress();
        }

        // 重置每日请求计数
        if (!today.equals(progress.getLastRequestDate())) {
            progress.setRequestCount(0);
            progress.setLastRequestDate(today);
        }

        // 检查请求次数限制
        if (progress.getRequestCount() >= 5000) {
            log.warn("已达每日请求上限，暂停同步");
            throw ServiceExceptionUtil.exception(new ErrorCode(429, "已达每日请求上限，明天继续"));
        }

        if(progress.getOffset() >= 10000){
            return;
        }

        // 设置 offset
        searchRequest.setOffset(progress.getOffset());

        try {
            while (true) {
                log.info("开始高级搜索 eBay 商品，关键字: {}, offset: {}", searchRequest.getQuery(), progress.getOffset());

                EbaySearchResponse searchResponse = ebayApiService.searchItemsAdvanced(searchRequest);
                EbaySearchRespVO respVO = ebayConvert.convertToSearchRespVO(searchResponse);

                log.info("高级搜索完成，关键字:{}, 返回 {} 条结果", searchRequest.getQuery(), respVO.getItemSummaries() != null ? respVO.getItemSummaries().size() : 0);

                // 同步数据
                if (Boolean.TRUE.equals(searchRequest.getSyncData()) && searchResponse != null && searchResponse.getItemSummaries() != null && !searchResponse.getItemSummaries().isEmpty()) {
                    syncSearchResultsData(searchResponse.getItemSummaries());
                }

                // 更新进度
                progress.setOffset(searchRequest.getOffset() + searchRequest.getLimit());
                progress.setRequestCount(progress.getRequestCount() + 1);
                cacheUtil.setCacheObject(progressKey, progress, 24L * 60 * 60, TimeUnit.SECONDS); // 缓存24小时

                // 如果返回数据不足 limit 条，说明拉取完成
                if (searchResponse != null && searchResponse.getItemSummaries().size() < searchRequest.getLimit()) {
                    log.warn("已查询完数据，保存进度并退出");
                    return;
                }

                // 如果达到每日请求上限，提前退出
                if (progress.getRequestCount() >= 5000) {
                    log.warn("已达每日请求上限，保存进度并退出");
                    return;
                }
                searchRequest.setOffset(progress.getOffset());
                // 发布事件
                Gson gson = new Gson();
                if (searchResponse != null) {
                    searchResponse.getItemSummaries().forEach(item->{
                        item.setSourcePlatform(ProductPlatformEnum.EBAY);
                        item.setDataChannel(DataChannelEnum.API);
                        item.setSelfOperated(false);
                        eventPublisher.publishEvent(new StandardDataEvent(this, gson.toJson(item)));
                        // 添加小延迟，避免事件处理过载
                        try {
                            Thread.sleep(500);
                        } catch (InterruptedException e) {
                            Thread.currentThread().interrupt();
                        }
                    });
                }
            }
        } catch (Exception e) {
            log.error("高级搜索 eBay 商品失败，关键字: {}", searchRequest.getQuery(), e);
            throw ServiceExceptionUtil.exception(new ErrorCode(500, "高级搜索商品失败: " + e.getMessage()));
        }
    }

    /**
     * 同步搜索结果数据
     * 对搜索到的商品进行详情查询和数据同步，使用缓存避免重复同步
     *
     * @param itemSummaries 搜索结果商品列表
     */
    private void syncSearchResultsData(List<EbaySearchResponse.ItemSummary> itemSummaries) {
        if (itemSummaries == null || itemSummaries.isEmpty()) {
            return;
        }

        int totalItems = itemSummaries.size();
        int syncedItems = 0;
        int skippedItems = 0;
        int failedItems = 0;

        log.info("🔄 开始同步搜索结果数据，总商品数: {}", totalItems);
        List<ProductDataDTO> dtoList = new ArrayList<>();
        for (EbaySearchResponse.ItemSummary itemSummary : itemSummaries) {
            try {
                String itemId = itemSummary.getItemId();

                // 检查缓存中是否已经同步过该商品
                if (ebaySyncService.checkItemInCache(itemId)) {
                    log.debug("⏭️ 商品已同步过，跳过处理，itemId: {}", itemId);
                    skippedItems++;
                    continue;
                }

                // 获取商品详情
                log.debug("📡 获取商品详情，itemId: {}", itemId);
                EbayItemDetailRespVO itemDetailResponse = ebayApiService.getItemDetail(itemId);

                if (itemDetailResponse == null) {
                    log.warn("⚠️ 获取商品详情失败，itemId: {}", itemId);
                    failedItems++;
                    continue;
                }

                // 转换为VO
                ProductDataDTO productDataDTO = ebayToProductDataConverter.convertToProductDataDTO(itemDetailResponse);
                dtoList.add(productDataDTO);

                // 标记商品为已同步
                ebaySyncService.markItemAsSynced(itemId);

                syncedItems++;
                log.debug("✅ 商品同步完成，itemId: {}", itemId);

            } catch (Exception e) {
                log.error("❌ 同步商品数据失败，itemId: {}", itemSummary.getItemId(), e);
                failedItems++;
            }
        }

        unifiedProductDataService.batchProcessProductData(dtoList);

        log.info("🎉 搜索结果数据同步完成，总数: {}, 已同步: {}, 跳过: {}, 失败: {}", totalItems, syncedItems, skippedItems, failedItems);
    }
}
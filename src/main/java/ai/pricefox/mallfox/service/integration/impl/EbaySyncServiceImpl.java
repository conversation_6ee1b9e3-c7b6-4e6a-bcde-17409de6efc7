package ai.pricefox.mallfox.service.integration.impl;

import ai.pricefox.mallfox.convert.ebay.EbayConvert;
import ai.pricefox.mallfox.convert.ebay.EbayToProductDataConverter;
import ai.pricefox.mallfox.domain.product.ProductDataOffers;
import ai.pricefox.mallfox.enums.DataChannelEnum;
import ai.pricefox.mallfox.job.ebay.EbayApiService;
import ai.pricefox.mallfox.job.ebay.EbayItemGroupResponse;
import ai.pricefox.mallfox.job.ebay.EbaySearchResponse;
import ai.pricefox.mallfox.model.dto.ProductDataDTO;
import ai.pricefox.mallfox.service.integration.EbaySyncService;
import ai.pricefox.mallfox.service.product.ProductDataSimplifyService;
import ai.pricefox.mallfox.service.product.UnifiedProductDataService;
import ai.pricefox.mallfox.vo.ebay.EbayItemDetailRespVO;
import ai.pricefox.mallfox.vo.ebay.EbaySearchReqVO;
import ai.pricefox.mallfox.vo.ebay.EbaySearchRespVO;
import ai.pricefox.mallfox.vo.base.CommonResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.Duration;
import java.util.*;
import java.util.concurrent.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import ai.pricefox.mallfox.common.constant.RedisKeyConstants;

import static ai.pricefox.mallfox.common.constant.RedisKeyConstants.*;

/**
 * eBay 数据同步服务实现
 * 
 * <AUTHOR>
 * @since 2025-01-07
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class EbaySyncServiceImpl implements EbaySyncService {

    private final ProductDataSimplifyService productDataSimplifyService;
    private final EbayApiService ebayApiService;
    private final EbayToProductDataConverter ebayToProductDataConverter;
    private final UnifiedProductDataService unifiedProductDataService;
    private final EbayConvert ebayConvert;
    private final RedisTemplate<String, Object> redisTemplate;

    private static final Pattern ITEM_GROUP_ID_PATTERN =
        Pattern.compile("item_group_id=([^&]+)");

    // 批处理配置
    private static final int BATCH_SIZE = 50; // 每批处理50个商品
    private static final int MAX_PAGES_PER_MODEL = 10000; // 每个model最多处理10000页
    private static final int THREAD_POOL_SIZE = 5; // 线程池大小

    // 线程池（懒加载）
    private volatile ExecutorService executorService;

    @Override
    public CommonResult<SyncResultVO> oneClickSyncFromApiData() {
        log.info("开始执行一键同步爬虫数据");
        long startTime = System.currentTimeMillis();

        SyncResultVO result = new SyncResultVO();
        result.setTotalModels(0);
        result.setProcessedModels(0);
        result.setTotalItems(0);
        result.setSyncedItems(0);
        result.setItemGroupItems(0);
        result.setFailedItems(0);
        result.setSkippedItems(0);
        result.setActualProcessedItems(0);
        result.setBatchCount(0);
        result.setTotalTimeMs(0L);

        BatchDataCollector dataCollector = new BatchDataCollector(unifiedProductDataService, result);

        try {
            // 1. 检查缓存中是否有待处理的model
            List<String> pendingModels = getPendingModelsFromCache();

            if (pendingModels.isEmpty()) {
                // 缓存为空，从数据库查询所有model并初始化缓存
                log.info("🔍 缓存为空，查询爬虫数据中的所有不重复model...");
                List<String> allModels = productDataSimplifyService.getDistinctModelsByDataChannel(DataChannelEnum.CRAWLER.getCode());
                log.info("📊 发现{}个不重复的model", allModels.size());

                if (allModels.isEmpty()) {
                    result.setMessage("没有找到需要同步的model数据");
                    return CommonResult.success(result);
                }

                // 初始化缓存
                initPendingModelsCache(allModels);
                pendingModels = allModels;
                log.info("💾 已将{}个model加入待处理缓存", allModels.size());
            } else {
                log.info("📋 从缓存中恢复{}个待处理的model，继续上次未完成的同步", pendingModels.size());
            }

            result.setTotalModels(pendingModels.size());

            // 2. 并行处理多个 model
            ExecutorService executor = getExecutorService();
            List<Future<Void>> futures = new ArrayList<>();
            final int totalModelsCount = pendingModels.size(); // 创建final变量供lambda使用

            log.info("🚀 开始并行处理 {} 个model，线程池大小: {}", totalModelsCount, THREAD_POOL_SIZE);

            for (String model : pendingModels) {
                Future<Void> future = executor.submit(() -> {
                    try {
                        log.info("🔄 开始处理model: {}", model);
                        processModelSyncOptimized(model, dataCollector);
                        synchronized (result) {
                            result.setProcessedModels(result.getProcessedModels() + 1);
                        }

                        // 处理完成后从缓存中移除该model
                        removeModelFromCache(model);
                        log.info("✅ Model '{}' 处理完成并从缓存移除，进度: {}/{}", model, result.getProcessedModels(), totalModelsCount);
                    } catch (Exception e) {
                        log.error("❌ 处理model失败: {}", model, e);
                        synchronized (result) {
                            result.setFailedItems(result.getFailedItems() + 1);
                        }
                        // 处理失败的model不从缓存中移除，下次可以重试
                    }
                    return null;
                });
                futures.add(future);
            }

            // 等待所有任务完成
            log.info("⏳ 等待所有model处理完成...");
            long modelWaitStartTime = System.currentTimeMillis();

            for (Future<Void> future : futures) {
                try {
                    future.get(60, TimeUnit.MINUTES); // 每个model最多等待60分钟
                } catch (TimeoutException e) {
                    log.error("❌ 处理model超时", e);
                    future.cancel(true);
                }
            }

            long modelWaitEndTime = System.currentTimeMillis();
            log.info("✅ 所有model处理完成，等待耗时: {}ms", (modelWaitEndTime - modelWaitStartTime));

            // 处理剩余的批次数据
            log.info("🔄 处理剩余的批次数据...");
            long remainingStartTime = System.currentTimeMillis();
            dataCollector.processRemaining();
            long remainingEndTime = System.currentTimeMillis();
            log.info("✅ 剩余数据处理完成，耗时: {}ms", (remainingEndTime - remainingStartTime));

            long endTime = System.currentTimeMillis();
            long totalTime = endTime - startTime;

            // 设置最终统计信息
            result.setTotalTimeMs(totalTime);
            result.setActualProcessedItems(dataCollector.getProcessedCount());

            // 性能分析
            double avgTimePerModel = pendingModels.isEmpty() ? 0 : (double) totalTime / pendingModels.size();
            double avgTimePerItem = result.getActualProcessedItems() == 0 ? 0 : (double) totalTime / result.getActualProcessedItems();
            double deduplicationRate = result.getTotalItems() == 0 ? 0 :
                (double) (result.getTotalItems() - result.getActualProcessedItems()) / result.getTotalItems() * 100;

            result.setMessage(String.format("同步完成，处理了%d个model，发现%d个商品，跳过%d个商品（无价格/品牌/model），去重后实际处理%d个，同步了%d个商品，共%d个批次，总耗时: %dms",
                result.getProcessedModels(), result.getTotalItems(), result.getSkippedItems(), result.getActualProcessedItems(),
                result.getSyncedItems(), result.getBatchCount(), totalTime));

            log.info("🎉 一键同步完成: {}", result.getMessage());
            log.info("📊 性能分析:");
            log.info("   ⏱️ 总耗时: {}ms ({:.2f}秒)", totalTime, totalTime / 1000.0);
            log.info("   📈 平均每个model耗时: {:.2f}ms", avgTimePerModel);
            log.info("   📈 平均每个商品耗时: {:.2f}ms", avgTimePerItem);
            log.info("   ⏭️ 跳过商品: {}个 (无价格/品牌/model)", result.getSkippedItems());
            log.info("   🔄 去重率: {:.2f}% (发现{}个，实际处理{}个)",
                deduplicationRate, result.getTotalItems(), result.getActualProcessedItems());
            log.info("   📦 批次处理: {}个批次，平均每批{:.1f}个商品",
                result.getBatchCount(), result.getBatchCount() == 0 ? 0 : (double) result.getSyncedItems() / result.getBatchCount());

            // 检查是否所有model都处理完成，如果是则清理缓存
            List<String> remainingModels = getPendingModelsFromCache();
            if (remainingModels.isEmpty()) {
                clearAllCache();
                log.info("🧹 所有model处理完成，已清理缓存");
            } else {
                log.info("📋 还有{}个model待处理，缓存保留", remainingModels.size());
            }

            return CommonResult.success(result);

        } catch (Exception e) {
            log.error("一键同步失败", e);
            result.setMessage("同步失败: " + e.getMessage());
            return CommonResult.error(500, result.getMessage());
        }
    }

    @Override
    public CommonResult<String> clearSyncCache() {
        try {
            List<String> pendingModels = getPendingModelsFromCache();
            int clearedCount = pendingModels.size();

            clearAllCache();

            String message = String.format("成功清理同步缓存，共清理了%d个待处理model", clearedCount);
            log.info("🧹 {}", message);
            return CommonResult.success(message);
        } catch (Exception e) {
            String errorMessage = "清理同步缓存失败: " + e.getMessage();
            log.error("❌ {}", errorMessage, e);
            return CommonResult.error(500, errorMessage);
        }
    }

    @Override
    public CommonResult<SyncCacheStatusVO> getSyncCacheStatus() {
        try {
            List<String> pendingModels = getPendingModelsFromCache();
            SyncCacheStatusVO statusVO = new SyncCacheStatusVO();

            statusVO.setCacheExists(!pendingModels.isEmpty());
            statusVO.setPendingModelsCount(pendingModels.size());

            // 只显示前10个model，避免返回数据过大
            if (pendingModels.size() > 10) {
                statusVO.setPendingModels(pendingModels.subList(0, 10));
                statusVO.setMessage(String.format("当前有%d个model待处理，显示前10个", pendingModels.size()));
            } else {
                statusVO.setPendingModels(pendingModels);
                if (pendingModels.isEmpty()) {
                    statusVO.setMessage("当前没有待处理的model");
                } else {
                    statusVO.setMessage(String.format("当前有%d个model待处理", pendingModels.size()));
                }
            }

            log.info("📊 查看同步缓存状态: {}", statusVO.getMessage());
            return CommonResult.success(statusVO);
        } catch (Exception e) {
            String errorMessage = "查看同步缓存状态失败: " + e.getMessage();
            log.error("❌ {}", errorMessage, e);
            return CommonResult.error(500, errorMessage);
        }
    }

    /**
     * 从缓存中获取待处理的model列表
     */
    @SuppressWarnings("unchecked")
    private List<String> getPendingModelsFromCache() {
        try {
            Object cachedModels = redisTemplate.opsForValue().get(EBAY_SYNC_PENDING_MODELS);
            if (cachedModels instanceof List) {
                return (List<String>) cachedModels;
            }
        } catch (Exception e) {
            log.warn("从缓存获取待处理model失败: {}", e.getMessage());
        }
        return new ArrayList<>();
    }

    /**
     * 初始化待处理model缓存
     */
    private void initPendingModelsCache(List<String> models) {
        try {
            redisTemplate.opsForValue().set(EBAY_SYNC_PENDING_MODELS, models,
                    EBAY_SYNC_TIMEOUT, TimeUnit.SECONDS);
            log.info("💾 已初始化待处理model缓存，包含{}个model", models.size());
        } catch (Exception e) {
            log.error("初始化model缓存失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 从缓存中移除已处理的model
     */
    private void removeModelFromCache(String model) {
        try {
            List<String> pendingModels = getPendingModelsFromCache();
            if (pendingModels.remove(model)) {
                redisTemplate.opsForValue().set(EBAY_SYNC_PENDING_MODELS, pendingModels,
                        EBAY_SYNC_TIMEOUT, TimeUnit.SECONDS);
                log.debug("🗑️ 已从缓存中移除model: {}", model);
            }
        } catch (Exception e) {
            log.error("从缓存移除model失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 清理所有缓存
     */
    private void clearAllCache() {
        try {
            redisTemplate.delete(EBAY_SYNC_PENDING_MODELS);
            redisTemplate.delete(EBAY_SYNC_STATUS);
            log.info("🧹 已清理所有同步缓存");
        } catch (Exception e) {
            log.error("清理缓存失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 处理单个model的同步（优化版本）
     */
    private void processModelSyncOptimized(String model, BatchDataCollector dataCollector) {
        long modelStartTime = System.currentTimeMillis();
        int offset = 0;
        int limit = 50; // 每页50条
        boolean hasNextPage = true;
        List<Future<Void>> itemGroupFutures = new ArrayList<>();
        int totalApiCalls = 0;

        log.info("🚀 开始处理model: {}", model);
        // 对model去掉空格统一小写
        model = model.replaceAll("\\s+", "").toLowerCase();
        try {
            // 第一阶段：搜索并收集所有符合条件的商品ID
            while (hasNextPage) {
                try {
                    long searchStartTime = System.currentTimeMillis();
                    log.info("📡 搜索model: {}, offset: {}, limit: {}", model, offset, limit);

                    // 调用搜索API
                    EbaySearchReqVO searchRequest = new EbaySearchReqVO();
                    searchRequest.setQuery(model);
                    searchRequest.setLimit(limit);
                    searchRequest.setOffset(offset);
                    searchRequest.setIncludeSoldItems(false);
                    searchRequest.setAvailableOnly(true);
                    searchRequest.setExactMatch(true);
                    searchRequest.setFieldgroups("EXTENDED,ADDITIONAL_SELLER_DETAILS,PRODUCT");
                    searchRequest.setMarketplaceId("EBAY_US");
                    searchRequest.setDeliveryCountry("US");
                    searchRequest.setCategoryIds("9355");
                    EbaySearchResponse searchResponse = ebayApiService.searchItemsAdvanced(searchRequest);
                    totalApiCalls++;
                    long searchEndTime = System.currentTimeMillis();
                    log.info("⏱️ 搜索API耗时: {}ms, model: {}, offset: {}",
                        (searchEndTime - searchStartTime), model, offset);

                    if (searchResponse == null || searchResponse.getItemSummaries() == null) {
                        log.warn("⚠️ 搜索结果为空，model: {}", model);
                        break;
                    }

                    List<EbaySearchResponse.ItemSummary> items = searchResponse.getItemSummaries();

                    // 检查是否为空结果
                    if (items.isEmpty()) {
                        if (offset == 0) {
                            log.warn("⚠️ 搜索结果为空，model: {} (第一页无结果，跳过)", model);
                        } else {
                            log.info("📄 已到达最后一页，model: {}, offset: {}", model, offset);
                        }
                        break;
                    }

                    synchronized (dataCollector.result) {
                        dataCollector.result.setTotalItems(dataCollector.result.getTotalItems() + items.size());
                    }

                    log.info("📦 本页获取到 {} 个商品，model: {}, offset: {}", items.size(), model, offset);

                    // 处理搜索结果
                    for (EbaySearchResponse.ItemSummary item : items) {
                        try {
                            processItemSyncOptimized(item, dataCollector, itemGroupFutures);
                        } catch (Exception e) {
                            log.error("❌ 处理商品失败，itemId: {}", item.getItemId(), e);
                            synchronized (dataCollector.result) {
                                dataCollector.result.setFailedItems(dataCollector.result.getFailedItems() + 1);
                            }
                        }
                    }

                    // 检查是否有下一页
                    hasNextPage = StringUtils.hasText(searchResponse.getNext());
                    offset += limit;

                    // 防止无限循环，最多处理10页
                    if (offset >= limit * MAX_PAGES_PER_MODEL) {
                        log.warn("⚠️ 达到最大页数限制，停止处理model: {}", model);
                        break;
                    }

                    // API调用频率控制
                    try {
                        Thread.sleep(50);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        break;
                    }

                } catch (Exception e) {
                    log.error("❌ 搜索失败，model: {}, offset: {}", model, offset, e);
                    break;
                }
            }



            // 等待所有itemGroup处理完成
            long itemGroupWaitStartTime = System.currentTimeMillis();
            log.info("⏳ 等待 {} 个itemGroup处理完成，model: {}", itemGroupFutures.size(), model);

            for (Future<Void> future : itemGroupFutures) {
                try {
                    future.get(5, TimeUnit.MINUTES);
                } catch (Exception e) {
                    log.error("❌ 等待itemGroup处理完成时出错", e);
                }
            }

            long itemGroupWaitEndTime = System.currentTimeMillis();
            long modelTotalTime = System.currentTimeMillis() - modelStartTime;

            log.info("✅ model处理完成: {}, 总耗时: {}ms, API调用次数: {}, itemGroup等待耗时: {}ms",
                model, modelTotalTime, totalApiCalls, (itemGroupWaitEndTime - itemGroupWaitStartTime));

        } catch (Exception e) {
            log.error("❌ 处理model失败: {}", model, e);
        }
    }

    /**
     * 处理单个model的同步（原版本，保留兼容性）
     */
    private void processModelSync(String model, SyncResultVO result) {
        int offset = 0;
        int limit = 50; // 每页50条
        boolean hasNextPage = true;

        while (hasNextPage) {
            try {
                log.info("搜索model: {}, offset: {}, limit: {}", model, offset, limit);
                
                // 调用搜索API
                EbaySearchReqVO searchRequest = new EbaySearchReqVO();
                searchRequest.setQuery(model);
                searchRequest.setLimit(limit);
                searchRequest.setOffset(offset);
                searchRequest.setIncludeSoldItems(false);
                searchRequest.setAvailableOnly(true);
                searchRequest.setExactMatch(true);
                searchRequest.setFieldgroups("EXTENDED,ADDITIONAL_SELLER_DETAILS,PRODUCT");
                searchRequest.setMarketplaceId("EBAY_US");
                searchRequest.setDeliveryCountry("US");
                searchRequest.setCategoryIds("9355");
                EbaySearchResponse searchResponse = ebayApiService.searchItemsAdvanced(searchRequest);
                
                if (searchResponse == null || searchResponse.getItemSummaries() == null) {
                    log.warn("搜索结果为空，model: {}", model);
                    break;
                }

                List<EbaySearchResponse.ItemSummary> items = searchResponse.getItemSummaries();
                result.setTotalItems(result.getTotalItems() + items.size());

                // 处理搜索结果
                for (EbaySearchResponse.ItemSummary item : items) {
                    try {
                        processItemSync(item, result);
                    } catch (Exception e) {
                        log.error("处理商品失败，itemId: {}", item.getItemId(), e);
                        result.setFailedItems(result.getFailedItems() + 1);
                    }
                }

                // 检查是否有下一页
                hasNextPage = StringUtils.hasText(searchResponse.getNext());
                offset += limit;

                // 防止无限循环，最多处理10页
                if (offset >= limit * 10) {
                    log.warn("达到最大页数限制，停止处理model: {}", model);
                    break;
                }

            } catch (Exception e) {
                log.error("搜索失败，model: {}, offset: {}", model, offset, e);
                break;
            }
        }
    }

    /**
     * 处理单个商品的同步（优化版本）
     */
    private void processItemSyncOptimized(EbaySearchResponse.ItemSummary item, BatchDataCollector dataCollector, List<Future<Void>> itemGroupFutures) {
        String itemId = item.getItemId();

        // 1. 检查缓存中是否已经同步过该商品
        if (isItemAlreadySynced(itemId)) {
            log.debug("⏭️ 商品已同步过，跳过处理，itemId: {}", itemId);
            synchronized (dataCollector.result) {
                dataCollector.result.setSkippedItems(dataCollector.result.getSkippedItems() + 1);
            }
            return;
        }

        try {
            // 2. 收集当前商品数据
            collectItemData(item, dataCollector);

            // 3. 同步成功后，将itemId记录到缓存中
            markItemAsSynced(itemId);
            log.debug("✅ 商品同步完成并记录到缓存，itemId: {}", itemId);

        } catch (Exception e) {
            log.error("❌ 商品同步失败，itemId: {}", itemId, e);
            throw e; // 重新抛出异常，让上层处理
        }

        // 4. 检查是否有 itemGroupHref，异步处理
        if (StringUtils.hasText(item.getItemGroupHref())) {
            String itemGroupId = extractItemGroupIdFromHref(item.getItemGroupHref());
            if (StringUtils.hasText(itemGroupId)) {
                log.info("发现商品组，异步处理商品组数据，itemGroupId: {}", itemGroupId);

                Future<Void> future = getExecutorService().submit(() -> {
                    try {
                        collectItemGroupData(itemGroupId, dataCollector);
                    } catch (Exception e) {
                        log.error("异步处理商品组数据失败，itemGroupId: {}", itemGroupId, e);
                    }
                    return null;
                });
                itemGroupFutures.add(future);
            }
        }
    }

    /**
     * 处理单个商品的同步（原版本，保留兼容性）
     */
    private void processItemSync(EbaySearchResponse.ItemSummary item, SyncResultVO result) {
        // 1. 直接同步当前商品数据
        syncItemData(item, result);

        // 2. 检查是否有 itemGroupHref
        if (StringUtils.hasText(item.getItemGroupHref())) {
            String itemGroupId = extractItemGroupIdFromHref(item.getItemGroupHref());
            if (StringUtils.hasText(itemGroupId)) {
                log.info("发现商品组，开始同步商品组数据，itemGroupId: {}", itemGroupId);
                syncItemGroupData(itemGroupId, result);
            }
        }
    }

    /**
     * 收集单个商品数据（优化版本）- 智能选择是否调用详情API
     */
    private void collectItemData(EbaySearchResponse.ItemSummary item, BatchDataCollector dataCollector) {
        long itemStartTime = System.currentTimeMillis();
        try {
            // 智能判断：检查商品是否符合基本条件
            boolean shouldProcess = shouldCallDetailApi(item, dataCollector);

            if (!shouldProcess) {
                // 不符合条件的商品直接跳过，不查询，不入库
                log.debug("⏭️ 商品不符合处理条件，跳过，itemId: {}", item.getItemId());
                synchronized (dataCollector.result) {
                    dataCollector.result.setSkippedItems(dataCollector.result.getSkippedItems() + 1);
                }
                return;
            }

            // 符合条件的商品都调用详情API获取完整信息
            if (shouldProcess) {
                log.debug("🔍 符合条件商品，调用详情接口，itemId: {}", item.getItemId());

                // 1. 调用eBay获取商品详情接口
                long detailApiStartTime = System.currentTimeMillis();
                EbayItemDetailRespVO itemDetailRespVO = ebayApiService.getItemDetail(item.getItemId());
                long detailApiEndTime = System.currentTimeMillis();

                log.debug("⏱️ 详情API调用耗时: {}ms, itemId: {}",
                    (detailApiEndTime - detailApiStartTime), item.getItemId());

                if (itemDetailRespVO == null) {
                    log.warn("⚠️ 获取商品详情失败，itemId: {}", item.getItemId());
                    synchronized (dataCollector.result) {
                        dataCollector.result.setFailedItems(dataCollector.result.getFailedItems() + 1);
                    }
                    return;
                }

                // 2. 转换为 ProductDataDTO
                long convertStartTime = System.currentTimeMillis();
                ProductDataDTO productDataDTO = ebayToProductDataConverter.convertToProductDataDTO(itemDetailRespVO);
                long convertEndTime = System.currentTimeMillis();

                log.debug("⏱️ 数据转换耗时: {}ms, itemId: {}",
                    (convertEndTime - convertStartTime), item.getItemId());

                // 3. 添加到批量收集器
                dataCollector.addProductData(productDataDTO, item.getItemId());

                // API调用频率控制
                try {
                    Thread.sleep(50); // 详情API调用间隔50ms
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            }

            long itemTotalTime = System.currentTimeMillis() - itemStartTime;
            log.debug("✅ 成功收集商品数据，总耗时: {}ms, itemId: {}", itemTotalTime, item.getItemId());

        } catch (Exception e) {
            long itemTotalTime = System.currentTimeMillis() - itemStartTime;
            log.error("❌ 收集商品数据失败，耗时: {}ms, itemId: {}", itemTotalTime, item.getItemId(), e);
            synchronized (dataCollector.result) {
                dataCollector.result.setFailedItems(dataCollector.result.getFailedItems() + 1);
            }
        }
    }

    /**
     * 收集商品组数据（优化版本）
     */
    private void collectItemGroupData(String itemGroupId, BatchDataCollector dataCollector) {
        long itemGroupStartTime = System.currentTimeMillis();
        try {
            log.debug("🔍 开始收集商品组数据，itemGroupId: {}", itemGroupId);

            long itemGroupApiStartTime = System.currentTimeMillis();
            EbayItemGroupResponse itemGroupResponse = ebayApiService.getItemsByItemGroup(itemGroupId);
            long itemGroupApiEndTime = System.currentTimeMillis();

            log.debug("⏱️ 商品组API调用耗时: {}ms, itemGroupId: {}",
                (itemGroupApiEndTime - itemGroupApiStartTime), itemGroupId);

            if (itemGroupResponse != null && itemGroupResponse.getItems() != null) {
                log.debug("📦 商品组包含 {} 个商品，itemGroupId: {}",
                    itemGroupResponse.getItems().size(), itemGroupId);

                for (var itemDetail : itemGroupResponse.getItems()) {
                    try {
                        long itemConvertStartTime = System.currentTimeMillis();

                        // 先转换为 EbayItemDetailRespVO，再转换为 ProductDataDTO
                        EbayItemDetailRespVO itemDetailRespVO = ebayConvert.convertItemDetailResponseToRespVO(itemDetail);
                        ProductDataDTO productDataDTO = ebayToProductDataConverter.convertToProductDataDTO(itemDetailRespVO);

                        // 添加到批量收集器
                        // 同步成功后，将itemId记录到缓存中
                        markItemAsSynced(itemDetail.getItemId());
                        dataCollector.addProductData(productDataDTO, itemDetail.getItemId());

                        synchronized (dataCollector.result) {
                            dataCollector.result.setItemGroupItems(dataCollector.result.getItemGroupItems() + 1);
                        }

                        long itemConvertEndTime = System.currentTimeMillis();
                        log.debug("⏱️ 商品组商品转换耗时: {}ms, itemId: {}",
                            (itemConvertEndTime - itemConvertStartTime), itemDetail.getItemId());
                        log.debug("成功收集商品组数据，itemId: {}", itemDetail.getItemId());

                    } catch (Exception e) {
                        log.error("收集商品组数据失败，itemId: {}", itemDetail.getItemId(), e);
                        synchronized (dataCollector.result) {
                            dataCollector.result.setFailedItems(dataCollector.result.getFailedItems() + 1);
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("获取商品组数据失败，itemGroupId: {}", itemGroupId, e);
            synchronized (dataCollector.result) {
                dataCollector.result.setFailedItems(dataCollector.result.getFailedItems() + 1);
            }
        }
    }

    /**
     * 同步单个商品数据（原版本，保留兼容性）
     */
    private void syncItemData(EbaySearchResponse.ItemSummary item, SyncResultVO result) {
        try {
            // 转换为 ProductDataDTO
            ProductDataDTO productDataDTO = ebayToProductDataConverter.convertSearchItemToProductDataDTO(item);
            
            // 调用批量处理方法
            unifiedProductDataService.batchProcessProductData(List.of(productDataDTO));
            
            result.setSyncedItems(result.getSyncedItems() + 1);
            log.debug("成功同步商品数据，itemId: {}", item.getItemId());
            
        } catch (Exception e) {
            log.error("同步商品数据失败，itemId: {}", item.getItemId(), e);
            result.setFailedItems(result.getFailedItems() + 1);
        }
    }

    /**
     * 同步商品组数据
     */
    private void syncItemGroupData(String itemGroupId, SyncResultVO result) {
        try {
            EbayItemGroupResponse itemGroupResponse = ebayApiService.getItemsByItemGroup(itemGroupId);
            
            if (itemGroupResponse != null && itemGroupResponse.getItems() != null) {
                for (var itemDetail : itemGroupResponse.getItems()) {
                    try {
                        // 先转换为 EbayItemDetailRespVO，再转换为 ProductDataDTO
                        EbayItemDetailRespVO itemDetailRespVO = ebayConvert.convertItemDetailResponseToRespVO(itemDetail);
                        ProductDataDTO productDataDTO = ebayToProductDataConverter.convertToProductDataDTO(itemDetailRespVO);
                        
                        // 调用批量处理方法
                        unifiedProductDataService.batchProcessProductData(List.of(productDataDTO));
                        
                        result.setItemGroupItems(result.getItemGroupItems() + 1);
                        log.debug("成功同步商品组数据，itemId: {}", itemDetail.getItemId());
                        
                    } catch (Exception e) {
                        log.error("同步商品组数据失败，itemId: {}", itemDetail.getItemId(), e);
                        result.setFailedItems(result.getFailedItems() + 1);
                    }
                }
            }
        } catch (Exception e) {
            log.error("获取商品组数据失败，itemGroupId: {}", itemGroupId, e);
            result.setFailedItems(result.getFailedItems() + 1);
        }
    }

    @Override
    public String extractItemGroupIdFromHref(String itemGroupHref) {
        if (!StringUtils.hasText(itemGroupHref)) {
            return null;
        }

        try {
            Matcher matcher = ITEM_GROUP_ID_PATTERN.matcher(itemGroupHref);
            if (matcher.find()) {
                return matcher.group(1);
            }
        } catch (Exception e) {
            log.error("提取item_group_id失败，itemGroupHref: {}", itemGroupHref, e);
        }

        return null;
    }

    /**
     * 智能判断是否需要调用详情API
     * 没有价格、品牌、model的商品直接跳过，不查询，不入库
     */
    private boolean shouldCallDetailApi(EbaySearchResponse.ItemSummary item, BatchDataCollector dataCollector) {
        // 必要条件检查：必须有价格
        if (item.getPrice() == null || item.getPrice().getValue() == null || item.getPrice().getValue().trim().isEmpty()) {
            log.debug("❌ 商品无价格信息，跳过处理，itemId: {}", item.getItemId());
            return false;
        }

        // 必要条件检查：必须有标题（作为model信息）
        if (item.getTitle() == null || item.getTitle().trim().isEmpty()) {
            log.debug("❌ 商品无标题信息，跳过处理，itemId: {}", item.getItemId());
            return false;
        }

        // 优先处理有图片的商品
        if (item.getImage() != null && item.getImage().getImageUrl() != null) {
            return true;
        }

        // 优先处理有额外图片的商品
        if (item.getAdditionalImages() != null && !item.getAdditionalImages().isEmpty()) {
            return true;
        }

        // 其他有基本信息的商品也处理
        return true;
    }



    /**
     * 获取线程池（懒加载）
     */
    private ExecutorService getExecutorService() {
        if (executorService == null) {
            synchronized (this) {
                if (executorService == null) {
                    executorService = Executors.newFixedThreadPool(THREAD_POOL_SIZE);
                }
            }
        }
        return executorService;
    }

    /**
     * 批量数据收集器
     */
    private static class BatchDataCollector {
        private final Set<String> processedItemIds = new HashSet<>();
        private final List<ProductDataDTO> collectedData = new ArrayList<>();
        private final UnifiedProductDataService unifiedProductDataService;
        private final SyncResultVO result;

        public BatchDataCollector(UnifiedProductDataService unifiedProductDataService, SyncResultVO result) {
            this.unifiedProductDataService = unifiedProductDataService;
            this.result = result;
        }

        /**
         * 添加商品数据（带去重）
         */
        public synchronized void addProductData(ProductDataDTO productDataDTO, String itemId) {
            if (processedItemIds.contains(itemId)) {
                log.debug("商品已处理过，跳过重复数据，itemId: {}", itemId);
                return;
            }

            processedItemIds.add(itemId);
            collectedData.add(productDataDTO);

            // 达到批次大小时自动处理
            if (collectedData.size() >= BATCH_SIZE) {
                processBatch();
            }
        }

        /**
         * 处理当前批次
         */
        public synchronized void processBatch() {
            if (collectedData.isEmpty()) {
                return;
            }

            try {
                int currentBatchNumber = result.getBatchCount() == null ? 1 : result.getBatchCount() + 1;
                log.info("🚀 开始批量处理第 {} 批，包含 {} 个商品", currentBatchNumber, collectedData.size());
                long startTime = System.currentTimeMillis();

                unifiedProductDataService.batchProcessProductData(new ArrayList<>(collectedData));

                long endTime = System.currentTimeMillis();
                long batchTime = endTime - startTime;
                double avgTimePerItem = (double) batchTime / collectedData.size();

                log.info("✅ 批量处理完成，第 {} 批，耗时: {}ms，平均每个商品: {:.2f}ms",
                    currentBatchNumber, batchTime, avgTimePerItem);

                result.setSyncedItems(result.getSyncedItems() + collectedData.size());
                result.setBatchCount(currentBatchNumber);
                collectedData.clear();

            } catch (Exception e) {
                log.error("❌ 批量处理失败，批次大小: {}", collectedData.size(), e);
                result.setFailedItems(result.getFailedItems() + collectedData.size());
                collectedData.clear();
            }
        }

        /**
         * 处理剩余数据
         */
        public synchronized void processRemaining() {
            if (!collectedData.isEmpty()) {
                processBatch();
            }
        }

        /**
         * 获取已处理的商品数量
         */
        public int getProcessedCount() {
            return processedItemIds.size();
        }
    }

    /**
     * 检查商品是否已经同步过
     *
     * @param itemId 商品ID
     * @return true-已同步过，false-未同步过
     */
    private boolean isItemAlreadySynced(String itemId) {
        if (!StringUtils.hasText(itemId)) {
            return false;
        }

        try {
            Boolean isMember = redisTemplate.opsForSet().isMember(EBAY_SYNC_SYNCED_ITEMS, itemId);
            return Boolean.TRUE.equals(isMember);
        } catch (Exception e) {
            log.warn("检查商品同步缓存失败，itemId: {}, 继续处理", itemId, e);
            return false; // 缓存异常时，继续处理商品
        }
    }

    /**
     * 将商品标记为已同步
     *
     * @param itemId 商品ID
     */
    @Override
    public void markItemAsSynced(String itemId) {
        if (!StringUtils.hasText(itemId)) {
            return;
        }

        try {
            // 将itemId添加到Redis Set中
            redisTemplate.opsForSet().add(EBAY_SYNC_SYNCED_ITEMS, itemId);

            // 设置过期时间（使用统一常量）
            redisTemplate.expire(EBAY_SYNC_SYNCED_ITEMS, EBAY_SYNC_TIMEOUT, TimeUnit.SECONDS);

            log.debug("商品已标记为已同步，itemId: {}", itemId);
        } catch (Exception e) {
            log.warn("标记商品为已同步失败，itemId: {}", itemId, e);
            // 不抛出异常，避免影响主流程
        }
    }

    /**
     * 清空已同步商品的缓存
     */
    public void clearSyncedItemsCache() {
        try {
            redisTemplate.delete(EBAY_SYNC_SYNCED_ITEMS);
            log.info("已清空已同步商品缓存");
        } catch (Exception e) {
            log.error("清空已同步商品缓存失败", e);
        }
    }

    /**
     * 获取已同步商品数量
     *
     * @return 已同步商品数量
     */
    public long getSyncedItemsCount() {
        try {
            Long count = redisTemplate.opsForSet().size(EBAY_SYNC_SYNCED_ITEMS);
            return count != null ? count : 0L;
        } catch (Exception e) {
            log.warn("获取已同步商品数量失败", e);
            return 0L;
        }
    }

    /**
     * 检查指定商品是否在缓存中
     *
     * @param itemId 商品ID
     * @return true-在缓存中，false-不在缓存中
     */
    public boolean checkItemInCache(String itemId) {
        return isItemAlreadySynced(itemId);
    }
}

package ai.pricefox.mallfox.service.integration.impl;

import ai.pricefox.mallfox.common.util.EmailUtil;
import ai.pricefox.mallfox.service.integration.EmailService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Random;
import java.util.UUID;
import java.util.regex.Pattern;

/**
 * 邮箱服务实现
 */
@Slf4j
@Service
@AllArgsConstructor
public class EmailServiceImpl implements EmailService {

    private final EmailUtil emailUtil;

    // 邮箱格式验证正则
    private static final String EMAIL_PATTERN = 
        "^[a-zA-Z0-9_+&*-]+(?:\\.[a-zA-Z0-9_+&*-]+)*@(?:[a-zA-Z0-9-]+\\.)+[a-zA-Z]{2,7}$";
    
    private static final Pattern EMAIL_REGEX = Pattern.compile(EMAIL_PATTERN);

    @Override
    public boolean sendVerifyCode(String email, String code, String businessType) {
        if (!isValidEmail(email) || !StringUtils.hasText(code)) {
            log.error("发送验证码失败：邮箱或验证码无效, email={}, code={}", email, code);
            return false;
        }

        try {
            String subject = getVerifyCodeSubject(businessType);
            String content = buildVerifyCodeContent(code, businessType);
            
            emailUtil.sendHtmlMail(email, subject, content);
            log.info("验证码邮件发送成功: email={}, businessType={}", email, businessType);
            return true;
        } catch (Exception e) {
            log.error("发送验证码邮件失败: email={}, businessType={}", email, businessType, e);
            return false;
        }
    }

    @Override
    public boolean sendResetPasswordEmail(String email, String resetToken, String resetUrl) {
        if (!isValidEmail(email) || !StringUtils.hasText(resetToken)) {
            log.error("发送重置密码邮件失败：参数无效, email={}, resetToken={}", email, resetToken);
            return false;
        }

        try {
            String subject = "重置您的密码 - PriceFox";
            String content = buildResetPasswordContent(resetToken, resetUrl);
            
            emailUtil.sendHtmlMail(email, subject, content);
            log.info("重置密码邮件发送成功: email={}", email);
            return true;
        } catch (Exception e) {
            log.error("发送重置密码邮件失败: email={}", email, e);
            return false;
        }
    }

    @Override
    public boolean sendWelcomeEmail(String email, String username) {
        if (!isValidEmail(email)) {
            log.error("发送欢迎邮件失败：邮箱无效, email={}", email);
            return false;
        }

        try {
            String subject = "欢迎加入 PriceFox！";
            String content = buildWelcomeContent(username);
            
            emailUtil.sendHtmlMail(email, subject, content);
            log.info("欢迎邮件发送成功: email={}, username={}", email, username);
            return true;
        } catch (Exception e) {
            log.error("发送欢迎邮件失败: email={}, username={}", email, username, e);
            return false;
        }
    }

    @Override
    public boolean sendNotificationEmail(String email, String subject, String content) {
        if (!isValidEmail(email) || !StringUtils.hasText(subject) || !StringUtils.hasText(content)) {
            log.error("发送通知邮件失败：参数无效, email={}, subject={}", email, subject);
            return false;
        }

        try {
            emailUtil.sendHtmlMail(email, subject, content);
            log.info("通知邮件发送成功: email={}, subject={}", email, subject);
            return true;
        } catch (Exception e) {
            log.error("发送通知邮件失败: email={}, subject={}", email, subject, e);
            return false;
        }
    }

    @Override
    public String generateVerifyCode() {
        Random random = new Random();
        int code = 100000 + random.nextInt(900000); // 生成6位数字
        return String.valueOf(code);
    }

    @Override
    public String generateResetToken() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    @Override
    public boolean isValidEmail(String email) {
        return StringUtils.hasText(email) && EMAIL_REGEX.matcher(email).matches();
    }

    /**
     * 获取验证码邮件主题
     */
    private String getVerifyCodeSubject(String businessType) {
        switch (businessType.toUpperCase()) {
            case "REGISTER":
                return "注册验证码 - PriceFox";
            case "LOGIN":
                return "登录验证码 - PriceFox";
            case "RESET_PASSWORD":
                return "重置密码验证码 - PriceFox";
            default:
                return "验证码 - PriceFox";
        }
    }

    /**
     * 构建验证码邮件内容
     */
    private String buildVerifyCodeContent(String code, String businessType) {
        String purpose = getBusinessTypeName(businessType);
        
        return String.format("""
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <title>验证码</title>
            </head>
            <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
                <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                    <div style="text-align: center; margin-bottom: 30px;">
                        <h1 style="color: #2c3e50;">PriceFox</h1>
                    </div>
                    
                    <div style="background-color: #f8f9fa; padding: 30px; border-radius: 10px;">
                        <h2 style="color: #2c3e50; margin-bottom: 20px;">%s验证码</h2>
                        
                        <p>您好！</p>
                        <p>您正在进行%s操作，验证码为：</p>
                        
                        <div style="text-align: center; margin: 30px 0;">
                            <span style="font-size: 32px; font-weight: bold; color: #e74c3c; 
                                         background-color: #fff; padding: 15px 30px; 
                                         border-radius: 8px; border: 2px dashed #e74c3c;">%s</span>
                        </div>
                        
                        <p style="color: #7f8c8d;">
                            <strong>注意：</strong>
                            <br>• 验证码有效期为5分钟
                            <br>• 请勿将验证码告诉他人
                            <br>• 如非本人操作，请忽略此邮件
                        </p>
                    </div>
                    
                    <div style="text-align: center; margin-top: 30px; color: #95a5a6; font-size: 12px;">
                        <p>此邮件由系统自动发送，请勿回复</p>
                        <p>&copy; 2024 PriceFox. All rights reserved.</p>
                    </div>
                </div>
            </body>
            </html>
            """, purpose, purpose, code);
    }

    /**
     * 构建重置密码邮件内容
     */
    private String buildResetPasswordContent(String resetToken, String resetUrl) {
        String fullResetUrl = resetUrl != null ? resetUrl : 
            "https://www.pricefox.ai/reset-password?token=" + resetToken;
            
        return String.format("""
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <title>重置密码</title>
            </head>
            <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
                <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                    <div style="text-align: center; margin-bottom: 30px;">
                        <h1 style="color: #2c3e50;">PriceFox</h1>
                    </div>
                    
                    <div style="background-color: #f8f9fa; padding: 30px; border-radius: 10px;">
                        <h2 style="color: #2c3e50; margin-bottom: 20px;">重置您的密码</h2>
                        
                        <p>您好！</p>
                        <p>我们收到了您重置密码的请求。请点击下面的按钮来重置您的密码：</p>
                        
                        <div style="text-align: center; margin: 30px 0;">
                            <a href="%s" 
                               style="display: inline-block; background-color: #3498db; color: white; 
                                      padding: 15px 30px; text-decoration: none; border-radius: 5px; 
                                      font-weight: bold;">重置密码</a>
                        </div>
                        
                        <p>如果按钮无法点击，请复制以下链接到浏览器地址栏：</p>
                        <p style="word-break: break-all; background-color: #ecf0f1; padding: 10px; border-radius: 5px;">
                            %s
                        </p>
                        
                        <p style="color: #7f8c8d;">
                            <strong>注意：</strong>
                            <br>• 此链接有效期为24小时
                            <br>• 如非本人操作，请忽略此邮件
                            <br>• 为了您的账户安全，请勿将此链接分享给他人
                        </p>
                    </div>
                    
                    <div style="text-align: center; margin-top: 30px; color: #95a5a6; font-size: 12px;">
                        <p>此邮件由系统自动发送，请勿回复</p>
                        <p>&copy; 2024 PriceFox. All rights reserved.</p>
                    </div>
                </div>
            </body>
            </html>
            """, fullResetUrl, fullResetUrl);
    }

    /**
     * 构建欢迎邮件内容
     */
    private String buildWelcomeContent(String username) {
        return String.format("""
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <title>欢迎加入</title>
            </head>
            <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
                <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                    <div style="text-align: center; margin-bottom: 30px;">
                        <h1 style="color: #2c3e50;">PriceFox</h1>
                    </div>
                    
                    <div style="background-color: #f8f9fa; padding: 30px; border-radius: 10px;">
                        <h2 style="color: #2c3e50; margin-bottom: 20px;">欢迎加入 PriceFox！</h2>
                        
                        <p>亲爱的 %s，</p>
                        <p>欢迎您加入 PriceFox 大家庭！我们很高兴您选择了我们的服务。</p>
                        
                        <div style="background-color: #e8f5e8; padding: 20px; border-radius: 8px; margin: 20px 0;">
                            <h3 style="color: #27ae60; margin-top: 0;">您现在可以：</h3>
                            <ul style="color: #2c3e50;">
                                <li>浏览我们的商品目录</li>
                                <li>享受个性化推荐</li>
                                <li>参与优惠活动</li>
                                <li>获得专属客户服务</li>
                            </ul>
                        </div>
                        
                        <div style="text-align: center; margin: 30px 0;">
                            <a href="https://www.pricefox.ai" 
                               style="display: inline-block; background-color: #27ae60; color: white; 
                                      padding: 15px 30px; text-decoration: none; border-radius: 5px; 
                                      font-weight: bold;">开始购物</a>
                        </div>
                        
                        <p>如果您有任何问题，请随时联系我们的客服团队。</p>
                    </div>
                    
                    <div style="text-align: center; margin-top: 30px; color: #95a5a6; font-size: 12px;">
                        <p>此邮件由系统自动发送，请勿回复</p>
                        <p>&copy; 2024 PriceFox. All rights reserved.</p>
                    </div>
                </div>
            </body>
            </html>
            """, username != null ? username : "用户");
    }

    /**
     * 获取业务类型名称
     */
    private String getBusinessTypeName(String businessType) {
        switch (businessType.toUpperCase()) {
            case "REGISTER":
                return "注册";
            case "LOGIN":
                return "登录";
            case "RESET_PASSWORD":
                return "重置密码";
            default:
                return "验证";
        }
    }
}

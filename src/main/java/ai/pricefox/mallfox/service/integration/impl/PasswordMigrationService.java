package ai.pricefox.mallfox.service.integration.impl;

import ai.pricefox.mallfox.domain.admin.AdminUser;
import ai.pricefox.mallfox.domain.auth.User;
import ai.pricefox.mallfox.mapper.admin.AdminUserMapper;
import ai.pricefox.mallfox.mapper.auth.UserMapper;
import ai.pricefox.mallfox.service.auth.PasswordService;
import ai.pricefox.mallfox.utils.AESUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 密码迁移服务
 * 用于将现有的明文密码转换为加密密码
 */
@Slf4j
@Service
@AllArgsConstructor
public class PasswordMigrationService implements CommandLineRunner {

    private final UserMapper userMapper;
    private final AdminUserMapper adminUserMapper;
    private final PasswordService passwordService;

    @Override
    public void run(String... args) throws Exception {
        // 在应用启动时自动执行密码迁移
        log.info("开始检查并迁移明文密码...");
        
        try {
            migrateUserPasswords();
            migrateAdminUserPasswords();
            log.info("密码迁移检查完成");
        } catch (Exception e) {
            log.error("密码迁移过程中发生错误", e);
        }
    }

    /**
     * 迁移门户用户密码
     */
    @Transactional(rollbackFor = Exception.class)
    public void migrateUserPasswords() {
        try {
            List<User> users = userMapper.selectAllUsers();
            int migratedCount = 0;
            
            for (User user : users) {
                if (StringUtils.hasText(user.getPassword()) && !AESUtil.isEncrypted(user.getPassword())) {
                    // 密码是明文，需要加密
                    String encryptedPassword = passwordService.encryptPassword(user.getPassword());
                    
                    User updateUser = new User();
                    updateUser.setId(user.getId());
                    updateUser.setPassword(encryptedPassword);
                    updateUser.setUpdateTime(LocalDateTime.now());
                    
                    userMapper.updateUserById(updateUser);
                    migratedCount++;
                    
                    log.info("已加密门户用户密码: userId={}, username={}", user.getId(), user.getUsername());
                }
            }
            
            if (migratedCount > 0) {
                log.info("门户用户密码迁移完成，共加密 {} 个用户的密码", migratedCount);
            } else {
                log.info("门户用户密码检查完成，无需迁移");
            }
        } catch (Exception e) {
            log.error("门户用户密码迁移失败", e);
            throw e;
        }
    }

    /**
     * 迁移后台用户密码
     */
    @Transactional(rollbackFor = Exception.class)
    public void migrateAdminUserPasswords() {
        try {
            List<AdminUser> adminUsers = adminUserMapper.selectAllAdminUsers();
            int migratedCount = 0;
            
            for (AdminUser adminUser : adminUsers) {
                if (StringUtils.hasText(adminUser.getPassword()) && !AESUtil.isEncrypted(adminUser.getPassword())) {
                    // 密码是明文，需要加密
                    String encryptedPassword = passwordService.encryptPassword(adminUser.getPassword());
                    
                    AdminUser updateUser = new AdminUser();
                    updateUser.setId(adminUser.getId());
                    updateUser.setPassword(encryptedPassword);
                    updateUser.setUpdateTime(LocalDateTime.now());
                    
                    adminUserMapper.updateAdminUserById(updateUser);
                    migratedCount++;
                    
                    log.info("已加密后台用户密码: userId={}, username={}", adminUser.getId(), adminUser.getUsername());
                }
            }
            
            if (migratedCount > 0) {
                log.info("后台用户密码迁移完成，共加密 {} 个用户的密码", migratedCount);
            } else {
                log.info("后台用户密码检查完成，无需迁移");
            }
        } catch (Exception e) {
            log.error("后台用户密码迁移失败", e);
            throw e;
        }
    }

    /**
     * 手动触发密码迁移
     */
    public void manualMigration() {
        log.info("手动触发密码迁移...");
        migrateUserPasswords();
        migrateAdminUserPasswords();
        log.info("手动密码迁移完成");
    }
}

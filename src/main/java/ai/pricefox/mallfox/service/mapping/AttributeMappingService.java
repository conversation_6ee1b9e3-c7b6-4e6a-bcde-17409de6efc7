package ai.pricefox.mallfox.service.mapping;

import ai.pricefox.mallfox.domain.standard.StandardAttributeMapping;
import ai.pricefox.mallfox.model.param.AttributeMappingCreateRequest;
import ai.pricefox.mallfox.model.param.AttributeMappingQueryRequest;
import ai.pricefox.mallfox.model.param.AttributeMappingUpdateRequest;
import ai.pricefox.mallfox.model.response.AttributeMappingBatchImportResultResponse;
import ai.pricefox.mallfox.model.response.AttributeMappingDetailResponse;
import ai.pricefox.mallfox.model.response.AttributeMappingResponse;
import ai.pricefox.mallfox.model.response.ExportFileResponse;
import ai.pricefox.mallfox.vo.base.CommonResult;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 属性映射管理Service接口
 *
 * <AUTHOR>
 * @date 2025-08-03
 */
public interface AttributeMappingService extends IService<StandardAttributeMapping> {

    /**
     * 分页查询属性映射列表
     *
     * @param queryRequest 查询请求
     * @return 分页结果
     */
    CommonResult<Page<AttributeMappingResponse>> getAttributeMappingList(AttributeMappingQueryRequest queryRequest);

    /**
     * 创建属性映射
     *
     * @param reqVO 创建请求
     * @return 创建结果
     */
    CommonResult<AttributeMappingResponse> createAttributeMapping(AttributeMappingCreateRequest reqVO);

    /**
     * 更新属性映射
     *
     * @param reqVO 更新请求
     * @return 更新结果
     */
    CommonResult<AttributeMappingResponse> updateAttributeMapping(AttributeMappingUpdateRequest reqVO);

    /**
     * 删除属性映射
     *
     * @param id 映射ID
     * @return 删除结果
     */
    CommonResult<Boolean> deleteAttributeMapping(Long id);

    /**
     * 批量删除属性映射
     *
     * @param ids 映射ID列表
     * @return 删除结果
     */
    CommonResult<Boolean> batchDeleteAttributeMapping(List<Long> ids);

    /**
     * 批量导入属性映射
     *
     * @param file Excel文件
     * @return 导入结果
     */
    CommonResult<AttributeMappingBatchImportResultResponse>  batchImportAttributeMapping(MultipartFile file);

    /**
     * 导出属性映射
     *
     * @param exportType 导出类型：filtered-筛选结果数据，all-全部数据
     * @param queryRequest 查询条件
     * @return 导出文件信息
     */
    CommonResult<ExportFileResponse> exportAttributeMapping(String exportType, AttributeMappingQueryRequest queryRequest);

    /**
     * 根据ID查询属性映射详情
     *
     * @param id 属性映射ID
     * @return 属性映射详情
     */
    CommonResult<AttributeMappingDetailResponse> getAttributeMappingById(Long id);

}

package ai.pricefox.mallfox.service.mapping;

import ai.pricefox.mallfox.model.param.AttributeValueMappingCreateRequest;
import ai.pricefox.mallfox.model.param.AttributeValueMappingEditRequest;
import ai.pricefox.mallfox.model.param.AttributeValueMappingQueryRequest;
import ai.pricefox.mallfox.model.response.AttributeValueMappingDetailResponse;
import ai.pricefox.mallfox.model.response.AttributeValueMappingPageInfo;
import ai.pricefox.mallfox.model.response.AttributeValueMappingResponse;
import ai.pricefox.mallfox.model.response.AttributeValueOption;
import ai.pricefox.mallfox.model.response.ExportFileResponse;
import ai.pricefox.mallfox.vo.base.CommonResult;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;

/**
 * 属性值映射管理Service接口
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
public interface AttributeValueMappingService {

    /**
     * 获取页面基础信息
     *
     * @param attributeId 属性映射ID
     * @return 页面基础信息
     */
    CommonResult<AttributeValueMappingPageInfo> getPageInfo(Long attributeId);

    /**
     * 查询属性值映射列表
     *
     * @param queryRequest 查询请求
     * @return 属性值映射列表
     */
    CommonResult<Page<AttributeValueMappingResponse>> getAttributeValueMappingList(AttributeValueMappingQueryRequest queryRequest);

    /**
     * 获取属性值选项
     *
     * @param attributeCode 属性编码
     * @param valueName 属性值名称
     * @return 属性值选项列表
     */
    CommonResult<List<AttributeValueOption>> getAttributeValueOptions(String attributeCode, String valueName);

    /**
     * 获取属性值映射详情
     *
     * @param valueCode 属性值编码
     * @return 属性值映射详情
     */
    CommonResult<AttributeValueMappingDetailResponse> getAttributeValueMappingDetail(String valueCode);

    /**
     * 创建属性值映射
     *
     * @param reqVO 创建请求
     * @return 创建结果
     */
    CommonResult<AttributeValueMappingDetailResponse> createAttributeValueMapping(AttributeValueMappingCreateRequest reqVO);

    /**
     * 更新属性值映射
     *
     * @param reqVO 编辑请求
     * @return 更新结果
     */
    CommonResult<Boolean> updateAttributeValueMapping(AttributeValueMappingEditRequest reqVO);

    /**
     * 删除属性值映射
     *
     * @param valueCode 属性值编码
     * @return 删除结果
     */
    CommonResult<Boolean> deleteAttributeValueMapping(String valueCode);

    /**
     * 批量删除属性值映射
     *
     * @param valueCodes 属性值编码列表
     * @return 删除结果
     */
    CommonResult<Boolean> batchDeleteAttributeValueMapping(List<String> valueCodes);

    /**
     * 导出属性值映射
     *
     * @param queryRequest 查询条件
     * @return 导出文件信息
     */
    CommonResult<ExportFileResponse> exportAttributeValueMapping(AttributeValueMappingQueryRequest queryRequest);
}
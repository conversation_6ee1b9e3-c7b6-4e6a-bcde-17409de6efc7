package ai.pricefox.mallfox.service.mapping;

import ai.pricefox.mallfox.domain.standard.StandardCategoryMapping;
import ai.pricefox.mallfox.model.param.CategoryMappingCreateRequest;
import ai.pricefox.mallfox.model.param.CategoryMappingUpdateRequest;
import ai.pricefox.mallfox.model.response.CategoryMappingBatchUpdateResultResponse;
import ai.pricefox.mallfox.model.response.CategoryMappingResponse;
import ai.pricefox.mallfox.model.response.ExportFileResponse;
import ai.pricefox.mallfox.vo.base.CommonResult;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 类目映射管理Service接口
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
public interface CategoryMappingService extends IService<StandardCategoryMapping> {

    /**
     * 获取类目映射树形结构
     *
     * @param categoryCode 类目编码（精确搜索）
     * @param categoryName 类目名称（模糊搜索）
     * @param categoryNameCn 类目中文名称（模糊搜索）
     * @return 树形结构
     */
    CommonResult<List<CategoryMappingResponse>> getCategoryMappingTree(String categoryCode, String categoryName, String categoryNameCn);

    /**
     * 根据ID查询类目映射详情
     *
     * @param id 映射ID
     * @return 映射详情
     */
    CommonResult<CategoryMappingResponse> getCategoryMappingById(Long id);

    /**
     * 创建类目映射
     *
     * @param reqVO 创建请求
     * @return 创建结果
     */
    CommonResult<CategoryMappingResponse> createCategoryMapping(CategoryMappingCreateRequest reqVO);

    /**
     * 更新类目映射
     *
     * @param reqVO 更新请求
     * @return 更新结果
     */
    CommonResult<CategoryMappingResponse> updateCategoryMapping(CategoryMappingUpdateRequest reqVO);

    /**
     * 删除类目映射
     *
     * @param id 映射ID
     * @return 删除结果
     */
    CommonResult<Boolean> deleteCategoryMapping(Long id);

    /**
     * 批量更新类目映射
     *
     * @param file Excel文件
     * @return 更新结果
     */
    CommonResult<CategoryMappingBatchUpdateResultResponse> batchUpdateCategoryMapping(MultipartFile file);

    /**
     * 下载更新模版
     *
     * @return 模版文件路径
     */
    CommonResult<String> downloadTemplate();

    /**
     * 导出类目映射
     *
     * @param categoryCode 类目编码
     * @param categoryName 类目名称
     * @param categoryNameCn 类目中文名称
     * @return 导出文件信息
     */
    CommonResult<ExportFileResponse> exportCategoryMapping(String categoryCode, String categoryName, String categoryNameCn);
}

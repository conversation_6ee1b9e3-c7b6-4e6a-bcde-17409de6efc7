package ai.pricefox.mallfox.service.mapping;

import ai.pricefox.mallfox.domain.standard.StandardCategoryMapping;
import ai.pricefox.mallfox.model.param.PlatformCategoryCreateRequest;
import ai.pricefox.mallfox.model.param.PlatformCategoryUpdateRequest;
import ai.pricefox.mallfox.model.response.ExportFileResponse;
import ai.pricefox.mallfox.model.response.PlatformCategoryResponse;
import ai.pricefox.mallfox.vo.base.CommonResult;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 平台类目管理Service接口
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
public interface PlatformCategoryService extends IService<StandardCategoryMapping> {

    /**
     * 获取平台类目树形结构
     *
     * @param platformCode 平台编码
     * @param categoryName 类目名称（可选）
     * @param categoryNameCn 类目中文名称（可选）
     * @return 树形结构
     */
    CommonResult<List<PlatformCategoryResponse>> getPlatformCategoryTree(String platformCode, String categoryName, String categoryNameCn);

    /**
     * 创建平台类目
     *
     * @param reqVO 创建请求
     * @return 创建结果
     */
    CommonResult<PlatformCategoryResponse> createPlatformCategory(PlatformCategoryCreateRequest reqVO);

    /**
     * 更新平台类目
     *
     * @param reqVO 更新请求
     * @return 更新结果
     */
    CommonResult<PlatformCategoryResponse> updatePlatformCategory(PlatformCategoryUpdateRequest reqVO);

    /**
     * 根据ID查询平台类目详情
     *
     * @param id 类目ID
     * @return 类目详情
     */
    CommonResult<PlatformCategoryResponse> getPlatformCategoryById(Long id);

    /**
     * 删除平台类目
     *
     * @param id 类目ID
     * @return 删除结果
     */
    CommonResult<Boolean> deletePlatformCategory(Long id);

    /**
     * 导出平台类目
     *
     * @param platformCode 平台编码
     * @param categoryName 类目名称（可选）
     * @param categoryNameCn 类目中文名称（可选）
     * @return 导出数据
     */
    CommonResult<ExportFileResponse> exportPlatformCategories(String platformCode, String categoryName, String categoryNameCn);
}
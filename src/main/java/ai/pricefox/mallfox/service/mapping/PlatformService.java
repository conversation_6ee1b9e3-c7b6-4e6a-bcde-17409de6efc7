package ai.pricefox.mallfox.service.mapping;

import ai.pricefox.mallfox.domain.standard.TripartitePlatform;
import ai.pricefox.mallfox.model.vo.mapping.platform.*;
import ai.pricefox.mallfox.vo.base.CommonResult;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 平台管理Service接口
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
public interface PlatformService extends IService<TripartitePlatform> {

    /**
     * 查询平台列表(不分页)
     *
     * @return 平台列表
     */
    CommonResult<List<PlatformRespVO>> getPlatformList();

    /**
     * 创建平台
     *
     * @param reqVO 创建请求
     * @return 创建结果
     */
    CommonResult<PlatformRespVO> createPlatform(PlatformCreateReqVO reqVO);

    /**
     * 更新平台
     *
     * @param reqVO 更新请求
     * @return 更新结果
     */
    CommonResult<PlatformRespVO> updatePlatform(PlatformUpdateReqVO reqVO);

    /**
     * 删除平台
     *
     * @param id 平台ID
     * @return 删除结果
     */
    CommonResult<Boolean> deletePlatform(Integer id);

    /**
     * 根据ID查询平台详情
     *
     * @param id 平台ID
     * @return 平台详情
     */
    CommonResult<PlatformRespVO> getPlatformById(Integer id);
}
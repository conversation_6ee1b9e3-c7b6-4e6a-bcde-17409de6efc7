package ai.pricefox.mallfox.service.mapping.impl;

import ai.pricefox.mallfox.common.exception.enums.ErrorCodeConstants;
import ai.pricefox.mallfox.common.util.AliOssUtil;
import ai.pricefox.mallfox.domain.standard.*;
import ai.pricefox.mallfox.mapper.standard.*;
import ai.pricefox.mallfox.model.excel.AttributeMappingImportExcelEntity;
import ai.pricefox.mallfox.model.excel.AttributeValueMappingImportExcelEntity;
import ai.pricefox.mallfox.model.excel.AttributeMappingExportExcelEntity;
import ai.pricefox.mallfox.model.excel.AttributeValueMappingExportExcelEntity;
import ai.pricefox.mallfox.model.param.AttributeMappingCreateRequest;
import ai.pricefox.mallfox.model.param.AttributeMappingQueryRequest;
import ai.pricefox.mallfox.model.param.AttributeMappingUpdateRequest;
import ai.pricefox.mallfox.model.response.AttributeMappingBatchImportResultResponse;
import ai.pricefox.mallfox.model.response.AttributeMappingDetailResponse;
import ai.pricefox.mallfox.model.response.AttributeMappingResponse;
import ai.pricefox.mallfox.model.response.ExportFileResponse;
import ai.pricefox.mallfox.service.mapping.AttributeMappingService;
import ai.pricefox.mallfox.utils.AdminTokenUtil;
import ai.pricefox.mallfox.vo.base.CommonResult;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import cn.hutool.core.date.DateUtil;
import jakarta.annotation.Resource;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static ai.pricefox.mallfox.common.exception.util.ServiceExceptionUtil.exception;
import static ai.pricefox.mallfox.common.exception.enums.ErrorCodeConstants.*;

/**
 * 属性映射管理Service实现类
 *
 * <AUTHOR>
 * @date 2025-08-03
 */
@Slf4j
@Service
@AllArgsConstructor
public class AttributeMappingServiceImpl extends ServiceImpl<StandardAttributeMappingMapper, StandardAttributeMapping> implements AttributeMappingService {

    private final StandardAttributeMappingMapper standardAttributeMappingMapper;

    private final StandardCategoryMapper standardCategoryMapper;

    private final StandardAttributeValueMappingMapper standardAttributeValueMappingMapper;

    private final StandardAttributeMapper standardAttributeMapper;

    @Resource
    private StandardAttributeValueMapper standardAttributeValueMapper;

    @Resource
    private AliOssUtil aliOssUtil;

    @Override
    public CommonResult<Page<AttributeMappingResponse>> getAttributeMappingList(AttributeMappingQueryRequest queryRequest) {
        log.info("查询属性映射列表，attributeCode: {}, attributeName: {}, categoryNames: {}",
                queryRequest.getAttributeCode(), queryRequest.getAttributeName(), queryRequest.getCategoryNames());

        try {
            // 使用自定义分页查询方法
            Page<AttributeMappingResponse> page = new Page<>(queryRequest.getPageNo(), queryRequest.getPageSize());
            Page<AttributeMappingResponse> result = standardAttributeMappingMapper.selectAttributeMappingPage(page, queryRequest);

            log.info("查询属性映射列表完成，共{}条记录", result.getTotal());
            return CommonResult.success(result);

        } catch (Exception e) {
            log.error("查询属性映射列表失败", e);
            return CommonResult.error(500, "查询失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<AttributeMappingResponse> createAttributeMapping(AttributeMappingCreateRequest reqVO) {
        log.info("创建属性映射，attributeCode: {}, mappingName: {}",
                reqVO.getAttributeCode(), reqVO.getMappingName());

        try {
            // 检查映射名称是否已存在
            LambdaQueryWrapper<StandardAttributeMapping> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(StandardAttributeMapping::getAttributeCode, reqVO.getAttributeCode())
                    .eq(StandardAttributeMapping::getMappingName, reqVO.getMappingName())
                    .eq(StandardAttributeMapping::getDeleted, false);

            StandardAttributeMapping existing = standardAttributeMappingMapper.selectOne(wrapper);
            if (existing != null) {
                throw exception(ATTRIBUTE_MAPPING_NAME_EXISTS);
            }

            // 创建属性映射
            StandardAttributeMapping mapping = new StandardAttributeMapping();
            mapping.setAttributeCode(reqVO.getAttributeCode());
            mapping.setMappingName(reqVO.getMappingName());
            mapping.setCreateDate(LocalDateTime.now());
            mapping.setCreateUsername("system");
            mapping.setDeleted(false);

            standardAttributeMappingMapper.insert(mapping);

            AttributeMappingResponse response = convertToResponse(mapping);
            log.info("创建属性映射完成，id: {}", mapping.getId());
            return CommonResult.success(response);

        } catch (Exception e) {
            log.error("创建属性映射失败", e);
            return CommonResult.error(500, "创建失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<AttributeMappingResponse> updateAttributeMapping(AttributeMappingUpdateRequest reqVO) {
        log.info("更新属性映射，id: {}", reqVO.getId());

        try {
            StandardAttributeMapping existing = standardAttributeMappingMapper.selectById(reqVO.getId());
            if (existing == null || existing.getDeleted()) {
                throw exception(ATTRIBUTE_MAPPING_NOT_EXIST);
            }

            // 检查映射名称是否已存在（排除自己）
            LambdaQueryWrapper<StandardAttributeMapping> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(StandardAttributeMapping::getAttributeCode, reqVO.getAttributeCode())
                    .eq(StandardAttributeMapping::getMappingName, reqVO.getMappingName())
                    .ne(StandardAttributeMapping::getId, reqVO.getId())
                    .eq(StandardAttributeMapping::getDeleted, false);

            StandardAttributeMapping duplicate = standardAttributeMappingMapper.selectOne(wrapper);
            if (duplicate != null) {
                throw exception(ATTRIBUTE_MAPPING_NAME_EXISTS);
            }

            // 更新属性映射
            existing.setAttributeCode(reqVO.getAttributeCode());
            existing.setMappingName(reqVO.getMappingName());
            existing.setUpdateDate(LocalDateTime.now());
            existing.setUpdateUsername("system");

            standardAttributeMappingMapper.updateById(existing);

            AttributeMappingResponse response = convertToResponse(existing);
            log.info("更新属性映射完成，id: {}", reqVO.getId());
            return CommonResult.success(response);

        } catch (Exception e) {
            log.error("更新属性映射失败，id: {}", reqVO.getId(), e);
            return CommonResult.error(500, "更新失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<Boolean> deleteAttributeMapping(Long id) {
        log.info("删除属性映射，id: {}", id);

        try {
            StandardAttributeMapping existing = standardAttributeMappingMapper.selectById(id);
            if (existing == null || existing.getDeleted()) {
                throw exception(ATTRIBUTE_MAPPING_NOT_EXIST);
            }

            String attributeCode = existing.getAttributeCode();

            // 1. 先删除该属性下的所有属性值映射关系
            LambdaQueryWrapper<StandardAttributeValueMapping> valueMappingWrapper = new LambdaQueryWrapper<>();
            valueMappingWrapper.eq(StandardAttributeValueMapping::getAttributeCode, attributeCode)
                    .eq(StandardAttributeValueMapping::getDeleted, false);

            List<StandardAttributeValueMapping> valueMappings = standardAttributeValueMappingMapper.selectList(valueMappingWrapper);

            if (!valueMappings.isEmpty()) {
                // 批量软删除属性值映射
                for (StandardAttributeValueMapping valueMapping : valueMappings) {
                    valueMapping.setDeleted(true);
                    valueMapping.setUpdateDate(LocalDateTime.now());
                    valueMapping.setUpdateUsername(AdminTokenUtil.getCurrentUsername());
                }

                // 批量更新属性值映射
                for (StandardAttributeValueMapping valueMapping : valueMappings) {
                    standardAttributeValueMappingMapper.updateById(valueMapping);
                }

                log.info("删除属性映射时，同时删除了{}个属性值映射关系", valueMappings.size());
            }

            // 2. 删除属性映射本身
            existing.setDeleted(true);
            existing.setUpdateDate(LocalDateTime.now());
            existing.setUpdateUsername(AdminTokenUtil.getCurrentUsername());
            standardAttributeMappingMapper.updateById(existing);

            log.info("删除属性映射完成，id: {}，同时删除属性值映射{}个", id, valueMappings.size());
            return CommonResult.success(true);

        } catch (Exception e) {
            log.error("删除属性映射失败，id: {}", id, e);
            return CommonResult.error(500, "删除失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<Boolean> batchDeleteAttributeMapping(List<Long> ids) {
        log.info("批量删除属性映射，ids: {}", ids);

        try {
            if (ids == null || ids.isEmpty()) {
                return CommonResult.error(400, "删除ID列表不能为空");
            }

            // 检查所有ID是否存在
            List<StandardAttributeMapping> existingMappings = standardAttributeMappingMapper.selectBatchIds(ids);
            if (existingMappings.size() != ids.size()) {
                return CommonResult.error(400, "部分属性映射不存在");
            }

            int totalValueMappingsDeleted = 0;

            // 批量处理每个属性映射
            for (StandardAttributeMapping mapping : existingMappings) {
                if (!mapping.getDeleted()) {
                    String attributeCode = mapping.getAttributeCode();

                    // 删除该属性下的所有属性值映射关系
                    LambdaQueryWrapper<StandardAttributeValueMapping> valueMappingWrapper = new LambdaQueryWrapper<>();
                    valueMappingWrapper.eq(StandardAttributeValueMapping::getAttributeCode, attributeCode)
                            .eq(StandardAttributeValueMapping::getDeleted, false);

                    List<StandardAttributeValueMapping> valueMappings = standardAttributeValueMappingMapper.selectList(valueMappingWrapper);

                    if (!valueMappings.isEmpty()) {
                        // 批量软删除属性值映射
                        for (StandardAttributeValueMapping valueMapping : valueMappings) {
                            valueMapping.setDeleted(true);
                            valueMapping.setUpdateDate(LocalDateTime.now());
                            valueMapping.setUpdateUsername(AdminTokenUtil.getCurrentUsername());
                            standardAttributeValueMappingMapper.updateById(valueMapping);
                        }
                        totalValueMappingsDeleted += valueMappings.size();
                    }

                    // 软删除属性映射
                    mapping.setDeleted(true);
                    mapping.setUpdateDate(LocalDateTime.now());
                    mapping.setUpdateUsername(AdminTokenUtil.getCurrentUsername());
                }
            }

            // 批量更新属性映射
            boolean success = updateBatchById(existingMappings);

            log.info("批量删除属性映射完成，成功删除{}条属性映射记录，{}条属性值映射记录",
                    existingMappings.size(), totalValueMappingsDeleted);
            return CommonResult.success(success);

        } catch (Exception e) {
            log.error("批量删除属性映射失败，ids: {}", ids, e);
            return CommonResult.error(500, "批量删除失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<AttributeMappingBatchImportResultResponse> batchImportAttributeMapping(MultipartFile file) {
        log.info("批量导入属性映射，文件名: {}", file.getOriginalFilename());

        try {
            // 文件格式校验
            if (!isExcelFile(file)) {
                return CommonResult.error(400, "文件格式非Excel");
            }

            AttributeMappingBatchImportResultResponse result = new AttributeMappingBatchImportResultResponse();
            StringBuilder failureDetails = new StringBuilder();

            // 2. 读取Sheet1：属性映射表
            List<AttributeMappingImportExcelEntity> attributeMappingList = EasyExcel.read(file.getInputStream())
                    .head(AttributeMappingImportExcelEntity.class)
                    .sheet(0)
                    .doReadSync();

            // 3. 读取Sheet2：属性值映射表
            List<AttributeValueMappingImportExcelEntity> attributeValueMappingList = EasyExcel.read(file.getInputStream())
                    .head(AttributeValueMappingImportExcelEntity.class)
                    .sheet(1)
                    .doReadSync();

            // 4. 处理属性映射数据
            processAttributeMappingImport(attributeMappingList, result, failureDetails);

            // 5. 处理属性值映射数据
            processAttributeValueMappingImport(attributeValueMappingList, result, failureDetails);

            result.setFailureDetails(failureDetails.toString());

            log.info("批量导入属性映射完成，属性映射成功: {}, 失败: {}, 属性值映射成功: {}, 失败: {}",
                    result.getAttributeMappingSuccess(), result.getAttributeMappingFailed(),
                    result.getAttributeValueMappingSuccess(), result.getAttributeValueMappingFailed());

            return CommonResult.success(result);

        } catch (Exception e) {
            log.error("批量导入属性映射失败", e);
            return CommonResult.error(500, "导入失败: " + e.getMessage());
        }
    }

    /**
     * 处理属性映射导入
     */
    private void processAttributeMappingImport(List<AttributeMappingImportExcelEntity> importList,
                                               AttributeMappingBatchImportResultResponse result,
                                               StringBuilder failureDetails) {
        int total = 0;
        int success = 0;
        int failed = 0;

        for (AttributeMappingImportExcelEntity importEntity : importList) {
            total++;
            try {
                // 校验属性编码
                if (!StringUtils.hasText(importEntity.getAttributeCode())) {
                    failed++;
                    failureDetails.append("第").append(total).append("行：属性编码不能为空\n");
                    continue;
                }

                // 校验属性是否存在
                StandardAttribute attribute = standardAttributeMapper.selectOne(
                        new LambdaQueryWrapper<StandardAttribute>()
                                .eq(StandardAttribute::getAttributeCode, importEntity.getAttributeCode())
                                .eq(StandardAttribute::getDeleted, false)
                );

                if (attribute == null) {
                    failed++;
                    failureDetails.append("第").append(total).append("行：属性编码不存在\n");
                    continue;
                }

                // 检查映射是否已存在
                StandardAttributeMapping existingMapping = standardAttributeMappingMapper.selectOne(
                        new LambdaQueryWrapper<StandardAttributeMapping>()
                                .eq(StandardAttributeMapping::getAttributeCode, importEntity.getAttributeCode())
                                .eq(StandardAttributeMapping::getMappingName, importEntity.getMappingName())
                                .eq(StandardAttributeMapping::getDeleted, false)
                );

                if (existingMapping != null) {
                    // 已存在，跳过（不算失败）
                    continue;
                }

                // 创建新的映射关系
                StandardAttributeMapping mapping = new StandardAttributeMapping();
                mapping.setAttributeCode(importEntity.getAttributeCode());
                mapping.setMappingName(importEntity.getMappingName());
                mapping.setCreateDate(LocalDateTime.now());
                mapping.setCreateUsername(AdminTokenUtil.getCurrentUsername());
                mapping.setDeleted(false);

                standardAttributeMappingMapper.insert(mapping);
                success++;

            } catch (Exception e) {
                failed++;
                failureDetails.append("第").append(total).append("行：").append(e.getMessage()).append("\n");
                log.error("处理属性映射导入失败，行号: {}", total, e);
            }
        }

        result.setAttributeMappingTotal(total);
        result.setAttributeMappingSuccess(success);
        result.setAttributeMappingFailed(failed);
    }

    /**
     * 处理属性值映射导入
     */
    private void processAttributeValueMappingImport(List<AttributeValueMappingImportExcelEntity> importList,
                                                    AttributeMappingBatchImportResultResponse result,
                                                    StringBuilder failureDetails) {
        int total = 0;
        int success = 0;
        int failed = 0;

        for (AttributeValueMappingImportExcelEntity importEntity : importList) {
            total++;
            try {
                // 校验属性值编码
                if (!StringUtils.hasText(importEntity.getValueCode())) {
                    failed++;
                    failureDetails.append("属性值第").append(total).append("行：属性值编码不能为空\n");
                    continue;
                }

                // 校验属性值是否存在
                StandardAttributeValue attributeValue = standardAttributeValueMapper.selectOne(
                        new LambdaQueryWrapper<StandardAttributeValue>()
                                .eq(StandardAttributeValue::getValueCode, importEntity.getValueCode())
                                .eq(StandardAttributeValue::getDeleted, false)
                );

                if (attributeValue == null) {
                    failed++;
                    failureDetails.append("属性值第").append(total).append("行：属性值编码不存在\n");
                    continue;
                }

                // 检查映射是否已存在
                StandardAttributeValueMapping existingMapping = standardAttributeValueMappingMapper.selectOne(
                        new LambdaQueryWrapper<StandardAttributeValueMapping>()
                                .eq(StandardAttributeValueMapping::getAttributeCode, attributeValue.getAttributeCode())
                                .eq(StandardAttributeValueMapping::getValueCode, importEntity.getValueCode())
                                .eq(StandardAttributeValueMapping::getValueName, importEntity.getMappingValueName())
                                .eq(StandardAttributeValueMapping::getDeleted, false)
                );

                if (existingMapping != null) {
                    // 已存在，跳过（不算失败）
                    continue;
                }

                // 创建新的映射关系
                StandardAttributeValueMapping mapping = new StandardAttributeValueMapping();
                mapping.setAttributeCode(attributeValue.getAttributeCode());
                mapping.setValueCode(importEntity.getValueCode());
                mapping.setValueName(importEntity.getMappingValueName());
                mapping.setCreateDate(LocalDateTime.now());
                mapping.setCreateUsername(AdminTokenUtil.getCurrentUsername());
                mapping.setDeleted(false);

                standardAttributeValueMappingMapper.insert(mapping);
                success++;

            } catch (Exception e) {
                failed++;
                failureDetails.append("属性值第").append(total).append("行：").append(e.getMessage()).append("\n");
                log.error("处理属性值映射导入失败，行号: {}", total, e);
            }
        }

        result.setAttributeValueMappingTotal(total);
        result.setAttributeValueMappingSuccess(success);
        result.setAttributeValueMappingFailed(failed);
    }

    /**
     * 检查是否为Excel文件
     */
    private boolean isExcelFile(MultipartFile file) {
        String fileName = file.getOriginalFilename();
        return fileName != null && (fileName.endsWith(".xlsx") || fileName.endsWith(".xls"));
    }

    @Override
    public CommonResult<ExportFileResponse> exportAttributeMapping(String exportType, AttributeMappingQueryRequest queryRequest) {
        log.info("导出属性映射，exportType: {}, queryRequest: {}", exportType, queryRequest);

        try {
            // 1. 根据导出类型决定查询条件
            AttributeMappingQueryRequest finalQueryRequest = queryRequest;
            if ("all".equals(exportType)) {
                // 全部数据：清空查询条件
                finalQueryRequest = new AttributeMappingQueryRequest();
                finalQueryRequest.setPageNo(1);
                finalQueryRequest.setPageSize(Integer.MAX_VALUE);
            } else {
                // 筛选结果数据：使用传入的查询条件
                finalQueryRequest.setPageSize(Integer.MAX_VALUE);
            }

            // 2. 查询属性映射数据
            Page<AttributeMappingResponse> page = new Page<>(finalQueryRequest.getPageNo(), finalQueryRequest.getPageSize());
            Page<AttributeMappingResponse> result = standardAttributeMappingMapper.selectAttributeMappingPage(page, finalQueryRequest);

            if (result.getRecords().isEmpty()) {
                log.info("未找到符合条件的属性映射数据");
                return CommonResult.success(new ExportFileResponse("", "", "未找到符合条件的属性映射数据"));
            }

            // 3. 查询属性值映射数据
            List<String> attributeCodes = result.getRecords().stream()
                    .map(AttributeMappingResponse::getAttributeCode)
                    .distinct()
                    .collect(Collectors.toList());

            List<StandardAttributeValueMapping> valueMappingList = standardAttributeValueMappingMapper.selectList(
                    new LambdaQueryWrapper<StandardAttributeValueMapping>()
                            .in(StandardAttributeValueMapping::getAttributeCode, attributeCodes)
                            .eq(StandardAttributeValueMapping::getDeleted, false)
                            .orderByDesc(StandardAttributeValueMapping::getCreateDate)
            );

            // 4. 构建属性映射导出数据
            List<AttributeMappingExportExcelEntity> attributeMappingExportList = buildAttributeMappingExportData(result.getRecords());

            // 5. 构建属性值映射导出数据
            List<AttributeValueMappingExportExcelEntity> attributeValueMappingExportList = buildAttributeValueMappingExportData(
                    result.getRecords(), valueMappingList);

            // 6. 生成文件名
            String fileName = "属性映射-" + DateUtil.format(DateUtil.date(), "MM-dd") + ".xlsx";

            // 7. 生成多Sheet Excel文件
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            ExcelWriter excelWriter = EasyExcel.write(outputStream).build();

            // Sheet1: 属性映射表
            WriteSheet attributeMappingSheet = EasyExcel.writerSheet(0, "属性映射表")
                    .head(AttributeMappingExportExcelEntity.class)
                    .build();
            excelWriter.write(attributeMappingExportList, attributeMappingSheet);

            // Sheet2: 属性值映射表
            WriteSheet attributeValueMappingSheet = EasyExcel.writerSheet(1, "属性值映射表")
                    .head(AttributeValueMappingExportExcelEntity.class)
                    .build();
            excelWriter.write(attributeValueMappingExportList, attributeValueMappingSheet);

            excelWriter.finish();

            // 8. 上传到OSS
            ByteArrayInputStream inputStream = new ByteArrayInputStream(outputStream.toByteArray());
            String ossUrl = aliOssUtil.uploadInputStream(inputStream, fileName, "exports/attribute-mappings");

            log.info("属性映射数据导出成功，属性映射{}条，属性值映射{}条，文件已上传到OSS: {}",
                    attributeMappingExportList.size(), attributeValueMappingExportList.size(), ossUrl);
            return CommonResult.success(new ExportFileResponse(ossUrl, fileName, "导出成功"));

        } catch (Exception e) {
            log.error("导出属性映射数据失败", e);
            throw exception(ErrorCodeConstants.ATTRIBUTE_MAPPING_EXPORT_FAILED);
        }
    }

    /**
     * 构建属性映射导出数据
     */
    private List<AttributeMappingExportExcelEntity> buildAttributeMappingExportData(List<AttributeMappingResponse> mappingList) {
        List<AttributeMappingExportExcelEntity> exportList = new ArrayList<>();

        for (AttributeMappingResponse mapping : mappingList) {
            // 每个映射名称一行
            String[] mappingNames = mapping.getMappingNames().split("、");
            for (String mappingName : mappingNames) {
                if (StringUtils.hasText(mappingName.trim())) {
                    AttributeMappingExportExcelEntity exportEntity = new AttributeMappingExportExcelEntity();
                    exportEntity.setCategoryCode(mapping.getCategoryCode());
                    exportEntity.setCategoryName(mapping.getCategoryName());
                    exportEntity.setCategoryNameCn(mapping.getCategoryNameCn());
                    exportEntity.setCategoryPath(buildCategoryPath(mapping.getCategoryCode()));
                    exportEntity.setAttributeCode(mapping.getAttributeCode());
                    exportEntity.setAttributeNameCn(mapping.getAttributeNameCn());
                    exportEntity.setAttributeNameEn(mapping.getAttributeNameEn());
                    exportEntity.setMappingName(mappingName.trim());
                    exportList.add(exportEntity);
                }
            }
        }

        return exportList;
    }

    /**
     * 构建属性值映射导出数据
     */
    private List<AttributeValueMappingExportExcelEntity> buildAttributeValueMappingExportData(
            List<AttributeMappingResponse> mappingList, List<StandardAttributeValueMapping> valueMappingList) {

        List<AttributeValueMappingExportExcelEntity> exportList = new ArrayList<>();

        // 按属性编码分组
        Map<String, AttributeMappingResponse> attributeMap = mappingList.stream()
                .collect(Collectors.toMap(AttributeMappingResponse::getAttributeCode, a -> a));

        for (StandardAttributeValueMapping valueMapping : valueMappingList) {
            AttributeMappingResponse attributeMapping = attributeMap.get(valueMapping.getAttributeCode());
            if (attributeMapping != null) {
                AttributeValueMappingExportExcelEntity exportEntity = new AttributeValueMappingExportExcelEntity();
                exportEntity.setCategoryCode(attributeMapping.getCategoryCode());
                exportEntity.setCategoryName(attributeMapping.getCategoryName());
                exportEntity.setCategoryNameCn(attributeMapping.getCategoryNameCn());
                exportEntity.setCategoryPath(buildCategoryPath(attributeMapping.getCategoryCode()));
                exportEntity.setAttributeCode(valueMapping.getAttributeCode());
                exportEntity.setAttributeNameEn(attributeMapping.getAttributeNameEn());
                exportEntity.setAttributeNameCn(attributeMapping.getAttributeNameCn());
                exportEntity.setValueCode(valueMapping.getValueCode());
                exportEntity.setValueNameCn(getValueNameCn(valueMapping.getValueCode()));
                exportEntity.setValueNameEn(getValueNameEn(valueMapping.getValueCode()));
                exportEntity.setMappingValueName(valueMapping.getValueName());
                exportList.add(exportEntity);
            }
        }

        return exportList;
    }

    /**
     * 构建类目路径
     */
    private String buildCategoryPath(String categoryCode) {
        // 直接使用 Mapper 中的方法构建类目路径
        return standardCategoryMapper.buildCategoryPath(categoryCode);
    }

    @Override
    public CommonResult<AttributeMappingDetailResponse> getAttributeMappingById(Long id) {
        log.info("查询属性映射详情，id: {}", id);

        try {
            AttributeMappingDetailResponse response = standardAttributeMappingMapper.selectAttributeMappingDetailById(id);
            if (response == null) {
                throw exception(ATTRIBUTE_MAPPING_NOT_EXIST);
            }

            log.info("查询属性映射详情完成，id: {}", id);
            return CommonResult.success(response);

        } catch (Exception e) {
            log.error("查询属性映射详情失败，id: {}", id, e);
            return CommonResult.error(500, "查询失败: " + e.getMessage());
        }
    }

    /**
     * 转换为响应对象
     */
    private AttributeMappingResponse convertToResponse(StandardAttributeMapping mapping) {
        AttributeMappingResponse response = new AttributeMappingResponse();
        BeanUtils.copyProperties(mapping, response);
        return response;
    }

    /**
     * 获取属性值中文名称
     */
    private String getValueNameCn(String valueCode) {
        StandardAttributeValue attributeValue = standardAttributeValueMapper.selectOne(
                new LambdaQueryWrapper<StandardAttributeValue>()
                        .eq(StandardAttributeValue::getValueCode, valueCode)
                        .eq(StandardAttributeValue::getDeleted, false)
        );
        return attributeValue != null ? attributeValue.getValueCn() : "";
    }

    /**
     * 获取属性值英文名称
     */
    private String getValueNameEn(String valueCode) {
        StandardAttributeValue attributeValue = standardAttributeValueMapper.selectOne(
                new LambdaQueryWrapper<StandardAttributeValue>()
                        .eq(StandardAttributeValue::getValueCode, valueCode)
                        .eq(StandardAttributeValue::getDeleted, false)
        );
        return attributeValue != null ? attributeValue.getValueEn() : "";
    }

    /**
     * 批量更新实体
     */
    private boolean updateBatchById(List<StandardAttributeMapping> mappings) {
        for (StandardAttributeMapping mapping : mappings) {
            standardAttributeMappingMapper.updateById(mapping);
        }
        return true;
    }
}

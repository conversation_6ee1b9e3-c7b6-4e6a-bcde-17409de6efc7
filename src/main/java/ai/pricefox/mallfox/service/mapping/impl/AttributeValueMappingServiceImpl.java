package ai.pricefox.mallfox.service.mapping.impl;

import ai.pricefox.mallfox.common.util.AliOssUtil;
import ai.pricefox.mallfox.domain.standard.StandardAttributeValue;
import ai.pricefox.mallfox.domain.standard.StandardAttributeValueMapping;
import ai.pricefox.mallfox.mapper.standard.StandardAttributeMappingMapper;
import ai.pricefox.mallfox.mapper.standard.StandardAttributeValueMapper;
import ai.pricefox.mallfox.mapper.standard.StandardAttributeValueMappingMapper;
import ai.pricefox.mallfox.model.excel.AttributeValueMappingExportExcelEntity;
import ai.pricefox.mallfox.model.param.AttributeValueMappingCreateRequest;
import ai.pricefox.mallfox.model.param.AttributeValueMappingEditRequest;
import ai.pricefox.mallfox.model.param.AttributeValueMappingQueryRequest;
import ai.pricefox.mallfox.model.response.*;
import ai.pricefox.mallfox.service.mapping.AttributeValueMappingService;
import ai.pricefox.mallfox.utils.AdminTokenUtil;
import ai.pricefox.mallfox.vo.base.CommonResult;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.annotation.Resource;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 属性值映射管理Service实现类
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
@Slf4j
@Service
@AllArgsConstructor
public class AttributeValueMappingServiceImpl extends ServiceImpl<StandardAttributeValueMappingMapper, StandardAttributeValueMapping> implements AttributeValueMappingService {

    private final StandardAttributeValueMappingMapper standardAttributeValueMappingMapper;
    private final StandardAttributeValueMapper standardAttributeValueMapper;

    @Resource
    private AliOssUtil aliOssUtil;

    @Override
    public CommonResult<AttributeValueMappingPageInfo> getPageInfo(Long attributeId) {
        log.info("获取属性值映射页面基础信息，attributeId: {}", attributeId);

        try {
            AttributeValueMappingPageInfo pageInfo = standardAttributeValueMappingMapper.selectPageInfoByAttributeId(attributeId);
            if (pageInfo == null) {
                return CommonResult.error(404, "属性映射不存在");
            }

            log.info("获取页面基础信息完成，attributeId: {}", attributeId);
            return CommonResult.success(pageInfo);

        } catch (Exception e) {
            log.error("获取页面基础信息失败，attributeId: {}", attributeId, e);
            return CommonResult.error(500, "获取失败: " + e.getMessage());
        }
    }

    @Override
    public CommonResult<Page<AttributeValueMappingResponse>> getAttributeValueMappingList(AttributeValueMappingQueryRequest queryRequest) {
        log.info("查询属性值映射列表，attributeId: {}, valueCode: {}, valueName: {}, valueNameCn: {}",
                queryRequest.getAttributeId(), queryRequest.getValueCode(), queryRequest.getValueName(), queryRequest.getValueNameCn());

        try {
            Page<AttributeValueMappingResponse> page = new Page<>(queryRequest.getPageNo(), queryRequest.getPageSize());
            Page<AttributeValueMappingResponse> result = standardAttributeValueMappingMapper.selectAttributeValueMappingPage(page, queryRequest);

            log.info("查询属性值映射列表完成，共{}条记录", result.getTotal());
            return CommonResult.success(result);

        } catch (Exception e) {
            log.error("查询属性值映射列表失败", e);
            return CommonResult.error(500, "查询失败: " + e.getMessage());
        }
    }

    @Override
    public CommonResult<List<AttributeValueOption>> getAttributeValueOptions(String attributeCode, String valueName) {
        log.info("获取属性值选项，attributeCode: {}, valueName: {}", attributeCode, valueName);

        try {
            List<AttributeValueOption> options = standardAttributeValueMappingMapper.selectAttributeValueOptions(attributeCode, valueName);

            log.info("获取属性值选项完成，共{}条记录", options.size());
            return CommonResult.success(options);

        } catch (Exception e) {
            log.error("获取属性值选项失败", e);
            return CommonResult.error(500, "获取失败: " + e.getMessage());
        }
    }

    @Override
    public CommonResult<AttributeValueMappingDetailResponse> getAttributeValueMappingDetail(String valueCode) {
        log.info("获取属性值映射详情，valueCode: {}", valueCode);

        try {
            AttributeValueMappingDetailResponse detail = standardAttributeValueMappingMapper.selectAttributeValueMappingDetail(valueCode);
            if (detail == null) {
                return CommonResult.error(404, "属性值不存在");
            }

            log.info("获取属性值映射详情完成，valueCode: {}", valueCode);
            return CommonResult.success(detail);

        } catch (Exception e) {
            log.error("获取属性值映射详情失败，valueCode: {}", valueCode, e);
            return CommonResult.error(500, "获取失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<AttributeValueMappingDetailResponse> createAttributeValueMapping(AttributeValueMappingCreateRequest reqVO) {
        log.info("创建属性值映射，attributeId: {}, valueCode: {}", reqVO.getAttributeId(), reqVO.getValueCode());

        try {
            // 检查属性值是否已存在映射
            AttributeValueMappingDetailResponse existingDetail = standardAttributeValueMappingMapper.selectAttributeValueMappingDetail(reqVO.getValueCode());

            if (existingDetail != null && !CollectionUtils.isEmpty(existingDetail.getMappingValues())) {
                // 已存在映射，返回现有数据
                log.info("属性值映射已存在，valueCode: {}", reqVO.getValueCode());
                return CommonResult.success(existingDetail);
            } else {
                // 不存在映射，返回基础信息供用户填写
                AttributeValueMappingDetailResponse detail = standardAttributeValueMappingMapper.selectAttributeValueMappingDetail(reqVO.getValueCode());
                if (detail == null) {
                    return CommonResult.error(404, "属性值不存在");
                }

                log.info("创建属性值映射完成，valueCode: {}", reqVO.getValueCode());
                return CommonResult.success(detail);
            }

        } catch (Exception e) {
            log.error("创建属性值映射失败", e);
            return CommonResult.error(500, "创建失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<Boolean> updateAttributeValueMapping(AttributeValueMappingEditRequest reqVO) {
        log.info("编辑属性值映射，valueCode: {}, mappingValues: {}", reqVO.getValueCode(), reqVO.getMappingValueNames());

        try {
            // 获取属性编码
            StandardAttributeValue attributeValue = standardAttributeValueMapper.selectOne(
                    new LambdaQueryWrapper<StandardAttributeValue>()
                            .eq(StandardAttributeValue::getValueCode, reqVO.getValueCode())
                            .eq(StandardAttributeValue::getDeleted, false)
            );

            if (attributeValue == null) {
                return CommonResult.error(404, "属性值不存在");
            }

            // 先删除现有映射
            standardAttributeValueMappingMapper.deleteByValueCode(reqVO.getValueCode());

            // 过滤空值并添加新映射
            if (!CollectionUtils.isEmpty(reqVO.getMappingValueNames())) {
                List<String> validMappingNames = reqVO.getMappingValueNames().stream()
                        .filter(StringUtils::hasText)
                        .collect(Collectors.toList());

                for (String mappingName : validMappingNames) {
                    StandardAttributeValueMapping mapping = new StandardAttributeValueMapping();
                    mapping.setAttributeCode(attributeValue.getAttributeCode());
                    mapping.setValueCode(reqVO.getValueCode());
                    mapping.setValueName(mappingName);
                    mapping.setDeleted(false);
                    mapping.setCreateDate(LocalDateTime.now());
                    mapping.setUpdateDate(LocalDateTime.now());
                    mapping.setCreateUsername(ai.pricefox.mallfox.utils.AdminTokenUtil.getCurrentUsername());
                    mapping.setUpdateUsername(AdminTokenUtil.getCurrentUsername());

                    standardAttributeValueMappingMapper.insert(mapping);
                }
            }

            log.info("编辑属性值映射完成，valueCode: {}", reqVO.getValueCode());
            return CommonResult.success(true);

        } catch (Exception e) {
            log.error("编辑属性值映射失败", e);
            return CommonResult.error(500, "编辑失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<Boolean> deleteAttributeValueMapping(String valueCode) {
        log.info("删除属性值映射，valueCode: {}", valueCode);

        try {
            standardAttributeValueMappingMapper.deleteByValueCode(valueCode);

            log.info("删除属性值映射完成，valueCode: {}", valueCode);
            return CommonResult.success(true);

        } catch (Exception e) {
            log.error("删除属性值映射失败，valueCode: {}", valueCode, e);
            return CommonResult.error(500, "删除失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<Boolean> batchDeleteAttributeValueMapping(List<String> valueCodes) {
        log.info("批量删除属性值映射，valueCodes: {}", valueCodes);

        try {
            if (CollectionUtils.isEmpty(valueCodes)) {
                return CommonResult.error(400, "请先勾选属性值");
            }

            for (String valueCode : valueCodes) {
                standardAttributeValueMappingMapper.deleteByValueCode(valueCode);
            }

            log.info("批量删除属性值映射完成，删除数量: {}", valueCodes.size());
            return CommonResult.success(true);

        } catch (Exception e) {
            log.error("批量删除属性值映射失败，valueCodes: {}", valueCodes, e);
            return CommonResult.error(500, "批量删除失败: " + e.getMessage());
        }
    }

    @Override
    public CommonResult<ExportFileResponse> exportAttributeValueMapping(AttributeValueMappingQueryRequest queryRequest) {
        log.info("导出属性值映射，attributeId: {}", queryRequest.getAttributeId());

        try {
            // 查询导出数据
            List<AttributeValueMappingExportExcelEntity> exportData = standardAttributeValueMappingMapper.selectExportData(queryRequest);

            if (CollectionUtils.isEmpty(exportData)) {
                return CommonResult.error(400, "没有可导出的数据");
            }

            // 生成文件名
            String fileName = "属性值映射-" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("MM-dd")) + ".xlsx";

            // 使用ByteArrayOutputStream生成Excel文件到内存
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            EasyExcel.write(outputStream, AttributeValueMappingExportExcelEntity.class)
                    .sheet("属性值映射")
                    .doWrite(exportData);

            // 将ByteArrayOutputStream转换为InputStream
            ByteArrayInputStream inputStream = new ByteArrayInputStream(outputStream.toByteArray());

            // 上传到OSS
            String fileUrl = aliOssUtil.uploadInputStream(inputStream, fileName, "exports/attribute-value-mappings");

            log.info("导出属性值映射完成，文件: {}, 记录数: {}", fileName, exportData.size());
            return CommonResult.success(new ExportFileResponse(fileUrl, fileName, "导出成功"));

        } catch (Exception e) {
            log.error("导出属性值映射失败", e);
            return CommonResult.error(500, "导出失败: " + e.getMessage());
        }
    }
}
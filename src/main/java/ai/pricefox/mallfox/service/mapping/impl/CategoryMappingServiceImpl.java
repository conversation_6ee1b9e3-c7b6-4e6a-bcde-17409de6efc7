package ai.pricefox.mallfox.service.mapping.impl;

import ai.pricefox.mallfox.common.exception.enums.ErrorCodeConstants;
import ai.pricefox.mallfox.common.util.AliOssUtil;
import ai.pricefox.mallfox.domain.standard.StandardCategory;
import ai.pricefox.mallfox.domain.standard.StandardCategoryMapping;
import ai.pricefox.mallfox.domain.standard.TripartitePlatform;
import ai.pricefox.mallfox.mapper.standard.StandardCategoryMapper;
import ai.pricefox.mallfox.mapper.standard.StandardCategoryMappingMapper;
import ai.pricefox.mallfox.mapper.standard.TripartitePlatformMapper;
import ai.pricefox.mallfox.model.excel.CategoryMappingImportExcelEntity;
import ai.pricefox.mallfox.model.param.CategoryMappingCreateRequest;
import ai.pricefox.mallfox.model.param.CategoryMappingUpdateRequest;
import ai.pricefox.mallfox.model.response.CategoryMappingBatchUpdateResultResponse;
import ai.pricefox.mallfox.model.response.CategoryMappingResponse;
import ai.pricefox.mallfox.model.response.ExportFileResponse;
import ai.pricefox.mallfox.service.mapping.CategoryMappingService;
import ai.pricefox.mallfox.utils.AdminTokenUtil;
import ai.pricefox.mallfox.vo.base.CommonResult;
import cn.hutool.core.date.DateUtil;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static ai.pricefox.mallfox.common.exception.util.ServiceExceptionUtil.exception;

/**
 * 类目映射管理Service实现类
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
@Slf4j
@Service
@AllArgsConstructor
public class CategoryMappingServiceImpl extends ServiceImpl<StandardCategoryMappingMapper, StandardCategoryMapping> implements CategoryMappingService {

    private final StandardCategoryMappingMapper standardCategoryMappingMapper;
    private final StandardCategoryMapper standardCategoryMapper;
    private final TripartitePlatformMapper tripartitePlatformMapper;
    private final AliOssUtil aliOssUtil;

    @Override
    public CommonResult<List<CategoryMappingResponse>> getCategoryMappingTree(String categoryCode, String categoryName, String categoryNameCn) {
        log.info("查询类目映射树形结构，categoryCode: {}, categoryName: {}, categoryNameCn: {}",
                categoryCode, categoryName, categoryNameCn);

        try {
            // 构建查询条件
            LambdaQueryWrapper<StandardCategoryMapping> wrapper = new LambdaQueryWrapper<>();

            // 类目编码精确匹配
            if (StringUtils.hasText(categoryCode)) {
                wrapper.eq(StandardCategoryMapping::getStandardCategoryCode, categoryCode);
            }

            // 类目名称模糊匹配
            if (StringUtils.hasText(categoryName)) {
                wrapper.like(StandardCategoryMapping::getStandardCategoryName, categoryName);
            }

            // 类目中文名称模糊匹配
            if (StringUtils.hasText(categoryNameCn)) {
                wrapper.like(StandardCategoryMapping::getPlatformCategoryNameCn, categoryNameCn);
            }

            wrapper.eq(StandardCategoryMapping::getDeleted, false)
                    .orderByAsc(StandardCategoryMapping::getStandardLevel, StandardCategoryMapping::getId);

            List<StandardCategoryMapping> mappingList = standardCategoryMappingMapper.selectList(wrapper);

            // 转换为树形结构
            List<CategoryMappingResponse> treeList = buildCategoryMappingTree(mappingList);

            log.info("查询类目映射树形结构完成，共{}条记录", treeList.size());
            return CommonResult.success(treeList);

        } catch (Exception e) {
            log.error("查询类目映射树形结构失败", e);
            return CommonResult.error(500, "查询失败: " + e.getMessage());
        }
    }

    @Override
    public CommonResult<CategoryMappingResponse> getCategoryMappingById(Long id) {
        log.info("查询类目映射详情，id: {}", id);

        try {
            StandardCategoryMapping mapping = standardCategoryMappingMapper.selectById(id);
            if (mapping == null || mapping.getDeleted()) {
                return CommonResult.error(404, "类目映射不存在");
            }

            CategoryMappingResponse response = convertToResponse(mapping);
            log.info("查询类目映射详情完成，id: {}", id);
            return CommonResult.success(response);

        } catch (Exception e) {
            log.error("查询类目映射详情失败，id: {}", id, e);
            return CommonResult.error(500, "查询失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<CategoryMappingResponse> createCategoryMapping(CategoryMappingCreateRequest reqVO) {
        log.info("创建类目映射，standardCategoryId: {}", reqVO.getStandardCategoryId());

        try {
            // 查询标准类目信息
            StandardCategory standardCategory = standardCategoryMapper.selectById(reqVO.getStandardCategoryId());
            if (standardCategory == null || standardCategory.getDeleted()) {
                return CommonResult.error(400, "标准类目不存在");
            }

            // 处理平台映射关系
            if (reqVO.getPlatformMappings() != null && !reqVO.getPlatformMappings().isEmpty()) {
                for (CategoryMappingCreateRequest.PlatformMappingItem platformMapping : reqVO.getPlatformMappings()) {
                    // 检查是否已存在相同的平台映射
                    LambdaQueryWrapper<StandardCategoryMapping> wrapper = new LambdaQueryWrapper<>();
                    wrapper.eq(StandardCategoryMapping::getPlatformCode, platformMapping.getPlatformCode())
                            .eq(StandardCategoryMapping::getPlatformCategoryName, platformMapping.getPlatformCategoryName())
                            .eq(StandardCategoryMapping::getDeleted, false);

                    StandardCategoryMapping existing = standardCategoryMappingMapper.selectOne(wrapper);
                    if (existing != null) {
                        return CommonResult.error(400, "平台类目映射已存在: " + platformMapping.getPlatformCategoryName());
                    }

                    // 创建映射记录
                    StandardCategoryMapping mapping = new StandardCategoryMapping();
                    mapping.setPlatformCode(platformMapping.getPlatformCode());
                    mapping.setStandardCategoryCode(standardCategory.getCategoryCode());
                    mapping.setStandardCategoryName(standardCategory.getCategoryNameEn());
                    mapping.setStandardLevel(standardCategory.getLevel());
                    mapping.setPlatformCategoryName(platformMapping.getPlatformCategoryName());
                    mapping.setPlatformCategoryNameCn(platformMapping.getPlatformCategoryName());
                    // 默认为1级
                    mapping.setPlatformLevel(1);
                    mapping.setCreateDate(LocalDateTime.now());
                    mapping.setCreateUsername("system");
                    mapping.setDeleted(false);

                    standardCategoryMappingMapper.insert(mapping);
                }
            }

            log.info("创建类目映射完成，standardCategoryId: {}", reqVO.getStandardCategoryId());
            return CommonResult.success(new CategoryMappingResponse());

        } catch (Exception e) {
            log.error("创建类目映射失败", e);
            return CommonResult.error(500, "创建失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<CategoryMappingResponse> updateCategoryMapping(CategoryMappingUpdateRequest reqVO) {
        log.info("更新类目映射，id: {}", reqVO.getId());

        try {
            StandardCategoryMapping existing = standardCategoryMappingMapper.selectById(reqVO.getId());
            if (existing == null || existing.getDeleted()) {
                return CommonResult.error(404, "类目映射不存在");
            }

            // 查询新的标准类目信息
            StandardCategory standardCategory = standardCategoryMapper.selectById(reqVO.getStandardCategoryId());
            if (standardCategory == null || standardCategory.getDeleted()) {
                return CommonResult.error(400, "标准类目不存在");
            }

            // 更新映射记录
            existing.setStandardCategoryCode(standardCategory.getCategoryCode());
            existing.setStandardCategoryName(standardCategory.getCategoryNameEn());
            existing.setStandardLevel(standardCategory.getLevel());
            existing.setUpdateDate(LocalDateTime.now());
            existing.setUpdateUsername("system");

            standardCategoryMappingMapper.updateById(existing);

            CategoryMappingResponse response = convertToResponse(existing);
            log.info("更新类目映射完成，id: {}", reqVO.getId());
            return CommonResult.success(response);

        } catch (Exception e) {
            log.error("更新类目映射失败，id: {}", reqVO.getId(), e);
            return CommonResult.error(500, "更新失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<Boolean> deleteCategoryMapping(Long id) {
        log.info("删除类目映射，id: {}", id);

        try {
            StandardCategoryMapping existing = standardCategoryMappingMapper.selectById(id);
            if (existing == null || existing.getDeleted()) {
                return CommonResult.error(404, "类目映射不存在");
            }

            // 软删除
            existing.setDeleted(true);
            existing.setUpdateDate(LocalDateTime.now());
            existing.setUpdateUsername("system");
            standardCategoryMappingMapper.updateById(existing);

            log.info("删除类目映射完成，id: {}", id);
            return CommonResult.success(true);

        } catch (Exception e) {
            log.error("删除类目映射失败，id: {}", id, e);
            return CommonResult.error(500, "删除失败: " + e.getMessage());
        }
    }

    /**
     * 构建树形结构
     */
    private List<CategoryMappingResponse> buildCategoryMappingTree(List<StandardCategoryMapping> mappingList) {
        // 按标准类目分组
        Map<String, List<StandardCategoryMapping>> groupedMappings = mappingList.stream()
                .collect(Collectors.groupingBy(StandardCategoryMapping::getStandardCategoryCode));

        List<CategoryMappingResponse> result = new ArrayList<>();

        for (Map.Entry<String, List<StandardCategoryMapping>> entry : groupedMappings.entrySet()) {
            List<StandardCategoryMapping> mappings = entry.getValue();
            if (!mappings.isEmpty()) {
                CategoryMappingResponse response = convertToResponse(mappings.get(0));

                // 设置平台映射列表
                List<CategoryMappingResponse.PlatformMappingInfo> platformMappings = mappings.stream()
                        .map(this::convertToPlatformMappingInfo)
                        .collect(Collectors.toList());
                response.setPlatformMappings(platformMappings);

                result.add(response);
            }
        }

        return result;
    }

    /**
     * 转换为响应对象
     */
    private CategoryMappingResponse convertToResponse(StandardCategoryMapping mapping) {
        CategoryMappingResponse response = new CategoryMappingResponse();
        response.setId(mapping.getId());
        response.setCreateTime(mapping.getCreateDate());
        response.setUpdateTime(mapping.getUpdateDate());

        // 设置标准类目信息
        CategoryMappingResponse.StandardCategoryInfo standardCategory = new CategoryMappingResponse.StandardCategoryInfo();
        standardCategory.setCategoryCode(mapping.getStandardCategoryCode());
        standardCategory.setCategoryName(mapping.getStandardCategoryName());
        standardCategory.setLevel(mapping.getStandardLevel());
        response.setStandardCategory(standardCategory);

        return response;
    }

    /**
     * 转换为平台映射信息
     */
    private CategoryMappingResponse.PlatformMappingInfo convertToPlatformMappingInfo(StandardCategoryMapping mapping) {
        CategoryMappingResponse.PlatformMappingInfo platformMapping = new CategoryMappingResponse.PlatformMappingInfo();
        platformMapping.setPlatformCode(mapping.getPlatformCode());
        platformMapping.setPlatformCategoryName(mapping.getPlatformCategoryName());
        platformMapping.setPlatformCategoryNameCn(mapping.getPlatformCategoryNameCn());
        return platformMapping;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<CategoryMappingBatchUpdateResultResponse> batchUpdateCategoryMapping(MultipartFile file) {
        log.info("批量更新类目映射，文件名: {}", file.getOriginalFilename());

        try {
            // 文件格式校验
            if (file.isEmpty()) {
                return CommonResult.error(400, "文件不能为空");
            }

            String fileName = file.getOriginalFilename();
            if (fileName == null || (!fileName.endsWith(".xlsx") && !fileName.endsWith(".xls"))) {
                return CommonResult.error(400, "文件格式非Excel");
            }

            //  读取Excel文件
            List<CategoryMappingImportExcelEntity> importList;
            try {
                importList = EasyExcel.read(file.getInputStream())
                        .head(CategoryMappingImportExcelEntity.class)
                        .sheet()
                        .doReadSync();
            } catch (Exception e) {
                return CommonResult.error(400, "模版格式错误，请下载更新模版");
            }

            if (importList.isEmpty()) {
                return CommonResult.error(400, "Excel文件内容为空");
            }

            // 数据校验和处理
            CategoryMappingBatchUpdateResultResponse result = new CategoryMappingBatchUpdateResultResponse();
            result.setTotalCount(importList.size());
            result.setSuccessCount(0);
            result.setFailureCount(0);

            Map<String, Set<String>> processedMappings = new HashMap<>();

            for (CategoryMappingImportExcelEntity importEntity : importList) {
                try {
                    // 数据校验
                    if (!StringUtils.hasText(importEntity.getCategoryCode()) ||
                            !StringUtils.hasText(importEntity.getPlatformCode()) ||
                            !StringUtils.hasText(importEntity.getMappingCategoryName())) {
                        result.setFailureCount(result.getFailureCount() + 1);
                        continue;
                    }

                    // 去重处理
                    String key = importEntity.getCategoryCode() + "_" + importEntity.getPlatformCode();
                    if (processedMappings.containsKey(key)) {
                        continue; // 跳过重复数据
                    }
                    processedMappings.computeIfAbsent(key, k -> new HashSet<>()).add(importEntity.getMappingCategoryName());

                    // 校验类目编码是否存在
                    StandardCategory category = standardCategoryMapper.selectOne(new LambdaQueryWrapper<StandardCategory>()
                            .eq(StandardCategory::getCategoryCode, importEntity.getCategoryCode())
                            .eq(StandardCategory::getDeleted, false));
                    if (category == null) {
                        log.error("类目编码不存在: {}", importEntity.getCategoryCode());
                        result.setFailureCount(result.getFailureCount() + 1);
                        continue;
                    }

                    // 校验平台编码是否正确
                    if (!StringUtils.hasText(importEntity.getPlatformCode())) {
                        log.error("平台编码不能为空: {}", importEntity.getPlatformCode());
                        result.setFailureCount(result.getFailureCount() + 1);
                        continue;
                    }
                    TripartitePlatform platform = tripartitePlatformMapper.selectOne(new LambdaQueryWrapper<TripartitePlatform>()
                            .eq(TripartitePlatform::getPlatformCode, importEntity.getPlatformCode())
                            .eq(TripartitePlatform::getEnable, true));
                    if (platform == null) {
                        log.error("平台编码不存在: {}", importEntity.getPlatformCode());
                        result.setFailureCount(result.getFailureCount() + 1);
                        continue;
                    }
                    // 增量更新映射关系
                    updateCategoryMappingIncremental(category, importEntity);
                    result.setSuccessCount(result.getSuccessCount() + 1);

                } catch (Exception e) {
                    log.error("处理类目映射数据失败: {}", importEntity, e);
                    result.setFailureCount(result.getFailureCount() + 1);
                }
            }

            result.setSuccess(result.getFailureCount() == 0);
            result.setResultMessage(String.format("类目映射更新数量%d，成功%d，失败%d",
                    result.getTotalCount(), result.getSuccessCount(), result.getFailureCount()));

            log.info("批量更新类目映射完成: {}", result.getResultMessage());
            return CommonResult.success(result);

        } catch (Exception e) {
            log.error("批量更新类目映射失败", e);
            return CommonResult.error(500, "批量更新失败: " + e.getMessage());
        }
    }

    @Override
    public CommonResult<String> downloadTemplate() {
        log.info("下载类目映射更新模版");

        try {
            // 生成模版文件
            String fileName = "类目映射更新模版.xlsx";

            // 创建模版数据
            List<CategoryMappingImportExcelEntity> templateData = new ArrayList<>();
            CategoryMappingImportExcelEntity example = new CategoryMappingImportExcelEntity();
            example.setCategoryCode("CAT001");
            example.setPlatformCode("TMALL");
            example.setMappingCategoryName("电子产品");
            templateData.add(example);

            // 使用ByteArrayOutputStream生成Excel文件到内存
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            EasyExcel.write(outputStream, CategoryMappingImportExcelEntity.class)
                    .sheet("类目映射更新模版")
                    .doWrite(templateData);

            // TODO: 将文件上传到文件服务器并返回下载链接
            // 这里返回临时路径，实际应该上传到OSS等文件服务
            String downloadUrl = "/template/" + fileName;

            log.info("下载类目映射更新模版完成");
            return CommonResult.success(downloadUrl);

        } catch (Exception e) {
            log.error("下载类目映射更新模版失败", e);
            return CommonResult.error(500, "下载失败: " + e.getMessage());
        }
    }

    @Override
    public CommonResult<ExportFileResponse> exportCategoryMapping(String categoryCode, String categoryName, String categoryNameCn) {
        log.info("导出类目映射，categoryCode: {}, categoryName: {}, categoryNameCn: {}",
                categoryCode, categoryName, categoryNameCn);

        try {
            // 1. 查询标准类目数据（主表）
            LambdaQueryWrapper<StandardCategory> categoryWrapper = new LambdaQueryWrapper<>();
            if (StringUtils.hasText(categoryCode)) {
                categoryWrapper.eq(StandardCategory::getCategoryCode, categoryCode);
            }
            if (StringUtils.hasText(categoryName)) {
                categoryWrapper.like(StandardCategory::getCategoryNameEn, categoryName);
            }
            if (StringUtils.hasText(categoryNameCn)) {
                categoryWrapper.like(StandardCategory::getCategoryNameCn, categoryNameCn);
            }
            categoryWrapper.eq(StandardCategory::getDeleted, false)
                    .orderByAsc(StandardCategory::getLevel, StandardCategory::getCategoryNameEn);

            List<StandardCategory> categoryList = standardCategoryMapper.selectList(categoryWrapper);

            if (categoryList.isEmpty()) {
                log.info("未找到符合条件的类目数据");
                return CommonResult.success(new ExportFileResponse("", "", "未找到符合条件的类目数据"));
            }

            //  查询类目映射关系
            List<String> categoryCodes = categoryList.stream()
                    .map(StandardCategory::getCategoryCode)
                    .collect(Collectors.toList());

            List<StandardCategoryMapping> mappingList = standardCategoryMappingMapper.selectList(
                    new LambdaQueryWrapper<StandardCategoryMapping>()
                            .in(StandardCategoryMapping::getStandardCategoryCode, categoryCodes)
                            .eq(StandardCategoryMapping::getDeleted, false)
            );

            //  获取所有涉及的平台编码
            Set<String> platformCodes = mappingList.stream()
                    .map(StandardCategoryMapping::getPlatformCode)
                    .collect(Collectors.toSet());

            //  查询平台信息
            Map<String, TripartitePlatform> platformMap;
            if (!platformCodes.isEmpty()) {
                List<TripartitePlatform> platformList = tripartitePlatformMapper.selectList(
                        new LambdaQueryWrapper<TripartitePlatform>()
                                .in(TripartitePlatform::getPlatformCode, platformCodes)
                                .eq(TripartitePlatform::getEnable, true)
                );
                platformMap = platformList.stream()
                        .collect(Collectors.toMap(TripartitePlatform::getPlatformCode, p -> p));
            } else {
                platformMap = new HashMap<>();
            }

            //  按类目编码分组映射关系
            Map<String, List<StandardCategoryMapping>> mappingMap = mappingList.stream()
                    .collect(Collectors.groupingBy(StandardCategoryMapping::getStandardCategoryCode));

            // 获取所有平台编码并排序（用于动态生成Excel列）
            List<String> sortedPlatformCodes = platformCodes.stream()
                    .sorted()
                    .collect(Collectors.toList());

            // 组装导出数据
            List<Map<String, Object>> exportDataList = new ArrayList<>();

            for (StandardCategory category : categoryList) {
                Map<String, Object> rowData = new LinkedHashMap<>();

                // 设置基本类目信息
                rowData.put("categoryCode", category.getCategoryCode());
                rowData.put("categoryName", category.getCategoryNameEn());
                rowData.put("categoryNameCn", category.getCategoryNameCn());
                rowData.put("level", category.getLevel());

                // 获取该类目的所有映射关系
                List<StandardCategoryMapping> categoryMappings = mappingMap.getOrDefault(
                        category.getCategoryCode(), new ArrayList<>());

                // 按平台编码分组映射关系
                Map<String, List<String>> platformMappingMap = categoryMappings.stream()
                        .collect(Collectors.groupingBy(
                                StandardCategoryMapping::getPlatformCode,
                                Collectors.mapping(StandardCategoryMapping::getPlatformCategoryName, Collectors.toList())
                        ));

                // 动态设置各平台映射列
                for (String platformCode : sortedPlatformCodes) {
                    TripartitePlatform platform = platformMap.get(platformCode);
                    String platformName = platform != null ? platform.getPlatformNameCn() : platformCode;

                    List<String> mappingValues = platformMappingMap.get(platformCode);
                    String mappingValue = mappingValues != null && !mappingValues.isEmpty()
                            ? String.join("; ", mappingValues) : "";

                    rowData.put("platform_" + platformCode, mappingValue);
                }

                // 设置汇总信息
                if (!categoryMappings.isEmpty()) {
                    String allMappings = categoryMappings.stream()
                            .map(mapping -> {
                                TripartitePlatform platform = platformMap.get(mapping.getPlatformCode());
                                String platformName = platform != null ? platform.getPlatformNameCn() : mapping.getPlatformCode();
                                return platformName + ":" + mapping.getPlatformCategoryName();
                            })
                            .collect(Collectors.joining("; "));
                    rowData.put("allPlatformMappings", allMappings);
                    rowData.put("mappingCount", categoryMappings.size());
                } else {
                    rowData.put("allPlatformMappings", "");
                    rowData.put("mappingCount", 0);
                }

                exportDataList.add(rowData);
            }

            // 生成动态Excel头部
            List<List<String>> headList = buildDynamicExcelHead(sortedPlatformCodes, platformMap);

            // 转换为Excel数据格式
            List<List<Object>> excelDataList = convertToExcelData(exportDataList, sortedPlatformCodes);

            // 生成文件名
            String fileName = "类目映射_" + DateUtil.format(DateUtil.date(), "MM_dd") + ".xlsx";

            // 使用ByteArrayOutputStream生成Excel文件到内存
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            EasyExcel.write(outputStream)
                    .head(headList)
                    .sheet("类目映射")
                    .doWrite(excelDataList);

            // 将ByteArrayOutputStream转换为InputStream
            ByteArrayInputStream inputStream = new ByteArrayInputStream(outputStream.toByteArray());

            // 上传到OSS
            String ossUrl = aliOssUtil.uploadInputStream(inputStream, fileName, "exports/category-mappings");

            log.info("类目映射数据导出成功，共{}条记录，涉及{}个平台，文件已上传到OSS: {}",
                    exportDataList.size(), sortedPlatformCodes.size(), ossUrl);
            return CommonResult.success(new ExportFileResponse(ossUrl, fileName, "导出成功"));

        } catch (Exception e) {
            log.error("导出类目映射数据失败", e);
            throw exception(ErrorCodeConstants.PLATFORM_CATEGORY_EXPORT_FAILED);
        }
    }

    /**
     * 构建动态Excel头部
     */
    private List<List<String>> buildDynamicExcelHead(List<String> platformCodes, Map<String, TripartitePlatform> platformMap) {
        List<List<String>> headList = new ArrayList<>();

        // 基本列
        headList.add(Collections.singletonList("类目编码"));
        headList.add(Collections.singletonList("类目名称"));
        headList.add(Collections.singletonList("类目CN"));
        headList.add(Collections.singletonList("类目级别"));

        // 动态平台列
        for (String platformCode : platformCodes) {
            TripartitePlatform platform = platformMap.get(platformCode);
            String platformName = platform != null ? platform.getPlatformNameCn() : platformCode;
            headList.add(Collections.singletonList("映射类目名称-" + platformName));
        }

        // 汇总列
        headList.add(Collections.singletonList("所有平台映射"));
        headList.add(Collections.singletonList("映射数量"));

        return headList;
    }

    /**
     * 转换为Excel数据格式
     */
    private List<List<Object>> convertToExcelData(List<Map<String, Object>> exportDataList, List<String> platformCodes) {
        List<List<Object>> excelDataList = new ArrayList<>();

        for (Map<String, Object> rowData : exportDataList) {
            List<Object> rowList = new ArrayList<>();

            // 基本列数据
            rowList.add(rowData.get("categoryCode"));
            rowList.add(rowData.get("categoryName"));
            rowList.add(rowData.get("categoryNameCn"));
            rowList.add(rowData.get("level"));

            // 动态平台列数据
            for (String platformCode : platformCodes) {
                rowList.add(rowData.get("platform_" + platformCode));
            }

            // 汇总列数据
            rowList.add(rowData.get("allPlatformMappings"));
            rowList.add(rowData.get("mappingCount"));

            excelDataList.add(rowList);
        }

        return excelDataList;
    }

    /**
     * 增量更新类目映射关系
     */
    private void updateCategoryMappingIncremental(StandardCategory category, CategoryMappingImportExcelEntity importEntity) {
        // 查询是否已存在相同的映射关系
        LambdaQueryWrapper<StandardCategoryMapping> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StandardCategoryMapping::getStandardCategoryCode, category.getCategoryCode())
                .eq(StandardCategoryMapping::getPlatformCode, importEntity.getPlatformCode())
                .eq(StandardCategoryMapping::getPlatformCategoryName, importEntity.getMappingCategoryName())
                .eq(StandardCategoryMapping::getDeleted, false);

        StandardCategoryMapping existing = standardCategoryMappingMapper.selectOne(wrapper);
        if (existing != null) {
            // 已存在相同映射，跳过
            return;
        }

        // 创建新的映射关系
        StandardCategoryMapping mapping = new StandardCategoryMapping();
        mapping.setStandardCategoryCode(category.getCategoryCode());
        mapping.setStandardCategoryName(category.getCategoryNameEn());
        mapping.setStandardLevel(category.getLevel());
        mapping.setPlatformCode(importEntity.getPlatformCode());
        mapping.setPlatformCategoryName(importEntity.getMappingCategoryName());
        mapping.setDeleted(false);
        mapping.setCreateDate(LocalDateTime.now());
        mapping.setUpdateDate(LocalDateTime.now());
        mapping.setCreateUsername(AdminTokenUtil.getCurrentUsername());
        mapping.setUpdateUsername(AdminTokenUtil.getCurrentUsername());

        standardCategoryMappingMapper.insert(mapping);
    }
}

package ai.pricefox.mallfox.service.mapping.impl;

import ai.pricefox.mallfox.common.exception.enums.ErrorCodeConstants;
import ai.pricefox.mallfox.common.util.AliOssUtil;
import ai.pricefox.mallfox.domain.standard.StandardCategoryMapping;
import ai.pricefox.mallfox.domain.standard.TripartitePlatform;
import ai.pricefox.mallfox.mapper.standard.StandardCategoryMappingMapper;
import ai.pricefox.mallfox.mapper.standard.TripartitePlatformMapper;
import ai.pricefox.mallfox.model.excel.PlatformCategoryExportExcelEntity;
import ai.pricefox.mallfox.model.param.PlatformCategoryCreateRequest;
import ai.pricefox.mallfox.model.param.PlatformCategoryUpdateRequest;
import ai.pricefox.mallfox.model.response.ExportFileResponse;
import ai.pricefox.mallfox.model.response.PlatformCategoryResponse;
import ai.pricefox.mallfox.service.mapping.PlatformCategoryService;
import ai.pricefox.mallfox.utils.AdminTokenUtil;
import ai.pricefox.mallfox.vo.base.CommonResult;
import cn.hutool.core.date.DateUtil;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static ai.pricefox.mallfox.common.exception.util.ServiceExceptionUtil.exception;

/**
 * 平台类目管理Service实现类
 *
 * <AUTHOR>
 * @date 2025-08-02
 */
@Service
@Slf4j
public class PlatformCategoryServiceImpl extends ServiceImpl<StandardCategoryMappingMapper, StandardCategoryMapping> implements PlatformCategoryService {

    @Resource
    private StandardCategoryMappingMapper standardCategoryMappingMapper;

    @Resource
    private TripartitePlatformMapper tripartitePlatformMapper;

    @Resource
    private AliOssUtil aliOssUtil;

    @Override
    public CommonResult<List<PlatformCategoryResponse>> getPlatformCategoryTree(String platformCode, String categoryName, String categoryNameCn) {
        List<PlatformCategoryResponse> categoryList = queryPlatformCategories(platformCode, categoryName, categoryNameCn);
        List<PlatformCategoryResponse> treeList = buildTree(categoryList);
        return CommonResult.success(treeList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<PlatformCategoryResponse> createPlatformCategory(PlatformCategoryCreateRequest reqVO) {
        validateParentCategory(reqVO.getPlatformParentCategoryId());
        checkCategoryNameDuplicate(reqVO.getPlatformCode(), reqVO.getPlatformCategoryName(), reqVO.getPlatformParentCategoryId(), null);

        StandardCategoryMapping platformCategory = buildPlatformCategory(reqVO);
        standardCategoryMappingMapper.insert(platformCategory);

        PlatformCategoryResponse respVO = convertToRespVO(platformCategory);
        return CommonResult.success(respVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<PlatformCategoryResponse> updatePlatformCategory(PlatformCategoryUpdateRequest reqVO) {
        StandardCategoryMapping existingCategory = getExistingCategory(reqVO.getId());
        validateParentCategory(reqVO.getPlatformParentCategoryId());
        checkCategoryNameDuplicate(existingCategory.getPlatformCode(), reqVO.getPlatformCategoryName(),
                reqVO.getPlatformParentCategoryId(), reqVO.getId());

        updateCategoryInfo(existingCategory, reqVO);
        standardCategoryMappingMapper.updateById(existingCategory);

        PlatformCategoryResponse respVO = convertToRespVO(existingCategory);
        return CommonResult.success(respVO);
    }

    @Override
    public CommonResult<PlatformCategoryResponse> getPlatformCategoryById(Long id) {
        StandardCategoryMapping platformCategory = standardCategoryMappingMapper.selectById(id);
        if (platformCategory == null) {
            throw exception(ErrorCodeConstants.PLATFORM_CATEGORY_NOT_EXIST);
        }

        PlatformCategoryResponse respVO = convertToRespVO(platformCategory);
        return CommonResult.success(respVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<Boolean> deletePlatformCategory(Long id) {
        StandardCategoryMapping platformCategory = getExistingCategory(id);

        // 检查是否有子类目
        LambdaQueryWrapper<StandardCategoryMapping> childWrapper = new LambdaQueryWrapper<>();
        childWrapper.eq(StandardCategoryMapping::getPlatformParentCategoryId, id);
        List<StandardCategoryMapping> childCategories = standardCategoryMappingMapper.selectList(childWrapper);
        if (!childCategories.isEmpty()) {
            throw exception(ErrorCodeConstants.PLATFORM_CATEGORY_HAS_CHILDREN);
        }

        // 修改为逻辑删除
        platformCategory.setDeleted(Boolean.TRUE);
        platformCategory.setUpdateUsername(AdminTokenUtil.getCurrentUsername());
        int result = standardCategoryMappingMapper.updateById(platformCategory);
        return CommonResult.success(result > 0);
    }

    @Override
    public CommonResult<ExportFileResponse> exportPlatformCategories(String platformCode, String categoryName, String categoryNameCn) {
        log.info("导出平台类目，platformCode: {}, categoryName: {}, categoryNameCn: {}",
                platformCode, categoryName, categoryNameCn);

        try {
            //  查询平台类目数据（按层级排序）
            List<PlatformCategoryResponse> categoryList = queryPlatformCategories(platformCode, categoryName, categoryNameCn);

            if (categoryList.isEmpty()) {
                log.info("未找到符合条件的平台类目数据");
                return CommonResult.success(new ExportFileResponse("", "", "未找到符合条件的平台类目数据"));
            }

            //  获取平台信息（用于生成文件名）
            String platformName = "平台类目";
            if (StringUtils.hasText(platformCode)) {
                TripartitePlatform platform = tripartitePlatformMapper.selectOne(
                        new LambdaQueryWrapper<TripartitePlatform>()
                                .eq(TripartitePlatform::getPlatformCode, platformCode)
                                .eq(TripartitePlatform::getEnable, true)
                );
                if (platform != null) {
                    platformName = platform.getPlatformNameCn();
                }
            }

            //  构建父类目映射关系（用于显示父类目名称）
            Map<Long, String> parentCategoryMap = new HashMap<>();
            List<StandardCategoryMapping> allMappings = standardCategoryMappingMapper.selectList(
                    new LambdaQueryWrapper<StandardCategoryMapping>()
                            .eq(StringUtils.hasText(platformCode), StandardCategoryMapping::getPlatformCode, platformCode)
                            .eq(StandardCategoryMapping::getDeleted, false)
            );

            for (StandardCategoryMapping mapping : allMappings) {
                if (mapping.getPlatformParentCategoryId() != null && mapping.getPlatformParentCategoryId() > 0) {
                    // 查找父类目名称
                    StandardCategoryMapping parentMapping = allMappings.stream()
                            .filter(m -> m.getId().equals(mapping.getPlatformParentCategoryId()))
                            .findFirst()
                            .orElse(null);
                    if (parentMapping != null) {
                        parentCategoryMap.put(mapping.getId(), parentMapping.getPlatformCategoryName());
                    }
                }
            }

            // 将树形结构扁平化并转换为导出实体（保持层级顺序）
            List<PlatformCategoryExportExcelEntity> exportList = new ArrayList<>();
            flattenCategoryTree(categoryList, exportList, parentCategoryMap);

            // 生成文件名：平台名称-类目-月-日
            String fileName = platformName + "-类目-" + DateUtil.format(DateUtil.date(), "MM-dd") + ".xlsx";

            // 使用ByteArrayOutputStream生成Excel文件到内存
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            EasyExcel.write(outputStream, PlatformCategoryExportExcelEntity.class)
                    .sheet("平台类目")
                    .doWrite(exportList);

            // 将ByteArrayOutputStream转换为InputStream
            ByteArrayInputStream inputStream = new ByteArrayInputStream(outputStream.toByteArray());

            // 上传到OSS
            String ossUrl = aliOssUtil.uploadInputStream(inputStream, fileName, "exports/platform-categories");

            log.info("平台类目数据导出成功，共{}条记录，文件已上传到OSS: {}", exportList.size(), ossUrl);
            return CommonResult.success(new ExportFileResponse(ossUrl, fileName, "导出成功"));

        } catch (Exception e) {
            log.error("导出平台类目数据失败", e);
            throw exception(ErrorCodeConstants.PLATFORM_CATEGORY_EXPORT_FAILED);
        }
    }

    /**
     * 查询平台类目列表
     */
    private List<PlatformCategoryResponse> queryPlatformCategories(String platformCode, String categoryName, String categoryNameCn) {
        LambdaQueryWrapper<StandardCategoryMapping> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StringUtils.hasText(platformCode), StandardCategoryMapping::getPlatformCode, platformCode);
        queryWrapper.like(StringUtils.hasText(categoryName), StandardCategoryMapping::getPlatformCategoryName, categoryName);
        queryWrapper.like(StringUtils.hasText(categoryNameCn), StandardCategoryMapping::getPlatformCategoryNameCn, categoryNameCn);
        queryWrapper.orderByAsc(StandardCategoryMapping::getPlatformLevel, StandardCategoryMapping::getId);

        return standardCategoryMappingMapper.selectList(queryWrapper)
                .stream()
                .map(this::convertToRespVO)
                .collect(Collectors.toList());
    }

    /**
     * 验证父类目是否存在
     */
    private void validateParentCategory(Long parentCategoryId) {
        if (parentCategoryId != null && parentCategoryId > 0) {
            StandardCategoryMapping parentCategory = standardCategoryMappingMapper.selectById(parentCategoryId);
            if (parentCategory == null) {
                throw exception(ErrorCodeConstants.PLATFORM_CATEGORY_PARENT_NOT_EXIST);
            }
        }
    }

    /**
     * 检查类目名称是否重复
     */
    private void checkCategoryNameDuplicate(String platformCode, String categoryName, Long parentCategoryId, Long excludeId) {
        if (!StringUtils.hasText(categoryName)) {
            return;
        }

        LambdaQueryWrapper<StandardCategoryMapping> nameWrapper = new LambdaQueryWrapper<>();
        nameWrapper.eq(StandardCategoryMapping::getPlatformCode, platformCode)
                .eq(StandardCategoryMapping::getPlatformCategoryName, categoryName)
                .eq(StandardCategoryMapping::getPlatformParentCategoryId, parentCategoryId != null ? parentCategoryId : 0L);

        if (excludeId != null) {
            nameWrapper.ne(StandardCategoryMapping::getId, excludeId);
        }

        StandardCategoryMapping existingByName = standardCategoryMappingMapper.selectOne(nameWrapper);
        if (existingByName != null) {
            throw exception(ErrorCodeConstants.PLATFORM_CATEGORY_NAME_EXISTS);
        }
    }

    /**
     * 构建平台类目对象
     */
    private StandardCategoryMapping buildPlatformCategory(PlatformCategoryCreateRequest reqVO) {
        StandardCategoryMapping platformCategory = new StandardCategoryMapping();
        BeanUtils.copyProperties(reqVO, platformCategory);
        platformCategory.setCreateDate(LocalDateTime.now());
        platformCategory.setCreateUsername(AdminTokenUtil.getCurrentUsername());
        return platformCategory;
    }

    /**
     * 获取已存在的类目并验证
     */
    private StandardCategoryMapping getExistingCategory(Long id) {
        StandardCategoryMapping existingCategory = standardCategoryMappingMapper.selectById(id);
        if (existingCategory == null) {
            throw exception(ErrorCodeConstants.PLATFORM_CATEGORY_NOT_EXIST);
        }
        return existingCategory;
    }

    /**
     * 更新类目信息
     */
    private void updateCategoryInfo(StandardCategoryMapping existingCategory, PlatformCategoryUpdateRequest reqVO) {
        BeanUtils.copyProperties(reqVO, existingCategory);
        existingCategory.setUpdateDate(LocalDateTime.now());
        existingCategory.setUpdateUsername(AdminTokenUtil.getCurrentUsername());
    }

    /**
     * 转换为响应VO
     */
    private PlatformCategoryResponse convertToRespVO(StandardCategoryMapping platformCategory) {
        PlatformCategoryResponse respVO = new PlatformCategoryResponse();
        BeanUtils.copyProperties(platformCategory, respVO);
        return respVO;
    }

    /**
     * 构建树形结构
     */
    private List<PlatformCategoryResponse> buildTree(List<PlatformCategoryResponse> allCategories) {
        Map<Long, List<PlatformCategoryResponse>> parentChildMap = allCategories.stream()
                .collect(Collectors.groupingBy(category ->
                        category.getPlatformParentCategoryId() != null ? category.getPlatformParentCategoryId() : 0L));

        List<PlatformCategoryResponse> rootCategories = parentChildMap.getOrDefault(0L, new ArrayList<>());

        for (PlatformCategoryResponse category : allCategories) {
            List<PlatformCategoryResponse> children = parentChildMap.getOrDefault(category.getId(), new ArrayList<>());
            category.setChildren(children);
        }

        return rootCategories;
    }

    /**
     * 将树形结构扁平化为导出列表（保持层级顺序）
     */
    private void flattenCategoryTree(List<PlatformCategoryResponse> categoryList,
                                     List<PlatformCategoryExportExcelEntity> exportList,
                                     Map<Long, String> parentCategoryMap) {
        for (PlatformCategoryResponse category : categoryList) {
            // 转换当前节点
            PlatformCategoryExportExcelEntity exportEntity = new PlatformCategoryExportExcelEntity();
            exportEntity.setPlatformCategoryName(category.getPlatformCategoryName());
            exportEntity.setPlatformCategoryNameCn(category.getPlatformCategoryNameCn());
            exportEntity.setPlatformLevel(category.getPlatformLevel());

            // 设置父类目名称
            if (category.getPlatformParentCategoryId() != null && category.getPlatformParentCategoryId() > 0) {
                exportEntity.setParentCategoryName(parentCategoryMap.get(category.getId()));
            } else {
                exportEntity.setParentCategoryName(""); // 一级类目无父类目
            }

            exportList.add(exportEntity);

            // 递归处理子节点
            if (category.getChildren() != null && !category.getChildren().isEmpty()) {
                flattenCategoryTree(category.getChildren(), exportList, parentCategoryMap);
            }
        }
    }
}

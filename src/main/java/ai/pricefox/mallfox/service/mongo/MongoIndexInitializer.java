package ai.pricefox.mallfox.service.mongo;

import ai.pricefox.mallfox.domain.standard.mongo.RawData;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.index.IndexDefinition;
import org.springframework.data.mongodb.core.index.IndexOperations;
import org.springframework.data.mongodb.core.index.IndexResolver;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;

import java.util.stream.StreamSupport;

/**
 * MongoDB索引初始化服务
 * 用于在应用启动时确保MongoDB索引被正确创建
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MongoIndexInitializer {

    private final MongoTemplate mongoTemplate;
    private final IndexResolver indexResolver;

    /**
     * 应用启动时初始化MongoDB索引
     */
    @PostConstruct
    public void initIndexes() {
        try {
            log.info("开始初始化MongoDB索引");

            // 为RawData实体创建索引
            IndexOperations indexOps = mongoTemplate.indexOps(RawData.class);
            Iterable<? extends IndexDefinition> indexDefinitions = indexResolver.resolveIndexFor(RawData.class);
            StreamSupport.stream(indexDefinitions.spliterator(), false).forEach(indexOps::ensureIndex);

            log.info("MongoDB索引初始化完成");
        } catch (Exception e) {
            log.error("MongoDB索引初始化失败", e);
        }
    }
}
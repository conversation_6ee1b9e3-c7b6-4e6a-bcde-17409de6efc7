package ai.pricefox.mallfox.service.mongo;

import ai.pricefox.mallfox.domain.standard.mongo.RawData;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.util.concurrent.atomic.AtomicLong;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 商品ID序列号服务
 * 用于生成全局唯一的SPU和SKU编码
 *
 * <AUTHOR>
 * @since 2025/8/2
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ProductIdSequenceService {

    private final MongoTemplate mongoTemplate;

    /**
     * SPU编码格式的正则表达式
     */
    private static final Pattern SPU_PATTERN = Pattern.compile("^PP(\\d{8})$");

    /**
     * SKU编码格式的正则表达式
     */
    private static final Pattern SKU_PATTERN = Pattern.compile("^PK(\\d{8})$");

    /**
     * 初始化序列号计数器
     * 通过查询MongoDB中现有数据的最大序列号来初始化
     */
    @PostConstruct
    public void initSequences() {
        log.info("SPU和SKU序列号服务已初始化");
    }

    /**
     * 从MongoDB中查找指定字段的最大序列号并加1
     *
     * @param fieldName 字段名 (standardSpu 或 standardSku)
     * @param pattern   编码格式正则表达式
     * @return 最大序列号加1
     */
    private synchronized long getNextSequenceFromMongoDB(String fieldName, Pattern pattern) {
        try {
            // 构建查询，按字段降序排列，只取第一个文档
            Query query = new Query().with(Sort.by(Sort.Direction.DESC, fieldName)).limit(1);
            RawData latestData = mongoTemplate.findOne(query, RawData.class);

            if (latestData != null) {
                String fieldValue = null;
                if ("standardSpu".equals(fieldName)) {
                    fieldValue = latestData.getStandardSpu();
                } else if ("standardSku".equals(fieldName)) {
                    fieldValue = latestData.getStandardSku();
                }

                if (fieldValue != null) {
                    Matcher matcher = pattern.matcher(fieldValue);
                    if (matcher.matches()) {
                        return Long.parseLong(matcher.group(1)) + 1;
                    }
                }
            }
        } catch (Exception e) {
            log.error("从MongoDB查询最大序列号时发生错误", e);
        }

        // 如果没有找到或者发生异常，返回1
        return 1L;
    }

    /**
     * 生成唯一的标准SPU编码
     * 格式：PP + 8位数字（递增）
     *
     * @return 唯一的SPU编码
     */
    public String generateUniqueSpu() {
        long sequence = getNextSequenceFromMongoDB("standardSpu", SPU_PATTERN);
        return String.format("PP%08d", sequence % 100000000);
    }

    /**
     * 生成唯一的标准SKU编码
     * 格式：PK + 8位数字（递增）
     *
     * @return 唯一的SKU编码
     */
    public String generateUniqueSku() {
        long sequence = getNextSequenceFromMongoDB("standardSku", SKU_PATTERN);
        return String.format("PK%08d", sequence % 100000000);
    }
}
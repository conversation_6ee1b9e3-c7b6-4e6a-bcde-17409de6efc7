package ai.pricefox.mallfox.service.mongo;

import ai.pricefox.mallfox.model.dto.DailyPlatformGrowthStats;
import ai.pricefox.mallfox.repository.mongo.RawDataRepository;
import ai.pricefox.mallfox.domain.standard.mongo.RawData;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.*;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;

// 数据存储服务
@Slf4j
@Service
@RequiredArgsConstructor
public class RawDataService {
    private final RawDataRepository rawDataRepository;
    private final ProductIdSequenceService productIdSequenceService;
    private final MongoTemplate mongoTemplate;

    /**
     * 保存原始数据
     *
     * @param platform     平台
     * @param channel      渠道
     * @param spu          spu
     * @param sku          sku
     * @param dataJson     原始数据
     * @param productId    自建商品ID
     * @param selfOperated 是否自营
     */
    public String saveRawData(String platform, String channel, String spu, String sku, String dataJson, String productId, Boolean selfOperated) {
        String standardSku = generateUniqueSpu();
        RawData rawData = new RawData();
        rawData.setProductPlatform(platform);
        rawData.setDataChannel(channel);
        rawData.setSpu(spu);
        rawData.setSku(sku);
        rawData.setDataJson(dataJson);
        rawData.setProductIdentifier(productId);
        rawData.setCreateTime(new Date());
        rawData.setStandardSpu(generateUniqueSpu());
        rawData.setStandardSku(standardSku);
        rawData.setSelfOperated(selfOperated);
        if (platform.equals("EBAY")) {
            rawData.setSelfOperated(false);
        }
        if (platform.equals("BESTBUY")) {
            rawData.setSelfOperated(false);
        }
        try {
            rawDataRepository.save(rawData);
        } catch (DuplicateKeyException e) {
            // 记录违反唯一索引约束的详细信息
            log.warn("保存原始数据时违反唯一索引约束: platform={}, channel={}, spu={}, sku={}, productId={}, message={}", platform, channel, spu, sku, productId, e.getMessage());
        } catch (Exception e) {
            // 记录其他保存异常
            log.error("保存原始数据时发生异常: platform={}, channel={}, spu={}, sku={}, productId={}", platform, channel, spu, sku, productId, e);
        }
        return standardSku;
    }

    /**
     * 根据平台、SPU和SKU查询合并数据
     *
     * @param platformEnum 平台枚举
     * @param spu          SPU
     * @param sku          SKU
     * @return 合并数据
     */
    public List<RawData> findMargeData(String platformEnum, String spu, String sku) {
        Query query = new Query();
        query.addCriteria(Criteria.where("productPlatform").is(platformEnum).and("spu").is(spu).and("sku").is(sku));
        return mongoTemplate.find(query, RawData.class);
    }

    /**
     * 生成唯一的标准SPU编码
     *
     * @return 唯一的SPU编码
     */
    public String generateUniqueSpu() {
        return productIdSequenceService.generateUniqueSpu();
    }

    /**
     * 生成唯一的标准SKU编码
     *
     * @return 唯一的SKU编码
     */
    public String generateUniqueSku() {
        return productIdSequenceService.generateUniqueSku();
    }

    /**
     * 统计每天每个平台每个渠道的SPU和SKU数据增长量
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 每天每个平台每个渠道的SPU和SKU增长量统计结果
     */
    public List<DailyPlatformGrowthStats> getDailyPlatformGrowthStats(LocalDate startDate, LocalDate endDate) {
        // 将LocalDate转换为Date
        Date start = Date.from(startDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
        Date end = Date.from(endDate.plusDays(1).atStartOfDay(ZoneId.systemDefault()).toInstant());

        // 创建聚合管道
        // 1. 匹配日期范围内的数据
        MatchOperation matchDate = Aggregation.match(Criteria.where("createTime").gte(start).lt(end));

        // 2. 按平台和渠道分组，并统计SPU和SKU数量
        GroupOperation group = Aggregation.group("productPlatform", "dataChannel").count().as("totalCount").sum(ConditionalOperators.when(Criteria.where("spu").exists(true).ne(null)).then(1).otherwise(0)).as("spuCount").sum(ConditionalOperators.when(Criteria.where("sku").exists(true).ne(null)).then(1).otherwise(0)).as("skuCount");

        // 3. 按平台和渠道排序
        SortOperation sort = Aggregation.sort(org.springframework.data.domain.Sort.Direction.ASC, "productPlatform", "dataChannel");

        // 执行聚合查询
        Aggregation aggregation = Aggregation.newAggregation(matchDate, group, sort);
        return mongoTemplate.aggregate(aggregation, "raw_data", DailyPlatformGrowthStats.class).getMappedResults();
    }
}
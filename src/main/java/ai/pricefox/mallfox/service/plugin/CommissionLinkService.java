package ai.pricefox.mallfox.service.plugin;
import org.springframework.stereotype.Service;
import java.net.URI;
import java.net.URISyntaxException;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @desc 生成佣金连接服务，负责将普通的电商商品URL转换为包含我们联盟营销追踪ID的佣金链接。
 * @since 2025/7/31
 */
@Service
public class CommissionLinkService {

    // TODO 这些ID应该从配置文件 (application.yml) 中读取
    private final String AMAZON_AFFILIATE_ID = "yourpricefox-1024";
    private final String EBAY_CAMPAIGN_ID = "*********";

    /**
     * 生成带佣金的跳转链接
     * @param originalUrl 从channel_offers表中获取的原始商品URL
     * @return 转换后的佣金链接；如果无法转换，则返回原始链接
     */
    public String generateLink(String originalUrl) {
        if (!StringUtils.hasText(originalUrl)) {
            return "";
        }

        try {
            URI uri = new URI(originalUrl);
            String host = uri.getHost().toLowerCase();

            if (host.contains("amazon.com")) {
                return appendQueryParam(originalUrl, "tag", AMAZON_AFFILIATE_ID);
            } else if (host.contains("ebay.com")) {
                // eBay的链接结构通常更复杂，这只是一个简化示例
                return appendQueryParam(originalUrl, "campid", EBAY_CAMPAIGN_ID);
            }
        } catch (URISyntaxException e) {
            // URL格式错误，直接返回原始URL
            return originalUrl;
        }

        // 默认返回原始链接
        return originalUrl;
    }

    /**
     * 一个健壮的、向URL追加查询参数的辅助方法
     */
    private String appendQueryParam(String url, String key, String value) {
        if (url.contains("?")) {
            // 已经有查询参数
            if (url.endsWith("?") || url.endsWith("&")) {
                return url + key + "=" + value;
            } else {
                return url + "&" + key + "=" + value;
            }
        } else {
            // 没有查询参数
            return url + "?" + key + "=" + value;
        }
    }
}

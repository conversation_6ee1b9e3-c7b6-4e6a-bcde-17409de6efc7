package ai.pricefox.mallfox.service.plugin;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @desc 汇率转换服务 负责将不同货币的金额转换为统一的基准货币（USD）。
 * @since 2025/7/31
 */
@Service
@Slf4j
public class ExchangeRateService {

    // 基准货币
    private static final String BASE_CURRENCY = "USD";

    /**
     * 将任意货币金额转换为美元
     *
     * @param fromCurrency 原始货币 (e.g., "EUR")
     * @param amount       原始金额
     * @return 转换后的美元金额
     */
    public BigDecimal convertToUsd(String fromCurrency, BigDecimal amount) {
        if (amount == null) {
            return BigDecimal.ZERO;
        }
        if (BASE_CURRENCY.equalsIgnoreCase(fromCurrency)) {
            return amount;
        }

        try {
            BigDecimal rate = getRate(fromCurrency, BASE_CURRENCY);
            return amount.multiply(rate).setScale(2, RoundingMode.HALF_UP);
        } catch (Exception e) {
            log.error("获取汇率失败 for currency: {}", fromCurrency, e);
            return amount;
        }
    }

    /**
     * 获取从一种货币到另一种货币的汇率*
     * @param from 原始货币
     * @param to   目标货币
     * @return 汇率
     */
//    @Cacheable(value = "exchange_rates", key = "#from + '_' + #to")
    public BigDecimal getRate(String from, String to) {
        log.info("缓存未命中，正在从外部API获取汇率: {} -> {}", from, to);
        // TODO 调用汇率API
        Map<String, BigDecimal> mockRates = new ConcurrentHashMap<>();
        mockRates.put("EUR", new BigDecimal("1.08"));
        mockRates.put("GBP", new BigDecimal("1.25"));
        mockRates.put("JPY", new BigDecimal("0.0067"));

        if (mockRates.containsKey(from) && to.equals(BASE_CURRENCY)) {
            return mockRates.get(from);
        }
        throw new RuntimeException("无法获取汇率 for " + from + " to " + to);
    }
}
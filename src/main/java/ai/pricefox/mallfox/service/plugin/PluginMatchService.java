package ai.pricefox.mallfox.service.plugin;

import ai.pricefox.mallfox.domain.product.ChannelOffers;
import ai.pricefox.mallfox.domain.standard.TripartitePlatform;
import ai.pricefox.mallfox.mapper.product.ChannelOffersMapper;
import ai.pricefox.mallfox.model.dto.PluginMatchResult;
import ai.pricefox.mallfox.model.param.PluginMatchRequest;
import ai.pricefox.mallfox.model.response.PluginMatchResponse;
import ai.pricefox.mallfox.service.standard.TripartitePlatformService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @desc 插件匹配与比价服务实现类
 * @since 2025/7/31
 */
@Service
@Slf4j
public class PluginMatchService {

    @Autowired
    private ProductMatchingEngine matchingEngine;

    @Autowired
    private ChannelOffersMapper channelOffersMapper;

    @Autowired
    private CommissionLinkService commissionLinkService;

    @Autowired
    private ExchangeRateService exchangeRateService;

    @Autowired
    private TripartitePlatformService tripartitePlatformService;

    public PluginMatchResponse processMatchRequest(PluginMatchRequest request) {

        // 检验数据状态
        TripartitePlatform tripartitePlatform = tripartitePlatformService.selectByName(request.getSourcePlatform());
        if (Objects.isNull(tripartitePlatform) || StringUtils.isBlank(tripartitePlatform.getPlatformCode())) {
            log.error("不支持的来源平台: {}", request.getSourcePlatform());
            throw new IllegalArgumentException("不支持的来源平台: " + request.getSourcePlatform());
        }

        // 调用匹配引擎，获取匹配结果
        request.setPlatformCode(tripartitePlatform.getPlatformCode());
        PluginMatchResult matchResult = matchingEngine.match(request);

        PluginMatchResponse.RawRequestInfo rawInfo = new PluginMatchResponse.RawRequestInfo()
                .setSourcePlatform(request.getSourcePlatform())
                .setPlatformSkuId(request.getProductId())
                .setTitle(request.getTitle())
                .setItemUrl(request.getItemUrl());

        // 如果匹配失败 -> 构造状态C的响应
        if (!matchResult.isSuccess()) {
            // 构造未匹配结果
            PluginMatchResponse.PluginMatchData data = new PluginMatchResponse.PluginMatchData()
                    .setRawRequest(rawInfo);
            return PluginMatchResponse.productNotFound(data);
        }

        String matchedSkuCode = matchResult.getSkuCode();

        // 将当前页面价格转换为基准货币（USD）
        BigDecimal currentPriceInUsd = exchangeRateService.convertToUsd(request.getCurrency(), request.getCurrentPrice());

        // 查询该SKU在所有渠道的、有货的、价格更低的报价
        List<ChannelOffers> lowerPriceOffers = channelOffersMapper.findLowerPriceOffers(matchedSkuCode, currentPriceInUsd);

        // 根据查询结果，决定返回状态A还是状态B
        if (CollectionUtils.isEmpty(lowerPriceOffers)) {
            // 已经是最低价 -> 构造状态B的响应
            // 生成带佣金的跳转链接
            String commissionUrl = commissionLinkService.generateLink(request.getItemUrl());
            PluginMatchResponse.PluginMatchData data = new PluginMatchResponse.PluginMatchData()
                    .setMatchedSkuCode(matchedSkuCode)
                    .setRawRequest(rawInfo)
                    .setCashbackAmount(new BigDecimal(100))
                    .setCommissionUrl(commissionUrl);
            return PluginMatchResponse.bestPriceConfirmed(data);
        } else {
            // 找到了更低价 -> 构造状态A的响应
            return buildLowerPriceResponse(matchedSkuCode, lowerPriceOffers, currentPriceInUsd, rawInfo);
        }
    }

    /**
     * 构造状态A (LOWER_PRICE_FOUND) 的响应体
     */
    private PluginMatchResponse buildLowerPriceResponse(String skuCode, List<ChannelOffers> offers, BigDecimal currentPrice, PluginMatchResponse.RawRequestInfo rawInfo) {

        // offers 列表已经是按价格升序排列的，第一个就是最低价
        ChannelOffers bestOfferEntity = offers.getFirst();

        TripartitePlatform tripartitePlatform = tripartitePlatformService.selectByCode(bestOfferEntity.getPlatformCode());

        // 创建bestOffer
        PluginMatchResponse.OfferSnippet bestOfferDto = new PluginMatchResponse.OfferSnippet();
        bestOfferDto.setPlatform(String.valueOf(tripartitePlatform.getPlatformName()));
        bestOfferDto.setPrice(bestOfferEntity.getPrice());
        bestOfferDto.setCondition(bestOfferEntity.getCondition());
        bestOfferDto.setItemUrl(commissionLinkService.generateLink(bestOfferEntity.getItemUrl()));

        // 创建otherOffers DTO列表 (取第2到第3个)
        List<PluginMatchResponse.OfferSnippet> otherOffersDto = offers.stream()
                // 跳过第一个（bestOffer）
                .skip(1)
                // 最多再取2个
                .limit(2)
                .map(offer -> {
                    PluginMatchResponse.OfferSnippet snippet = new PluginMatchResponse.OfferSnippet();
                    snippet.setPlatform(String.valueOf(tripartitePlatformService.selectByCode(bestOfferEntity.getPlatformCode()).getPlatformName()));
                    snippet.setPrice(offer.getPrice());
                    snippet.setCondition(offer.getCondition());
                    snippet.setItemUrl(commissionLinkService.generateLink(offer.getItemUrl()));
                    return snippet;
                })
                .collect(Collectors.toList());

        // 计算节省金额
        BigDecimal savings = currentPrice.subtract(exchangeRateService.convertToUsd("USD", bestOfferEntity.getPrice()));
        String savingsText = "$" + savings.setScale(2, RoundingMode.HALF_UP).toPlainString() + " less";

        PluginMatchResponse.PluginMatchData data = new PluginMatchResponse.PluginMatchData()
                .setMatchedSkuCode(skuCode)
                .setRawRequest(rawInfo)
                .setBestOffer(bestOfferDto)
                .setOtherOffers(otherOffersDto)
                .setSavingsText(savingsText);


        return PluginMatchResponse.lowerPriceFound(data);
    }
}

package ai.pricefox.mallfox.service.plugin;

import ai.pricefox.mallfox.domain.product.ChannelOffers;
import ai.pricefox.mallfox.mapper.product.ChannelOffersMapper;
import ai.pricefox.mallfox.model.dto.PluginMatchResult;
import ai.pricefox.mallfox.model.param.PluginMatchRequest;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @desc 商品比价插件引擎，负责将来自外部平台的商品信息，匹配到我们系统内部的唯一SKU。
 * @since 2025/7/31
 */

@Service
@Slf4j
public class ProductMatchingEngine {


    @Autowired
    private ChannelOffersMapper channelOffersMapper;

    /**
     * 引擎主入口方法
     */
    public PluginMatchResult match(PluginMatchRequest request) {
        log.info("开始为平台商品 [{}:{}] 进行匹配", request.getSourcePlatform(), request.getProductId());
        // ID直连匹配
        return matchById(request);
    }


    /**
     * ID直连匹配
     *
     * @param request
     * @return 匹配结果。如果成功
     */
    public PluginMatchResult matchById(PluginMatchRequest request) {
        log.info("开始为平台商品 [{}:{}] 进行ID直连匹配", request.getSourcePlatform(), request.getProductId());


        PluginMatchResult result = new PluginMatchResult();
        result.setSuccess(false);

        if (!StringUtils.hasText(request.getSourcePlatform()) || !StringUtils.hasText(request.getProductId())) {
            log.warn("ID直连匹配失败：平台或平台唯一ID为空。");
            return result;
        }

        ChannelOffers offer = channelOffersMapper.selectOne(
                new LambdaQueryWrapper<ChannelOffers>()
                        .eq(ChannelOffers::getPlatformCode, request.getPlatformCode())
                        .eq(ChannelOffers::getPlatformSkuId, request.getProductId())
        );
        if (offer != null && StringUtils.hasText(offer.getSkuCode())) {
            log.info("ID直连匹配成功 -> productId: {}, 匹配到 SKU: {}", request.getProductId(), offer.getSkuCode());
            result.setSuccess(true);
            result.setSkuCode(offer.getSkuCode());
            return result;
        } else {
            // 匹配失败
            log.info("ID直连匹配失败，在channel_offers表中未找到记录 for [{}:{}]", request.getPlatformCode(), request.getProductId());
            return result;
        }
    }
}

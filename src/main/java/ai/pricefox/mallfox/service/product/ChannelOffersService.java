package ai.pricefox.mallfox.service.product;

import ai.pricefox.mallfox.domain.product.ChannelOffers;
import com.baomidou.mybatisplus.extension.service.IService;
import java.util.List;

/**
 * 渠道商品报价服务
 */
public interface ChannelOffersService extends IService<ChannelOffers> {
    
    /**
     * 根据SKU ID获取所有渠道报价
     */
    List<ChannelOffers> getOffersBySkuId(String skuId);
    
    /**
     * 获取指定平台的最低价第三方卖家报价
     */
    ChannelOffers getLowestThirdPartyOffer(String skuId, String platform);
    
    /**
     * 获取指定平台的自营报价
     */
    ChannelOffers getPlatformRetailOffer(String skuId, String platform);
}

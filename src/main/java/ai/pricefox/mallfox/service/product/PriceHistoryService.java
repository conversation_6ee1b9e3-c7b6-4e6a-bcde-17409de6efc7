package ai.pricefox.mallfox.service.product;

import ai.pricefox.mallfox.model.response.PriceHistoryResponseDTO;
import ai.pricefox.mallfox.vo.product.ProductDetailRespVO;

import java.util.List;

public interface PriceHistoryService {

    /**
     * 获取商品价格历史
     * @param skuCode
     * @param period
     * @param platforms
     * @return
     */
    PriceHistoryResponseDTO getPriceHistory(String skuCode, String period, List<String> platforms);

    /**
     * 获取30天商品价格历史
     * @param skuCode
     * @return
     */
    List<ProductDetailRespVO.PriceHistoryPointVO> getPriceHistory(String skuCode);
}

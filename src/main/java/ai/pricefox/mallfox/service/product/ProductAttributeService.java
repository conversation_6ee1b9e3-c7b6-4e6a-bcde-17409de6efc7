package ai.pricefox.mallfox.service.product;

import ai.pricefox.mallfox.domain.product.ProductAttribute;
import ai.pricefox.mallfox.model.param.ProductAttributeParam;
import com.baomidou.mybatisplus.extension.service.IService;

/**
* <AUTHOR>
* @description 针对表【product_attribute(商品属性表)】的数据库操作Service
* @createDate 2025-05-18 12:14:02
*/
public interface ProductAttributeService extends IService<ProductAttribute> {

    Boolean saveProductAttribute(ProductAttributeParam productAttribute);

    ProductAttributeParam getProductAttributeById(String id);
}

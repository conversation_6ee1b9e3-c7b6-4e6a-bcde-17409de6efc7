package ai.pricefox.mallfox.service.product;


import ai.pricefox.mallfox.model.param.ProductBestPriceSubmitRequest;
import ai.pricefox.mallfox.model.param.ProductPriceCompareRequest;
import ai.pricefox.mallfox.model.response.ProductPriceCompareResponse;

import java.util.List;

/**
 * 商品价格比较服务
 */
public interface ProductCompareService {

    /**
     * 商品价格比较
     */
    List<ProductPriceCompareResponse> priceCompare(ProductPriceCompareRequest request);

    /**
     * 提交最佳价格
     */
    boolean bestPriceSubmit(ProductBestPriceSubmitRequest request);
}

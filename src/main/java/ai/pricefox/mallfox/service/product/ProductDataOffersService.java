package ai.pricefox.mallfox.service.product;

import ai.pricefox.mallfox.domain.product.ProductDataOffers;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【product_data_offers(多平台商品高频表)】的数据库操作Service
* @createDate 2025-06-15 19:12:11
*/
public interface ProductDataOffersService extends IService<ProductDataOffers> {

    /**
     * 保存或更新offer数据（带价格历史记录）
     * 
     * @param offer 要保存的offer数据
     * @param triggerSource 触发来源
     * @return 是否成功
     */
    boolean saveOrUpdateWithPriceHistory(ProductDataOffers offer, String triggerSource);
    
    /**
     * 批量保存或更新offer数据（带价格历史记录）
     * 
     * @param offerList offer数据列表
     * @param triggerSource 触发来源
     * @return 是否全部成功
     */
    boolean batchSaveOrUpdateWithPriceHistory(List<ProductDataOffers> offerList, String triggerSource);
    
    /**
     * 删除offer数据（带价格历史记录）
     * 
     * @param offerId offer ID
     * @param triggerSource 触发来源
     * @return 是否成功
     */
    boolean removeWithPriceHistory(Long offerId, String triggerSource);
}

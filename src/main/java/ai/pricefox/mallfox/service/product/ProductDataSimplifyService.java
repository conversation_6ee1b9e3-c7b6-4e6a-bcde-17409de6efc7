package ai.pricefox.mallfox.service.product;

import ai.pricefox.mallfox.domain.product.ProductDataSimplify;
import ai.pricefox.mallfox.model.dto.ProductModelProcessDTO;
import ai.pricefox.mallfox.model.param.ProductDataSimplifyParam;
import ai.pricefox.mallfox.model.vo.product.data.ProductModelMergeReqVO;
import ai.pricefox.mallfox.model.vo.product.data.ProductModelMergeRespVO;
import ai.pricefox.mallfox.model.vo.product.data.ProductModelProcessReqVO;
import ai.pricefox.mallfox.model.vo.product.data.ProductModelProcessRespVO;
import ai.pricefox.mallfox.model.vo.product.data.ProductSkuMergeReqVO;
import ai.pricefox.mallfox.model.vo.product.data.ProductSkuMergeRespVO;
import ai.pricefox.mallfox.model.vo.product.data.UpcCodeFixReqVO;
import ai.pricefox.mallfox.model.vo.product.data.UpcCodeFixRespVO;
import ai.pricefox.mallfox.vo.base.CommonResult;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【product_data_simplify(商品低频数据表)】的数据库操作Service
* @createDate 2025-06-15 19:12:11
*/
public interface ProductDataSimplifyService extends IService<ProductDataSimplify> {

    CommonResult saveProductDataSimplify(ProductDataSimplifyParam productDataSimplify);

    /**
     * 根据数据渠道查询不重复的model列表
     * @param dataChannel 数据渠道 ({@link ai.pricefox.mallfox.enums.DataChannelEnum#CRAWLER}: 爬虫, {@link ai.pricefox.mallfox.enums.DataChannelEnum#API}: API)
     * @return 不重复的model列表
     */
    List<String> getDistinctModelsByDataChannel(Integer dataChannel);

    /**
     * 处理商品型号字段
     * 根据品牌信息去除型号中的品牌内容，并格式化型号
     *
     * @param reqVO 处理请求参数
     * @return 处理结果
     */
    ProductModelProcessRespVO processProductModel(ProductModelProcessReqVO reqVO);

    /**
     * 合并相同UPC或者型号的商品数据
     * 1.相同UPC数据合并到统一的SKU
     * 2.根据型号匹配（不区分大小写和空格），将相同型号的数据合并到统一的SKU
     *
     * @param reqVO 合并请求参数
     * @return 合并结果
     */
    ProductModelMergeRespVO mergeProductModel(ProductModelMergeReqVO reqVO);

    /**
     * 合并商品SKU
     * 在相同spuid下，根据color、storage、condition_new、service_provider四个字段匹配，将相同SKU的数据合并
     *
     * @param reqVO 合并请求参数
     * @return 合并结果
     */
    ProductSkuMergeRespVO mergeProductSku(ProductSkuMergeReqVO reqVO);

    /**
     * 修复UPC码为"Does not apply"的记录的spuid
     * 重新应用SPU匹配逻辑，让这些记录按照正常的业务规则重新分配到正确的spuid
     *
     * @param reqVO 修复请求参数
     * @return 修复结果
     */
    UpcCodeFixRespVO fixUpcCodeDoesNotApplySpuIds(UpcCodeFixReqVO reqVO);
}

package ai.pricefox.mallfox.service.product;

import ai.pricefox.mallfox.domain.product.ProductPriceHistory;
import com.baomidou.mybatisplus.extension.service.IService;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 价格历史服务接口
 * 
 * <AUTHOR>
 * @since 2025-07-12
 */
public interface ProductPriceHistoryService extends IService<ProductPriceHistory> {
    
    /**
     * 根据SKU ID查询价格历史
     * 
     * @param skuId SKU ID
     * @return 价格历史列表
     */
    List<ProductPriceHistory> getBySkuId(String skuId);
    
    /**
     * 根据SPU ID查询价格历史
     * 
     * @param spuId SPU ID
     * @return 价格历史列表
     */
    List<ProductPriceHistory> getBySpuId(String spuId);

    /**
     * 根据SPU ID查询价格历史
     *
     * @param spuIds SPU ID
     * @return 价格历史列表
     */
    Map<String, Integer> getMapBySpuIds(List<String> spuIds);

    /**
     * 根据SKU ID查询价格历史
     *
     * @param skuIds SKU ID
     * @return 价格历史列表
     */
    Map<String, Integer> getMapBySkuIds(List<String> skuIds);
    
    /**
     * 根据时间范围查询价格历史
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 价格历史列表
     */
    List<ProductPriceHistory> getByTimeRange(LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 获取最新的价格历史记录
     * 
     * @param offerId offer ID
     * @return 最新价格历史记录
     */
    ProductPriceHistory getLatestByOfferId(Long offerId);
    
    /**
     * 根据平台和变化类型查询历史记录
     * 
     * @param sourcePlatform 来源平台
     * @param changeType 变化类型
     * @param limit 限制数量
     * @return 价格历史列表
     */
    List<ProductPriceHistory> getByPlatformAndChangeType(String sourcePlatform, Integer changeType, Integer limit);
    
    /**
     * 统计指定SKU的价格变化次数
     * 
     * @param skuId SKU ID
     * @return 变化次数
     */
    Integer countPriceChanges(String skuId);
    
    /**
     * 查询指定SKU在指定天数内的价格历史
     * 
     * @param skuId SKU ID
     * @param days 天数
     * @return 价格历史列表
     */
    List<ProductPriceHistory> getBySkuIdAndDays(String skuId, Integer days);
    
    /**
     * 查询价格变化最大的记录
     * 
     * @param limit 限制数量
     * @return 价格历史列表
     */
    List<ProductPriceHistory> getTopPriceChanges(Integer limit);
    
    /**
     * 批量保存价格历史记录
     * 
     * @param historyList 价格历史列表
     * @return 是否成功
     */
    boolean saveBatch(List<ProductPriceHistory> historyList);
}
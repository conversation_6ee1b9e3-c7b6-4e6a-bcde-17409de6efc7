package ai.pricefox.mallfox.service.product.engine;

import ai.pricefox.mallfox.model.dto.ProductDataDTO;
import ai.pricefox.mallfox.service.product.engine.config.ProcessingConfig;
import ai.pricefox.mallfox.service.product.engine.result.ProcessingResult;

import java.util.List;

/**
 * 商品数据处理引擎接口
 * 提供统一的商品数据处理能力，支持型号处理、型号合并、SKU合并等功能
 *
 * <AUTHOR>
 * @since 2025-01-10
 */
public interface ProductDataProcessingEngine {

    /**
     * 处理新增的商品数据
     * 包含完整的处理流程：型号处理 -> 型号合并 -> SKU合并
     *
     * @param dataList 新增的商品数据列表
     * @param config 处理配置
     * @return 处理结果
     */
    ProcessingResult processNewData(List<ProductDataDTO> dataList, ProcessingConfig config);

    /**
     * 处理存量商品数据
     * 对数据库中已存在的数据进行处理和合并
     *
     * @param config 处理配置
     * @return 处理结果
     */
    ProcessingResult processExistingData(ProcessingConfig config);

    /**
     * 仅处理商品型号
     * 根据品牌信息去除型号中的品牌内容，并格式化型号
     *
     * @param config 处理配置
     * @return 处理结果
     */
    ProcessingResult processModelOnly(ProcessingConfig config);

    /**
     * 仅合并商品型号
     * 根据型号匹配将相同型号的数据合并到统一的SPU下
     *
     * @param config 处理配置
     * @return 处理结果
     */
    ProcessingResult mergeModelOnly(ProcessingConfig config);

    /**
     * 仅合并商品SKU
     * 在相同spuid下，根据四个字段匹配将相同SKU的数据合并
     *
     * @param config 处理配置
     * @return 处理结果
     */
    ProcessingResult mergeSkuOnly(ProcessingConfig config);
}

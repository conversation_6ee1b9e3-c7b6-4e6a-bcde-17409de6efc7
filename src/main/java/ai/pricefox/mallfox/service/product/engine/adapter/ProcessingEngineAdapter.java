package ai.pricefox.mallfox.service.product.engine.adapter;

import ai.pricefox.mallfox.model.dto.ProductDataDTO;
import ai.pricefox.mallfox.model.vo.product.data.*;
import ai.pricefox.mallfox.service.product.engine.ProductDataProcessingEngine;
import ai.pricefox.mallfox.service.product.engine.config.ProcessingConfig;
import ai.pricefox.mallfox.service.product.engine.result.ProcessingResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 处理引擎适配器
 * 将新的通用处理引擎适配到现有的接口中
 *
 * <AUTHOR>
 * @since 2025-01-10
 */
@Service
@Slf4j
public class ProcessingEngineAdapter {

    @Autowired
    private ProductDataProcessingEngine processingEngine;

    /**
     * 处理商品型号（适配ProductDataSimplifyService.processProductModel）
     *
     * @param reqVO 处理请求参数
     * @return 处理结果
     */
    public ProductModelProcessRespVO processProductModel(ProductModelProcessReqVO reqVO) {
        log.info("通过处理引擎处理商品型号，参数：{}", reqVO);
        
        ProcessingConfig config = ProcessingConfig.builder()
                .previewMode(reqVO.getPreviewMode())
                .sourcePlatform(reqVO.getSourcePlatform())
                .pageNo(reqVO.getPageNo())
                .pageSize(reqVO.getPageSize())
                .onlyWithBrand(reqVO.getOnlyWithBrand())
                .build();

        ProcessingResult result = processingEngine.processModelOnly(config);
        
        return convertToModelProcessRespVO(result);
    }

    /**
     * 合并商品型号（适配ProductDataSimplifyService.mergeProductModel）
     *
     * @param reqVO 合并请求参数
     * @return 合并结果
     */
    public ProductModelMergeRespVO mergeProductModel(ProductModelMergeReqVO reqVO) {
        log.info("通过处理引擎合并商品型号，参数：{}", reqVO);
        
        ProcessingConfig config = ProcessingConfig.builder()
                .previewMode(reqVO.getPreviewMode())
                .sourcePlatform(reqVO.getSourcePlatform())
                .pageNo(reqVO.getPageNo())
                .pageSize(reqVO.getPageSize())
                .targetModel(reqVO.getTargetModel())
                .enableUpcPriority(true)
                .build();

        ProcessingResult result = processingEngine.mergeModelOnly(config);
        
        return convertToModelMergeRespVO(result);
    }

    /**
     * 合并商品SKU（适配ProductDataSimplifyService.mergeProductSku）
     *
     * @param reqVO 合并请求参数
     * @return 合并结果
     */
    public ProductSkuMergeRespVO mergeProductSku(ProductSkuMergeReqVO reqVO) {
        log.info("通过处理引擎合并商品SKU，参数：{}", reqVO);
        
        ProcessingConfig config = ProcessingConfig.builder()
                .previewMode(reqVO.getPreviewMode())
                .sourcePlatform(reqVO.getSourcePlatform())
                .pageNo(reqVO.getPageNo())
                .pageSize(reqVO.getPageSize())
                .targetSpuId(reqVO.getTargetSpuId())
                .enableLatestDataUpdate(true)
                .build();

        ProcessingResult result = processingEngine.mergeSkuOnly(config);
        
        return convertToSkuMergeRespVO(result);
    }



    /**
     * 转换为型号处理响应VO
     */
    private ProductModelProcessRespVO convertToModelProcessRespVO(ProcessingResult result) {
        ProductModelProcessRespVO respVO = new ProductModelProcessRespVO();
        respVO.setTotalProcessed(result.getTotalProcessed());
        respVO.setSuccessCount(result.getSuccessCount());
        respVO.setSkippedCount(result.getSkippedCount());
        respVO.setFailedCount(result.getFailedCount());
        respVO.setHasMore(result.getHasMore());
        respVO.setMessage(result.getMessage());
        return respVO;
    }

    /**
     * 转换为型号合并响应VO
     */
    private ProductModelMergeRespVO convertToModelMergeRespVO(ProcessingResult result) {
        ProductModelMergeRespVO respVO = new ProductModelMergeRespVO();
        respVO.setTotalModelGroups(result.getTotalGroups());
        respVO.setSuccessCount(result.getSuccessCount());
        respVO.setSkippedCount(result.getSkippedCount());
        respVO.setFailedCount(result.getFailedCount());
        respVO.setHasMore(result.getHasMore());
        respVO.setMessage(result.getMessage());
        return respVO;
    }

    /**
     * 转换为SKU合并响应VO
     */
    private ProductSkuMergeRespVO convertToSkuMergeRespVO(ProcessingResult result) {
        ProductSkuMergeRespVO respVO = new ProductSkuMergeRespVO();
        respVO.setTotalSkuGroups(result.getTotalGroups());
        respVO.setSuccessCount(result.getSuccessCount());
        respVO.setSkippedCount(result.getSkippedCount());
        respVO.setFailedCount(result.getFailedCount());
        respVO.setHasMore(result.getHasMore());
        respVO.setMessage(result.getMessage());
        return respVO;
    }
}

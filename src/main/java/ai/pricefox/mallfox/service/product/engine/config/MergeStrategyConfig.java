package ai.pricefox.mallfox.service.product.engine.config;

import lombok.Data;
import lombok.Builder;

import java.util.Arrays;
import java.util.List;

/**
 * 合并策略配置
 *
 * <AUTHOR>
 * @since 2025-01-10
 */
@Data
@Builder
public class MergeStrategyConfig {

    /**
     * SKU匹配字段列表
     * 默认：color, storage, condition_new, service_provider
     */
    @Builder.Default
    private List<String> skuMatchFields = Arrays.asList("color", "storage", "conditionNew", "serviceProvider");

    /**
     * 是否启用存储容量标准化匹配（忽略大小写和空格）
     */
    @Builder.Default
    private Boolean enableStorageNormalization = true;

    /**
     * 是否启用文本字段大小写不敏感匹配
     */
    @Builder.Default
    private Boolean enableCaseInsensitiveMatch = true;

    /**
     * 是否启用UPC优先匹配策略
     */
    @Builder.Default
    private Boolean enableUpcPriorityMatch = true;

    /**
     * 是否启用标准化型号匹配
     */
    @Builder.Default
    private Boolean enableNormalizedModelMatch = true;

    /**
     * 是否启用最新数据更新策略
     */
    @Builder.Default
    private Boolean enableLatestDataUpdate = true;

    /**
     * 是否启用非空字段更新策略
     */
    @Builder.Default
    private Boolean enableNonEmptyFieldUpdate = true;

    /**
     * 是否启用关联表同步更新
     */
    @Builder.Default
    private Boolean enableRelatedTableUpdate = true;

    /**
     * 批量更新大小
     */
    @Builder.Default
    private Integer batchUpdateSize = 100;

    /**
     * 获取默认配置
     */
    public static MergeStrategyConfig getDefault() {
        return MergeStrategyConfig.builder().build();
    }

    /**
     * 创建严格匹配配置
     */
    public static MergeStrategyConfig strictMatch() {
        return MergeStrategyConfig.builder()
                .enableStorageNormalization(false)
                .enableCaseInsensitiveMatch(false)
                .build();
    }

    /**
     * 创建宽松匹配配置
     */
    public static MergeStrategyConfig looseMatch() {
        return MergeStrategyConfig.builder()
                .enableStorageNormalization(true)
                .enableCaseInsensitiveMatch(true)
                .build();
    }

    /**
     * 创建仅UPC匹配配置
     */
    public static MergeStrategyConfig upcOnlyMatch() {
        return MergeStrategyConfig.builder()
                .enableUpcPriorityMatch(true)
                .enableNormalizedModelMatch(false)
                .build();
    }

    /**
     * 创建自定义SKU匹配字段配置
     */
    public static MergeStrategyConfig customSkuFields(List<String> fields) {
        return MergeStrategyConfig.builder()
                .skuMatchFields(fields)
                .build();
    }

    /**
     * 检查是否需要更新字段
     */
    public boolean shouldUpdateField(Object newValue, Object oldValue) {
        if (!enableNonEmptyFieldUpdate) {
            return true; // 如果不启用非空字段更新策略，则总是更新
        }
        
        // 新值为空或空字符串时不更新
        if (newValue == null || (newValue instanceof String && ((String) newValue).trim().isEmpty())) {
            return false;
        }
        
        return true;
    }
}

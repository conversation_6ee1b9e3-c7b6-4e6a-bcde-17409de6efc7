package ai.pricefox.mallfox.service.product.engine.config;

import lombok.Data;
import lombok.Builder;

import java.util.Arrays;
import java.util.List;

/**
 * 型号处理配置
 *
 * <AUTHOR>
 * @since 2025-01-10
 */
@Data
@Builder
public class ModelProcessingConfig {

    /**
     * 是否启用品牌去除功能
     */
    @Builder.Default
    private Boolean enableBrandRemoval = true;

    /**
     * 是否启用型号格式化功能
     */
    @Builder.Default
    private Boolean enableFormatting = true;

    /**
     * 需要去除的网络制式关键词（不区分大小写）
     */
    @Builder.Default
    private List<String> networkKeywordsToRemove = Arrays.asList("3G", "4G", "5G");

    /**
     * 是否保持原始空格结构
     */
    @Builder.Default
    private Boolean preserveSpaceStructure = true;

    /**
     * 是否启用智能单词匹配
     */
    @Builder.Default
    private Boolean enableSmartWordMatching = true;

    /**
     * 是否启用字符串匹配（当单词匹配失败时的备选方案）
     */
    @Builder.Default
    private Boolean enableStringMatching = true;

    /**
     * 是否要求完整单词匹配
     */
    @Builder.Default
    private Boolean requireCompleteWordMatch = true;

    /**
     * 获取默认配置
     */
    public static ModelProcessingConfig getDefault() {
        return ModelProcessingConfig.builder().build();
    }

    /**
     * 创建仅格式化配置（不去除品牌）
     */
    public static ModelProcessingConfig formatOnly() {
        return ModelProcessingConfig.builder()
                .enableBrandRemoval(false)
                .enableFormatting(true)
                .build();
    }

    /**
     * 创建仅去除品牌配置（不格式化）
     */
    public static ModelProcessingConfig brandRemovalOnly() {
        return ModelProcessingConfig.builder()
                .enableBrandRemoval(true)
                .enableFormatting(false)
                .build();
    }

    /**
     * 创建自定义网络制式关键词配置
     */
    public static ModelProcessingConfig customNetworkKeywords(List<String> keywords) {
        return ModelProcessingConfig.builder()
                .networkKeywordsToRemove(keywords)
                .build();
    }

    /**
     * 获取网络制式正则表达式
     */
    public String getNetworkKeywordsRegex() {
        if (networkKeywordsToRemove == null || networkKeywordsToRemove.isEmpty()) {
            return null;
        }
        
        StringBuilder regex = new StringBuilder("(?i)\\b(");
        for (int i = 0; i < networkKeywordsToRemove.size(); i++) {
            if (i > 0) {
                regex.append("|");
            }
            regex.append(networkKeywordsToRemove.get(i));
        }
        regex.append(")\\b");
        
        return regex.toString();
    }
}

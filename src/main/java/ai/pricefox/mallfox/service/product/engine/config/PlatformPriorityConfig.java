package ai.pricefox.mallfox.service.product.engine.config;

import lombok.Data;
import lombok.Builder;

import java.util.Arrays;
import java.util.List;

/**
 * 平台优先级配置
 *
 * <AUTHOR>
 * @since 2025-01-10
 */
@Data
@Builder
public class PlatformPriorityConfig {

    /**
     * 平台优先级列表，按优先级从高到低排序
     * 默认：Amazon > BestBuy > Walmart > eBay
     */
    @Builder.Default
    private List<String> priorityOrder = Arrays.asList("amazon", "bestbuy", "walmart", "ebay");

    /**
     * 是否启用平台优先级策略
     */
    @Builder.Default
    private Boolean enabled = true;

    /**
     * 获取默认配置
     */
    public static PlatformPriorityConfig getDefault() {
        return PlatformPriorityConfig.builder().build();
    }

    /**
     * 创建自定义优先级配置
     */
    public static PlatformPriorityConfig custom(List<String> priorityOrder) {
        return PlatformPriorityConfig.builder()
                .priorityOrder(priorityOrder)
                .build();
    }

    /**
     * 获取平台的优先级权重
     * 权重越高，优先级越高
     */
    public int getPlatformWeight(String platform) {
        if (!enabled || platform == null) {
            return 0;
        }
        
        int index = priorityOrder.indexOf(platform.toLowerCase());
        if (index == -1) {
            return 0; // 未知平台权重为0
        }
        
        // 权重 = 总数 - 索引，确保第一个平台权重最高
        return priorityOrder.size() - index;
    }

    /**
     * 比较两个平台的优先级
     * @return 正数表示platform1优先级更高，负数表示platform2优先级更高，0表示相等
     */
    public int comparePlatforms(String platform1, String platform2) {
        return Integer.compare(getPlatformWeight(platform1), getPlatformWeight(platform2));
    }
}

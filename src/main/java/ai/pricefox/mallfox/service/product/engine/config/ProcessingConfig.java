package ai.pricefox.mallfox.service.product.engine.config;

import lombok.Data;
import lombok.Builder;

/**
 * 商品数据处理配置
 *
 * <AUTHOR>
 * @since 2025-01-10
 */
@Data
@Builder
public class ProcessingConfig {

    /**
     * 是否预览模式，true=只返回处理结果不更新数据库
     */
    @Builder.Default
    private Boolean previewMode = false;

    /**
     * 数据来源平台，为空则处理所有平台
     */
    private String sourcePlatform;

    /**
     * 页码，从1开始
     */
    @Builder.Default
    private Integer pageNo = 1;

    /**
     * 每页数量
     */
    @Builder.Default
    private Integer pageSize = 100;

    /**
     * 是否只处理有品牌信息的数据（型号处理时使用）
     */
    @Builder.Default
    private Boolean onlyWithBrand = true;

    /**
     * 指定要处理的型号（型号合并时使用）
     */
    private String targetModel;

    /**
     * 指定要处理的SPU ID（SKU合并时使用）
     */
    private String targetSpuId;

    /**
     * 平台优先级配置
     */
    @Builder.Default
    private PlatformPriorityConfig platformPriority = PlatformPriorityConfig.getDefault();

    /**
     * 型号处理配置
     */
    @Builder.Default
    private ModelProcessingConfig modelProcessing = ModelProcessingConfig.getDefault();

    /**
     * 合并策略配置
     */
    @Builder.Default
    private MergeStrategyConfig mergeStrategy = MergeStrategyConfig.getDefault();

    /**
     * 是否启用UPC匹配优先策略
     */
    @Builder.Default
    private Boolean enableUpcPriority = true;

    /**
     * 是否启用最新数据更新策略
     */
    @Builder.Default
    private Boolean enableLatestDataUpdate = true;

    /**
     * 创建默认配置
     */
    public static ProcessingConfig getDefault() {
        return ProcessingConfig.builder().build();
    }

    /**
     * 创建预览模式配置
     */
    public static ProcessingConfig getPreviewConfig() {
        return ProcessingConfig.builder()
                .previewMode(true)
                .build();
    }

    /**
     * 创建指定平台配置
     */
    public static ProcessingConfig forPlatform(String platform) {
        return ProcessingConfig.builder()
                .sourcePlatform(platform)
                .build();
    }

    /**
     * 创建指定型号配置
     */
    public static ProcessingConfig forModel(String model) {
        return ProcessingConfig.builder()
                .targetModel(model)
                .build();
    }

    /**
     * 创建指定SPU配置
     */
    public static ProcessingConfig forSpu(String spuId) {
        return ProcessingConfig.builder()
                .targetSpuId(spuId)
                .build();
    }
}

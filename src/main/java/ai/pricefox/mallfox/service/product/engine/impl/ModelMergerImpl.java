package ai.pricefox.mallfox.service.product.engine.impl;

import ai.pricefox.mallfox.domain.product.ProductDataOffers;
import ai.pricefox.mallfox.domain.product.ProductDataSimplify;
import ai.pricefox.mallfox.enums.DataChannelEnum;
import ai.pricefox.mallfox.mapper.product.ProductDataSimplifyMapper;
import ai.pricefox.mallfox.model.dto.ProductModelMergeDTO;
import ai.pricefox.mallfox.model.dto.ProductModelUpcDTO;
import ai.pricefox.mallfox.service.product.ProductDataOffersService;
import ai.pricefox.mallfox.service.product.ProductDataSimplifyService;
import ai.pricefox.mallfox.service.product.engine.config.ProcessingConfig;
import ai.pricefox.mallfox.service.product.engine.processor.ModelMerger;
import ai.pricefox.mallfox.service.product.engine.result.ProcessingResult;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 商品型号合并器实现
 *
 * <AUTHOR>
 * @since 2025-01-10
 */
@Service
@Slf4j
public class ModelMergerImpl implements ModelMerger {

    @Autowired
    private ProductDataSimplifyService simplifyService;

    @Autowired
    private ProductDataSimplifyMapper baseMapper;

    @Autowired
    private ProductDataOffersService offersService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProcessingResult mergeByModel(String targetModel, ProcessingConfig config) {
        log.info("合并指定型号：{}", targetModel);
        
        LocalDateTime startTime = LocalDateTime.now();
        
        try {
            List<String> targetModels = Collections.singletonList(targetModel);
            int[] counts = processModelList(targetModels, config);
            
            LocalDateTime endTime = LocalDateTime.now();
            String mode = Boolean.TRUE.equals(config.getPreviewMode()) ? "预览" : "合并";
            String message = String.format("%s完成，共处理%d个型号组，成功合并%d条记录，跳过%d条，失败%d条",
                    mode, counts[0], counts[1], counts[2], counts[3]);

            return ProcessingResult.withStats(counts[1] + counts[2] + counts[3], counts[1], counts[2], counts[3])
                    .setDuration(startTime, endTime);
                    
        } catch (Exception e) {
            log.error("合并指定型号失败：{}", targetModel, e);
            return ProcessingResult.failure(e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProcessingResult mergeAllModels(ProcessingConfig config) {
        log.info("批量合并所有型号数据");
        
        LocalDateTime startTime = LocalDateTime.now();
        
        try {
            List<String> allModels = getAllDistinctModels();
            log.info("共找到{}个不重复的型号需要处理", allModels.size());
            
            int[] counts = processModelList(allModels, config);
            
            LocalDateTime endTime = LocalDateTime.now();
            String mode = Boolean.TRUE.equals(config.getPreviewMode()) ? "预览" : "合并";
            String message = String.format("%s完成，共处理%d个型号组，成功合并%d条记录，跳过%d条，失败%d条",
                    mode, counts[0], counts[1], counts[2], counts[3]);

            return ProcessingResult.withStats(counts[1] + counts[2] + counts[3], counts[1], counts[2], counts[3])
                    .setDuration(startTime, endTime);
                    
        } catch (Exception e) {
            log.error("批量合并所有型号数据失败", e);
            return ProcessingResult.failure(e.getMessage());
        }
    }

    @Override
    public boolean mergeModelGroup(List<ProductModelMergeDTO> group, ProcessingConfig config) {
        try {
            // 选择基准数据
            ProductModelMergeDTO baseData = selectBaseData(group, config);
            
            // 需要更新的数据（排除基准数据）
            List<ProductModelMergeDTO> toUpdate = group.stream()
                    .filter(item -> !item.getId().equals(baseData.getId()))
                    .collect(Collectors.toList());

            if (!Boolean.TRUE.equals(config.getPreviewMode())) {
                // 获取所有相关记录的完整信息，用于后续数据更新
                List<Long> allIds = group.stream().map(ProductModelMergeDTO::getId).collect(Collectors.toList());
                List<ProductDataSimplify> allRecords = simplifyService.listByIds(allIds);

                // 按平台分组，每个平台内部找到创建时间最新的记录作为数据源
                Map<String, List<ProductDataSimplify>> platformGroups = allRecords.stream()
                        .collect(Collectors.groupingBy(ProductDataSimplify::getSourcePlatform));

                // 更新ProductDataSimplify表
                List<ProductDataSimplify> simplifyUpdateList = new ArrayList<>();
                for (ProductModelMergeDTO item : toUpdate) {
                    // 只有同一平台的最新记录才能更新其他字段
                    String platform = item.getSourcePlatform();
                    List<ProductDataSimplify> platformRecords = platformGroups.get(platform);

                    ProductDataSimplify updateEntity = new ProductDataSimplify();
                    updateEntity.setId(item.getId());
                    updateEntity.setSpuId(baseData.getSpuId());
                    updateEntity.setUpdateTime(LocalDateTime.now());

                    if (platformRecords != null && !platformRecords.isEmpty()) {
                        // 找到该平台创建时间最新的记录作为数据源
                        ProductDataSimplify latestRecord = platformRecords.stream()
                                .max(Comparator.comparing(ProductDataSimplify::getCreateTime))
                                .orElse(null);

                        if (latestRecord != null) {
                            log.info("选择平台{}最新记录作为数据源，ID: {}, 创建时间: {}",
                                    platform, latestRecord.getId(), latestRecord.getCreateTime());

                            // 根据同一平台的最新记录更新其他字段（非空字段才更新）
                            updateNonEmptySimplifyFields(updateEntity, latestRecord, config);
                        }
                    }

                    simplifyUpdateList.add(updateEntity);
                }

                if (!simplifyUpdateList.isEmpty()) {
                    simplifyService.updateBatchById(simplifyUpdateList);
                    log.info("更新ProductDataSimplify表 {} 条记录", simplifyUpdateList.size());
                }

                // 更新ProductDataOffers表
                for (ProductModelMergeDTO item : toUpdate) {
                    LambdaUpdateWrapper<ProductDataOffers> updateWrapper = new LambdaUpdateWrapper<>();
                    updateWrapper.eq(ProductDataOffers::getSpuId, item.getSpuId())
                            .set(ProductDataOffers::getSpuId, baseData.getSpuId())
                            .set(ProductDataOffers::getUpdateTime, LocalDateTime.now());

                    boolean updateResult = offersService.update(updateWrapper);
                    log.info("更新ProductDataOffers表(仅SpuId匹配)，原SPU: {}, 新SPU: {}, 更新结果: {}",
                            item.getSpuId(), baseData.getSpuId(), updateResult ? "成功" : "失败");
                }
            }
            
            return true;
        } catch (Exception e) {
            log.error("合并型号组失败", e);
            return false;
        }
    }

    @Override
    public ProductModelMergeDTO selectBaseData(List<ProductModelMergeDTO> group, ProcessingConfig config) {
        // 根据平台优先级选择基准数据
        List<String> priorityOrder = config.getPlatformPriority().getPriorityOrder();
        
        for (String platform : priorityOrder) {
            Optional<ProductModelMergeDTO> platformData = group.stream()
                    .filter(item -> platform.equalsIgnoreCase(item.getSourcePlatform()))
                    .findFirst();
            if (platformData.isPresent()) {
                return platformData.get();
            }
        }
        
        // 如果没有找到优先平台的数据，返回第一条
        return group.get(0);
    }

    @Override
    public int[] mergeByUpcMatch(List<ProductModelMergeDTO> dataList, ProcessingConfig config) {
        int totalGroups = 0;
        int successCount = 0;
        int skippedCount = 0;
        int failedCount = 0;

        if (!config.getEnableUpcPriority()) {
            return new int[]{totalGroups, successCount, skippedCount, failedCount};
        }

        try {
            // 获取所有SPU ID
            List<String> spuIds = dataList.stream()
                    .map(ProductModelMergeDTO::getSpuId)
                    .distinct()
                    .collect(Collectors.toList());

            // 查询UPC信息并按UPC分组
            List<ProductModelUpcDTO> upcList = baseMapper.selectUpcInfoBySpuIds(spuIds);
            Map<String, List<ProductModelUpcDTO>> upcGroups = upcList.stream()
                    .filter(upc -> upc.getUpcCode() != null && !upc.getUpcCode().trim().isEmpty())
                    .collect(Collectors.groupingBy(ProductModelUpcDTO::getUpcCode));

            // 处理UPC一致的组
            for (Map.Entry<String, List<ProductModelUpcDTO>> upcEntry : upcGroups.entrySet()) {
                List<ProductModelUpcDTO> upcGroup = upcEntry.getValue();
                if (upcGroup.size() > 1) {
                    // UPC一致，直接合并
                    List<String> upcSpuIds = upcGroup.stream()
                            .map(ProductModelUpcDTO::getSpuId)
                            .collect(Collectors.toList());

                    List<ProductModelMergeDTO> upcMergeGroup = dataList.stream()
                            .filter(item -> upcSpuIds.contains(item.getSpuId()))
                            .collect(Collectors.toList());

                    if (upcMergeGroup.size() > 1) {
                        totalGroups++;
                        try {
                            if (mergeModelGroup(upcMergeGroup, config)) {
                                successCount += upcMergeGroup.size();
                            } else {
                                failedCount += upcMergeGroup.size();
                            }
                            log.info("基于UPC一致性合并型号组，UPC: {}, 合并记录数: {}", upcEntry.getKey(), upcMergeGroup.size());
                        } catch (Exception e) {
                            log.error("基于UPC合并型号组失败，UPC: {}, 原因: {}", upcEntry.getKey(), e.getMessage());
                            failedCount += upcMergeGroup.size();
                        }
                    }

                    // 从原数据列表中移除已处理的数据
                    dataList.removeIf(item -> upcSpuIds.contains(item.getSpuId()));
                }
            }
        } catch (Exception e) {
            log.error("UPC匹配合并失败", e);
        }

        return new int[]{totalGroups, successCount, skippedCount, failedCount};
    }

    @Override
    public int[] mergeByNormalizedModel(List<ProductModelMergeDTO> dataList, ProcessingConfig config) {
        int totalGroups = 0;
        int successCount = 0;
        int skippedCount = 0;
        int failedCount = 0;

        try {
            // 按标准化型号分组
            Map<String, List<ProductModelMergeDTO>> modelGroups = dataList.stream()
                    .collect(Collectors.groupingBy(ProductModelMergeDTO::getNormalizedModel));

            // 处理每个型号组
            for (List<ProductModelMergeDTO> group : modelGroups.values()) {
                if (group.size() <= 1) {
                    skippedCount += group.size();
                    continue;
                }

                totalGroups++;
                try {
                    if (mergeModelGroup(group, config)) {
                        successCount += group.size();
                    } else {
                        failedCount += group.size();
                    }
                } catch (Exception e) {
                    log.error("合并型号组失败，型号: {}, 原因: {}", group.get(0).getModel(), e.getMessage());
                    failedCount += group.size();
                }
            }
        } catch (Exception e) {
            log.error("标准化型号匹配合并失败", e);
        }

        return new int[]{totalGroups, successCount, skippedCount, failedCount};
    }

    @Override
    public List<String> getAllDistinctModels() {
        List<String> crawlerModels = simplifyService.getDistinctModelsByDataChannel(DataChannelEnum.CRAWLER.getCode());
        List<String> apiModels = simplifyService.getDistinctModelsByDataChannel(DataChannelEnum.API.getCode());

        // 合并并去重
        Set<String> allModelsSet = new HashSet<>();
        if (crawlerModels != null) {
            allModelsSet.addAll(crawlerModels);
        }
        if (apiModels != null) {
            allModelsSet.addAll(apiModels);
        }

        return new ArrayList<>(allModelsSet);
    }

    /**
     * 处理型号列表
     */
    private int[] processModelList(List<String> models, ProcessingConfig config) {
        int totalModelGroups = 0;
        int successCount = 0;
        int skippedCount = 0;
        int failedCount = 0;

        // 循环处理每个型号
        for (String model : models) {
            try {
                log.debug("开始处理型号：{}", model);

                // 创建分页对象，设置较大的页面大小以获取该型号的所有数据
                Page<ProductModelMergeDTO> page = new Page<>(1, 500);

                // 查询该型号的数据
                Page<ProductModelMergeDTO> dataPage = baseMapper.selectProductModelMergeData(
                        page, config.getSourcePlatform(), model);

                List<ProductModelMergeDTO> dataList = dataPage.getRecords();
                if (dataList.isEmpty()) {
                    log.debug("型号{}无数据，跳过", model);
                    continue;
                }

                // 先处理UPC一致的组
                int[] upcCounts = mergeByUpcMatch(dataList, config);
                totalModelGroups += upcCounts[0];
                successCount += upcCounts[1];
                skippedCount += upcCounts[2];
                failedCount += upcCounts[3];

                // 对剩余数据按标准化型号分组处理
                if (!dataList.isEmpty()) {
                    int[] modelCounts = mergeByNormalizedModel(dataList, config);
                    totalModelGroups += modelCounts[0];
                    successCount += modelCounts[1];
                    skippedCount += modelCounts[2];
                    failedCount += modelCounts[3];
                }
            } catch (Exception e) {
                log.error("处理型号{}时发生异常: {}", model, e.getMessage());
                failedCount++;
            }
        }

        return new int[]{totalModelGroups, successCount, skippedCount, failedCount};
    }

    /**
     * 更新ProductDataSimplify表的非空字段
     *
     * @param updateEntity 要更新的实体
     * @param latestRecord 最新记录
     * @param config 处理配置
     */
    private void updateNonEmptySimplifyFields(ProductDataSimplify updateEntity,
                                             ProductDataSimplify latestRecord, ProcessingConfig config) {
        if (!config.getMergeStrategy().getEnableNonEmptyFieldUpdate()) {
            return;
        }

        // 更新基本信息字段（非空才更新）
        if (isNotEmpty(latestRecord.getModel())) {
            updateEntity.setModel(latestRecord.getModel());
        }
        if (isNotEmpty(latestRecord.getModelBack())) {
            updateEntity.setModelBack(latestRecord.getModelBack());
        }
        if (isNotEmpty(latestRecord.getModelYear())) {
            updateEntity.setModelYear(latestRecord.getModelYear());
        }
        if (isNotEmpty(latestRecord.getColor())) {
            updateEntity.setColor(latestRecord.getColor());
        }
        if (isNotEmpty(latestRecord.getStorage())) {
            updateEntity.setStorage(latestRecord.getStorage());
        }
        if (isNotEmpty(latestRecord.getServiceProvider())) {
            updateEntity.setServiceProvider(latestRecord.getServiceProvider());
        }
        if (isNotEmpty(latestRecord.getConditionNew())) {
            updateEntity.setConditionNew(latestRecord.getConditionNew());
        }

        // 更新平台相关字段
        if (isNotEmpty(latestRecord.getPlatformSpuId())) {
            updateEntity.setPlatformSpuId(latestRecord.getPlatformSpuId());
        }
        if (isNotEmpty(latestRecord.getPlatformSkuId())) {
            updateEntity.setPlatformSkuId(latestRecord.getPlatformSkuId());
        }

        // 更新图片字段
        if (isNotEmpty(latestRecord.getProductMainImageUrls())) {
            updateEntity.setProductMainImageUrls(latestRecord.getProductMainImageUrls());
        }
        if (isNotEmpty(latestRecord.getProductSpecColorUrl())) {
            updateEntity.setProductSpecColorUrl(latestRecord.getProductSpecColorUrl());
        }

        // 更新硬件规格字段
        if (isNotEmpty(latestRecord.getRamMemoryInstalledSize())) {
            updateEntity.setRamMemoryInstalledSize(latestRecord.getRamMemoryInstalledSize());
        }
        if (isNotEmpty(latestRecord.getOperatingSystem())) {
            updateEntity.setOperatingSystem(latestRecord.getOperatingSystem());
        }
        if (isNotEmpty(latestRecord.getProcessor())) {
            updateEntity.setProcessor(latestRecord.getProcessor());
        }
        if (isNotEmpty(latestRecord.getCellularTechnology())) {
            updateEntity.setCellularTechnology(latestRecord.getCellularTechnology());
        }

        // 更新显示相关字段
        if (isNotEmpty(latestRecord.getScreenSize())) {
            updateEntity.setScreenSize(latestRecord.getScreenSize());
        }
        if (isNotEmpty(latestRecord.getResolution())) {
            updateEntity.setResolution(latestRecord.getResolution());
        }
        if (isNotEmpty(latestRecord.getRefreshRate())) {
            updateEntity.setRefreshRate(latestRecord.getRefreshRate());
        }
        if (isNotEmpty(latestRecord.getDisplayType())) {
            updateEntity.setDisplayType(latestRecord.getDisplayType());
        }

        // 更新电池和摄像头字段
        if (isNotEmpty(latestRecord.getBatteryPower())) {
            updateEntity.setBatteryPower(latestRecord.getBatteryPower());
        }
        if (isNotEmpty(latestRecord.getAverageTalkTime())) {
            updateEntity.setAverageTalkTime(latestRecord.getAverageTalkTime());
        }
        if (isNotEmpty(latestRecord.getBatteryChargeTime())) {
            updateEntity.setBatteryChargeTime(latestRecord.getBatteryChargeTime());
        }
        if (isNotEmpty(latestRecord.getFrontPhotoSensorResolution())) {
            updateEntity.setFrontPhotoSensorResolution(latestRecord.getFrontPhotoSensorResolution());
        }
        if (isNotEmpty(latestRecord.getRearFacingCameraPhotoSensorResolution())) {
            updateEntity.setRearFacingCameraPhotoSensorResolution(latestRecord.getRearFacingCameraPhotoSensorResolution());
        }
        if (latestRecord.getNumberOfRearFacingCameras() != null) {
            updateEntity.setNumberOfRearFacingCameras(latestRecord.getNumberOfRearFacingCameras());
        }
        if (isNotEmpty(latestRecord.getEffectiveVideoResolution())) {
            updateEntity.setEffectiveVideoResolution(latestRecord.getEffectiveVideoResolution());
        }
        if (isNotEmpty(latestRecord.getVideoCaptureResolution())) {
            updateEntity.setVideoCaptureResolution(latestRecord.getVideoCaptureResolution());
        }

        // 更新连接和物理特性字段
        if (isNotEmpty(latestRecord.getSimCardSlotCount())) {
            updateEntity.setSimCardSlotCount(latestRecord.getSimCardSlotCount());
        }
        if (isNotEmpty(latestRecord.getConnectorType())) {
            updateEntity.setConnectorType(latestRecord.getConnectorType());
        }
        if (isNotEmpty(latestRecord.getWaterResistance())) {
            updateEntity.setWaterResistance(latestRecord.getWaterResistance());
        }
        if (isNotEmpty(latestRecord.getDimensions())) {
            updateEntity.setDimensions(latestRecord.getDimensions());
        }
        if (isNotEmpty(latestRecord.getItemWeight())) {
            updateEntity.setItemWeight(latestRecord.getItemWeight());
        }
        if (isNotEmpty(latestRecord.getBiometricSecurityFeature())) {
            updateEntity.setBiometricSecurityFeature(latestRecord.getBiometricSecurityFeature());
        }
        if (isNotEmpty(latestRecord.getSupportedSatelliteNavigationSystem())) {
            updateEntity.setSupportedSatelliteNavigationSystem(latestRecord.getSupportedSatelliteNavigationSystem());
        }

        // 更新功能和政策字段
        if (isNotEmpty(latestRecord.getFeatures())) {
            updateEntity.setFeatures(latestRecord.getFeatures());
        }
        if (isNotEmpty(latestRecord.getReturnPolicy())) {
            updateEntity.setReturnPolicy(latestRecord.getReturnPolicy());
        }
        if (isNotEmpty(latestRecord.getPaymentInstallment())) {
            updateEntity.setPaymentInstallment(latestRecord.getPaymentInstallment());
        }
        if (isNotEmpty(latestRecord.getInstallPayment())) {
            updateEntity.setInstallPayment(latestRecord.getInstallPayment());
        }
        if (isNotEmpty(latestRecord.getWarrantyDescription())) {
            updateEntity.setWarrantyDescription(latestRecord.getWarrantyDescription());
        }
        if (isNotEmpty(latestRecord.getShippingTime())) {
            updateEntity.setShippingTime(latestRecord.getShippingTime());
        }

        // 更新评价相关字段
        if (latestRecord.getReviewNumber() != null) {
            updateEntity.setReviewNumber(latestRecord.getReviewNumber());
        }
        if (latestRecord.getReviewScore() != null) {
            updateEntity.setReviewScore(latestRecord.getReviewScore());
        }
        if (isNotEmpty(latestRecord.getReviewRatingDistribution())) {
            updateEntity.setReviewRatingDistribution(latestRecord.getReviewRatingDistribution());
        }
        if (isNotEmpty(latestRecord.getReviewDimensionalRatings())) {
            updateEntity.setReviewDimensionalRatings(latestRecord.getReviewDimensionalRatings());
        }
        if (isNotEmpty(latestRecord.getReviewOverviewProsCons())) {
            updateEntity.setReviewOverviewProsCons(latestRecord.getReviewOverviewProsCons());
        }
        if (isNotEmpty(latestRecord.getReviewProsConsByStar())) {
            updateEntity.setReviewProsConsByStar(latestRecord.getReviewProsConsByStar());
        }

        // 更新数据渠道和标记字段
        if (latestRecord.getDataChannel() != null) {
            updateEntity.setDataChannel(latestRecord.getDataChannel());
        }
        if (latestRecord.getMark() != null) {
            updateEntity.setMark(latestRecord.getMark());
        }
    }

    /**
     * 检查字符串是否非空
     */
    private boolean isNotEmpty(String value) {
        return org.springframework.util.StringUtils.hasText(value);
    }
}

package ai.pricefox.mallfox.service.product.engine.impl;

import ai.pricefox.mallfox.common.exception.enums.ErrorCodeConstants;
import ai.pricefox.mallfox.common.exception.util.ServiceExceptionUtil;
import ai.pricefox.mallfox.domain.product.ProductDataSimplify;
import ai.pricefox.mallfox.mapper.product.ProductDataSimplifyMapper;
import ai.pricefox.mallfox.model.dto.ProductModelProcessDTO;
import ai.pricefox.mallfox.service.product.ProductDataSimplifyService;
import ai.pricefox.mallfox.service.product.engine.config.ProcessingConfig;
import ai.pricefox.mallfox.service.product.engine.processor.ModelProcessor;
import ai.pricefox.mallfox.service.product.engine.result.ProcessingResult;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 商品型号处理器实现
 *
 * <AUTHOR>
 * @since 2025-01-10
 */
@Service
@Slf4j
public class ModelProcessorImpl implements ModelProcessor {

    @Autowired
    private ProductDataSimplifyService simplifyService;

    @Autowired
    private ProductDataSimplifyMapper baseMapper;

    @Override
    public String processModelString(String originalModel, String brand, ProcessingConfig config) {
        if (!StringUtils.hasText(originalModel) || !StringUtils.hasText(brand)) {
            return null;
        }

        // 检查是否启用品牌去除
        if (!config.getModelProcessing().getEnableBrandRemoval()) {
            return config.getModelProcessing().getEnableFormatting() ? 
                    formatModelString(originalModel, config) : originalModel;
        }

        // 去除空格进行比较
        String modelForCompare = originalModel.replaceAll("\\s+", "").toLowerCase();
        String brandForCompare = brand.replaceAll("\\s+", "").toLowerCase();

        // 检查品牌是否存在于型号中
        if (!modelForCompare.contains(brandForCompare)) {
            return null;
        }

        // 智能去除品牌内容，保持原始空格结构
        String processedModel = removeBrandFromModel(originalModel, brand, config);

        if (processedModel == null || processedModel.trim().isEmpty()) {
            return null;
        }

        // 格式化型号
        return config.getModelProcessing().getEnableFormatting() ? 
                formatModelString(processedModel, config) : processedModel;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProcessingResult processExistingModels(ProcessingConfig config) {
        log.info("开始处理存量商品型号，配置：{}", config);

        LocalDateTime startTime = LocalDateTime.now();
        int totalProcessed = 0;
        int successCount = 0;
        int skippedCount = 0;
        int failedCount = 0;

        try {
            // 创建分页对象
            Page<ProductModelProcessDTO> page = new Page<>(config.getPageNo(), config.getPageSize());

            // 查询需要处理的数据
            Page<ProductModelProcessDTO> dataPage = baseMapper.selectProductModelProcessData(
                    page, config.getSourcePlatform(), config.getOnlyWithBrand());

            List<ProductModelProcessDTO> dataList = dataPage.getRecords();
            if (dataList.isEmpty()) {
                throw ServiceExceptionUtil.exception(ErrorCodeConstants.DATA_MODEL_NO_DATA_TO_PROCESS);
            }

            totalProcessed = dataList.size();
            List<ProductDataSimplify> updateList = new ArrayList<>();

            // 处理每条数据
            for (ProductModelProcessDTO dto : dataList) {
                try {
                    String processedModel = processModelString(dto.getOriginalModel(), dto.getBrand(), config);

                    if (processedModel != null && !processedModel.equals(dto.getOriginalModel())) {
                        // 如果不是预览模式，准备更新数据
                        if (!Boolean.TRUE.equals(config.getPreviewMode())) {
                            ProductDataSimplify updateEntity = new ProductDataSimplify();
                            updateEntity.setId(dto.getId());
                            updateEntity.setModelBack(dto.getOriginalModel());
                            updateEntity.setModel(processedModel);
                            updateEntity.setUpdateTime(LocalDateTime.now());
                            updateList.add(updateEntity);
                        }
                        successCount++;
                    } else {
                        skippedCount++;
                    }
                } catch (Exception e) {
                    log.error("处理型号失败，ID: {}, 原始型号: {}", dto.getId(), dto.getOriginalModel(), e);
                    failedCount++;
                }
            }

            // 批量更新数据库
            if (!Boolean.TRUE.equals(config.getPreviewMode()) && !updateList.isEmpty()) {
                simplifyService.updateBatchById(updateList);
                log.info("批量更新商品型号 {} 条记录", updateList.size());
            }

            LocalDateTime endTime = LocalDateTime.now();
            String mode = Boolean.TRUE.equals(config.getPreviewMode()) ? "预览" : "处理";
            String message = String.format("%s完成，共%s%d条记录，成功%d条，跳过%d条，失败%d条",
                    mode, mode, totalProcessed, successCount, skippedCount, failedCount);

            return ProcessingResult.withStats(totalProcessed, successCount, skippedCount, failedCount)
                    .setDuration(startTime, endTime);

        } catch (Exception e) {
            log.error("处理存量商品型号失败", e);
            return ProcessingResult.failure(e.getMessage());
        }
    }

    @Override
    public String formatModelString(String model, ProcessingConfig config) {
        if (!StringUtils.hasText(model)) {
            return model;
        }

        if (!config.getModelProcessing().getEnableFormatting()) {
            return model;
        }

        String formatted = model;

        // 去除网络制式关键词
        String networkRegex = config.getModelProcessing().getNetworkKeywordsRegex();
        if (networkRegex != null) {
            formatted = formatted.replaceAll(networkRegex, "");
        }

        // 去除多余空格
        formatted = formatted.replaceAll("\\s+", " ").trim();

        return formatted;
    }

    @Override
    public String removeBrandFromModel(String originalModel, String brand, ProcessingConfig config) {
        if (!config.getModelProcessing().getEnableBrandRemoval()) {
            return originalModel;
        }

        // 先尝试精确的单词匹配
        String result = removeByWordMatching(originalModel, brand, config);

        // 如果单词匹配失败，尝试字符串匹配（去除空格后）
        if (result.equals(originalModel) && config.getModelProcessing().getEnableStringMatching()) {
            result = removeByStringMatching(originalModel, brand, config);
        }

        return result;
    }

    @Override
    public boolean validateProcessedModel(String originalModel, String processedModel, String brand) {
        if (!StringUtils.hasText(processedModel)) {
            return false;
        }

        // 检查处理后的型号是否为空或只包含空格
        if (processedModel.trim().isEmpty()) {
            return false;
        }

        // 检查处理后的型号长度是否合理（不能太短）
        if (processedModel.trim().length() < 2) {
            return false;
        }

        // 检查是否还包含品牌信息（可能去除不完全）
        if (StringUtils.hasText(brand)) {
            String processedLower = processedModel.toLowerCase();
            String brandLower = brand.toLowerCase();
            if (processedLower.contains(brandLower)) {
                // 如果还包含品牌，检查是否是合理的情况（如品牌是型号的一部分）
                return processedModel.length() > brand.length() * 1.5;
            }
        }

        return true;
    }

    /**
     * 通过单词匹配去除品牌
     */
    private String removeByWordMatching(String originalModel, String brand, ProcessingConfig config) {
        if (!config.getModelProcessing().getEnableSmartWordMatching()) {
            return originalModel;
        }

        // 将原始字符串按空格分割成单词数组
        String[] modelWords = originalModel.trim().split("\\s+");
        String[] brandWords = brand.trim().split("\\s+");

        // 如果品牌只有一个单词，使用简单匹配
        if (brandWords.length == 1) {
            return removeSingleWordBrand(modelWords, brandWords[0]);
        }

        // 如果品牌有多个单词，使用连续匹配
        return removeMultiWordBrand(modelWords, brandWords);
    }

    /**
     * 通过字符串匹配去除品牌（去除空格后比较）
     */
    private String removeByStringMatching(String originalModel, String brand, ProcessingConfig config) {
        String modelNoSpaces = originalModel.replaceAll("\\s+", "").toLowerCase();
        String brandNoSpaces = brand.replaceAll("\\s+", "").toLowerCase();

        if (!modelNoSpaces.contains(brandNoSpaces)) {
            return originalModel;
        }

        // 检查是否是完整的单词匹配（考虑单词边界）
        if (config.getModelProcessing().getRequireCompleteWordMatch() && 
            !isCompleteWordMatch(originalModel, brand)) {
            return originalModel;
        }

        // 找到品牌在原始字符串中的位置（忽略空格）
        int brandStart = findBrandPosition(originalModel, brand);
        if (brandStart == -1) {
            return originalModel;
        }

        // 计算品牌在原始字符串中的结束位置
        int brandEnd = findBrandEndPosition(originalModel, brand, brandStart);

        // 去除品牌部分
        String before = originalModel.substring(0, brandStart).trim();
        String after = originalModel.substring(brandEnd).trim();

        if (before.isEmpty()) {
            return after;
        } else if (after.isEmpty()) {
            return before;
        } else {
            return before + " " + after;
        }
    }

    /**
     * 去除单个单词的品牌
     */
    private String removeSingleWordBrand(String[] modelWords, String brandWord) {
        StringBuilder result = new StringBuilder();
        boolean first = true;

        for (String word : modelWords) {
            // 不区分大小写比较
            if (!word.equalsIgnoreCase(brandWord)) {
                if (!first) {
                    result.append(" ");
                }
                result.append(word);
                first = false;
            }
        }

        return result.toString();
    }

    /**
     * 去除多个单词的品牌（连续匹配）
     */
    private String removeMultiWordBrand(String[] modelWords, String[] brandWords) {
        StringBuilder result = new StringBuilder();
        boolean first = true;

        for (int i = 0; i < modelWords.length; i++) {
            // 检查从当前位置开始是否匹配品牌的所有单词
            boolean isMatch = true;
            if (i + brandWords.length <= modelWords.length) {
                for (int j = 0; j < brandWords.length; j++) {
                    if (!modelWords[i + j].equalsIgnoreCase(brandWords[j])) {
                        isMatch = false;
                        break;
                    }
                }
            } else {
                isMatch = false;
            }

            if (isMatch) {
                // 跳过整个品牌
                i += brandWords.length - 1;
            } else {
                // 保留当前单词
                if (!first) {
                    result.append(" ");
                }
                result.append(modelWords[i]);
                first = false;
            }
        }

        return result.toString();
    }

    /**
     * 检查是否是完整的单词匹配
     */
    private boolean isCompleteWordMatch(String originalModel, String brand) {
        // 简化实现：检查品牌前后是否有单词边界
        String pattern = "\\b" + brand.replaceAll("\\s+", "\\\\s+") + "\\b";
        return originalModel.matches(".*" + pattern + ".*");
    }

    /**
     * 找到品牌在原始字符串中的位置
     */
    private int findBrandPosition(String originalModel, String brand) {
        String modelLower = originalModel.toLowerCase();
        String brandLower = brand.toLowerCase();
        
        // 简化实现：直接查找第一个匹配位置
        return modelLower.indexOf(brandLower);
    }

    /**
     * 找到品牌在原始字符串中的结束位置
     */
    private int findBrandEndPosition(String originalModel, String brand, int brandStart) {
        return brandStart + brand.length();
    }
}

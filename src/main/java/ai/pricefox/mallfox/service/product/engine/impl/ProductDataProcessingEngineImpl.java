package ai.pricefox.mallfox.service.product.engine.impl;

import ai.pricefox.mallfox.model.dto.ProductDataDTO;
import ai.pricefox.mallfox.service.product.ProductDataSimplifyService;
import ai.pricefox.mallfox.service.product.engine.ProductDataProcessingEngine;
import ai.pricefox.mallfox.service.product.engine.config.ProcessingConfig;
import ai.pricefox.mallfox.service.product.engine.processor.ModelMerger;
import ai.pricefox.mallfox.service.product.engine.processor.ModelProcessor;
import ai.pricefox.mallfox.service.product.engine.processor.SkuMerger;
import ai.pricefox.mallfox.service.product.engine.result.ProcessingResult;
import ai.pricefox.mallfox.service.product.engine.result.StepResult;
import ai.pricefox.mallfox.service.product.impl.ProductMatchingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 商品数据处理引擎实现
 *
 * <AUTHOR>
 * @since 2025-01-10
 */
@Service
@Slf4j
public class ProductDataProcessingEngineImpl implements ProductDataProcessingEngine {

    @Autowired
    private ModelProcessor modelProcessor;

    @Autowired
    private ModelMerger modelMerger;

    @Autowired
    private SkuMerger skuMerger;

    @Autowired
    private ProductMatchingService matchingService;

    @Autowired
    private ProductDataSimplifyService simplifyService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProcessingResult processNewData(List<ProductDataDTO> dataList, ProcessingConfig config) {
        log.info("开始处理新增商品数据，数据量：{}", dataList.size());

        LocalDateTime startTime = LocalDateTime.now();
        ProcessingResult result = ProcessingResult.builder()
                .startTime(startTime)
                .build();

        try {
            // 步骤：数据匹配和保存
            StepResult matchStep = processDataMatching(dataList, config);
            result.addStepResult(matchStep);

            // 汇总结果
            result.setSuccess(true);
            result.setTotalProcessed(dataList.size());
            result.setMessage("新增数据处理完成");

        } catch (Exception e) {
            log.error("处理新增商品数据失败", e);
            result.setSuccess(false);
            result.setErrorMessage(e.getMessage());
        }

        LocalDateTime endTime = LocalDateTime.now();
        result.setDuration(startTime, endTime);

        log.info("新增商品数据处理完成，耗时：{}ms", result.getDuration());
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProcessingResult processExistingData(ProcessingConfig config) {
        log.info("开始处理存量商品数据");
        
        LocalDateTime startTime = LocalDateTime.now();
        ProcessingResult result = ProcessingResult.builder()
                .startTime(startTime)
                .build();

        try {
            // 步骤1：型号处理
            ProcessingResult modelProcessResult = modelProcessor.processExistingModels(config);
            mergeProcessingResults(result, modelProcessResult);

            // 添加步骤结果
            StepResult modelProcessStep = convertToStepResult("型号处理", modelProcessResult);
            result.addStepResult(modelProcessStep);

            // 步骤2：型号合并
            ProcessingResult modelMergeResult = modelMerger.mergeAllModels(config);
            mergeProcessingResults(result, modelMergeResult);

            // 添加步骤结果
            StepResult modelMergeStep = convertToStepResult("型号合并", modelMergeResult);
            result.addStepResult(modelMergeStep);

            // 步骤3：SKU合并
            ProcessingResult skuMergeResult = skuMerger.mergeAllSkus(config);
            mergeProcessingResults(result, skuMergeResult);

            // 添加步骤结果
            StepResult skuMergeStep = convertToStepResult("SKU合并", skuMergeResult);
            result.addStepResult(skuMergeStep);

            // 汇总结果
            result.setSuccess(true);
            result.setMessage("存量数据处理完成");

        } catch (Exception e) {
            log.error("处理存量商品数据失败", e);
            result.setSuccess(false);
            result.setErrorMessage(e.getMessage());
        }

        LocalDateTime endTime = LocalDateTime.now();
        result.setDuration(startTime, endTime);
        
        log.info("存量商品数据处理完成，耗时：{}ms", result.getDuration());
        return result;
    }

    @Override
    public ProcessingResult processModelOnly(ProcessingConfig config) {
        log.info("开始处理商品型号");
        return modelProcessor.processExistingModels(config);
    }

    @Override
    public ProcessingResult mergeModelOnly(ProcessingConfig config) {
        log.info("开始合并商品型号");
        
        if (config.getTargetModel() != null && !config.getTargetModel().trim().isEmpty()) {
            return modelMerger.mergeByModel(config.getTargetModel(), config);
        } else {
            return modelMerger.mergeAllModels(config);
        }
    }

    @Override
    public ProcessingResult mergeSkuOnly(ProcessingConfig config) {
        log.info("开始合并商品SKU");
        
        if (config.getTargetSpuId() != null && !config.getTargetSpuId().trim().isEmpty()) {
            return skuMerger.mergeBySpuId(config.getTargetSpuId(), config);
        } else {
            return skuMerger.mergeAllSkus(config);
        }
    }

    /**
     * 处理数据匹配和保存
     */
    private StepResult processDataMatching(List<ProductDataDTO> dataList, ProcessingConfig config) {
        LocalDateTime stepStart = LocalDateTime.now();

        try {
            int successCount = 0;
            int failedCount = 0;

            for (ProductDataDTO dto : dataList) {
                try {
                    // 查找或创建黄金记录
                    matchingService.findOrCreateSimplifyRecord(dto);
                    successCount++;
                } catch (Exception e) {
                    log.error("处理商品数据失败：{}", dto.getModel(), e);
                    failedCount++;
                }
            }

            LocalDateTime stepEnd = LocalDateTime.now();
            return StepResult.withStats("数据匹配", dataList.size(), successCount, 0, failedCount)
                    .setDuration(stepStart, stepEnd);

        } catch (Exception e) {
            log.error("数据匹配步骤失败", e);
            return StepResult.failure("数据匹配", e.getMessage());
        }
    }

    /**
     * 将ProcessingResult转换为StepResult
     */
    private StepResult convertToStepResult(String stepName, ProcessingResult processingResult) {
        return StepResult.builder()
                .stepName(stepName)
                .success(processingResult.getSuccess())
                .startTime(processingResult.getStartTime())
                .endTime(processingResult.getEndTime())
                .duration(processingResult.getDuration())
                .processedCount(processingResult.getTotalProcessed())
                .successCount(processingResult.getSuccessCount())
                .skippedCount(processingResult.getSkippedCount())
                .failedCount(processingResult.getFailedCount())
                .message(processingResult.getMessage())
                .errorMessage(processingResult.getErrorMessage())
                .build();
    }

    /**
     * 合并处理结果的统计信息
     */
    private void mergeProcessingResults(ProcessingResult target, ProcessingResult source) {
        if (source == null) {
            return;
        }

        // 合并统计信息
        target.setTotalProcessed(target.getTotalProcessed() + source.getTotalProcessed());
        target.setSuccessCount(target.getSuccessCount() + source.getSuccessCount());
        target.setSkippedCount(target.getSkippedCount() + source.getSkippedCount());
        target.setFailedCount(target.getFailedCount() + source.getFailedCount());
        target.setTotalGroups(target.getTotalGroups() + source.getTotalGroups());

        // 如果有任何步骤失败，整体结果也标记为失败
        if (!source.getSuccess()) {
            target.setSuccess(false);
            if (target.getErrorMessage() == null) {
                target.setErrorMessage(source.getErrorMessage());
            } else {
                target.setErrorMessage(target.getErrorMessage() + "; " + source.getErrorMessage());
            }
        }
    }
}

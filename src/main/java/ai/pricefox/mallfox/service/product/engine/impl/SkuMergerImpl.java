package ai.pricefox.mallfox.service.product.engine.impl;

import ai.pricefox.mallfox.domain.product.ProductDataOffers;
import ai.pricefox.mallfox.domain.product.ProductDataSimplify;
import ai.pricefox.mallfox.enums.DataChannelEnum;
import ai.pricefox.mallfox.mapper.product.ProductDataSimplifyMapper;
import ai.pricefox.mallfox.model.dto.ProductSkuMergeDTO;
import ai.pricefox.mallfox.service.product.ProductDataOffersService;
import ai.pricefox.mallfox.service.product.ProductDataSimplifyService;
import ai.pricefox.mallfox.service.product.engine.config.ProcessingConfig;
import ai.pricefox.mallfox.service.product.engine.processor.SkuMerger;
import ai.pricefox.mallfox.service.product.engine.result.ProcessingResult;
import ai.pricefox.mallfox.service.product.util.ProductMatchingUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 商品SKU合并器实现
 *
 * <AUTHOR>
 * @since 2025-01-10
 */
@Service
@Slf4j
public class SkuMergerImpl implements SkuMerger {

    @Autowired
    private ProductDataSimplifyService simplifyService;

    @Autowired
    private ProductDataSimplifyMapper baseMapper;

    @Autowired
    private ProductDataOffersService offersService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProcessingResult mergeBySpuId(String targetSpuId, ProcessingConfig config) {
        log.info("合并指定SPU下的SKU：{}", targetSpuId);
        
        LocalDateTime startTime = LocalDateTime.now();
        
        try {
            // 创建分页对象，设置较大的页面大小以获取该SPU的所有数据
            Page<ProductSkuMergeDTO> page = new Page<>(1, 500);

            // 查询指定SPU的数据
            Page<ProductSkuMergeDTO> dataPage = baseMapper.selectProductSkuMergeData(
                    page, config.getSourcePlatform(), targetSpuId);

            List<ProductSkuMergeDTO> dataList = dataPage.getRecords();
            if (dataList.isEmpty()) {
                return ProcessingResult.success();
            }

            int[] counts = processSkuDataList(dataList, config);
            
            LocalDateTime endTime = LocalDateTime.now();
            String mode = Boolean.TRUE.equals(config.getPreviewMode()) ? "预览" : "合并";
            String message = String.format("%s完成，共处理%d个SKU组，成功合并%d条记录，跳过%d条，失败%d条",
                    mode, counts[0], counts[1], counts[2], counts[3]);

            return ProcessingResult.withStats(counts[1] + counts[2] + counts[3], counts[1], counts[2], counts[3])
                    .setDuration(startTime, endTime);
                    
        } catch (Exception e) {
            log.error("合并指定SPU的SKU失败：{}", targetSpuId, e);
            return ProcessingResult.failure(e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProcessingResult mergeAllSkus(ProcessingConfig config) {
        log.info("批量合并所有SKU数据");
        
        LocalDateTime startTime = LocalDateTime.now();
        int totalSkuGroups = 0;
        int successCount = 0;
        int skippedCount = 0;
        int failedCount = 0;
        
        try {
            int pageNo = 1;
            boolean hasMore = true;
            
            while (hasMore) {
                // 分页查询数据
                Page<ProductSkuMergeDTO> page = new Page<>(pageNo, config.getPageSize());
                Page<ProductSkuMergeDTO> dataPage = baseMapper.selectProductSkuMergeData(
                        page, config.getSourcePlatform(), null);

                List<ProductSkuMergeDTO> dataList = dataPage.getRecords();
                if (dataList.isEmpty()) {
                    break;
                }

                int[] counts = processSkuDataList(dataList, config);
                totalSkuGroups += counts[0];
                successCount += counts[1];
                skippedCount += counts[2];
                failedCount += counts[3];

                hasMore = dataPage.hasNext();
                pageNo++;
            }
            
            LocalDateTime endTime = LocalDateTime.now();
            String mode = Boolean.TRUE.equals(config.getPreviewMode()) ? "预览" : "合并";
            String message = String.format("%s完成，共处理%d个SKU组，成功合并%d条记录，跳过%d条，失败%d条",
                    mode, totalSkuGroups, successCount, skippedCount, failedCount);

            return ProcessingResult.withStats(successCount + skippedCount + failedCount, successCount, skippedCount, failedCount)
                    .setDuration(startTime, endTime);
                    
        } catch (Exception e) {
            log.error("批量合并所有SKU数据失败", e);
            return ProcessingResult.failure(e.getMessage());
        }
    }

    @Override
    public boolean mergeSkuGroup(List<ProductSkuMergeDTO> group, ProcessingConfig config) {
        try {
            // 选择基准数据
            ProductSkuMergeDTO baseData = selectBaseData(group, config);
            
            // 需要更新的数据（排除基准数据）
            List<ProductSkuMergeDTO> toUpdate = group.stream()
                    .filter(item -> !item.getId().equals(baseData.getId()))
                    .collect(Collectors.toList());

            if (!Boolean.TRUE.equals(config.getPreviewMode())) {
                // 获取所有相关记录的完整信息，用于后续数据更新
                List<Long> allIds = group.stream().map(ProductSkuMergeDTO::getId).collect(Collectors.toList());
                List<ProductDataSimplify> allRecords = simplifyService.listByIds(allIds);

                // 按平台分组，每个平台内部找到创建时间最新的记录作为数据源
                Map<String, List<ProductDataSimplify>> platformGroups = allRecords.stream()
                        .collect(Collectors.groupingBy(ProductDataSimplify::getSourcePlatform));

                // 更新ProductDataSimplify表
                for (ProductSkuMergeDTO item : toUpdate) {
                    // 获取当前记录的完整信息
                    ProductDataSimplify currentRecord = allRecords.stream()
                            .filter(record -> record.getId().equals(item.getId()))
                            .findFirst()
                            .orElse(null);

                    if (currentRecord == null) {
                        log.warn("未找到ID为{}的记录，跳过字段更新", item.getId());
                        continue;
                    }

                    // 只有同一平台的最新记录才能更新其他字段
                    String platform = item.getSourcePlatform();
                    List<ProductDataSimplify> platformRecords = platformGroups.get(platform);

                    if (platformRecords == null || platformRecords.isEmpty()) {
                        log.warn("未找到平台{}的记录，跳过字段更新", platform);
                        continue;
                    }

                    // 找到该平台创建时间最新的记录作为数据源
                    ProductDataSimplify latestRecord = platformRecords.stream()
                            .max(Comparator.comparing(ProductDataSimplify::getCreateTime))
                            .orElse(null);

                    if (latestRecord == null) {
                        log.warn("未找到平台{}的最新记录，跳过字段更新", platform);
                        continue;
                    }

                    log.info("选择平台{}最新记录作为数据源，ID: {}, 创建时间: {}",
                            platform, latestRecord.getId(), latestRecord.getCreateTime());

                    LambdaUpdateWrapper<ProductDataSimplify> updateWrapper = new LambdaUpdateWrapper<>();
                    updateWrapper.eq(ProductDataSimplify::getId, item.getId())
                            .set(ProductDataSimplify::getSkuId, baseData.getSkuId())
                            .set(ProductDataSimplify::getUpdateTime, LocalDateTime.now());

                    // 根据数据渠道优先级和同一平台的最新记录更新其他字段
                    updateNonEmptyFieldsByDataChannel(updateWrapper, currentRecord, latestRecord, config);

                    boolean updateResult = simplifyService.update(updateWrapper);
                    log.info("更新ProductDataSimplify表，ID: {}, 原SKU: {}, 新SKU: {}, 更新结果: {}",
                            item.getId(), item.getSkuId(), baseData.getSkuId(), updateResult ? "成功" : "失败");
                }

                // 更新ProductDataOffers表
                updateProductDataOffers(allRecords, config);

                // 更新ProductDataOffers表的SKU ID
                for (ProductSkuMergeDTO item : toUpdate) {
                    updateRelatedOffers(item, baseData.getSkuId(), config);
                }
            }
            
            return true;
        } catch (Exception e) {
            log.error("合并SKU组失败", e);
            return false;
        }
    }

    @Override
    public ProductSkuMergeDTO selectBaseData(List<ProductSkuMergeDTO> group, ProcessingConfig config) {
        // 根据平台优先级选择基准数据
        List<String> priorityOrder = config.getPlatformPriority().getPriorityOrder();
        
        for (String platform : priorityOrder) {
            Optional<ProductSkuMergeDTO> platformData = group.stream()
                    .filter(item -> platform.equalsIgnoreCase(item.getSourcePlatform()))
                    .findFirst();
            if (platformData.isPresent()) {
                return platformData.get();
            }
        }
        
        // 如果没有找到优先平台的数据，返回第一条
        return group.get(0);
    }

    @Override
    public String generateSkuGroupKey(ProductSkuMergeDTO data, ProcessingConfig config) {
        StringBuilder keyBuilder = new StringBuilder();
        
        // SPU ID
        keyBuilder.append(data.getSpuId()).append("|");
        
        // 颜色
        String color = data.getColor();
        if (config.getMergeStrategy().getEnableCaseInsensitiveMatch() && color != null) {
            color = color.toLowerCase().trim();
        }
        keyBuilder.append(color != null ? color : "").append("|");
        
        // 存储容量（标准化）
        String storage = data.getStorage();
        if (config.getMergeStrategy().getEnableStorageNormalization()) {
            storage = normalizeStorage(storage);
        }
        keyBuilder.append(storage != null ? storage : "").append("|");
        
        // 商品状态
        String condition = data.getConditionNew();
        if (config.getMergeStrategy().getEnableCaseInsensitiveMatch() && condition != null) {
            condition = condition.toLowerCase().trim();
        }
        keyBuilder.append(condition != null ? condition : "").append("|");
        
        // 服务提供商
        String serviceProvider = data.getServiceProvider();
        if (config.getMergeStrategy().getEnableCaseInsensitiveMatch() && serviceProvider != null) {
            serviceProvider = serviceProvider.toLowerCase().trim();
        }
        keyBuilder.append(serviceProvider != null ? serviceProvider : "");
        
        return keyBuilder.toString();
    }

    @Override
    public String normalizeStorage(String storage) {
        if (!StringUtils.hasText(storage)) {
            return storage;
        }
        
        // 去除空格并转换为小写
        return storage.replaceAll("\\s+", "").toLowerCase();
    }

    @Override
    public boolean validateSkuMatchFields(ProductSkuMergeDTO data, ProcessingConfig config) {
        // 检查关键字段是否非空
        return StringUtils.hasText(data.getColor()) &&
               StringUtils.hasText(data.getStorage()) &&
               StringUtils.hasText(data.getConditionNew()) &&
               StringUtils.hasText(data.getServiceProvider());
    }

    @Override
    public boolean updateRelatedOffers(ProductSkuMergeDTO skuData, String newSkuId, ProcessingConfig config) {
        try {
            // 情况1：SpuId和SkuId都匹配，更新SKU ID
            LambdaUpdateWrapper<ProductDataOffers> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(ProductDataOffers::getSpuId, skuData.getSpuId())
                    .eq(ProductDataOffers::getSkuId, skuData.getSkuId())
                    .set(ProductDataOffers::getSkuId, newSkuId)
                    .set(ProductDataOffers::getUpdateTime, LocalDateTime.now());

            boolean updateResult = offersService.update(updateWrapper);
            log.info("更新ProductDataOffers表SKU ID，原SPU: {}, 原SKU: {}, 新SKU: {}, 更新结果: {}",
                    skuData.getSpuId(), skuData.getSkuId(), newSkuId, updateResult ? "成功" : "失败");
            
            return updateResult;
        } catch (Exception e) {
            log.error("更新关联的ProductDataOffers表失败", e);
            return false;
        }
    }

    /**
     * 处理SKU数据列表
     */
    private int[] processSkuDataList(List<ProductSkuMergeDTO> dataList, ProcessingConfig config) {
        int totalSkuGroups = 0;
        int successCount = 0;
        int skippedCount = 0;
        int failedCount = 0;

        // 过滤出有效的数据（四个关键字段都不为空）
        List<ProductSkuMergeDTO> validDataList = dataList.stream()
                .filter(data -> validateSkuMatchFields(data, config))
                .collect(Collectors.toList());

        if (validDataList.isEmpty()) {
            return new int[]{totalSkuGroups, successCount, skippedCount, failedCount};
        }

        // 按SKU分组键进行分组
        Map<String, List<ProductSkuMergeDTO>> skuGroups = validDataList.stream()
                .collect(Collectors.groupingBy(data -> generateSkuGroupKey(data, config)));

        // 处理每个SKU组
        for (List<ProductSkuMergeDTO> group : skuGroups.values()) {
            // 跳过只有一条记录的组
            if (group.size() <= 1) {
                skippedCount += group.size();
                continue;
            }

            totalSkuGroups++;

            try {
                if (mergeSkuGroup(group, config)) {
                    successCount += group.size();
                } else {
                    failedCount += group.size();
                }
            } catch (Exception e) {
                log.error("合并SKU组失败，SPU: {}, 原因: {}", group.get(0).getSpuId(), e.getMessage());
                failedCount += group.size();
            }
        }

        return new int[]{totalSkuGroups, successCount, skippedCount, failedCount};
    }

    /**
     * 根据数据渠道优先级更新非空字段
     *
     * @param updateWrapper 更新包装器
     * @param currentRecord 当前数据库记录
     * @param latestRecord 最新记录（数据源）
     * @param config 处理配置
     */
    private void updateNonEmptyFieldsByDataChannel(LambdaUpdateWrapper<ProductDataSimplify> updateWrapper,
                                                  ProductDataSimplify currentRecord,
                                                  ProductDataSimplify latestRecord,
                                                  ProcessingConfig config) {
        if (!config.getMergeStrategy().getEnableNonEmptyFieldUpdate()) {
            return;
        }

        Integer currentChannel = currentRecord.getDataChannel();
        Integer latestChannel = latestRecord.getDataChannel();

        // 默认值处理
        if (currentChannel == null) {
            currentChannel = DataChannelEnum.CRAWLER.getCode();
        }
        if (latestChannel == null) {
            latestChannel = DataChannelEnum.CRAWLER.getCode();
        }

        log.info("数据渠道优先级更新，当前记录渠道: {}, 新数据渠道: {}", currentChannel, latestChannel);

        // 情况一：当前是API数据，新数据是爬虫数据 - API有值则不更新，API无值则更新
        if (currentChannel.equals(DataChannelEnum.API.getCode()) &&
            latestChannel.equals(DataChannelEnum.CRAWLER.getCode())) {
            updateApiWithCrawlerDataSimplify(updateWrapper, currentRecord, latestRecord);
        }
        // 情况二：当前是爬虫数据，新数据是API数据 - API数据全部覆盖
        else if (currentChannel.equals(DataChannelEnum.CRAWLER.getCode()) &&
                 latestChannel.equals(DataChannelEnum.API.getCode())) {
            updateCrawlerWithApiDataSimplify(updateWrapper, currentRecord, latestRecord);
        }
        // 情况三：相同渠道数据，使用最新数据更新
        else if (currentChannel.equals(latestChannel)) {
            updateSameChannelDataSimplify(updateWrapper, currentRecord, latestRecord);
        }
    }

    /**
     * 检查字符串是否非空
     */
    private boolean isNotEmpty(String value) {
        return StringUtils.hasText(value);
    }

    /**
     * 更新ProductDataOffers表数据
     * 根据同一平台的最新记录更新其他字段
     *
     * @param allRecords 所有相关记录
     * @param config 处理配置
     */
    private void updateProductDataOffers(List<ProductDataSimplify> allRecords, ProcessingConfig config) {
        List<ProductDataOffers> offersList = new ArrayList<>();

        // 查询所有相关的ProductDataOffers记录
        allRecords.forEach(record -> {
            LambdaQueryWrapper<ProductDataOffers> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ProductDataOffers::getSkuId, record.getSkuId())
                    .eq(ProductDataOffers::getSpuId, record.getSpuId());
            offersList.addAll(offersService.list(queryWrapper));
        });

        if (offersList.isEmpty()) {
            log.info("没有找到相关的ProductDataOffers记录");
            return;
        }

        // 按平台分组
        Map<String, List<ProductDataOffers>> platformOfferGroups = offersList.stream()
                .collect(Collectors.groupingBy(ProductDataOffers::getSourcePlatform));

        // 对每个平台的数据进行处理
        for (Map.Entry<String, List<ProductDataOffers>> entry : platformOfferGroups.entrySet()) {
            String platform = entry.getKey();
            List<ProductDataOffers> platformOffers = entry.getValue();

            if (platformOffers.size() <= 1) {
                continue; // 只有一条记录，无需更新
            }

            // 找到该平台创建时间最新的记录作为数据源
            ProductDataOffers latestOffer = platformOffers.stream()
                    .max(Comparator.comparing(ProductDataOffers::getCreateTime))
                    .orElse(null);

            if (latestOffer == null) {
                log.warn("未找到平台{}的最新ProductDataOffers记录", platform);
                continue;
            }

            log.info("选择平台{}最新ProductDataOffers记录作为数据源，ID: {}, 创建时间: {}",
                    platform, latestOffer.getId(), latestOffer.getCreateTime());

            // 更新该平台的其他记录
            for (ProductDataOffers offer : platformOffers) {
                if (offer.getId().equals(latestOffer.getId())) {
                    continue; // 跳过最新记录本身
                }

                LambdaUpdateWrapper<ProductDataOffers> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.eq(ProductDataOffers::getId, offer.getId())
                        .set(ProductDataOffers::getUpdateTime, LocalDateTime.now());

                // 根据数据渠道优先级和同一平台的最新记录更新其他字段
                updateNonEmptyOffersFieldsByDataChannel(updateWrapper, offer, latestOffer, config);

                boolean updateResult = offersService.update(updateWrapper);
                log.info("更新ProductDataOffers表数据，ID: {}, 平台: {}, 更新结果: {}",
                        offer.getId(), platform, updateResult ? "成功" : "失败");
            }
        }
    }

    /**
     * API数据被爬虫数据更新：API有值则不更新，API无值则更新
     */
    private void updateApiWithCrawlerDataSimplify(LambdaUpdateWrapper<ProductDataSimplify> updateWrapper,
                                                 ProductDataSimplify currentRecord,
                                                 ProductDataSimplify latestRecord) {
        // 规格相关字段 - 只更新API数据中为空的字段
        if (!ProductMatchingUtil.hasValue(currentRecord.getModelYear()) && ProductMatchingUtil.hasValue(latestRecord.getModelYear())) {
            updateWrapper.set(ProductDataSimplify::getModelYear, latestRecord.getModelYear());
        }

        // 图片相关字段
        if (!ProductMatchingUtil.hasValue(currentRecord.getProductMainImageUrls()) && ProductMatchingUtil.hasValue(latestRecord.getProductMainImageUrls())) {
            updateWrapper.set(ProductDataSimplify::getProductMainImageUrls, latestRecord.getProductMainImageUrls());
        }
        if (!ProductMatchingUtil.hasValue(currentRecord.getProductSpecColorUrl()) && ProductMatchingUtil.hasValue(latestRecord.getProductSpecColorUrl())) {
            updateWrapper.set(ProductDataSimplify::getProductSpecColorUrl, latestRecord.getProductSpecColorUrl());
        }

        // 硬件规格字段
        if (!ProductMatchingUtil.hasValue(currentRecord.getRamMemoryInstalledSize()) && ProductMatchingUtil.hasValue(latestRecord.getRamMemoryInstalledSize())) {
            updateWrapper.set(ProductDataSimplify::getRamMemoryInstalledSize, latestRecord.getRamMemoryInstalledSize());
        }
        if (!ProductMatchingUtil.hasValue(currentRecord.getOperatingSystem()) && ProductMatchingUtil.hasValue(latestRecord.getOperatingSystem())) {
            updateWrapper.set(ProductDataSimplify::getOperatingSystem, latestRecord.getOperatingSystem());
        }
        if (!ProductMatchingUtil.hasValue(currentRecord.getProcessor()) && ProductMatchingUtil.hasValue(latestRecord.getProcessor())) {
            updateWrapper.set(ProductDataSimplify::getProcessor, latestRecord.getProcessor());
        }
        if (!ProductMatchingUtil.hasValue(currentRecord.getCellularTechnology()) && ProductMatchingUtil.hasValue(latestRecord.getCellularTechnology())) {
            updateWrapper.set(ProductDataSimplify::getCellularTechnology, latestRecord.getCellularTechnology());
        }

        // 显示相关字段
        if (!ProductMatchingUtil.hasValue(currentRecord.getScreenSize()) && ProductMatchingUtil.hasValue(latestRecord.getScreenSize())) {
            updateWrapper.set(ProductDataSimplify::getScreenSize, latestRecord.getScreenSize());
        }
        if (!ProductMatchingUtil.hasValue(currentRecord.getResolution()) && ProductMatchingUtil.hasValue(latestRecord.getResolution())) {
            updateWrapper.set(ProductDataSimplify::getResolution, latestRecord.getResolution());
        }
        if (!ProductMatchingUtil.hasValue(currentRecord.getRefreshRate()) && ProductMatchingUtil.hasValue(latestRecord.getRefreshRate())) {
            updateWrapper.set(ProductDataSimplify::getRefreshRate, latestRecord.getRefreshRate());
        }
        if (!ProductMatchingUtil.hasValue(currentRecord.getDisplayType()) && ProductMatchingUtil.hasValue(latestRecord.getDisplayType())) {
            updateWrapper.set(ProductDataSimplify::getDisplayType, latestRecord.getDisplayType());
        }

        // 电池相关字段
        if (!ProductMatchingUtil.hasValue(currentRecord.getBatteryPower()) && ProductMatchingUtil.hasValue(latestRecord.getBatteryPower())) {
            updateWrapper.set(ProductDataSimplify::getBatteryPower, latestRecord.getBatteryPower());
        }
        if (!ProductMatchingUtil.hasValue(currentRecord.getAverageTalkTime()) && ProductMatchingUtil.hasValue(latestRecord.getAverageTalkTime())) {
            updateWrapper.set(ProductDataSimplify::getAverageTalkTime, latestRecord.getAverageTalkTime());
        }
        if (!ProductMatchingUtil.hasValue(currentRecord.getBatteryChargeTime()) && ProductMatchingUtil.hasValue(latestRecord.getBatteryChargeTime())) {
            updateWrapper.set(ProductDataSimplify::getBatteryChargeTime, latestRecord.getBatteryChargeTime());
        }

        // 摄像头相关字段
        if (!ProductMatchingUtil.hasValue(currentRecord.getFrontPhotoSensorResolution()) && ProductMatchingUtil.hasValue(latestRecord.getFrontPhotoSensorResolution())) {
            updateWrapper.set(ProductDataSimplify::getFrontPhotoSensorResolution, latestRecord.getFrontPhotoSensorResolution());
        }
        if (!ProductMatchingUtil.hasValue(currentRecord.getRearFacingCameraPhotoSensorResolution()) && ProductMatchingUtil.hasValue(latestRecord.getRearFacingCameraPhotoSensorResolution())) {
            updateWrapper.set(ProductDataSimplify::getRearFacingCameraPhotoSensorResolution, latestRecord.getRearFacingCameraPhotoSensorResolution());
        }
        if (currentRecord.getNumberOfRearFacingCameras() == null && latestRecord.getNumberOfRearFacingCameras() != null) {
            updateWrapper.set(ProductDataSimplify::getNumberOfRearFacingCameras, latestRecord.getNumberOfRearFacingCameras());
        }
    }

    /**
     * 爬虫数据被API数据更新：API数据全部覆盖
     */
    private void updateCrawlerWithApiDataSimplify(LambdaUpdateWrapper<ProductDataSimplify> updateWrapper,
                                                 ProductDataSimplify currentRecord,
                                                 ProductDataSimplify latestRecord) {
        // API数据优先级高，有值就覆盖

        // 规格相关字段
        if (ProductMatchingUtil.hasValue(latestRecord.getModelYear())) {
            updateWrapper.set(ProductDataSimplify::getModelYear, latestRecord.getModelYear());
        }

        // 图片相关字段
        if (ProductMatchingUtil.hasValue(latestRecord.getProductMainImageUrls())) {
            updateWrapper.set(ProductDataSimplify::getProductMainImageUrls, latestRecord.getProductMainImageUrls());
        }
        if (ProductMatchingUtil.hasValue(latestRecord.getProductSpecColorUrl())) {
            updateWrapper.set(ProductDataSimplify::getProductSpecColorUrl, latestRecord.getProductSpecColorUrl());
        }

        // 硬件规格字段
        if (ProductMatchingUtil.hasValue(latestRecord.getRamMemoryInstalledSize())) {
            updateWrapper.set(ProductDataSimplify::getRamMemoryInstalledSize, latestRecord.getRamMemoryInstalledSize());
        }
        if (ProductMatchingUtil.hasValue(latestRecord.getOperatingSystem())) {
            updateWrapper.set(ProductDataSimplify::getOperatingSystem, latestRecord.getOperatingSystem());
        }
        if (ProductMatchingUtil.hasValue(latestRecord.getProcessor())) {
            updateWrapper.set(ProductDataSimplify::getProcessor, latestRecord.getProcessor());
        }
        if (ProductMatchingUtil.hasValue(latestRecord.getCellularTechnology())) {
            updateWrapper.set(ProductDataSimplify::getCellularTechnology, latestRecord.getCellularTechnology());
        }

        // 显示相关字段
        if (ProductMatchingUtil.hasValue(latestRecord.getScreenSize())) {
            updateWrapper.set(ProductDataSimplify::getScreenSize, latestRecord.getScreenSize());
        }
        if (ProductMatchingUtil.hasValue(latestRecord.getResolution())) {
            updateWrapper.set(ProductDataSimplify::getResolution, latestRecord.getResolution());
        }
        if (ProductMatchingUtil.hasValue(latestRecord.getRefreshRate())) {
            updateWrapper.set(ProductDataSimplify::getRefreshRate, latestRecord.getRefreshRate());
        }
        if (ProductMatchingUtil.hasValue(latestRecord.getDisplayType())) {
            updateWrapper.set(ProductDataSimplify::getDisplayType, latestRecord.getDisplayType());
        }

        // 电池相关字段
        if (ProductMatchingUtil.hasValue(latestRecord.getBatteryPower())) {
            updateWrapper.set(ProductDataSimplify::getBatteryPower, latestRecord.getBatteryPower());
        }
        if (ProductMatchingUtil.hasValue(latestRecord.getAverageTalkTime())) {
            updateWrapper.set(ProductDataSimplify::getAverageTalkTime, latestRecord.getAverageTalkTime());
        }
        if (ProductMatchingUtil.hasValue(latestRecord.getBatteryChargeTime())) {
            updateWrapper.set(ProductDataSimplify::getBatteryChargeTime, latestRecord.getBatteryChargeTime());
        }

        // 摄像头相关字段
        if (ProductMatchingUtil.hasValue(latestRecord.getFrontPhotoSensorResolution())) {
            updateWrapper.set(ProductDataSimplify::getFrontPhotoSensorResolution, latestRecord.getFrontPhotoSensorResolution());
        }
        if (ProductMatchingUtil.hasValue(latestRecord.getRearFacingCameraPhotoSensorResolution())) {
            updateWrapper.set(ProductDataSimplify::getRearFacingCameraPhotoSensorResolution, latestRecord.getRearFacingCameraPhotoSensorResolution());
        }
        if (latestRecord.getNumberOfRearFacingCameras() != null) {
            updateWrapper.set(ProductDataSimplify::getNumberOfRearFacingCameras, latestRecord.getNumberOfRearFacingCameras());
        }

        // 更新数据渠道为API
        updateWrapper.set(ProductDataSimplify::getDataChannel, DataChannelEnum.API.getCode());
    }

    /**
     * 相同渠道数据更新：使用最新数据
     */
    private void updateSameChannelDataSimplify(LambdaUpdateWrapper<ProductDataSimplify> updateWrapper,
                                              ProductDataSimplify currentRecord,
                                              ProductDataSimplify latestRecord) {
        // 相同渠道，使用新数据更新有值的字段

        // 规格相关字段
        if (ProductMatchingUtil.hasValue(latestRecord.getModelYear())) {
            updateWrapper.set(ProductDataSimplify::getModelYear, latestRecord.getModelYear());
        }

        // 图片相关字段
        if (ProductMatchingUtil.hasValue(latestRecord.getProductMainImageUrls())) {
            updateWrapper.set(ProductDataSimplify::getProductMainImageUrls, latestRecord.getProductMainImageUrls());
        }
        if (ProductMatchingUtil.hasValue(latestRecord.getProductSpecColorUrl())) {
            updateWrapper.set(ProductDataSimplify::getProductSpecColorUrl, latestRecord.getProductSpecColorUrl());
        }

        // 硬件规格字段
        if (ProductMatchingUtil.hasValue(latestRecord.getRamMemoryInstalledSize())) {
            updateWrapper.set(ProductDataSimplify::getRamMemoryInstalledSize, latestRecord.getRamMemoryInstalledSize());
        }
        if (ProductMatchingUtil.hasValue(latestRecord.getOperatingSystem())) {
            updateWrapper.set(ProductDataSimplify::getOperatingSystem, latestRecord.getOperatingSystem());
        }
        if (ProductMatchingUtil.hasValue(latestRecord.getProcessor())) {
            updateWrapper.set(ProductDataSimplify::getProcessor, latestRecord.getProcessor());
        }

        // 显示相关字段
        if (ProductMatchingUtil.hasValue(latestRecord.getScreenSize())) {
            updateWrapper.set(ProductDataSimplify::getScreenSize, latestRecord.getScreenSize());
        }
        if (ProductMatchingUtil.hasValue(latestRecord.getResolution())) {
            updateWrapper.set(ProductDataSimplify::getResolution, latestRecord.getResolution());
        }

        // 电池相关字段
        if (ProductMatchingUtil.hasValue(latestRecord.getBatteryPower())) {
            updateWrapper.set(ProductDataSimplify::getBatteryPower, latestRecord.getBatteryPower());
        }
        if (ProductMatchingUtil.hasValue(latestRecord.getAverageTalkTime())) {
            updateWrapper.set(ProductDataSimplify::getAverageTalkTime, latestRecord.getAverageTalkTime());
        }
        if (ProductMatchingUtil.hasValue(latestRecord.getBatteryChargeTime())) {
            updateWrapper.set(ProductDataSimplify::getBatteryChargeTime, latestRecord.getBatteryChargeTime());
        }

        // 摄像头相关字段
        if (ProductMatchingUtil.hasValue(latestRecord.getFrontPhotoSensorResolution())) {
            updateWrapper.set(ProductDataSimplify::getFrontPhotoSensorResolution, latestRecord.getFrontPhotoSensorResolution());
        }
        if (ProductMatchingUtil.hasValue(latestRecord.getRearFacingCameraPhotoSensorResolution())) {
            updateWrapper.set(ProductDataSimplify::getRearFacingCameraPhotoSensorResolution, latestRecord.getRearFacingCameraPhotoSensorResolution());
        }
        if (latestRecord.getNumberOfRearFacingCameras() != null) {
            updateWrapper.set(ProductDataSimplify::getNumberOfRearFacingCameras, latestRecord.getNumberOfRearFacingCameras());
        }
    }

    /**
     * 根据数据渠道优先级更新ProductDataOffers表的非空字段
     *
     * @param updateWrapper 更新包装器
     * @param currentOffer 当前数据库记录
     * @param latestOffer 最新记录（数据源）
     * @param config 处理配置
     */
    private void updateNonEmptyOffersFieldsByDataChannel(LambdaUpdateWrapper<ProductDataOffers> updateWrapper,
                                                        ProductDataOffers currentOffer,
                                                        ProductDataOffers latestOffer,
                                                        ProcessingConfig config) {
        if (!config.getMergeStrategy().getEnableNonEmptyFieldUpdate()) {
            return;
        }

        Integer currentChannel = currentOffer.getDataChannel();
        Integer latestChannel = latestOffer.getDataChannel();

        // 默认值处理
        if (currentChannel == null) {
            currentChannel = DataChannelEnum.CRAWLER.getCode();
        }
        if (latestChannel == null) {
            latestChannel = DataChannelEnum.CRAWLER.getCode();
        }

        log.info("ProductDataOffers数据渠道优先级更新，当前记录渠道: {}, 新数据渠道: {}", currentChannel, latestChannel);

        // 情况一：当前是API数据，新数据是爬虫数据 - API有值则不更新，API无值则更新
        if (currentChannel.equals(DataChannelEnum.API.getCode()) &&
            latestChannel.equals(DataChannelEnum.CRAWLER.getCode())) {
            updateApiOffersWithCrawlerData(updateWrapper, currentOffer, latestOffer);
        }
        // 情况二：当前是爬虫数据，新数据是API数据 - API数据全部覆盖
        else if (currentChannel.equals(DataChannelEnum.CRAWLER.getCode()) &&
                 latestChannel.equals(DataChannelEnum.API.getCode())) {
            updateCrawlerOffersWithApiData(updateWrapper, currentOffer, latestOffer);
        }
        // 情况三：相同渠道数据，使用最新数据更新
        else if (currentChannel.equals(latestChannel)) {
            updateSameChannelOffersData(updateWrapper, currentOffer, latestOffer);
        }
    }

    /**
     * API Offers数据被爬虫数据更新：API有值则不更新，API无值则更新
     */
    private void updateApiOffersWithCrawlerData(LambdaUpdateWrapper<ProductDataOffers> updateWrapper,
                                               ProductDataOffers currentOffer,
                                               ProductDataOffers latestOffer) {
        // 基本信息字段 - 只更新API数据中为空的字段
        if (!ProductMatchingUtil.hasValue(currentOffer.getTitle()) && ProductMatchingUtil.hasValue(latestOffer.getTitle())) {
            updateWrapper.set(ProductDataOffers::getTitle, latestOffer.getTitle());
        }
        if (!ProductMatchingUtil.hasValue(currentOffer.getBrand()) && ProductMatchingUtil.hasValue(latestOffer.getBrand())) {
            updateWrapper.set(ProductDataOffers::getBrand, latestOffer.getBrand());
        }
        if (!ProductMatchingUtil.hasValue(currentOffer.getSeries()) && ProductMatchingUtil.hasValue(latestOffer.getSeries())) {
            updateWrapper.set(ProductDataOffers::getSeries, latestOffer.getSeries());
        }
        if (!ProductMatchingUtil.hasValue(currentOffer.getUpcCode()) && ProductMatchingUtil.hasValue(latestOffer.getUpcCode())) {
            updateWrapper.set(ProductDataOffers::getUpcCode, latestOffer.getUpcCode());
        }

        // 价格相关字段
        if (currentOffer.getPrice() == null && latestOffer.getPrice() != null) {
            updateWrapper.set(ProductDataOffers::getPrice, latestOffer.getPrice());
        }
        if (currentOffer.getListPrice() == null && latestOffer.getListPrice() != null) {
            updateWrapper.set(ProductDataOffers::getListPrice, latestOffer.getListPrice());
        }
        if (currentOffer.getDiscount() == null && latestOffer.getDiscount() != null) {
            updateWrapper.set(ProductDataOffers::getDiscount, latestOffer.getDiscount());
        }

        // 库存和销量
        if (!ProductMatchingUtil.hasValue(currentOffer.getInventory()) && ProductMatchingUtil.hasValue(latestOffer.getInventory())) {
            updateWrapper.set(ProductDataOffers::getInventory, latestOffer.getInventory());
        }
        if (!ProductMatchingUtil.hasValue(currentOffer.getSalesLast30Days()) && ProductMatchingUtil.hasValue(latestOffer.getSalesLast30Days())) {
            updateWrapper.set(ProductDataOffers::getSalesLast30Days, latestOffer.getSalesLast30Days());
        }

        // 卖家信息
        if (!ProductMatchingUtil.hasValue(currentOffer.getSeller()) && ProductMatchingUtil.hasValue(latestOffer.getSeller())) {
            updateWrapper.set(ProductDataOffers::getSeller, latestOffer.getSeller());
        }
        if (currentOffer.getMerchantRating() == null && latestOffer.getMerchantRating() != null) {
            updateWrapper.set(ProductDataOffers::getMerchantRating, latestOffer.getMerchantRating());
        }
    }

    /**
     * 爬虫Offers数据被API数据更新：API数据全部覆盖
     */
    private void updateCrawlerOffersWithApiData(LambdaUpdateWrapper<ProductDataOffers> updateWrapper,
                                               ProductDataOffers currentOffer,
                                               ProductDataOffers latestOffer) {
        // 基本信息字段 - API有值就覆盖
        if (ProductMatchingUtil.hasValue(latestOffer.getTitle())) {
            updateWrapper.set(ProductDataOffers::getTitle, latestOffer.getTitle());
        }
        if (ProductMatchingUtil.hasValue(latestOffer.getBrand())) {
            updateWrapper.set(ProductDataOffers::getBrand, latestOffer.getBrand());
        }
        if (ProductMatchingUtil.hasValue(latestOffer.getSeries())) {
            updateWrapper.set(ProductDataOffers::getSeries, latestOffer.getSeries());
        }
        if (ProductMatchingUtil.hasValue(latestOffer.getUpcCode())) {
            updateWrapper.set(ProductDataOffers::getUpcCode, latestOffer.getUpcCode());
        }

        // 价格相关字段
        if (latestOffer.getPrice() != null) {
            updateWrapper.set(ProductDataOffers::getPrice, latestOffer.getPrice());
        }
        if (latestOffer.getListPrice() != null) {
            updateWrapper.set(ProductDataOffers::getListPrice, latestOffer.getListPrice());
        }
        if (latestOffer.getDiscount() != null) {
            updateWrapper.set(ProductDataOffers::getDiscount, latestOffer.getDiscount());
        }

        // 库存和销量
        if (ProductMatchingUtil.hasValue(latestOffer.getInventory())) {
            updateWrapper.set(ProductDataOffers::getInventory, latestOffer.getInventory());
            // 更新库存更新时间
            updateWrapper.set(ProductDataOffers::getInventoryUpdateTime, LocalDateTime.now());
        }
        if (ProductMatchingUtil.hasValue(latestOffer.getSalesLast30Days())) {
            updateWrapper.set(ProductDataOffers::getSalesLast30Days, latestOffer.getSalesLast30Days());
        }

        // 卖家信息
        if (ProductMatchingUtil.hasValue(latestOffer.getSeller())) {
            updateWrapper.set(ProductDataOffers::getSeller, latestOffer.getSeller());
        }
        if (latestOffer.getMerchantRating() != null) {
            updateWrapper.set(ProductDataOffers::getMerchantRating, latestOffer.getMerchantRating());
        }

        // 更新数据渠道为API
        updateWrapper.set(ProductDataOffers::getDataChannel, DataChannelEnum.API.getCode());
    }

    /**
     * 相同渠道Offers数据更新：使用最新数据
     */
    private void updateSameChannelOffersData(LambdaUpdateWrapper<ProductDataOffers> updateWrapper,
                                            ProductDataOffers currentOffer,
                                            ProductDataOffers latestOffer) {
        // 相同渠道，使用新数据更新有值的字段
        if (ProductMatchingUtil.hasValue(latestOffer.getTitle())) {
            updateWrapper.set(ProductDataOffers::getTitle, latestOffer.getTitle());
        }
        if (ProductMatchingUtil.hasValue(latestOffer.getBrand())) {
            updateWrapper.set(ProductDataOffers::getBrand, latestOffer.getBrand());
        }
        if (ProductMatchingUtil.hasValue(latestOffer.getSeries())) {
            updateWrapper.set(ProductDataOffers::getSeries, latestOffer.getSeries());
        }
        if (ProductMatchingUtil.hasValue(latestOffer.getUpcCode())) {
            updateWrapper.set(ProductDataOffers::getUpcCode, latestOffer.getUpcCode());
        }

        // 价格相关字段
        if (latestOffer.getPrice() != null) {
            updateWrapper.set(ProductDataOffers::getPrice, latestOffer.getPrice());
        }
        if (latestOffer.getListPrice() != null) {
            updateWrapper.set(ProductDataOffers::getListPrice, latestOffer.getListPrice());
        }
        if (latestOffer.getDiscount() != null) {
            updateWrapper.set(ProductDataOffers::getDiscount, latestOffer.getDiscount());
        }

        // 库存和销量
        if (ProductMatchingUtil.hasValue(latestOffer.getInventory())) {
            updateWrapper.set(ProductDataOffers::getInventory, latestOffer.getInventory());
            updateWrapper.set(ProductDataOffers::getInventoryUpdateTime, LocalDateTime.now());
        }
        if (ProductMatchingUtil.hasValue(latestOffer.getSalesLast30Days())) {
            updateWrapper.set(ProductDataOffers::getSalesLast30Days, latestOffer.getSalesLast30Days());
        }

        // 卖家信息
        if (ProductMatchingUtil.hasValue(latestOffer.getSeller())) {
            updateWrapper.set(ProductDataOffers::getSeller, latestOffer.getSeller());
        }
        if (latestOffer.getMerchantRating() != null) {
            updateWrapper.set(ProductDataOffers::getMerchantRating, latestOffer.getMerchantRating());
        }
    }
}

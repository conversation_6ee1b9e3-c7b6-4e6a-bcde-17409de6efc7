package ai.pricefox.mallfox.service.product.engine.processor;

import ai.pricefox.mallfox.model.dto.ProductModelMergeDTO;
import ai.pricefox.mallfox.service.product.engine.config.ProcessingConfig;
import ai.pricefox.mallfox.service.product.engine.result.ProcessingResult;

import java.util.List;

/**
 * 商品型号合并器接口
 * 负责根据型号匹配将相同型号的数据合并到统一的SPU下
 *
 * <AUTHOR>
 * @since 2025-01-10
 */
public interface ModelMerger {

    /**
     * 合并指定型号的数据
     * 根据型号匹配（不区分大小写和空格），将相同型号的数据合并到统一的SPU下
     *
     * @param targetModel 指定要合并的型号
     * @param config 处理配置
     * @return 处理结果
     */
    ProcessingResult mergeByModel(String targetModel, ProcessingConfig config);

    /**
     * 批量合并所有型号数据
     * 自动查找需要合并的型号并进行合并
     *
     * @param config 处理配置
     * @return 处理结果
     */
    ProcessingResult mergeAllModels(ProcessingConfig config);

    /**
     * 合并单个型号组
     * 将同一型号的多条数据合并为一条
     *
     * @param group 同一型号的数据组
     * @param config 处理配置
     * @return 是否合并成功
     */
    boolean mergeModelGroup(List<ProductModelMergeDTO> group, ProcessingConfig config);

    /**
     * 选择基准数据
     * 根据平台优先级选择作为合并基准的数据
     *
     * @param group 数据组
     * @param config 处理配置
     * @return 基准数据
     */
    ProductModelMergeDTO selectBaseData(List<ProductModelMergeDTO> group, ProcessingConfig config);

    /**
     * 基于UPC匹配进行合并
     * 优先检查UPC码一致性，如果UPC一致直接合并
     *
     * @param dataList 数据列表
     * @param config 处理配置
     * @return 处理结果统计 [总组数, 成功数, 跳过数, 失败数]
     */
    int[] mergeByUpcMatch(List<ProductModelMergeDTO> dataList, ProcessingConfig config);

    /**
     * 基于标准化型号进行合并
     * 按标准化型号分组并合并
     *
     * @param dataList 数据列表
     * @param config 处理配置
     * @return 处理结果统计 [总组数, 成功数, 跳过数, 失败数]
     */
    int[] mergeByNormalizedModel(List<ProductModelMergeDTO> dataList, ProcessingConfig config);

    /**
     * 获取所有不重复的型号列表
     * 从爬虫和API数据中获取所有不重复的型号
     *
     * @return 型号列表
     */
    List<String> getAllDistinctModels();
}

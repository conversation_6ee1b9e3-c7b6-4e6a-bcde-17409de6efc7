package ai.pricefox.mallfox.service.product.engine.processor;

import ai.pricefox.mallfox.service.product.engine.config.ProcessingConfig;
import ai.pricefox.mallfox.service.product.engine.result.ProcessingResult;

/**
 * 商品型号处理器接口
 * 负责根据品牌信息去除型号中的品牌内容，并格式化型号
 *
 * <AUTHOR>
 * @since 2025-01-10
 */
public interface ModelProcessor {

    /**
     * 处理单个型号字符串
     * 根据品牌信息去除型号中的品牌内容，并格式化型号
     *
     * @param originalModel 原始型号
     * @param brand 品牌名称
     * @param config 处理配置
     * @return 处理后的型号，如果无需处理则返回null
     */
    String processModelString(String originalModel, String brand, ProcessingConfig config);

    /**
     * 批量处理数据库中的型号数据
     * 分页查询并处理数据库中的商品型号
     *
     * @param config 处理配置
     * @return 处理结果
     */
    ProcessingResult processExistingModels(ProcessingConfig config);

    /**
     * 格式化型号字符串
     * 应用格式化规则，如去除网络制式关键词等
     *
     * @param model 待格式化的型号
     * @param config 处理配置
     * @return 格式化后的型号
     */
    String formatModelString(String model, ProcessingConfig config);

    /**
     * 智能去除品牌内容
     * 保持原始空格结构的同时去除品牌信息
     *
     * @param originalModel 原始型号
     * @param brand 品牌名称
     * @param config 处理配置
     * @return 去除品牌后的型号
     */
    String removeBrandFromModel(String originalModel, String brand, ProcessingConfig config);

    /**
     * 验证处理结果
     * 检查处理后的型号是否符合预期
     *
     * @param originalModel 原始型号
     * @param processedModel 处理后的型号
     * @param brand 品牌名称
     * @return 是否有效
     */
    boolean validateProcessedModel(String originalModel, String processedModel, String brand);
}

package ai.pricefox.mallfox.service.product.engine.processor;

import ai.pricefox.mallfox.model.dto.ProductSkuMergeDTO;
import ai.pricefox.mallfox.service.product.engine.config.ProcessingConfig;
import ai.pricefox.mallfox.service.product.engine.result.ProcessingResult;

import java.util.List;

/**
 * 商品SKU合并器接口
 * 负责在相同spuid下，根据四个字段匹配将相同SKU的数据合并
 *
 * <AUTHOR>
 * @since 2025-01-10
 */
public interface SkuMerger {

    /**
     * 合并指定SPU下的SKU数据
     * 在指定SPU下，根据匹配字段将相同SKU的数据合并
     *
     * @param targetSpuId 指定要合并的SPU ID
     * @param config 处理配置
     * @return 处理结果
     */
    ProcessingResult mergeBySpuId(String targetSpuId, ProcessingConfig config);

    /**
     * 批量合并所有SKU数据
     * 自动查找需要合并的SKU并进行合并
     *
     * @param config 处理配置
     * @return 处理结果
     */
    ProcessingResult mergeAllSkus(ProcessingConfig config);

    /**
     * 合并单个SKU组
     * 将同一SKU的多条数据合并为一条
     *
     * @param group 同一SKU的数据组
     * @param config 处理配置
     * @return 是否合并成功
     */
    boolean mergeSkuGroup(List<ProductSkuMergeDTO> group, ProcessingConfig config);

    /**
     * 选择基准数据
     * 根据平台优先级选择作为合并基准的数据
     *
     * @param group 数据组
     * @param config 处理配置
     * @return 基准数据
     */
    ProductSkuMergeDTO selectBaseData(List<ProductSkuMergeDTO> group, ProcessingConfig config);

    /**
     * 生成SKU分组键
     * 根据匹配字段生成用于分组的键值
     *
     * @param data SKU数据
     * @param config 处理配置
     * @return 分组键
     */
    String generateSkuGroupKey(ProductSkuMergeDTO data, ProcessingConfig config);

    /**
     * 标准化存储容量
     * 忽略大小写和空格进行标准化
     *
     * @param storage 原始存储容量
     * @return 标准化后的存储容量
     */
    String normalizeStorage(String storage);

    /**
     * 验证SKU匹配字段
     * 检查SKU的关键字段是否完整
     *
     * @param data SKU数据
     * @param config 处理配置
     * @return 是否有效
     */
    boolean validateSkuMatchFields(ProductSkuMergeDTO data, ProcessingConfig config);

    /**
     * 更新关联的ProductDataOffers表
     * 同步更新高频数据表中的SKU ID
     *
     * @param skuData SKU数据
     * @param newSkuId 新的SKU ID
     * @param config 处理配置
     * @return 是否更新成功
     */
    boolean updateRelatedOffers(ProductSkuMergeDTO skuData, String newSkuId, ProcessingConfig config);
}

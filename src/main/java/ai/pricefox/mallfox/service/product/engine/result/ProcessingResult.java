package ai.pricefox.mallfox.service.product.engine.result;

import lombok.Data;
import lombok.Builder;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 商品数据处理结果
 *
 * <AUTHOR>
 * @since 2025-01-10
 */
@Data
@Builder
public class ProcessingResult {

    /**
     * 处理是否成功
     */
    @Builder.Default
    private Boolean success = true;

    /**
     * 处理开始时间
     */
    private LocalDateTime startTime;

    /**
     * 处理结束时间
     */
    private LocalDateTime endTime;

    /**
     * 处理耗时（毫秒）
     */
    private Long duration;

    /**
     * 处理的总记录数
     */
    @Builder.Default
    private Integer totalProcessed = 0;

    /**
     * 成功处理的记录数
     */
    @Builder.Default
    private Integer successCount = 0;

    /**
     * 跳过的记录数
     */
    @Builder.Default
    private Integer skippedCount = 0;

    /**
     * 失败的记录数
     */
    @Builder.Default
    private Integer failedCount = 0;

    /**
     * 处理的组数（型号组或SKU组）
     */
    @Builder.Default
    private Integer totalGroups = 0;

    /**
     * 是否还有更多数据需要处理
     */
    @Builder.Default
    private Boolean hasMore = false;

    /**
     * 处理详情信息
     */
    private String message;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 详细的处理步骤结果
     */
    private List<StepResult> stepResults;

    /**
     * 额外的统计信息
     */
    private Map<String, Object> statistics;

    /**
     * 创建成功结果
     */
    public static ProcessingResult success() {
        return ProcessingResult.builder()
                .success(true)
                .startTime(LocalDateTime.now())
                .endTime(LocalDateTime.now())
                .build();
    }

    /**
     * 创建失败结果
     */
    public static ProcessingResult failure(String errorMessage) {
        return ProcessingResult.builder()
                .success(false)
                .errorMessage(errorMessage)
                .startTime(LocalDateTime.now())
                .endTime(LocalDateTime.now())
                .build();
    }

    /**
     * 创建带统计信息的结果
     */
    public static ProcessingResult withStats(int total, int success, int skipped, int failed) {
        return ProcessingResult.builder()
                .success(true)
                .totalProcessed(total)
                .successCount(success)
                .skippedCount(skipped)
                .failedCount(failed)
                .build();
    }

    /**
     * 添加步骤结果
     */
    public ProcessingResult addStepResult(StepResult stepResult) {
        if (this.stepResults == null) {
            this.stepResults = new java.util.ArrayList<>();
        }
        this.stepResults.add(stepResult);
        return this;
    }

    /**
     * 设置处理耗时
     */
    public ProcessingResult setDuration(LocalDateTime start, LocalDateTime end) {
        this.startTime = start;
        this.endTime = end;
        if (start != null && end != null) {
            this.duration = java.time.Duration.between(start, end).toMillis();
        }
        return this;
    }

    /**
     * 合并其他结果
     */
    public ProcessingResult merge(ProcessingResult other) {
        if (other == null) {
            return this;
        }

        this.totalProcessed += other.getTotalProcessed();
        this.successCount += other.getSuccessCount();
        this.skippedCount += other.getSkippedCount();
        this.failedCount += other.getFailedCount();
        this.totalGroups += other.getTotalGroups();

        if (other.getStepResults() != null) {
            if (this.stepResults == null) {
                this.stepResults = new java.util.ArrayList<>();
            }
            this.stepResults.addAll(other.getStepResults());
        }

        return this;
    }

    /**
     * 生成处理摘要信息
     */
    public String generateSummary(String operation) {
        return String.format("%s完成，共处理%d条记录，成功%d条，跳过%d条，失败%d条",
                operation, totalProcessed, successCount, skippedCount, failedCount);
    }
}

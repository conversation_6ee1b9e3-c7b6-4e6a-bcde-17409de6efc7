package ai.pricefox.mallfox.service.product.engine.result;

import lombok.Data;
import lombok.Builder;

import java.time.LocalDateTime;

/**
 * 处理步骤结果
 *
 * <AUTHOR>
 * @since 2025-01-10
 */
@Data
@Builder
public class StepResult {

    /**
     * 步骤名称
     */
    private String stepName;

    /**
     * 步骤是否成功
     */
    @Builder.Default
    private Boolean success = true;

    /**
     * 步骤开始时间
     */
    private LocalDateTime startTime;

    /**
     * 步骤结束时间
     */
    private LocalDateTime endTime;

    /**
     * 步骤耗时（毫秒）
     */
    private Long duration;

    /**
     * 处理的记录数
     */
    @Builder.Default
    private Integer processedCount = 0;

    /**
     * 成功的记录数
     */
    @Builder.Default
    private Integer successCount = 0;

    /**
     * 跳过的记录数
     */
    @Builder.Default
    private Integer skippedCount = 0;

    /**
     * 失败的记录数
     */
    @Builder.Default
    private Integer failedCount = 0;

    /**
     * 步骤详情信息
     */
    private String message;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 创建成功步骤结果
     */
    public static StepResult success(String stepName) {
        return StepResult.builder()
                .stepName(stepName)
                .success(true)
                .startTime(LocalDateTime.now())
                .endTime(LocalDateTime.now())
                .build();
    }

    /**
     * 创建失败步骤结果
     */
    public static StepResult failure(String stepName, String errorMessage) {
        return StepResult.builder()
                .stepName(stepName)
                .success(false)
                .errorMessage(errorMessage)
                .startTime(LocalDateTime.now())
                .endTime(LocalDateTime.now())
                .build();
    }

    /**
     * 创建带统计信息的步骤结果
     */
    public static StepResult withStats(String stepName, int processed, int success, int skipped, int failed) {
        return StepResult.builder()
                .stepName(stepName)
                .success(true)
                .processedCount(processed)
                .successCount(success)
                .skippedCount(skipped)
                .failedCount(failed)
                .build();
    }

    /**
     * 设置步骤耗时
     */
    public StepResult setDuration(LocalDateTime start, LocalDateTime end) {
        this.startTime = start;
        this.endTime = end;
        if (start != null && end != null) {
            this.duration = java.time.Duration.between(start, end).toMillis();
        }
        return this;
    }

    /**
     * 生成步骤摘要信息
     */
    public String generateSummary() {
        if (!success) {
            return String.format("步骤[%s]失败：%s", stepName, errorMessage);
        }
        return String.format("步骤[%s]完成，处理%d条记录，成功%d条，跳过%d条，失败%d条",
                stepName, processedCount, successCount, skippedCount, failedCount);
    }
}

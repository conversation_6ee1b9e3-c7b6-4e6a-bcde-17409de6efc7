package ai.pricefox.mallfox.service.product.impl;

import ai.pricefox.mallfox.domain.product.ChannelOffers;
import ai.pricefox.mallfox.enums.SellerTypeEnum;
import ai.pricefox.mallfox.mapper.product.ChannelOffersMapper;
import ai.pricefox.mallfox.service.product.ChannelOffersService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ChannelOffersServiceImpl extends ServiceImpl<ChannelOffersMapper, ChannelOffers> implements ChannelOffersService {

    @Override
    public List<ChannelOffers> getOffersBySkuId(String skuId) {
        return list(new LambdaQueryWrapper<ChannelOffers>()
                .eq(ChannelOffers::getSkuCode, skuId)
                .isNotNull(ChannelOffers::getPrice)
                .orderBy(true, true, ChannelOffers::getPrice));
    }

    @Override
    public ChannelOffers getLowestThirdPartyOffer(String skuId, String platform) {
        return getOne(new LambdaQueryWrapper<ChannelOffers>()
                .eq(ChannelOffers::getSkuCode, skuId)
                .eq(ChannelOffers::getPlatformCode, platform)
                 .eq(ChannelOffers::getSellerType, SellerTypeEnum.THIRD_PARTY.getCode())
                .isNotNull(ChannelOffers::getPrice)
                .orderByAsc(ChannelOffers::getPrice)
                .last("LIMIT 1"));
    }

    @Override
    public ChannelOffers getPlatformRetailOffer(String skuId, String platform) {
        return getOne(new LambdaQueryWrapper<ChannelOffers>()
                .eq(ChannelOffers::getSkuCode, skuId)
                .eq(ChannelOffers::getPlatformCode, platform)
                 .eq(ChannelOffers::getSellerType, SellerTypeEnum.PLATFORM.getCode())
                .isNotNull(ChannelOffers::getPrice));
    }
}

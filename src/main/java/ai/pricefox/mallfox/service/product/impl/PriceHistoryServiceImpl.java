package ai.pricefox.mallfox.service.product.impl;

import ai.pricefox.mallfox.common.exception.ServiceException;
import ai.pricefox.mallfox.config.ThreadPoolConfig;
import ai.pricefox.mallfox.mapper.product.PriceHistoryMapper;
import ai.pricefox.mallfox.model.response.PriceHistoryResponseDTO;
import ai.pricefox.mallfox.service.product.PriceHistoryService;
import ai.pricefox.mallfox.vo.product.ProductDetailRespVO;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;

/**
 * @description 价格历史服务实现类
 * <AUTHOR>
 * @date 2025-7-9 16:08:25
 * @version 1.0
 */
@Slf4j
@Service
public class PriceHistoryServiceImpl implements PriceHistoryService {

    @Autowired
    private PriceHistoryMapper priceHistoryMapper;

    @Autowired
    private ThreadPoolConfig threadPoolConfig;

    // 查询超时时间（秒）
    private static final int QUERY_TIMEOUT_SECONDS = 30;

    @Override
    public PriceHistoryResponseDTO getPriceHistory(String skuCode, String period, List<String> platforms) {
        log.info("开始查询价格历史 - skuCode: {}, period: {}, platforms: {}", skuCode, period, platforms);

        // 性能监控
        StopWatch stopWatch = new StopWatch("getPriceHistory");
        stopWatch.start("总体查询");

        try {
            // 计算日期范围
            LocalDate endDate = LocalDate.now();
            LocalDate startDate = calculateStartDate(period);
            log.debug("查询日期范围: {} 到 {}", startDate, endDate);

            // 确定查询的平台，如果传入为空，则默认为查询全平台聚合数据
            List<String> targetPlatforms = (CollectionUtils.isEmpty(platforms))
                    ? Collections.singletonList("ALL")
                    : platforms;
            log.debug("目标平台: {}", targetPlatforms);

            // 使用自定义线程池并行执行三个查询
            stopWatch.stop();
            stopWatch.start("并行查询");

            Future<List<PriceHistoryResponseDTO.DataPoint>> dataPointsFuture =
                threadPoolConfig.submitWithCustomPool(() -> {
                    log.debug("开始查询数据点 - 线程: {}", Thread.currentThread().getName());
                    try {
                        List<PriceHistoryResponseDTO.DataPoint> result =
                            priceHistoryMapper.findDataPoints(skuCode, startDate, endDate, targetPlatforms);
                        log.debug("数据点查询完成，返回 {} 个数据点", result != null ? result.size() : 0);
                        return result;
                    } catch (Exception e) {
                        log.error("查询数据点失败", e);
                        throw new RuntimeException("查询数据点失败: " + e.getMessage(), e);
                    }
                });

            Future<PriceHistoryResponseDTO.PricePoint> lowestInPeriodFuture =
                threadPoolConfig.submitWithCustomPool(() -> {
                    log.debug("开始查询周期最低价 - 线程: {}", Thread.currentThread().getName());
                    try {
                        PriceHistoryResponseDTO.PricePoint result =
                            priceHistoryMapper.findLowestPriceInPeriod(skuCode, startDate, endDate, targetPlatforms);
                        log.debug("周期最低价查询完成: {}", result);
                        return result;
                    } catch (Exception e) {
                        log.error("查询周期最低价失败", e);
                        throw new RuntimeException("查询周期最低价失败: " + e.getMessage(), e);
                    }
                });

            Future<PriceHistoryResponseDTO.PricePoint> latestPriceFuture =
                threadPoolConfig.submitWithCustomPool(() -> {
                    log.debug("开始查询最新价格 - 线程: {}", Thread.currentThread().getName());
                    try {
                        PriceHistoryResponseDTO.PricePoint result =
                            priceHistoryMapper.findLatestPrice(skuCode, targetPlatforms);
                        log.debug("最新价格查询完成: {}", result);
                        return result;
                    } catch (Exception e) {
                        log.error("查询最新价格失败", e);
                        throw new RuntimeException("查询最新价格失败: " + e.getMessage(), e);
                    }
                });

            stopWatch.stop();
            stopWatch.start("等待结果");

            // 等待所有查询完成，设置超时时间
            List<PriceHistoryResponseDTO.DataPoint> dataPoints;
            PriceHistoryResponseDTO.PricePoint lowestInPeriod;
            PriceHistoryResponseDTO.PricePoint latestPrice;

            try {
                dataPoints = dataPointsFuture.get(QUERY_TIMEOUT_SECONDS, TimeUnit.SECONDS);
                lowestInPeriod = lowestInPeriodFuture.get(QUERY_TIMEOUT_SECONDS, TimeUnit.SECONDS);
                latestPrice = latestPriceFuture.get(QUERY_TIMEOUT_SECONDS, TimeUnit.SECONDS);
            } catch (TimeoutException e) {
                log.error("查询超时，取消未完成的任务");
                dataPointsFuture.cancel(true);
                lowestInPeriodFuture.cancel(true);
                latestPriceFuture.cancel(true);
                throw new RuntimeException("查询价格历史超时，请稍后重试", e);
            } catch (Exception e) {
                log.error("查询价格历史时发生错误", e);
                throw new RuntimeException("查询价格历史时发生错误: " + e.getMessage(), e);
            }

            stopWatch.stop();
            stopWatch.start("组装响应");

            // 组装响应
            PriceHistoryResponseDTO response = new PriceHistoryResponseDTO();
            response.setDataPoints(dataPoints != null ? dataPoints : Collections.emptyList());
            response.setLowestPriceInPeriod(lowestInPeriod);
            response.setLatestPrice(latestPrice);

            stopWatch.stop();

            log.info("价格历史查询完成 - skuCode: {}, 数据点数量: {}, 总耗时: {}ms",
                    skuCode,
                    dataPoints != null ? dataPoints.size() : 0,
                    stopWatch.getTotalTimeMillis());

            // 记录详细的性能信息
            if (log.isDebugEnabled()) {
                log.debug("性能详情: {}", stopWatch.prettyPrint());
            }

            // 记录线程池状态
            logThreadPoolStatus();

            // 如果没有数据，生成补0的历史数据
            if (CollUtil.isEmpty(response.getDataPoints())) {
                List<ProductDetailRespVO.PriceHistoryPointVO> priceHistoryPointVOS = generateEmptyPriceHistory(startDate, endDate);
                response.setDataPoints(BeanUtil.copyToList(priceHistoryPointVOS, PriceHistoryResponseDTO.DataPoint.class));
                return response;
            }

            List<ProductDetailRespVO.PriceHistoryPointVO> priceHistoryPointVOS =
                    fillMissingDates(BeanUtil.copyToList(response.getDataPoints(), ProductDetailRespVO.PriceHistoryPointVO.class), startDate, endDate);
            response.setDataPoints(BeanUtil.copyToList(priceHistoryPointVOS, PriceHistoryResponseDTO.DataPoint.class));

            return response;

        } catch (IllegalArgumentException e) {
            log.warn("参数错误: {}", e.getMessage());
            throw e;
        } catch (RuntimeException e) {
            log.error("业务异常: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("系统异常", e);
            throw new RuntimeException("系统异常，请联系管理员", e);
        } finally {
            if (stopWatch.isRunning()) {
                stopWatch.stop();
            }
        }
    }


    /**
     * 获取30天价格历史
     */
    @Override
    public List<ProductDetailRespVO.PriceHistoryPointVO> getPriceHistory(String skuCode) {
        try {
            // 获取30天前的日期
            LocalDate endDate = LocalDate.now();
            LocalDate startDate = endDate.minusDays(30);

            // 查询价格历史数据
            List<String> platforms = List.of("ALL"); // 查询全平台聚合数据
            List<PriceHistoryResponseDTO.DataPoint> dataPoints =
                    priceHistoryMapper.findDataPoints(skuCode, startDate, endDate, platforms);

            // 转换为响应格式
            List<ProductDetailRespVO.PriceHistoryPointVO> history = dataPoints.stream()
                    .map(this::convertToHistoryPoint)
                    .collect(Collectors.toList());

            // 如果没有数据，生成补0的历史数据
            if (history.isEmpty()) {
                return generateEmptyPriceHistory(startDate, endDate);
            }

            // 补充缺失的日期（补0）
            return fillMissingDates(history, startDate, endDate);

        } catch (Exception e) {
            log.error("获取价格历史失败: skuCode={}, error={}", skuCode, e.getMessage(), e);
            return generateEmptyPriceHistory(LocalDate.now().minusDays(30), LocalDate.now());
        }
    }

    /**
     * 转换数据点格式
     */
    private ProductDetailRespVO.PriceHistoryPointVO convertToHistoryPoint(
            PriceHistoryResponseDTO.DataPoint dataPoint) {
        ProductDetailRespVO.PriceHistoryPointVO point =
                new ProductDetailRespVO.PriceHistoryPointVO();
        point.setDate(dataPoint.getDate().toString());
        point.setLowestPrice(dataPoint.getLowestPrice());
        point.setAveragePrice(dataPoint.getAveragePrice());
        return point;
    }

    /**
     * 生成空的价格历史数据（补0）
     */
    private List<ProductDetailRespVO.PriceHistoryPointVO> generateEmptyPriceHistory(
            LocalDate startDate, LocalDate endDate) {

        List<ProductDetailRespVO.PriceHistoryPointVO> history = new ArrayList<>();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        LocalDate current = startDate;
        while (!current.isAfter(endDate)) {
            ProductDetailRespVO.PriceHistoryPointVO point =
                    new ProductDetailRespVO.PriceHistoryPointVO();
            point.setDate(current.format(formatter));
            point.setLowestPrice(BigDecimal.ZERO);
            point.setAveragePrice(BigDecimal.ZERO);

            history.add(point);
            current = current.plusDays(1);
        }

        return history;
    }

    /**
     * 填充缺失的日期数据（补0）
     */
    private List<ProductDetailRespVO.PriceHistoryPointVO> fillMissingDates(
            List<ProductDetailRespVO.PriceHistoryPointVO> existingHistory,
            LocalDate startDate, LocalDate endDate) {

        Map<String, ProductDetailRespVO.PriceHistoryPointVO> historyMap = existingHistory.stream()
                .collect(Collectors.toMap(
                        ProductDetailRespVO.PriceHistoryPointVO::getDate,
                        point -> point
                ));

        List<ProductDetailRespVO.PriceHistoryPointVO> completeHistory = new ArrayList<>();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        LocalDate current = startDate;
        while (!current.isAfter(endDate)) {
            String dateStr = current.format(formatter);
            ProductDetailRespVO.PriceHistoryPointVO point = historyMap.get(dateStr);

            if (point == null) {
                // 创建补0的数据点
                point = new ProductDetailRespVO.PriceHistoryPointVO();
                point.setDate(dateStr);
                point.setLowestPrice(BigDecimal.ZERO);
                point.setAveragePrice(BigDecimal.ZERO);
            }

            completeHistory.add(point);
            current = current.plusDays(1);
        }

        return completeHistory;
    }



    /**
     * 根据传入的周期字符串计算开始日期。
     */
    private LocalDate calculateStartDate(String period) {
        if (period == null) {
            return LocalDate.now().minusMonths(1); // 默认1个月
        }
        switch (period.toUpperCase()) {
            case "1M": return LocalDate.now().minusMonths(1);
            case "6M": return LocalDate.now().minusMonths(6);
            case "1Y": return LocalDate.now().minusYears(1);
            case "2Y": return LocalDate.now().minusYears(2);
            case "ALL": return LocalDate.of(2000, 1, 1); // 一个足够早的日期代表“全部”
            case "3M":
            default: return LocalDate.now().minusMonths(3);
        }
    }

    /**
     * 记录线程池状态信息
     */
    private void logThreadPoolStatus() {
        try {
            ThreadPoolConfig.ThreadPoolStatus status = threadPoolConfig.getThreadPoolStatus();
            if (status != null && log.isDebugEnabled()) {
                log.debug("当前线程池状态: {}", status);
            }
        } catch (Exception e) {
            log.warn("获取线程池状态失败", e);
        }
    }
}

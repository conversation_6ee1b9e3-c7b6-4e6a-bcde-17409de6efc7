package ai.pricefox.mallfox.service.product.impl;

import ai.pricefox.mallfox.domain.product.ProductAttributeValue;
import ai.pricefox.mallfox.mapper.product.ProductAttributeMapper;
import ai.pricefox.mallfox.model.param.ProductAttributeParam;
import ai.pricefox.mallfox.service.product.ProductAttributeValueService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import ai.pricefox.mallfox.domain.product.ProductAttribute;
import ai.pricefox.mallfox.service.product.ProductAttributeService;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【product_attribute(商品属性表)】的数据库操作Service实现
 * @createDate 2025-05-18 12:14:02
 */
@Service
public class ProductAttributeServiceImpl extends ServiceImpl<ProductAttributeMapper, ProductAttribute> implements ProductAttributeService {

    @Autowired
    private ProductAttributeValueService productAttributeValueService;

    @Override
    public Boolean saveProductAttribute(ProductAttributeParam productAttribute) {
        ProductAttribute attribute = new ProductAttribute();
        BeanUtils.copyProperties(productAttribute, attribute);
        boolean save = this.save(attribute);
        if (save) {
            List<ProductAttributeValue> values = productAttribute.getValues();
            values.forEach(value -> value.setAttributeId(attribute.getId()));
            if (ObjectUtils.isNotEmpty(values)) {
              return   productAttributeValueService.saveOrUpdateBatch(values);

            }
        }
        return false;
    }

    @Override
    public ProductAttributeParam getProductAttributeById(String id) {
        ProductAttribute attribute = this.getById(id);
        ProductAttributeParam param = new ProductAttributeParam();
        BeanUtils.copyProperties(attribute, param);
        List<ProductAttributeValue> list = productAttributeValueService.list(new LambdaQueryWrapper<>(ProductAttributeValue.class).eq(ProductAttributeValue::getAttributeId, id));
        param.setValues(list);
        return param;
    }
}





package ai.pricefox.mallfox.service.product.impl;

import ai.pricefox.mallfox.mapper.product.ProductAttributeValueMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import ai.pricefox.mallfox.domain.product.ProductAttributeValue;
import ai.pricefox.mallfox.service.product.ProductAttributeValueService;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【product_attribute_value(商品属性值表)】的数据库操作Service实现
* @createDate 2025-05-18 12:14:02
*/
@Service
public class ProductAttributeValueServiceImpl extends ServiceImpl<ProductAttributeValueMapper, ProductAttributeValue>
    implements ProductAttributeValueService{

}





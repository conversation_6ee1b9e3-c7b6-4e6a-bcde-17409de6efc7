package ai.pricefox.mallfox.service.product.impl;

import ai.pricefox.mallfox.domain.product.ChannelOffers;
import ai.pricefox.mallfox.domain.product.ProductBestPriceSubmit;
import ai.pricefox.mallfox.enums.SellerTypeEnum;
import ai.pricefox.mallfox.mapper.product.ProductBestPriceSubmitMapper;
import ai.pricefox.mallfox.mapper.standard.TripartitePlatformMapper;
import ai.pricefox.mallfox.model.param.ProductBestPriceSubmitRequest;
import ai.pricefox.mallfox.model.param.ProductPriceCompareRequest;
import ai.pricefox.mallfox.model.response.ProductPriceCompareResponse;
import ai.pricefox.mallfox.service.product.ProductCompareService;
import ai.pricefox.mallfox.service.product.ChannelOffersService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @desc
 * @since 2025/7/30
 */
@Slf4j
@Service
public class ProductCompareServiceImpl implements ProductCompareService {

    @Autowired
    private ChannelOffersService channelOffersService;

    @Autowired
    private ProductBestPriceSubmitMapper productBestPriceSubmitMapper;

    @Override
    public List<ProductPriceCompareResponse> priceCompare(ProductPriceCompareRequest request) {
        log.info("开始价格比较查询: skuCode={}, platform={}, sellerType={}",
                request.getSkuCode(), request.getPlatformCode(), request.getSellerType());

        List<ProductPriceCompareResponse> result = new ArrayList<>();

        try {
            //  根据skuCode查找对应的ChannelOffers
            List<ChannelOffers> allOffers = findOffersBySkuCode(request.getSkuCode());
            if (allOffers.isEmpty()) {
                log.warn("未找到SKU的报价数据: skuCode={}", request.getSkuCode());
                return result;
            }

            //  应用筛选条件
            List<ChannelOffers> filteredOffers = applyFilters(allOffers, request);

            // 按平台分组处理
            Map<String, List<ChannelOffers>> platformGroups = filteredOffers.stream()
                    .collect(Collectors.groupingBy(ChannelOffers::getPlatformCode));

            //  为每个平台生成报价记录
            for (Map.Entry<String, List<ChannelOffers>> entry : platformGroups.entrySet()) {
                String platform = entry.getKey();
                List<ChannelOffers> platformOffers = entry.getValue();

                // 处理自营报价
                ChannelOffers retailOffer = findBestRetailOffer(platformOffers);
                if (retailOffer != null) {
                    result.add(buildPriceCompareResponse(retailOffer, SellerTypeEnum.PLATFORM.getCode()));
                }

                // 处理第三方最低价报价
                ChannelOffers thirdPartyOffer = findBestThirdPartyOffer(platformOffers);
                if (thirdPartyOffer != null && !isSameOffer(retailOffer, thirdPartyOffer)) {
                    result.add(buildPriceCompareResponse(thirdPartyOffer, SellerTypeEnum.THIRD_PARTY.getCode()));
                }
            }

            // 按最终价格排序
            result.sort(Comparator.comparing(ProductPriceCompareResponse::getFinalPrice,
                    Comparator.nullsLast(Comparator.naturalOrder())));

            // 根据状态过滤
            if (CollectionUtils.isEmpty(request.getConditions())) {
                result = result.stream()
                        .filter(r -> request.getConditions().contains(r.getCondition()))
                        .collect(Collectors.toList());
            }

            log.info("价格比较完成: skuCode={}, 找到{}条报价", request.getSkuCode(), result.size());

        } catch (Exception e) {
            log.error("价格比较失败: skuCode={}, error={}", request.getSkuCode(), e.getMessage(), e);
        }

        return result;
    }


    @Override
    public boolean bestPriceSubmit(ProductBestPriceSubmitRequest request) {
        log.info("收到最佳价格提交请求: skuCode={}, price={}", request.getSkuCode(), request.getPrice());

        try {
            // 创建提交记录
            ProductBestPriceSubmit submit = new ProductBestPriceSubmit();
            submit.setPrice(request.getPrice());
            submit.setItemUrl(request.getItemUrl());
            submit.setSubmitTime(LocalDateTime.now());
            submit.setSubmitUser(request.getSubmitUser());
            submit.setUserEmail(request.getUserEmail());
            submit.setSkuCode(request.getSkuCode());
            submit.setOptional(request.getOptional());

            // 同一天 同一个用户，同一个商品，只能提交一次
            LambdaQueryWrapper<ProductBestPriceSubmit> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ProductBestPriceSubmit::getSubmitUser, request.getSubmitUser())
                    .eq(ProductBestPriceSubmit::getSkuCode, request.getSkuCode())
                    .eq(ProductBestPriceSubmit::getSubmitTime, LocalDateTime.now().toLocalDate());
            if (productBestPriceSubmitMapper.selectCount(queryWrapper) > 0) {
                log.warn("用户今天已经提交过最佳价格: skuCode={}, user={}",
                        request.getSkuCode(), request.getSubmitUser());
                return false;
            }
            // 使用Mapper直接插入数据库
            int result = productBestPriceSubmitMapper.insert(submit);

            if (result > 0) {
                log.info("最佳价格提交成功: id={}, skuCode={}, price={}",
                        submit.getId(), request.getSkuCode(), request.getPrice());
                return true;
            } else {
                log.error("最佳价格提交失败: skuCode={}, price={}",
                        request.getSkuCode(), request.getPrice());
                return false;
            }

        } catch (Exception e) {
            log.error("提交最佳价格异常: skuCode={}, price={}, error={}",
                    request.getSkuCode(), request.getPrice(), e.getMessage(), e);
            return false;
        }
    }

    /**
     * 根据skuCode查找ChannelOffers
     */
    private List<ChannelOffers> findOffersBySkuCode(String skuCode) {
        // 查询离当前最近一天的sku列表
//        LocalDateTime now = LocalDateTime.now();
//        LocalDateTime oneDayAgo = now.minusDays(1);

        LambdaQueryWrapper<ChannelOffers> queryWrapper = new LambdaQueryWrapper<ChannelOffers>()
                .eq(ChannelOffers::getSkuCode, skuCode)
                .isNotNull(ChannelOffers::getPrice)
//                .ge(ChannelOffers::getLastRecordTime, oneDayAgo)
                .orderByAsc(ChannelOffers::getPrice);

        return channelOffersService.list(queryWrapper);
    }

    /**
     * 应用筛选条件
     */
    private List<ChannelOffers> applyFilters(List<ChannelOffers> offers, ProductPriceCompareRequest request) {
        return offers.stream()
                .filter(offer -> filterByPlatform(offer, request.getPlatformCode()))
                .filter(offer -> filterBySellerType(offer, request.getSellerType()))
                .filter(offer -> filterByMerchantRating(offer, request.getMinMerchantRating()))
                .filter(offer -> filterByShippingTimeRange(offer, request.getMinShippingTime(), request.getMaxShippingTime()))
                .filter(offer -> filterByConditions(offer, request.getConditions()))
                .collect(Collectors.toList());
    }

    /**
     * 平台筛选
     */
    private boolean filterByPlatform(ChannelOffers offer, String platform) {
        if (!StringUtils.hasText(platform)) {
            return true;
        }
        return platform.equalsIgnoreCase(offer.getPlatformCode());
    }

    /**
     * 卖家类型筛选
     */
    private boolean filterBySellerType(ChannelOffers offer, String sellerType) {
        if (!StringUtils.hasText(sellerType)) {
            return true;
        }
        if (SellerTypeEnum.getByCode(sellerType) == null) {
            log.warn("未知的卖家类型: {}", sellerType);
            return true;
        }

        return SellerTypeEnum.getByCode(sellerType).getCode().equals(offer.getSellerType());
    }

    /**
     * 商家评分筛选
     */
    private boolean filterByMerchantRating(ChannelOffers offer, Double minRating) {
        if (minRating == null) {
            return true;
        }
        if (offer.getMerchantRating() == null) {
            return false;
        }
        return offer.getMerchantRating().doubleValue() >= minRating;
    }

    /**
     * 发货时效区间筛选
     */
    private boolean filterByShippingTimeRange(ChannelOffers offer, Double minShippingTime, Double maxShippingTime) {
        String shippingTime = offer.getShippingTime();
        if (!StringUtils.hasText(shippingTime)) {
            return false;
        }

        // 解析发货时间，提取数字部分
        Double shippingDays = parseShippingDays(shippingTime);
        if (shippingDays == null) {
            return false;
        }

        // 区间筛选逻辑
        boolean matchMin = (minShippingTime == null) || (shippingDays >= minShippingTime);
        boolean matchMax = (maxShippingTime == null) || (shippingDays <= maxShippingTime);

        return matchMin && matchMax;
    }

    /**
     * 商品成色筛选
     */
    private boolean filterByConditions(ChannelOffers offer, List<String> conditions) {
        if (CollectionUtils.isEmpty(conditions)) {
            return true;
        }
        // TODO: 需要ChannelOffers表添加condition字段
        // 临时方案：默认认为是New
        String offerCondition = "New"; // offer.getCondition();

        return conditions.contains(offerCondition);
    }

    /**
     * 查找最佳自营报价
     */
    private ChannelOffers findBestRetailOffer(List<ChannelOffers> platformOffers) {
        return platformOffers.stream()
                .filter(offer -> SellerTypeEnum.isPlatform(offer.getSellerType()))
                .filter(offer -> offer.getPrice() != null)
                .min(Comparator.comparing(ChannelOffers::getPrice))
                .orElse(null);
    }

    /**
     * 查找最佳第三方报价
     */
    private ChannelOffers findBestThirdPartyOffer(List<ChannelOffers> platformOffers) {
        return platformOffers.stream()
                .filter(offer -> SellerTypeEnum.isThirdParty(offer.getSellerType()))
                .filter(offer -> offer.getPrice() != null)
                .min(Comparator.comparing(ChannelOffers::getPrice))
                .orElse(null);
    }

    /**
     * 判断是否为自营卖家
     */
    private boolean isRetailSeller(String sellerName) {
        if (!StringUtils.hasText(sellerName)) {
            return false;
        }

        String lowerName = sellerName.toLowerCase();
        return lowerName.contains("amazon") ||
                lowerName.contains("ebay") ||
                lowerName.equals("lenovo") ||
                lowerName.contains("官方") ||
                lowerName.contains("自营") ||
                lowerName.contains("flagship") ||
                lowerName.contains("official");
    }

    /**
     * 判断是否为同一个报价
     */
    private boolean isSameOffer(ChannelOffers offer1, ChannelOffers offer2) {
        if (offer1 == null || offer2 == null) {
            return false;
        }
        return Objects.equals(offer1.getId(), offer2.getId());
    }

    /**
     * 构建价格比较响应对象
     */
    private ProductPriceCompareResponse buildPriceCompareResponse(ChannelOffers offer, String sellerType) {
        ProductPriceCompareResponse response = new ProductPriceCompareResponse();

        // 基础价格信息
        response.setItemPrice(offer.getPrice());
        response.setFinalPrice(offer.getPrice());

        BigDecimal discount = offer.getListPrice().subtract(offer.getPrice());
        response.setDiscount(discount.divide(offer.getListPrice(), 2, RoundingMode.HALF_UP).multiply(new BigDecimal("100")).toPlainString() + "%");

        // 平台和卖家信息
        response.setPlatform(offer.getPlatformCode());
        response.setMerchant(offer.getSellerName());
        response.setMerchantRating(offer.getMerchantRating());
        response.setSellerType(sellerType);
        response.setMerchantRatingCount(offer.getMerchantRatingCount());
        response.setCondition(offer.getCondition());

        // 发货和退货信息
        response.setShippingTime(formatShippingTime(offer.getShippingTime()));
        response.setReturnPolicy(offer.getReturnPolicy());

        // 支付信息
        response.setPaymentInstallment(offer.getInstallmentInfo());
        response.setShopUrl(offer.getItemUrl());

        return response;
    }

    /**
     * 计算最终价格
     */
    private BigDecimal calculateFinalPrice(ChannelOffers offer) {
        // TODO: 实现包含税费、运费等的最终价格计算
        // 临时方案：如果有划线价且低于当前价格，使用划线价，否则返回当前价格
        if (offer.getListPrice() != null && offer.getListPrice().compareTo(offer.getPrice()) < 0) {
            return offer.getListPrice();
        }
        return offer.getPrice();
    }

    /**
     * 格式化发货时间
     */
    private String formatShippingTime(String shippingTime) {
        if (!StringUtils.hasText(shippingTime)) {
            return "未知";
        }

        // 标准化发货时间格式
        if (shippingTime.toLowerCase().contains("day")) {
            return shippingTime;
        }

        // 尝试提取数字并格式化
        Double days = parseShippingDays(shippingTime);
        if (days != null) {
            if (days <= 1) {
                return "1 day";
            } else {
                return String.format("%.0f days", days);
            }
        }

        return shippingTime;
    }

    /**
     * 解析发货天数
     */
    private Double parseShippingDays(String shippingTime) {
        if (!StringUtils.hasText(shippingTime)) {
            return null;
        }

        String lowerTime = shippingTime.toLowerCase().trim();

        try {
            // 处理 "1-2 days" 格式，取最大值
            if (lowerTime.matches("\\d+-\\d+\\s*days?")) {
                String[] parts = lowerTime.replaceAll("[^0-9-]", "").split("-");
                if (parts.length == 2) {
                    // 取区间最大值
                    return Double.parseDouble(parts[1]);
                }
            }

            // 处理 "Ships in 2 days" 格式
            if (lowerTime.contains("ships in") && lowerTime.contains("day")) {
                String numberPart = lowerTime.replaceAll(".*ships in\\s*(\\d+).*", "$1");
                if (numberPart.matches("\\d+")) {
                    return Double.parseDouble(numberPart);
                }
            }

            // 处理 "2-day shipping" 格式
            if (lowerTime.contains("day shipping")) {
                String numberPart = lowerTime.replaceAll(".*(\\d+)-day shipping.*", "$1");
                if (numberPart.matches("\\d+")) {
                    return Double.parseDouble(numberPart);
                }
            }

            // 处理 "Next-day delivery available" - 认为是1天
            if (lowerTime.contains("next-day") || lowerTime.contains("next day")) {
                return 1.0;
            }

            // 处理 "Ships by May 30" 等日期格式 - 暂时返回null，需要具体日期计算
            if (lowerTime.contains("ships by")) {
                // TODO: 可以根据具体日期计算天数
                return null;
            }

            // 通用数字提取 - 兜底方案
            String numberPart = lowerTime.replaceAll("[^0-9.]", "");
            if (StringUtils.hasText(numberPart)) {
                return Double.parseDouble(numberPart);
            }

        } catch (NumberFormatException e) {
            log.debug("无法解析发货时间: {}", shippingTime);
        }

        return null;
    }
}

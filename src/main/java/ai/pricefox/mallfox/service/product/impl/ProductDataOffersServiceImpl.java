package ai.pricefox.mallfox.service.product.impl;

import ai.pricefox.mallfox.domain.product.ProductPriceHistory;
import ai.pricefox.mallfox.mapper.product.ProductDataOffersMapper;
import ai.pricefox.mallfox.service.product.listener.PriceChangeListener;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import ai.pricefox.mallfox.domain.product.ProductDataOffers;
import ai.pricefox.mallfox.service.product.ProductDataOffersService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【product_data_offers(多平台商品高频表)】的数据库操作Service实现
 * @createDate 2025-06-15 19:12:11
 */
@Service
@Slf4j
public class ProductDataOffersServiceImpl extends ServiceImpl<ProductDataOffersMapper, ProductDataOffers>
        implements ProductDataOffersService {

    @Autowired
    private PriceChangeListener priceChangeListener;

    @Override
    @Transactional
    public boolean saveOrUpdateWithPriceHistory(ProductDataOffers newOffer, String triggerSource) {
        if (newOffer == null) {
            log.warn("新offer数据为空，无法保存");
            return false;
        }
        
        ProductDataOffers oldOffer = null;
        
        try {
            // 如果有ID，先查询旧数据
            if (newOffer.getId() != null) {
                oldOffer = this.getById(newOffer.getId());
            } else {
                // 根据业务逻辑查找是否存在相同的offer
                // 使用SKU ID、平台、平台SKU ID作为唯一性判断
                LambdaQueryWrapper<ProductDataOffers> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(ProductDataOffers::getSkuId, newOffer.getSkuId())
                       .eq(ProductDataOffers::getSourcePlatform, newOffer.getSourcePlatform())
                       .eq(ProductDataOffers::getPlatformSkuId, newOffer.getPlatformSkuId());
                oldOffer = this.getOne(wrapper);
                
                if (oldOffer != null) {
                    newOffer.setId(oldOffer.getId()); // 设置ID用于更新
                }
            }
            
            // 保存或更新数据
            boolean success = this.saveOrUpdate(newOffer);
            
            // 如果保存成功，触发价格变化监听
            if (success) {
                // 确保触发源不为空
                String actualTriggerSource = triggerSource != null ? triggerSource : 
                    ProductPriceHistory.TriggerSource.MANUAL_UPDATE.getCode();
                priceChangeListener.onPriceChange(oldOffer, newOffer, actualTriggerSource);
                log.debug("offer数据保存成功并已触发价格历史记录: offerId={}", newOffer.getId());
            } else {
                log.error("offer数据保存失败: skuId={}", newOffer.getSkuId());
            }
            
            return success;
            
        } catch (Exception e) {
            log.error("保存offer数据异常: skuId={}, error={}", newOffer.getSkuId(), e.getMessage(), e);
            throw e; // 重新抛出异常，让@Transactional处理回滚
        }
    }
    
    @Override
    @Transactional
    public boolean batchSaveOrUpdateWithPriceHistory(List<ProductDataOffers> offerList, String triggerSource) {
        if (CollectionUtils.isEmpty(offerList)) {
            log.warn("offer列表为空，跳过批量保存");
            return true;
        }
        
        log.info("开始批量保存offer数据（带价格历史）: size={}", offerList.size());
        
        boolean allSuccess = true;
        int successCount = 0;
        int failedCount = 0;
        
        for (ProductDataOffers offer : offerList) {
            try {
                boolean success = saveOrUpdateWithPriceHistory(offer, triggerSource);
                if (success) {
                    successCount++;
                } else {
                    allSuccess = false;
                    failedCount++;
                    log.error("批量保存中单个offer失败: skuId={}", offer.getSkuId());
                }
            } catch (Exception e) {
                allSuccess = false;
                failedCount++;
                log.error("批量保存中单个offer异常: skuId={}, error={}", offer.getSkuId(), e.getMessage(), e);
            }
        }
        
        log.info("批量保存offer数据完成: 总数={}, 成功={}, 失败={}", 
            offerList.size(), successCount, failedCount);
        
        return allSuccess;
    }
    
    @Override
    @Transactional
    public boolean removeWithPriceHistory(Long offerId, String triggerSource) {
        if (offerId == null) {
            log.warn("offer ID为空，无法删除");
            return false;
        }
        
        try {
            // 先查询要删除的数据，用于记录删除历史
            ProductDataOffers deletedOffer = this.getById(offerId);
            if (deletedOffer == null) {
                log.warn("要删除的offer不存在: offerId={}", offerId);
                return true; // 数据不存在也算删除成功
            }
            
            // 执行删除操作
            boolean success = this.removeById(offerId);
            
            // 如果删除成功，记录删除历史
            if (success) {
                String actualTriggerSource = triggerSource != null ? triggerSource : 
                    ProductPriceHistory.TriggerSource.MANUAL_UPDATE.getCode();
                priceChangeListener.onOfferDeleted(deletedOffer, actualTriggerSource);
                log.info("offer数据删除成功并已记录删除历史: offerId={}", offerId);
            } else {
                log.error("offer数据删除失败: offerId={}", offerId);
            }
            
            return success;
            
        } catch (Exception e) {
            log.error("删除offer数据异常: offerId={}, error={}", offerId, e.getMessage(), e);
            throw e; // 重新抛出异常，让@Transactional处理回滚
        }
    }
}





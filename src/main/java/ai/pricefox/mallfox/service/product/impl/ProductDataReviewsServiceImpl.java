package ai.pricefox.mallfox.service.product.impl;

import ai.pricefox.mallfox.mapper.product.ProductDataReviewsMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import ai.pricefox.mallfox.domain.product.ProductDataReviews;
import ai.pricefox.mallfox.service.product.ProductDataReviewsService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description 针对表【product_data_reviews(多平台商品评论数据表)】的数据库操作Service实现
 * @createDate 2025-06-15 19:12:11
 */
@Service
public class ProductDataReviewsServiceImpl extends ServiceImpl<ProductDataReviewsMapper, ProductDataReviews>
        implements ProductDataReviewsService {
}





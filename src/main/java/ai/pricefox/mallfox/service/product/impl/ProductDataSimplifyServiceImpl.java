package ai.pricefox.mallfox.service.product.impl;

import ai.pricefox.mallfox.domain.product.ProductDataOffers;
import ai.pricefox.mallfox.domain.product.ProductDataSimplify;
import ai.pricefox.mallfox.mapper.product.ProductDataSimplifyMapper;
import ai.pricefox.mallfox.model.param.ProductDataSimplifyParam;
import ai.pricefox.mallfox.model.vo.product.data.ProductModelMergeReqVO;
import ai.pricefox.mallfox.model.vo.product.data.ProductModelMergeRespVO;
import ai.pricefox.mallfox.model.vo.product.data.ProductModelProcessReqVO;
import ai.pricefox.mallfox.model.vo.product.data.ProductModelProcessRespVO;
import ai.pricefox.mallfox.model.vo.product.data.ProductSkuMergeReqVO;
import ai.pricefox.mallfox.model.vo.product.data.ProductSkuMergeRespVO;
import ai.pricefox.mallfox.model.vo.product.data.UpcCodeFixReqVO;
import ai.pricefox.mallfox.model.vo.product.data.UpcCodeFixRespVO;
import ai.pricefox.mallfox.service.product.ProductDataOffersService;
import ai.pricefox.mallfox.service.product.ProductDataSimplifyService;
import ai.pricefox.mallfox.service.product.engine.adapter.ProcessingEngineAdapter;
import ai.pricefox.mallfox.service.product.util.ProductMatchingUtil;
import ai.pricefox.mallfox.common.util.IdGenerator;
import ai.pricefox.mallfox.mapper.product.ProductDataOffersMapper;
import ai.pricefox.mallfox.vo.base.CommonResult;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【product_data_simplify(商品低频数据表)】的数据库操作Service实现
 * @createDate 2025-06-15 19:12:11
 */
@Service
@Slf4j
public class ProductDataSimplifyServiceImpl extends ServiceImpl<ProductDataSimplifyMapper, ProductDataSimplify>
        implements ProductDataSimplifyService {

    @Autowired
    private ProductDataOffersService productDataOffersService;

    @Autowired
    private ProcessingEngineAdapter processingEngineAdapter;

    @Autowired
    private ProductDataOffersMapper productDataOffersMapper;

    // FIXME 第一版修改后期去掉，无用的代码
    @Override
    public CommonResult saveProductDataSimplify(ProductDataSimplifyParam productDataSimplify) {
        ProductDataSimplify dataSimplify = new ProductDataSimplify();
        BeanUtils.copyProperties(productDataSimplify, dataSimplify);
        return save(dataSimplify) ? CommonResult.success() : null;
    }

    @Override
    public List<String> getDistinctModelsByDataChannel(Integer dataChannel) {
        return baseMapper.selectDistinctModelsByDataChannel(dataChannel);
    }

    @Override
    public ProductModelProcessRespVO processProductModel(ProductModelProcessReqVO reqVO) {
        log.info("通过处理引擎处理商品型号，参数：{}", reqVO);

        // 使用通用处理引擎适配器
        return processingEngineAdapter.processProductModel(reqVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProductModelMergeRespVO mergeProductModel(ProductModelMergeReqVO reqVO) {
        log.info("通过处理引擎合并商品型号，参数：{}", reqVO);

        // 使用通用处理引擎适配器
        return processingEngineAdapter.mergeProductModel(reqVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProductSkuMergeRespVO mergeProductSku(ProductSkuMergeReqVO reqVO) {
        log.info("通过处理引擎合并商品SKU，参数：{}", reqVO);

        // 使用通用处理引擎适配器
        return processingEngineAdapter.mergeProductSku(reqVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public UpcCodeFixRespVO fixUpcCodeDoesNotApplySpuIds(UpcCodeFixReqVO reqVO) {
        log.info("开始修复UPC码为'Does not apply'的记录的spuid，参数：{}", reqVO);

        UpcCodeFixRespVO respVO = new UpcCodeFixRespVO();
        respVO.setDetails(new ArrayList<>());
        respVO.setErrors(new ArrayList<>());

        try {
            // 分页查询需要修复的记录
            Page<ProductDataOffers> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
            Page<ProductDataOffers> resultPage = productDataOffersMapper.selectUpcCodeDoesNotApplyRecords(
                    page, reqVO.getSourcePlatform());

            List<ProductDataOffers> records = resultPage.getRecords();
            respVO.setTotalProcessed(records.size());
            respVO.setHasMore(resultPage.hasNext());

            if (records.isEmpty()) {
                respVO.setMessage("没有找到需要修复的记录");
                respVO.setSuccessCount(0);
                respVO.setSkippedCount(0);
                respVO.setFailedCount(0);
                respVO.setNewSpuCount(0);
                respVO.setReusedSpuCount(0);
                return respVO;
            }

            log.info("找到{}条需要修复的记录", records.size());

            if (reqVO.getPreviewMode()) {
                return previewUpcCodeFix(records, respVO);
            } else {
                return executeUpcCodeFix(records, respVO, reqVO.getForceUpdate());
            }

        } catch (Exception e) {
            log.error("修复UPC码记录失败", e);
            respVO.getErrors().add("修复过程中发生异常：" + e.getMessage());
            respVO.setMessage("修复失败：" + e.getMessage());
            throw e;
        }
    }

    /**
     * 预览模式：分析需要修复的记录
     */
    private UpcCodeFixRespVO previewUpcCodeFix(List<ProductDataOffers> records, UpcCodeFixRespVO respVO) {
        log.info("预览模式：分析{}条记录", records.size());

        Map<String, List<ProductDataOffers>> groupedByModel = new HashMap<>();
        int newSpuCount = 0;
        int reusedSpuCount = 0;

        for (ProductDataOffers record : records) {
            try {
                // 获取对应的simplify记录来获取processed_model
                ProductDataSimplify simplify = baseMapper.selectOneBySkuAndSpu(record.getSkuId(), record.getSpuId());
                if (simplify == null) {
                    respVO.getDetails().add("记录ID " + record.getId() + " 找不到对应的simplify记录");
                    continue;
                }

                String processedModel = simplify.getModel();
                if (!StringUtils.hasText(processedModel)) {
                    processedModel = "EMPTY_MODEL";
                }

                String normalizedModel = ProductMatchingUtil.normalizeString(processedModel);
                groupedByModel.computeIfAbsent(normalizedModel, k -> new ArrayList<>()).add(record);

                // 检查是否能找到现有的SPU
                List<ProductDataSimplify> existingSpuRecords = baseMapper.selectByNormalizedModel(normalizedModel);
                if (existingSpuRecords.isEmpty()) {
                    newSpuCount++;
                } else {
                    reusedSpuCount++;
                }

            } catch (Exception e) {
                log.error("预览记录ID {}失败", record.getId(), e);
                respVO.getErrors().add("预览记录ID " + record.getId() + " 失败：" + e.getMessage());
            }
        }

        respVO.setSuccessCount(records.size());
        respVO.setSkippedCount(0);
        respVO.setFailedCount(0);
        respVO.setNewSpuCount(newSpuCount);
        respVO.setReusedSpuCount(reusedSpuCount);
        respVO.setMessage(String.format("预览完成，共分析%d条记录，将生成%d个新SPU，重用%d个现有SPU",
                records.size(), newSpuCount, reusedSpuCount));

        respVO.getDetails().add(String.format("按型号分组结果：共%d个不同的型号组", groupedByModel.size()));
        for (Map.Entry<String, List<ProductDataOffers>> entry : groupedByModel.entrySet()) {
            respVO.getDetails().add(String.format("型号组'%s'：%d条记录",
                    entry.getKey(), entry.getValue().size()));
        }

        return respVO;
    }

    /**
     * 执行修复：重新应用SPU匹配逻辑
     */
    private UpcCodeFixRespVO executeUpcCodeFix(List<ProductDataOffers> records, UpcCodeFixRespVO respVO, Boolean forceUpdate) {
        log.info("执行修复：处理{}条记录", records.size());

        int successCount = 0;
        int skippedCount = 0;
        int failedCount = 0;
        int newSpuCount = 0;
        int reusedSpuCount = 0;

        // 按SKU分组处理，避免同一个SKU的多个offers记录重复处理
        Map<String, List<ProductDataOffers>> groupedBySkuId = records.stream()
                .collect(Collectors.groupingBy(ProductDataOffers::getSkuId));

        for (Map.Entry<String, List<ProductDataOffers>> entry : groupedBySkuId.entrySet()) {
            String skuId = entry.getKey();
            List<ProductDataOffers> skuOffers = entry.getValue();

            try {
                // 获取对应的simplify记录
                ProductDataOffers firstOffer = skuOffers.get(0);
                ProductDataSimplify simplify = baseMapper.selectOneBySkuAndSpu(skuId, firstOffer.getSpuId());

                if (simplify == null) {
                    log.warn("SKU {} 找不到对应的simplify记录，跳过", skuId);
                    skippedCount += skuOffers.size();
                    respVO.getDetails().add("SKU " + skuId + " 找不到对应的simplify记录，跳过");
                    continue;
                }

                String processedModel = simplify.getModel();
                if (!StringUtils.hasText(processedModel)) {
                    log.warn("SKU {} 的型号为空，跳过", skuId);
                    skippedCount += skuOffers.size();
                    respVO.getDetails().add("SKU " + skuId + " 的型号为空，跳过");
                    continue;
                }

                // 重新应用SPU匹配逻辑
                String newSpuId = findOrCreateSpuIdForModel(processedModel, skuId);

                if (newSpuId.equals(firstOffer.getSpuId()) && !forceUpdate) {
                    log.debug("SKU {} 的spuid无需更新", skuId);
                    skippedCount += skuOffers.size();
                    continue;
                }

                // 更新ProductDataOffers表
                List<Long> offerIds = skuOffers.stream()
                        .map(ProductDataOffers::getId)
                        .collect(Collectors.toList());

                int updatedOffers = productDataOffersMapper.updateSpuIdByIds(offerIds, newSpuId);

                // 更新ProductDataSimplify表
                int updatedSimplify = baseMapper.updateSpuIdBySkuIds(Collections.singletonList(skuId), newSpuId);

                if (updatedOffers > 0 && updatedSimplify > 0) {
                    successCount += skuOffers.size();

                    // 判断是新生成的SPU还是重用现有的SPU
                    if (isNewlyGeneratedSpu(newSpuId, processedModel)) {
                        newSpuCount++;
                    } else {
                        reusedSpuCount++;
                    }

                    respVO.getDetails().add(String.format("SKU %s 更新成功，新spuid: %s，影响%d条offers记录",
                            skuId, newSpuId, skuOffers.size()));
                } else {
                    failedCount += skuOffers.size();
                    respVO.getErrors().add("SKU " + skuId + " 更新失败");
                }

            } catch (Exception e) {
                log.error("处理SKU {}失败", skuId, e);
                failedCount += skuOffers.size();
                respVO.getErrors().add("处理SKU " + skuId + " 失败：" + e.getMessage());
            }
        }

        respVO.setSuccessCount(successCount);
        respVO.setSkippedCount(skippedCount);
        respVO.setFailedCount(failedCount);
        respVO.setNewSpuCount(newSpuCount);
        respVO.setReusedSpuCount(reusedSpuCount);
        respVO.setMessage(String.format("修复完成，共处理%d条记录，成功%d条，跳过%d条，失败%d条",
                records.size(), successCount, skippedCount, failedCount));

        return respVO;
    }

    /**
     * 重新应用SPU匹配逻辑（重用现有的逻辑）
     */
    private String findOrCreateSpuIdForModel(String processedModel, String skuId) {
        String normalizedModel = ProductMatchingUtil.normalizeString(processedModel);

        // 查找现有的SPU记录
        List<ProductDataSimplify> spuRecords = baseMapper.selectByNormalizedModel(normalizedModel);

        if (!spuRecords.isEmpty() && !spuRecords.get(0).getSkuId().equals(skuId)) {
            String spuId = spuRecords.get(0).getSpuId();
            log.debug("型号[{}] SPU匹配成功，使用现有SPU ID: {}", processedModel, spuId);
            return spuId;
        } else {
            String spuId = IdGenerator.generateSpu();
            log.debug("型号[{}]未匹配到现有SPU，生成新SPU ID: {}", processedModel, spuId);
            return spuId;
        }
    }

    /**
     * 判断是否为新生成的SPU
     */
    private boolean isNewlyGeneratedSpu(String spuId, String processedModel) {
        String normalizedModel = ProductMatchingUtil.normalizeString(processedModel);
        List<ProductDataSimplify> existingRecords = baseMapper.selectByNormalizedModel(normalizedModel);

        // 如果找不到现有记录，说明是新生成的SPU
        return existingRecords.isEmpty() ||
               existingRecords.stream().noneMatch(record -> spuId.equals(record.getSpuId()));
    }

}

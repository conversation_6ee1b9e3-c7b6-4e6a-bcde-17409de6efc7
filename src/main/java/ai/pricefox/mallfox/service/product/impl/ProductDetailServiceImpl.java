package ai.pricefox.mallfox.service.product.impl;

import ai.pricefox.mallfox.domain.auth.WishlistItem;
import ai.pricefox.mallfox.domain.product.StandardProduct;
import ai.pricefox.mallfox.domain.product.ProductVariants;
import ai.pricefox.mallfox.mapper.product.StandardProductMapper;
import ai.pricefox.mallfox.mapper.product.ProductVariantsMapper;
import ai.pricefox.mallfox.service.auth.WishlistItemService;
import ai.pricefox.mallfox.mapper.product.PriceHistoryMapper;
import ai.pricefox.mallfox.model.response.PriceHistoryResponseDTO;
import ai.pricefox.mallfox.service.product.PriceHistoryService;
import ai.pricefox.mallfox.service.product.ProductDetailService;
import ai.pricefox.mallfox.utils.TokenUtil;
import ai.pricefox.mallfox.vo.product.ProductDetailRespVO;
import ai.pricefox.mallfox.common.constant.RedisKeyConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 商品详情服务实现类 - 重构版
 * <AUTHOR>
 */
@Service
@Slf4j
public class ProductDetailServiceImpl implements ProductDetailService {

    @Autowired
    private StandardProductMapper standardProductMapper;

    @Autowired
    private ProductVariantsMapper productVariantsMapper;

    @Autowired
    private WishlistItemService wishlistItemService;

    @Autowired
    private PriceHistoryService priceHistoryService;

    @Override
    @Cacheable(cacheNames = RedisKeyConstants.PRODUCT_DETAIL + "#" + RedisKeyConstants.PRODUCT_DETAIL_TIMEOUT,
               key = "#skuCode", unless = "#result == null")
    public ProductDetailRespVO getProductDetail(String skuCode) {
        log.info("开始获取商品详情: skuCode={}", skuCode);

        try {
            // 1. 通过skuCode查询变体信息获取spuCode
            ProductVariants currentVariant = productVariantsMapper.findBySkuCode(skuCode);
            if (currentVariant == null) {
                log.warn("未找到SKU对应的变体信息: skuCode={}", skuCode);
                return null;
            }

            String spuCode = currentVariant.getSpuCode();
            if (!StringUtils.hasText(spuCode)) {
                log.warn("变体中SPU编码为空: skuCode={}", skuCode);
                return null;
            }

            // 2. 查询标准商品信息
            StandardProduct standardProduct = standardProductMapper.findBySpuCode(spuCode);
            if (standardProduct == null) {
                log.warn("未找到SPU对应的标准商品信息: spuCode={}", spuCode);
                return null;
            }

            // 3. 获取当前用户ID（可能为空）
            Long currentUserId = TokenUtil.getCurrentUserIdOrNull();
            log.debug("当前用户ID: {}", currentUserId);

            // 4. 构建基础商品信息
            ProductDetailRespVO.BaseProductVO baseProduct = buildBaseProduct(standardProduct);

            // 5. 查询变体信息
            List<ProductDetailRespVO.ProductVariantVO> variants = buildVariants(
                standardProduct, spuCode, currentUserId);

            // 6. 构建响应对象
            ProductDetailRespVO response = new ProductDetailRespVO();
            response.setBaseProduct(baseProduct);
            response.setVariants(variants);

            log.info("商品详情获取成功: skuCode={}, variants数量={}", skuCode, variants.size());
            return response;

        } catch (Exception e) {
            log.error("获取商品详情失败: skuCode={}, error={}", 
                skuCode, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 构建基础商品信息 - 仅包含StandardProduct数据
     */
    private ProductDetailRespVO.BaseProductVO buildBaseProduct(StandardProduct standardProduct) {
        ProductDetailRespVO.BaseProductVO baseProduct = new ProductDetailRespVO.BaseProductVO();
        
        // 基础信息 - 仅来自 StandardProduct
        baseProduct.setSpuCode(standardProduct.getSpuCode());
        baseProduct.setTitle(standardProduct.getTitle());
        baseProduct.setSortTitle(standardProduct.getSortTitle());
        baseProduct.setBrandName(standardProduct.getBrandName());
        baseProduct.setBrandCode(standardProduct.getBrandCode());
        baseProduct.setCategoryCode(standardProduct.getCategoryCode());
        baseProduct.setBrief(standardProduct.getBrief());
        baseProduct.setDescription(standardProduct.getDescription());
        baseProduct.setMasterImgUrl(standardProduct.getMasterImgUrl());
        baseProduct.setHasOnlyDefaultVariant(standardProduct.getHasOnlyDefaultVariant());
        baseProduct.setPublished(standardProduct.getPublished());
        baseProduct.setPublishedAt(standardProduct.getPublishedAt());
        baseProduct.setTags(standardProduct.getTags());
        baseProduct.setSeoTitle(standardProduct.getSeoTitle());
        baseProduct.setSeoDescription(standardProduct.getSeoDescription());
        baseProduct.setSeoKeywords(standardProduct.getSeoKeywords());

        return baseProduct;
    }

    /**
     * 构建变体列表
     */
    private List<ProductDetailRespVO.ProductVariantVO> buildVariants(
            StandardProduct standardProduct, String spuCode, Long currentUserId) {
        
        // 1. 判断是否为单一变体
        if (standardProduct.getHasOnlyDefaultVariant() != null && standardProduct.getHasOnlyDefaultVariant()) {
            // 单变体：仅返回当前变体
            log.info("商品为单一变体: spuCode={}", spuCode);
            List<ProductVariants> singleVariantList = productVariantsMapper.findBySpuCode(spuCode);
            if (!singleVariantList.isEmpty()) {
                ProductVariants singleVariant = singleVariantList.get(0);
                ProductDetailRespVO.ProductVariantVO variantVO = buildVariant(singleVariant, currentUserId);
                return List.of(variantVO);
            }
            return List.of();
        }

        // 2. 多变体：查询所有相同spuCode的变体
        List<ProductVariants> variantsList = productVariantsMapper.findBySpuCode(spuCode);
        
        if (variantsList.isEmpty()) {
            log.warn("未找到SPU相关的变体数据: spuCode={}", spuCode);
            return List.of();
        }

        // 3. 构建变体列表（移除收藏状态的批量查询，因为现在每个变体单独处理）
        List<ProductDetailRespVO.ProductVariantVO> variants = new ArrayList<>();
        
        for (ProductVariants variant : variantsList) {
            ProductDetailRespVO.ProductVariantVO variantVO = buildVariant(variant, currentUserId);
            variants.add(variantVO);
        }

        // 5. 按价格排序，限制100个
        return variants.stream()
            .sorted(Comparator.comparing(v -> v.getPrice() != null ? v.getPrice() : BigDecimal.ZERO))
            .limit(100)
            .collect(Collectors.toList());
    }

    /**
     * 构建单个变体 - 包含完整变体信息
     */
    private ProductDetailRespVO.ProductVariantVO buildVariant(
            ProductVariants variant, Long currentUserId) {

        ProductDetailRespVO.ProductVariantVO variantVO = new ProductDetailRespVO.ProductVariantVO();
        
        // 基础信息
        variantVO.setSkuCode(variant.getSkuCode());
        variantVO.setTitle(variant.getTitle());
        variantVO.setOptionJson(variant.getOptionJson());
        variantVO.setSort(variant.getSort());
        
        // 价格信息
        variantVO.setPrice(variant.getPrice());
        variantVO.setProcurePrice(variant.getProcurePrice());
        variantVO.setCostPrice(variant.getCostPrice());
        variantVO.setDiscountPercentage(calculateDiscountPercentage(
            variant.getProcurePrice(), variant.getPrice()));
        
        // 库存信息
        variantVO.setInventoryQuantity(variant.getInventoryQuantity());
        
        // 规格信息
        variantVO.setWeight(variant.getWeight());
        variantVO.setHeight(variant.getHeight());
        variantVO.setLength(variant.getLength());
        variantVO.setWidth(variant.getWidth());
        
        // 其他信息
        variantVO.setBarcode(variant.getBarcode());
        variantVO.setNote(variant.getNote());
        variantVO.setRemarks(variant.getRemarks());
        
        // 个性化信息（每个变体独立）
        variantVO.setIsFavorited(checkFavoriteStatus(currentUserId, variant.getSkuCode()));
        variantVO.setPriceHistory(priceHistoryService.getPriceHistory(variant.getSkuCode()));
        
        // 暂时不设置渠道报价信息，因为需要重新设计这部分逻辑
        // variantVO.setOffer(null);
        
        return variantVO;
    }

    /**
     * 计算折扣百分比
     */
    private BigDecimal calculateDiscountPercentage(BigDecimal procurePrice, BigDecimal price) {
        if (procurePrice == null || price == null || 
            procurePrice.compareTo(BigDecimal.ZERO) <= 0 || 
            price.compareTo(procurePrice) >= 0) {
            return BigDecimal.ZERO;
        }
        
        BigDecimal discount = procurePrice.subtract(price);
        return discount.divide(procurePrice, 4, RoundingMode.HALF_UP)
                      .multiply(BigDecimal.valueOf(100))
                      .setScale(2, RoundingMode.HALF_UP);
    }

    /**
     * 检查收藏状态
     */
    private Boolean checkFavoriteStatus(Long userId, String skuCode) {
        if (userId == null) {
            return false;
        }
        return wishlistItemService.isFavorited(userId, skuCode);
    }


}
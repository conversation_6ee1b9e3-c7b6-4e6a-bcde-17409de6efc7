package ai.pricefox.mallfox.service.product.impl;

import ai.pricefox.mallfox.domain.product.ProductPriceHistory;
import ai.pricefox.mallfox.mapper.product.ProductPriceHistoryMapper;
import ai.pricefox.mallfox.service.product.ProductPriceHistoryService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 价格历史服务实现
 * 
 * <AUTHOR>
 * @since 2025-07-12
 */
@Service
@Slf4j
public class ProductPriceHistoryServiceImpl extends ServiceImpl<ProductPriceHistoryMapper, ProductPriceHistory> 
        implements ProductPriceHistoryService {
    
    @Override
    public List<ProductPriceHistory> getBySkuId(String skuId) {
        try {
            return baseMapper.selectBySkuId(skuId);
        } catch (Exception e) {
            log.error("查询SKU价格历史失败: skuId={}, error={}", skuId, e.getMessage(), e);
            throw new RuntimeException("查询SKU价格历史失败", e);
        }
    }
    
    @Override
    public List<ProductPriceHistory> getBySpuId(String spuId) {
        try {
            return baseMapper.selectBySpuId(spuId);
        } catch (Exception e) {
            log.error("查询SPU价格历史失败: spuId={}, error={}", spuId, e.getMessage(), e);
            throw new RuntimeException("查询SPU价格历史失败", e);
        }
    }

    @Override
    public Map<String, Integer> getMapBySpuIds(List<String> spuIds) {
        try {
            return baseMapper.selectCountBySpuIds(spuIds);
        } catch (Exception e) {
            log.error("查询SPU价格历史失败: spuIds={}, error={}", spuIds, e.getMessage(), e);
            throw new RuntimeException("查询SPU价格历史失败", e);
        }
    }

    @Override
    public Map<String, Integer> getMapBySkuIds(List<String> skuIds) {
        try {
            return baseMapper.selectCountBySkuIds(skuIds);
        } catch (Exception e) {
            log.error("查询SKU价格历史失败: skuIds={}, error={}", skuIds, e.getMessage(), e);
            throw new RuntimeException("查询SKU价格历史失败", e);
        }
    }

    @Override
    public List<ProductPriceHistory> getByTimeRange(LocalDateTime startTime, LocalDateTime endTime) {
        try {
            return baseMapper.selectByTimeRange(startTime, endTime);
        } catch (Exception e) {
            log.error("查询时间范围价格历史失败: startTime={}, endTime={}, error={}", 
                startTime, endTime, e.getMessage(), e);
            throw new RuntimeException("查询时间范围价格历史失败", e);
        }
    }
    
    @Override
    public ProductPriceHistory getLatestByOfferId(Long offerId) {
        try {
            return baseMapper.selectLatestByOfferId(offerId);
        } catch (Exception e) {
            log.error("查询最新价格历史失败: offerId={}, error={}", offerId, e.getMessage(), e);
            throw new RuntimeException("查询最新价格历史失败", e);
        }
    }
    
    @Override
    public List<ProductPriceHistory> getByPlatformAndChangeType(String sourcePlatform, Integer changeType, Integer limit) {
        try {
            return baseMapper.selectByPlatformAndChangeType(sourcePlatform, changeType, limit);
        } catch (Exception e) {
            log.error("查询平台变化类型价格历史失败: platform={}, changeType={}, error={}", 
                sourcePlatform, changeType, e.getMessage(), e);
            throw new RuntimeException("查询平台变化类型价格历史失败", e);
        }
    }
    
    @Override
    public Integer countPriceChanges(String skuId) {
        try {
            return baseMapper.countPriceChanges(skuId);
        } catch (Exception e) {
            log.error("统计价格变化次数失败: skuId={}, error={}", skuId, e.getMessage(), e);
            throw new RuntimeException("统计价格变化次数失败", e);
        }
    }
    
    @Override
    public List<ProductPriceHistory> getBySkuIdAndDays(String skuId, Integer days) {
        try {
            return baseMapper.selectBySkuIdAndDays(skuId, days);
        } catch (Exception e) {
            log.error("查询指定天数价格历史失败: skuId={}, days={}, error={}", 
                skuId, days, e.getMessage(), e);
            throw new RuntimeException("查询指定天数价格历史失败", e);
        }
    }
    
    @Override
    public List<ProductPriceHistory> getTopPriceChanges(Integer limit) {
        try {
            return baseMapper.selectTopPriceChanges(limit);
        } catch (Exception e) {
            log.error("查询最大价格变化记录失败: limit={}, error={}", limit, e.getMessage(), e);
            throw new RuntimeException("查询最大价格变化记录失败", e);
        }
    }
    
    @Override
    public boolean saveBatch(List<ProductPriceHistory> historyList) {
        if (CollectionUtils.isEmpty(historyList)) {
            log.warn("价格历史列表为空，跳过批量保存");
            return true;
        }
        
        try {
            boolean result = super.saveBatch(historyList);
            if (result) {
                log.info("批量保存价格历史成功: size={}", historyList.size());
            } else {
                log.error("批量保存价格历史失败: size={}", historyList.size());
            }
            return result;
        } catch (Exception e) {
            log.error("批量保存价格历史异常: size={}, error={}", historyList.size(), e.getMessage(), e);
            return false;
        }
    }
    
    @Override
    public boolean save(ProductPriceHistory entity) {
        try {
            // 价格历史记录只执行插入操作，不进行更新
            // 每次价格变化都会生成新的历史记录
            boolean result = super.save(entity);
            if (result) {
                log.debug("插入价格历史记录成功: offerId={}, changeType={}, priceChange={}", 
                    entity.getOfferId(), entity.getChangeType(), entity.getPriceChangeAmount());
            } else {
                log.error("插入价格历史记录失败: offerId={}", entity.getOfferId());
            }
            return result;
        } catch (Exception e) {
            log.error("插入价格历史记录异常: offerId={}, error={}", entity.getOfferId(), e.getMessage(), e);
            return false;
        }
    }
}
package ai.pricefox.mallfox.service.product.impl;

import ai.pricefox.mallfox.domain.product.Product;
import ai.pricefox.mallfox.mapper.product.ProductMapper;
import ai.pricefox.mallfox.service.product.ProductService;
import ai.pricefox.mallfox.service.product.ProductSkuService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @description 针对表【product(商品SPU表)】的数据库操作Service实现
 * @createDate 2025-05-18 12:14:02
 */
@Service
@AllArgsConstructor
public class ProductServiceImpl extends ServiceImpl<ProductMapper, Product>
        implements ProductService {
    private final ProductSkuService productSkuService;

    //事物注解
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean saveProduct(Product product) {
//        if (this.saveOrUpdate(product)) {
//            product.getProductSkuList().forEach(productSku -> productSku.setProductId(product.getId()));
//            return productSkuService.saveOrUpdateBatch(product.getProductSkuList());
//        }
        return false;
    }
}





package ai.pricefox.mallfox.service.product.impl;

import ai.pricefox.mallfox.mapper.product.ProductSkuMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import ai.pricefox.mallfox.domain.product.ProductSku;
import ai.pricefox.mallfox.service.product.ProductSkuService;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【product_sku(商品SKU表)】的数据库操作Service实现
* @createDate 2025-05-18 12:14:02
*/
@Service
public class ProductSkuServiceImpl extends ServiceImpl<ProductSkuMapper, ProductSku>
    implements ProductSkuService{

}





package ai.pricefox.mallfox.service.product.impl;

import ai.pricefox.mallfox.common.exception.util.BizException;
import ai.pricefox.mallfox.common.exception.enums.ResultCode;
import ai.pricefox.mallfox.common.constant.RedisKeyConstants;
import ai.pricefox.mallfox.common.util.CacheUtil;
import ai.pricefox.mallfox.domain.integration.DataCalibrationTags;
import ai.pricefox.mallfox.domain.product.ProductDataOffers;
import ai.pricefox.mallfox.domain.product.ProductTableRead;
import ai.pricefox.mallfox.enums.ProductPlatformEnum;
import ai.pricefox.mallfox.mapper.product.ProductDataOffersMapper;
import ai.pricefox.mallfox.mapper.product.ProductDataSimplifyMapper;
import ai.pricefox.mallfox.mapper.product.ProductTableReadMapper;
import ai.pricefox.mallfox.mapper.product.ProductViewMapper;
import ai.pricefox.mallfox.model.param.CalibrationTagRequest;
import ai.pricefox.mallfox.model.param.ProductDataSearchRequest;
import ai.pricefox.mallfox.model.param.ProductMarkReadRequest;
import ai.pricefox.mallfox.model.response.ProductDataExportResponse;
import ai.pricefox.mallfox.model.response.ProductDataViewResponse;
import ai.pricefox.mallfox.model.response.SkuMasterViewDTO;
import ai.pricefox.mallfox.model.response.SpuGroupViewResponse;
import ai.pricefox.mallfox.service.integration.impl.CalibrationService;
import ai.pricefox.mallfox.service.product.ProductPriceHistoryService;
import ai.pricefox.mallfox.service.tracking.FieldTrackingService;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static ai.pricefox.mallfox.domain.product.ProductDataOffers.isOffersTableField;
import static ai.pricefox.mallfox.domain.product.ProductDataSimplify.isSimplifyTableField;

/**
 * <AUTHOR>
 * @desc 商品数据展示视图
 * @since 2025/6/24
 */
@Service
@Slf4j
public class ProductViewGroupedService {

    @Autowired
    private ProductViewMapper productViewMapper;

    @Autowired
    private CacheUtil cacheUtil;

    @Autowired
    private CalibrationService calibrationService;

    @Autowired
    private ProductDataSimplifyMapper productDataSimplifyMapper;

    @Autowired
    private ProductDataOffersMapper productDataOffersMapper;

    @Autowired
    private FieldTrackingService fieldTrackingService;

    @Autowired
    private ProductPriceHistoryService productPriceHistoryService;

    @Autowired
    private ProductTableReadMapper productTableReadMapper;


    public IPage<SpuGroupViewResponse> getSpuGroupedView(ProductDataSearchRequest request) {
        // 一级：分页查询SPU列表
        Page<ProductDataSearchRequest> page = new Page<>(request.getPageNo(), request.getPageSize());
        IPage<SpuGroupViewResponse> spuPage = productViewMapper.selectSpuList(page, request);
        List<SpuGroupViewResponse> spuList = spuPage.getRecords();

        if (CollectionUtils.isEmpty(spuList)) {
            return new Page<>(request.getPageNo(), request.getPageSize());
        }

        // 为SPU数据附加校准标记
        attachCalibrationTagsForSpu(spuList);

        // 为SPU数据附加字段来源信息
        attachFieldSourcesForSpu(spuList);

        // 为SPU数据添加已读状态
        attachReadStatus(spuList, "simplify");

        //获取spuid下的sku数量
        List<String> spuIds = spuList.stream().map(SpuGroupViewResponse::getSpuId).collect(Collectors.toList());
        List<ProductDataOffers> offers = productDataOffersMapper.selectSkuCountBySpuIds(spuIds);
        Map<String, List<ProductDataOffers>> map = offers.stream().collect(Collectors.groupingBy(ProductDataOffers::getSpuId));

        //获取价格更新频次
        Map<String, Integer> priceUpdateFrequencyMap = productPriceHistoryService.getMapBySpuIds(spuIds);

        // 为每个 SPU 设置一级树形结构的 id 和 pid
        for (SpuGroupViewResponse spuGroup : spuList) {
            // id + 随机4位数字
            Random random = new Random();
            int randomNum = random.nextInt(10000);
            spuGroup.setId(spuGroup.getId() * 10000 + randomNum);
            spuGroup.setPid(0L);
            //sku数量
            List<ProductDataOffers> spuOffers = map.get(spuGroup.getSpuId());
            spuGroup.setSkuCount(spuOffers != null ? spuOffers.size() : 0);
            //价格更新频次
            if(CollUtil.isNotEmpty(priceUpdateFrequencyMap)) {
                spuGroup.setPriceUpdateFrequency(priceUpdateFrequencyMap.get(spuGroup.getSpuId()));
            }
        }
        return spuPage;
    }

    /**
     * 二级：根据SPU ID获取SKU列表
     *
     * @param request 包含spuId和分页参数的请求
     * @return SKU列表分页结果
     */
    public IPage<SpuGroupViewResponse> getSkuListBySpuId(ProductDataSearchRequest request) {
        // 使用分页查询该SPU下的SKU
        ProductDataOffers dataOffers = productDataOffersMapper.selectById(request.getId());
        if(dataOffers == null){
            return  new Page<>(request.getPageNo(), request.getPageSize());
        }
        Long id = request.getId();
        Page<ProductDataSearchRequest> page = new Page<>(request.getPageNo(), request.getPageSize());
        IPage<SkuMasterViewDTO> skuPage = productViewMapper.selectSkuListBySpuIdWithPage(page, dataOffers.getSpuId());

        List<SkuMasterViewDTO> skuList = skuPage.getRecords();
        if (CollectionUtils.isEmpty(skuList)) {
            return new Page<>(request.getPageNo(), request.getPageSize());
        }

        //获取skuid下的sku数量
        List<String> skuIds = skuList.stream().map(SkuMasterViewDTO::getSkuId).toList();
        List<ProductDataViewResponse> offers = productViewMapper.selectAllPlatformOffersBySkuIds(skuIds);
        Map<String, List<ProductDataViewResponse>> map = offers.stream().collect(Collectors.groupingBy(ProductDataViewResponse::getSkuId));

        //获取价格更新频次
        Map<String, Integer> priceUpdateFrequencyMap = productPriceHistoryService.getMapBySkuIds(skuIds);

        // 获取并附加校准标记数据
        attachCalibrationTags(skuList);

        // 为SKU数据附加字段来源信息
        attachFieldSourcesForSku(skuList);

        // 为SKU数据添加已读状态
        attachReadStatus(skuList, "offers");


        // 转换为SpuGroupViewResponse，设置二级结构
        List<SpuGroupViewResponse> skuResponses = skuList.stream()
                .map(sku -> {
                    SpuGroupViewResponse response = new SpuGroupViewResponse();
                    BeanUtils.copyProperties(sku, response);
                    // 设置二级结构的ID和PID
                    // id + 随机4位数字
                    Random random = new Random();
                    int randomNum = random.nextInt(10000);
                    response.setId(response.getId() * 10000 + randomNum);
                    response.setPid(id); // pid = spuId
                    response.setHasChildren(true);
                    //sku数量
                    List<ProductDataViewResponse> spuOffers = map.get(response.getSkuId());
                    response.setSkuCount(spuOffers != null ? spuOffers.size() : 0);
                    //价格更新频次
                    if(CollUtil.isNotEmpty(priceUpdateFrequencyMap)) {
                        response.setPriceUpdateFrequency(priceUpdateFrequencyMap.get(response.getSkuId()));
                    }
                    return response;
                })
                .collect(Collectors.toList());

        // 创建分页结果
        IPage<SpuGroupViewResponse> resultPage = new Page<>(
                skuPage.getCurrent(),
                skuPage.getSize(),
                skuPage.getTotal()
        );
        resultPage.setRecords(skuResponses);

        return resultPage;
    }

    /**
     * 为一批SKU数据附加校准标记
     *
     * @param skuMasterViewList 从数据库查出的主数据列表
     */
    private void attachCalibrationTags(List<SkuMasterViewDTO> skuMasterViewList) {
        if (CollectionUtils.isEmpty(skuMasterViewList)) {
            return;
        }

        // 提取所有需要查询标记的 offer ID
        List<Long> offerIds = skuMasterViewList.stream()
                .map(SkuMasterViewDTO::getOfferId)
                .distinct()
                .toList();

        // 批量从Redis获取缓存
        List<String> redisKeys = offerIds.stream()
                .map(id -> RedisKeyConstants.PRODUCT_CALIBRATION_OFFERS+ id)
                .collect(Collectors.toList());

        // 缓存批量获取标记的字段
        List<Map<String, Integer>> cachedTagsList = cacheUtil.multiGetCacheObject(redisKeys);

        Map<Long, Map<String, Integer>> cachedTagsMap = new HashMap<>();

        // 获取缓存命中的ID
        List<Long> cacheMissOfferIds = getMissOfferIds(offerIds, cachedTagsList, cachedTagsMap);


        //  对缓存未命中的ID，批量查询数据库
        if (!cacheMissOfferIds.isEmpty()) {

            // 查询到db中标记的tag
            List<DataCalibrationTags> dbTags = calibrationService.getTagsByOfferId("offers", cacheMissOfferIds);

            // 按 offerId 对数据库查询结果进行分组
            Map<Long, List<DataCalibrationTags>> dbTagsGrouped = dbTags.stream()
                    .collect(Collectors.groupingBy(DataCalibrationTags::getTargetId));

            // 处理数据库结果，并写回缓存
            for (Long offerId : cacheMissOfferIds) {
                List<DataCalibrationTags> tagsForOffer = dbTagsGrouped.get(offerId);
                Map<String, Integer> tagsToCache = new HashMap<>();
                if (!CollectionUtils.isEmpty(tagsForOffer)) {
                    tagsToCache = tagsForOffer.stream()
                            .collect(Collectors.toMap(DataCalibrationTags::getFieldName, DataCalibrationTags::getTagStatus));
                }

                // 存入本次查询结果的map中
                cachedTagsMap.put(offerId, tagsToCache);
                // 写回Redis缓存，即使是空结果也缓存，防止缓存穿透
                String redisKey = RedisKeyConstants.PRODUCT_CALIBRATION_OFFERS + offerId;
                cacheUtil.setCacheObject(redisKey, tagsToCache, RedisKeyConstants.PRODUCT_CALIBRATION_TIMEOUT, TimeUnit.SECONDS);
            }
        }

        // 将获取到的所有标记
        for (SkuMasterViewDTO skuDto : skuMasterViewList) {
            Map<String, Integer> tags = cachedTagsMap.get(skuDto.getOfferId());
            skuDto.setCalibrationTags(tags != null ? tags : Collections.emptyMap());
        }
    }

    private static List<Long> getMissOfferIds(List<Long> offerIds, List<Map<String, Integer>> cachedTagsList, Map<Long, Map<String, Integer>> cachedTagsMap) {
        List<Long> cacheMissOfferIds = new ArrayList<>();

        // 遍历批量获取的结果
        for (int i = 0; i < offerIds.size(); i++) {
            Long offerId = offerIds.get(i);
            Map<String, Integer> tags = (cachedTagsList != null && cachedTagsList.size() > i) ? cachedTagsList.get(i) : null;
            if (tags != null) {
                cachedTagsMap.put(offerId, tags);
            } else {
                cacheMissOfferIds.add(offerId);
            }
        }
        return cacheMissOfferIds;
    }

    /**
     * 根据skuId获取全平台报价
     */
    public List<ProductDataViewResponse> getAllPlatformOffersBySkuId(Long id) {
        ProductDataOffers dataOffers = productDataOffersMapper.selectById(id);
        if(dataOffers == null){
            return Collections.emptyList();
        }

        List<ProductDataViewResponse> offers = productViewMapper.selectAllPlatformOffers(dataOffers.getSkuId());
        if (CollectionUtils.isEmpty(offers)) {
            return Collections.emptyList();
        }
        List<SkuMasterViewDTO> tempSkus = offers.stream()
                .map(dto -> {
                    SkuMasterViewDTO sku = new SkuMasterViewDTO();
                    BeanUtils.copyProperties(dto, sku);
                    return sku;
                })
                .collect(Collectors.toList());

        //获取价格更新频次
        List<String> skuIds = offers.stream().map(ProductDataViewResponse::getSkuId).toList();
        Map<String, Integer> priceUpdateFrequencyMap = productPriceHistoryService.getMapBySkuIds(skuIds);

        // 复用现有逻辑附加校准标签
        attachCalibrationTags(tempSkus);

        // 为平台商品详情附加字段来源信息
        attachFieldSourcesForOffers(offers);

        // 为SKU数据添加已读状态
        attachReadStatus(offers, "plat-offers");

        for (ProductDataViewResponse response : offers) {
            Optional<SkuMasterViewDTO> matched = tempSkus.stream()
                    .filter(sku -> Objects.equals(sku.getOfferId(), response.getOfferId()))
                    .findFirst();
            String str = response.getDataChannel() == 1 ? "数据采集" : "api";
            response.setDataSource(Objects.requireNonNull(ProductPlatformEnum.getByCode(response.getSourcePlatform())).getName() + " " + str);
            // id + 随机4位数字
            Random random = new Random();
            int randomNum = random.nextInt(10000);
            response.setId(response.getId() * 10000 + randomNum);
            response.setPid(response.getPid());
            matched.ifPresent(sku -> response.setCalibrationTags(sku.getCalibrationTags()));

            response.setSkuCount(0);
            //价格更新频次
            if(CollUtil.isNotEmpty(priceUpdateFrequencyMap)) {
                response.setPriceUpdateFrequency(priceUpdateFrequencyMap.get(response.getSkuId()));
            }
        }

        return offers;
    }

    /**
     * 为SPU数据附加校准标记
     *
     * @param spuList SPU列表
     */
    private void attachCalibrationTagsForSpu(List<SpuGroupViewResponse> spuList) {
        if (CollectionUtils.isEmpty(spuList)) {
            return;
        }

        // 提取所有需要查询标记的 SPU ID (这里使用offerId作为simplify表的主键)
        List<Long> spuOfferIds = spuList.stream()
                .map(SpuGroupViewResponse::getOfferId)
                .distinct()
                .collect(Collectors.toList());

        // 批量从Redis获取缓存
        List<String> redisKeys = spuOfferIds.stream()
                .map(id -> RedisKeyConstants.PRODUCT_CALIBRATION_SIMPLIFY + id)
                .collect(Collectors.toList());

        // 缓存批量获取标记的字段
        List<Map<String, Integer>> cachedTagsList = cacheUtil.multiGetCacheObject(redisKeys);

        Map<Long, Map<String, Integer>> cachedTagsMap = new HashMap<>();

        // 获取缓存未命中的ID
        List<Long> cacheMissOfferIds = getMissOfferIds(spuOfferIds, cachedTagsList, cachedTagsMap);

        // 对缓存未命中的ID，批量查询数据库
        if (!cacheMissOfferIds.isEmpty()) {
            // 查询simplify表中标记的tag
            List<DataCalibrationTags> dbTags = calibrationService.getTagsByOfferId("simplify", cacheMissOfferIds);

            // 按 offerId 对数据库查询结果进行分组
            Map<Long, List<DataCalibrationTags>> dbTagsGrouped = dbTags.stream()
                    .collect(Collectors.groupingBy(DataCalibrationTags::getTargetId));

            // 处理数据库结果，并写回缓存
            for (Long offerId : cacheMissOfferIds) {
                List<DataCalibrationTags> tagsForOffer = dbTagsGrouped.get(offerId);
                Map<String, Integer> tagsToCache = new HashMap<>();
                if (!CollectionUtils.isEmpty(tagsForOffer)) {
                    tagsToCache = tagsForOffer.stream()
                            .collect(Collectors.toMap(DataCalibrationTags::getFieldName, DataCalibrationTags::getTagStatus));
                }

                // 存入本次查询结果的map中
                cachedTagsMap.put(offerId, tagsToCache);
                // 写回Redis缓存，即使是空结果也缓存，防止缓存穿透
                String redisKey = RedisKeyConstants.PRODUCT_CALIBRATION_SIMPLIFY + offerId;
                cacheUtil.setCacheObject(redisKey, tagsToCache, RedisKeyConstants.PRODUCT_CALIBRATION_TIMEOUT, TimeUnit.SECONDS);
            }
        }

        // 将查询到的标记数据附加到每个SPU对象上
        for (SpuGroupViewResponse spu : spuList) {
            Map<String, Integer> tags = cachedTagsMap.get(spu.getOfferId());
            spu.setCalibrationTags(tags != null ? tags : new HashMap<>());
        }
    }

    /**
     * 为SPU数据附加字段来源信息
     *
     * 根据selectSpuList的SQL分析：
     * - 以s.开头的字段来自product_data_simplify表，使用spu.getId()查询
     * - 以o.开头的字段来自product_data_offers表，使用spu.getOfferId()查询
     *
     * @param spuList SPU列表
     */
    private void attachFieldSourcesForSpu(List<SpuGroupViewResponse> spuList) {
        if (CollectionUtils.isEmpty(spuList)) {
            return;
        }

        log.debug("开始为{}个SPU附加字段来源信息", spuList.size());

        // 为每个SPU查询字段来源信息
        for (SpuGroupViewResponse spu : spuList) {
            try {
                Map<String, Integer> combinedFieldSources = new HashMap<>();

                // 查询product_data_simplify表的字段来源（使用spu.getId()）
                if (spu.getSimplifyId() != null) {
                    Map<String, Integer> simplifyFieldSources = fieldTrackingService.getFieldSources("product_data_simplify", spu.getSimplifyId());
                    if (simplifyFieldSources != null) {
                        // 只添加来自simplify表的字段
                        for (Map.Entry<String, Integer> entry : simplifyFieldSources.entrySet()) {
                            String fieldName = entry.getKey();
                            if (isSimplifyTableField(fieldName)) {
                                combinedFieldSources.put(fieldName, entry.getValue());
                            }
                        }
                    }
                }

                // 查询product_data_offers表的字段来源（使用spu.getOfferId()）
                if (spu.getOfferId() != null) {
                    Map<String, Integer> offersFieldSources = fieldTrackingService.getFieldSources("product_data_offers", spu.getOfferId());
                    if (offersFieldSources != null) {
                        // 只添加来自offers表的字段
                        for (Map.Entry<String, Integer> entry : offersFieldSources.entrySet()) {
                            String fieldName = entry.getKey();
                            if (isOffersTableField(fieldName)) {
                                combinedFieldSources.put(fieldName, entry.getValue());
                            }
                        }
                    }
                }

                spu.setFieldSources(combinedFieldSources);

                log.debug("为SPU {} 设置了 {} 个字段来源信息（simplify表ID: {}, offers表ID: {})",
                    spu.getSpuId(), combinedFieldSources.size(), spu.getId(), spu.getOfferId());
            } catch (Exception e) {
                log.warn("查询SPU {} 字段来源信息失败: {}", spu.getSpuId(), e.getMessage());
                spu.setFieldSources(new HashMap<>());
            }
        }

        log.debug("完成SPU字段来源信息附加");
    }

    /**
     * 为SKU数据附加字段来源信息
     *
     * 根据selectSkuListBySpuIdWithPage的SQL分析：
     * - 以s.开头的字段来自product_data_simplify表，使用sku.getSimplifyId()查询
     * - 以o.开头的字段来自product_data_offers表，使用sku.getOfferId()查询
     *
     * @param skuList SKU列表
     */
    private void attachFieldSourcesForSku(List<SkuMasterViewDTO> skuList) {
        if (CollectionUtils.isEmpty(skuList)) {
            return;
        }

        log.debug("开始为{}个SKU附加字段来源信息", skuList.size());

        // 为每个SKU查询字段来源信息
        for (SkuMasterViewDTO sku : skuList) {
            try {
                Map<String, Integer> combinedFieldSources = new HashMap<>();

                // 查询product_data_simplify表的字段来源（使用sku.getSimplifyId()）
                if (sku.getSimplifyId() != null) {
                    Map<String, Integer> simplifyFieldSources = fieldTrackingService.getFieldSources("product_data_simplify", sku.getSimplifyId());
                    if (simplifyFieldSources != null) {
                        // 只添加来自simplify表的字段
                        for (Map.Entry<String, Integer> entry : simplifyFieldSources.entrySet()) {
                            String fieldName = entry.getKey();
                            if (isSimplifyTableField(fieldName)) {
                                combinedFieldSources.put(fieldName, entry.getValue());
                            }
                        }
                    }
                }

                // 查询product_data_offers表的字段来源（使用sku.getOfferId()）
                if (sku.getOfferId() != null) {
                    Map<String, Integer> offersFieldSources = fieldTrackingService.getFieldSources("product_data_offers", sku.getOfferId());
                    if (offersFieldSources != null) {
                        // 只添加来自offers表的字段
                        for (Map.Entry<String, Integer> entry : offersFieldSources.entrySet()) {
                            String fieldName = entry.getKey();
                            if (isOffersTableField(fieldName)) {
                                combinedFieldSources.put(fieldName, entry.getValue());
                            }
                        }
                    }
                }

                sku.setFieldSources(combinedFieldSources);

                log.debug("为SKU {} 设置了 {} 个字段来源信息（simplify表ID: {}, offers表ID: {})",
                    sku.getSkuId(), combinedFieldSources.size(), sku.getSimplifyId(), sku.getOfferId());
            } catch (Exception e) {
                log.warn("查询SKU {} 字段来源信息失败: {}", sku.getSkuId(), e.getMessage());
                sku.setFieldSources(new HashMap<>());
            }
        }

        log.debug("完成SKU字段来源信息附加");
    }

    /**
     * 为平台商品详情附加字段来源信息
     *
     * 根据selectAllPlatformOffers的SQL分析：
     * - 以s.开头的字段来自product_data_simplify表，使用offer.getSimplifyId()查询
     * - 以o.开头的字段来自product_data_offers表，使用offer.getOfferId()查询
     *
     * @param offers 平台商品详情列表
     */
    private void attachFieldSourcesForOffers(List<ProductDataViewResponse> offers) {
        if (CollectionUtils.isEmpty(offers)) {
            return;
        }

        log.debug("开始为{}个平台商品详情附加字段来源信息", offers.size());

        // 为每个平台商品详情查询字段来源信息
        for (ProductDataViewResponse offer : offers) {
            try {
                Map<String, Integer> combinedFieldSources = new HashMap<>();

                // 查询product_data_simplify表的字段来源（使用offer.getSimplifyId()）
                if (offer.getSimplifyId() != null) {
                    Map<String, Integer> simplifyFieldSources = fieldTrackingService.getFieldSources("product_data_simplify", offer.getSimplifyId());
                    if (simplifyFieldSources != null) {
                        // 只添加来自simplify表的字段
                        for (Map.Entry<String, Integer> entry : simplifyFieldSources.entrySet()) {
                            String fieldName = entry.getKey();
                            if (isSimplifyTableField(fieldName)) {
                                combinedFieldSources.put(fieldName, entry.getValue());
                            }
                        }
                    }
                }

                // 查询product_data_offers表的字段来源（使用offer.getOfferId()）
                if (offer.getOfferId() != null) {
                    Map<String, Integer> offersFieldSources = fieldTrackingService.getFieldSources("product_data_offers", offer.getOfferId());
                    if (offersFieldSources != null) {
                        // 只添加来自offers表的字段
                        for (Map.Entry<String, Integer> entry : offersFieldSources.entrySet()) {
                            String fieldName = entry.getKey();
                            if (isOffersTableField(fieldName)) {
                                combinedFieldSources.put(fieldName, entry.getValue());
                            }
                        }
                    }
                }

                offer.setFieldSources(combinedFieldSources);

                log.debug("为平台商品详情 {} 设置了 {} 个字段来源信息（simplify表ID: {}, offers表ID: {})",
                    offer.getSkuId(), combinedFieldSources.size(), offer.getSimplifyId(), offer.getOfferId());
            } catch (Exception e) {
                log.warn("查询平台商品详情 {} 字段来源信息失败: {}", offer.getSkuId(), e.getMessage());
                offer.setFieldSources(new HashMap<>());
            }
        }

        log.debug("完成平台商品详情字段来源信息附加");
    }

    public void updateCalibrationTags(List<CalibrationTagRequest> dtoList) {
        if (CollectionUtils.isEmpty(dtoList)) {
            throw new BizException(ResultCode.PARAM_INVALIDATE);
        }
        log.info("开始标记更新 Calibration Tags: {}", JSON.toJSONString(dtoList));
        calibrationService.batchSaveOrUpdateTags(dtoList);

        // 根据 targetTable 清除对应的缓存
        dtoList.forEach(dto -> {
            String cacheKey = getCacheKeyByTargetTable(dto.getTargetTable(), dto.getTargetId());
            cacheUtil.deleteObject(cacheKey);
            log.debug("清除缓存: {}", cacheKey);
        });
    }

    /**
     * 根据目标表名获取对应的缓存Key
     */
    private String getCacheKeyByTargetTable(String targetTable, Long targetId) {
        if ("offers".equals(targetTable) || "product_data_offers".equals(targetTable)) {
            return RedisKeyConstants.PRODUCT_CALIBRATION_OFFERS + targetId;
        } else if ("simplify".equals(targetTable) || "product_data_simplify".equals(targetTable)) {
            return RedisKeyConstants.PRODUCT_CALIBRATION_SIMPLIFY + targetId;
        } else {
            log.warn("未知的目标表名: {}, 使用默认的offer缓存Key", targetTable);
            return RedisKeyConstants.PRODUCT_CALIBRATION_OFFERS + targetId;
        }
    }


    /**
     * 批量查询已读状态
     */
    private void attachReadStatus(List<?> dataList, String targetTable) {
        if (CollectionUtils.isEmpty(dataList)) {
            return;
        }

        // 提取ID列表
        List<Long> targetIds = dataList.stream()
                .map(item -> {
                    if (item instanceof SkuMasterViewDTO) {
                        return ((SkuMasterViewDTO) item).getOfferId();
                    } else if (item instanceof SpuGroupViewResponse) {
                        return ((SpuGroupViewResponse) item).getOfferId();
                    } else if (item instanceof ProductDataViewResponse) {
                        return ((ProductDataViewResponse) item).getOfferId();
                    }
                    return null;
                })
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        if (targetIds.isEmpty()) {
            return;
        }

        // 批量查询已读状态
        List<ProductTableRead> readRecords = productTableReadMapper.selectByTargetTableAndIds(targetTable, targetIds);
        Set<Long> readIds = readRecords.stream()
                .map(ProductTableRead::getTargetId)
                .collect(Collectors.toSet());

        // 设置已读状态
        dataList.forEach(item -> {
            Long offerId = null;
            if (item instanceof SkuMasterViewDTO) {
                offerId = ((SkuMasterViewDTO) item).getOfferId();
                ((SkuMasterViewDTO) item).setIsRead(readIds.contains(offerId));
            } else if (item instanceof SpuGroupViewResponse) {
                offerId = ((SpuGroupViewResponse) item).getOfferId();
                ((SpuGroupViewResponse) item).setIsRead(readIds.contains(offerId));
            } else if (item instanceof ProductDataViewResponse) {
                offerId = ((ProductDataViewResponse) item).getOfferId();
                ((ProductDataViewResponse) item).setIsRead(readIds.contains(offerId));
            }
        });
    }

    /**
     * 导出商品数据 - 聚合SPU、SKU、平台SKU三级数据
     *
     * @param request 查询参数
     * @return 导出数据列表
     */
    public List<ProductDataExportResponse> exportProductData(ProductDataSearchRequest request) {
        List<ProductDataExportResponse> exportList = new ArrayList<>();

        // 查询SPU列表（不分页，查询所有）
        List<SpuGroupViewResponse> spuList = getAllSpuList(request);
        if (CollectionUtils.isEmpty(spuList)) {
            return exportList;
        }

        // 为SPU数据附加校准标记
//        attachCalibrationTagsForSpu(spuList);

        // 处理每个SPU
        for (SpuGroupViewResponse spu : spuList) {
            // 添加SPU记录 (sign=1)
            ProductDataExportResponse spuRecord = convertSpuToExportResponse(spu);
            // SPU标记
            spuRecord.setSign(1);
            spuRecord.setId(spu.getSpuId());
            spuRecord.setPid("0");
            exportList.add(spuRecord);

            // 查询该SPU下的所有SKU
            List<SkuMasterViewDTO> skuList = productViewMapper.selectSkuListBySpuIds(List.of(spu.getSpuId()));
            if (CollectionUtils.isEmpty(skuList)) {
                continue;
            }

            // 为SKU数据附加校准标记
//            attachCalibrationTags(skuList);

            // 按SKU ID分组
            Map<String, List<SkuMasterViewDTO>> skuGroupMap = skuList.stream()
                    .collect(Collectors.groupingBy(SkuMasterViewDTO::getSkuId));

            // 处理每个SKU组
            for (Map.Entry<String, List<SkuMasterViewDTO>> skuEntry : skuGroupMap.entrySet()) {
                String skuId = skuEntry.getKey();
                List<SkuMasterViewDTO> skuOffers = skuEntry.getValue();

                if (CollectionUtils.isEmpty(skuOffers)) {
                    continue;
                }

                // 取第一个作为SKU代表数据
                SkuMasterViewDTO skuRepresentative = skuOffers.get(0);

                // 添加SKU记录 (sign=2)
                ProductDataExportResponse skuRecord = convertSkuToExportResponse(skuRepresentative);
                // SKU标记
                skuRecord.setSign(2);
                skuRecord.setId(skuId);
                skuRecord.setPid(spu.getSpuId());
                exportList.add(skuRecord);

                // 添加该SKU下的所有平台SKU记录 (sign=3)
                for (SkuMasterViewDTO platformSku : skuOffers) {
                    ProductDataExportResponse platformRecord = convertSkuToExportResponse(platformSku);
                    platformRecord.setSign(3);
                    platformRecord.setId(platformSku.getOfferId().toString());
                    platformRecord.setPid(skuId);
                    exportList.add(platformRecord);
                }
            }
        }

        log.info("导出商品数据完成，总记录数: {}", exportList.size());
        return exportList;
    }

    /**
     * 获取所有SPU列表（不分页）
     */
    private List<SpuGroupViewResponse> getAllSpuList(ProductDataSearchRequest request) {
        // 设置一个很大的分页大小来获取所有数据
        ProductDataSearchRequest allRequest = new ProductDataSearchRequest();
        BeanUtils.copyProperties(request, allRequest);
        allRequest.setPageNo(request.getPageNo());
        allRequest.setPageSize(request.getPageSize());

        Page<ProductDataSearchRequest> page = new Page<>(allRequest.getPageNo(), allRequest.getPageSize());
        IPage<SpuGroupViewResponse> spuPage = productViewMapper.selectSpuList(page, allRequest);
        return spuPage.getRecords();
    }

    /**
     * 将SPU数据转换为导出响应对象
     */
    private ProductDataExportResponse convertSpuToExportResponse(SpuGroupViewResponse spu) {
        ProductDataExportResponse export = new ProductDataExportResponse();
        BeanUtils.copyProperties(spu, export);
        export.setCalibrationTags(spu.getCalibrationTags());
        return export;
    }

    /**
     * 将SKU数据转换为导出响应对象
     */
    private ProductDataExportResponse convertSkuToExportResponse(SkuMasterViewDTO sku) {
        ProductDataExportResponse export = new ProductDataExportResponse();
        BeanUtils.copyProperties(sku, export);
        return export;
    }

    /**
     * 标记商品记录已读
     */
    public void markRead(List<ProductMarkReadRequest> requestList) {
        if (CollectionUtils.isEmpty(requestList)) {
            throw new BizException(ResultCode.PARAM_INVALIDATE);
        }
        log.info("开始标记商品记录已读: {}", JSON.toJSONString(requestList));

        List<ProductTableRead> readRecords = requestList.stream()
                .map(request -> {
                    ProductTableRead record = new ProductTableRead();
                    record.setTargetTable(request.getTargetTable());
                    record.setTargetId(request.getTargetId());
                    record.setIsRead(1);
                    record.setCreateTime(LocalDateTime.now());
                    return record;
                })
                .collect(Collectors.toList());

        // 批量插入或更新
        productTableReadMapper.batchInsertOrUpdate(readRecords);

        log.info("商品记录已读标记完成，共处理 {} 条记录", readRecords.size());
    }
}

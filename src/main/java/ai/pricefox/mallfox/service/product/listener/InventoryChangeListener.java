package ai.pricefox.mallfox.service.product.listener;

import ai.pricefox.mallfox.domain.product.ProductDataOffers;
import ai.pricefox.mallfox.domain.product.ProductInventoryHistory;
import ai.pricefox.mallfox.enums.DataChangeTypeEnum;
import ai.pricefox.mallfox.mapper.product.ProductInventoryHistoryMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * 库存变化监听器
 * 
 * <AUTHOR>
 * @since 2025年07月24日
 */
@Component
@Slf4j
public class InventoryChangeListener {
    
    @Autowired
    private ProductInventoryHistoryMapper inventoryHistoryMapper;
    
    /**
     * 记录库存变化历史
     * 
     * @param oldOffer 旧的offer数据
     * @param newOffer 新的offer数据
     * @param changeType 变化类型
     * @param triggerSource 触发来源
     */
    public void recordInventoryChange(ProductDataOffers oldOffer, ProductDataOffers newOffer,
                                      DataChangeTypeEnum changeType, String triggerSource) {
        try {
            // 检查库存是否发生变化
            if (!isInventoryChanged(oldOffer, newOffer, changeType)) {
                log.debug("库存未发生变化，跳过记录: offerId={}", newOffer.getId());
                return;
            }
            
            ProductInventoryHistory history = buildInventoryHistory(oldOffer, newOffer, changeType, triggerSource);
            inventoryHistoryMapper.insert(history);
            
            log.info("库存变化历史记录已保存: offerId={}, changeType={}, oldQuantity={}, newQuantity={}", 
                    history.getOfferId(), changeType.getDesc(), history.getOldQuantity(), history.getNewQuantity());
                    
        } catch (Exception e) {
            log.error("记录库存变化历史失败: changeType={}, triggerSource={}", changeType, triggerSource, e);
        }
    }
    
    /**
     * 检查库存是否发生变化
     */
    private boolean isInventoryChanged(ProductDataOffers oldOffer, ProductDataOffers newOffer,
                                       DataChangeTypeEnum changeType) {
        // 新增或删除操作总是记录
        if (changeType == DataChangeTypeEnum.CREATE ||
            changeType == DataChangeTypeEnum.DELETE) {
            return true;
        }
        
        // 更新操作需要检查库存是否真的发生变化
        if (changeType == DataChangeTypeEnum.UPDATE && oldOffer != null && newOffer != null) {
            String oldInventory = oldOffer.getInventory();
            String newInventory = newOffer.getInventory();
            return !Objects.equals(oldInventory, newInventory);
        }
        
        return false;
    }
    
    /**
     * 构建库存历史记录
     */
    private ProductInventoryHistory buildInventoryHistory(ProductDataOffers oldOffer, ProductDataOffers newOffer,
                                                        DataChangeTypeEnum changeType, String triggerSource) {
        // 删除操作，newOffer为null，使用oldOffer的基本信息
        ProductDataOffers sourceOffer = newOffer != null ? newOffer : oldOffer;
        
        ProductInventoryHistory.ProductInventoryHistoryBuilder builder = ProductInventoryHistory.builder()
            .offerId(sourceOffer.getId())
            .skuId(sourceOffer.getSkuId())
            .spuId(sourceOffer.getSpuId())
            .sourcePlatform(sourceOffer.getSourcePlatform())
            .platformSpuId(sourceOffer.getPlatformSpuId())
            .platformSkuId(sourceOffer.getPlatformSkuId())
            .changeType(changeType.getCode())
            .dataChannel(sourceOffer.getDataChannel())
            .triggerSource(triggerSource)
            .inventoryUpdateTime(sourceOffer.getInventoryUpdateTime())
            .recordTime(LocalDateTime.now());
        
        // 设置库存变化信息
        if (changeType == DataChangeTypeEnum.CREATE) {
            // 新增操作
            builder.newQuantity(newOffer.getInventory());
        } else if (changeType == DataChangeTypeEnum.DELETE) {
            // 删除操作
            builder.oldQuantity(oldOffer.getInventory());
            builder.newQuantity("DELETED");
        } else if (changeType ==DataChangeTypeEnum.UPDATE) {
            // 更新操作
            builder.oldQuantity(oldOffer.getInventory())
                   .newQuantity(newOffer.getInventory());
        }
        
        return builder.build();
    }
}
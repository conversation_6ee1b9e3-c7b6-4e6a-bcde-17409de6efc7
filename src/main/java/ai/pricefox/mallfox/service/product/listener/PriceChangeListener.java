package ai.pricefox.mallfox.service.product.listener;

import ai.pricefox.mallfox.config.PriceHistoryConfig;
import ai.pricefox.mallfox.domain.product.ProductDataOffers;
import ai.pricefox.mallfox.domain.product.ProductPriceHistory;
import ai.pricefox.mallfox.service.product.ProductPriceHistoryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;

/**
 * 价格变化监听器
 * 在ProductDataOffers数据变化时自动记录价格历史
 * 
 * <AUTHOR>
 * @since 2025-07-12
 */
@Component
@Slf4j
public class PriceChangeListener {
    
    @Autowired
    private ProductPriceHistoryService priceHistoryService;
    
    @Autowired
    private PriceHistoryConfig priceHistoryConfig;
    
    /**
     * 监听价格变化并记录历史
     * 只在价格发生变化时插入新记录，不进行更新操作
     * 
     * @param oldOffer 旧的offer数据(可能为null，表示新增)
     * @param newOffer 新的offer数据
     * @param triggerSource 触发来源
     */
    public void onPriceChange(ProductDataOffers oldOffer, ProductDataOffers newOffer, String triggerSource) {
        if (!priceHistoryConfig.getEnabled()) {
            if (priceHistoryConfig.getDebugLogEnabled()) {
                log.debug("价格历史记录功能已禁用，跳过记录");
            }
            return;
        }
        
        if (newOffer == null) {
            log.warn("新offer数据为空，跳过价格历史记录");
            return;
        }
        
        try {
            boolean needInsert = false;
            ProductPriceHistory.ChangeType changeType = ProductPriceHistory.ChangeType.CREATE;
            
            if (oldOffer == null) {
                // 新增记录 - 如果启用了记录新增且价格不为空
                if (priceHistoryConfig.getRecordCreate() && newOffer.getPrice() != null) {
                    needInsert = true;
                    if (priceHistoryConfig.getDebugLogEnabled()) {
                        log.debug("检测到新增价格记录: offerId={}, price={}", newOffer.getId(), newOffer.getPrice());
                    }
                }
            } else {
                // 更新记录 - 只有价格真的发生变化时才插入新记录
                if (priceHistoryConfig.getRecordUpdate() && isPriceChanged(oldOffer, newOffer)) {
                    changeType = ProductPriceHistory.ChangeType.UPDATE;
                    needInsert = true;
                    if (priceHistoryConfig.getDebugLogEnabled()) {
                        log.debug("检测到价格变化: offerId={}, 旧价格={}, 新价格={}", 
                            newOffer.getId(), oldOffer.getPrice(), newOffer.getPrice());
                    }
                } else {
                    if (priceHistoryConfig.getDebugLogEnabled()) {
                        log.debug("价格未发生变化，跳过历史记录: offerId={}", newOffer.getId());
                    }
                    return; // 价格未变化，直接返回，不插入记录
                }
            }
            
            if (needInsert) {
                ProductPriceHistory history = buildPriceHistory(oldOffer, newOffer, changeType, triggerSource);
                
                // 检查变化量是否达到阈值
                if (changeType == ProductPriceHistory.ChangeType.UPDATE && !meetChangeThreshold(history)) {
                    if (priceHistoryConfig.getDebugLogEnabled()) {
                        log.debug("价格变化量未达到阈值，跳过记录: offerId={}, changeAmount={}", 
                            newOffer.getId(), history.getPriceChangeAmount());
                    }
                    return;
                }
                
                // 直接插入新记录，不进行更新操作
                boolean saved = priceHistoryService.save(history);
                if (saved) {
                    log.info("价格历史记录已插入: offerId={}, changeType={}, 价格变化={}", 
                        newOffer.getId(), changeType.getDesc(), 
                        history.getPriceChangeAmount() != null ? history.getPriceChangeAmount() : "新增");
                } else {
                    log.error("价格历史记录插入失败: offerId={}", newOffer.getId());
                }
            }
            
        } catch (Exception e) {
            log.error("记录价格历史失败: offerId={}, error={}", newOffer.getId(), e.getMessage(), e);
        }
    }
    
    /**
     * 记录删除操作的价格历史
     * 只插入删除记录，不进行更新操作
     * 
     * @param deletedOffer 被删除的offer数据
     * @param triggerSource 触发来源
     */
    public void onOfferDeleted(ProductDataOffers deletedOffer, String triggerSource) {
        if (!priceHistoryConfig.getEnabled() || !priceHistoryConfig.getRecordDelete()) {
            if (priceHistoryConfig.getDebugLogEnabled()) {
                log.debug("价格历史记录功能已禁用或删除记录已禁用，跳过记录");
            }
            return;
        }
        
        if (deletedOffer == null) {
            log.warn("删除的offer数据为空，跳过价格历史记录");
            return;
        }
        
        try {
            // 直接插入删除记录，不进行更新操作
            ProductPriceHistory history = buildPriceHistory(
                deletedOffer, null, ProductPriceHistory.ChangeType.DELETE, triggerSource);
            boolean saved = priceHistoryService.save(history);
            if (saved) {
                log.info("删除操作价格历史记录已插入: offerId={}, 删除前价格={}", 
                    deletedOffer.getId(), deletedOffer.getPrice());
            } else {
                log.error("删除操作价格历史记录插入失败: offerId={}", deletedOffer.getId());
            }
        } catch (Exception e) {
            log.error("记录删除操作价格历史失败: offerId={}, error={}", deletedOffer.getId(), e.getMessage(), e);
        }
    }
    
    /**
     * 检查价格是否发生变化
     * 主要检查price字段，同时也检查listPrice和discount
     */
    private boolean isPriceChanged(ProductDataOffers oldOffer, ProductDataOffers newOffer) {
        // 首先检查主要的price字段
        boolean priceChanged = !isSamePrice(oldOffer.getPrice(), newOffer.getPrice());
        
        // 如果主价格有变化，直接返回true
        if (priceChanged) {
            if (priceHistoryConfig.getDebugLogEnabled()) {
                log.debug("主价格发生变化: {} -> {}", oldOffer.getPrice(), newOffer.getPrice());
            }
            return true;
        }
        
        // 如果主价格没变化，检查其他价格字段
        boolean listPriceChanged = !isSamePrice(oldOffer.getListPrice(), newOffer.getListPrice());
        boolean discountChanged = !isSamePrice(oldOffer.getDiscount(), newOffer.getDiscount());
        
        if (listPriceChanged || discountChanged) {
            if (priceHistoryConfig.getDebugLogEnabled()) {
                log.debug("其他价格字段发生变化: listPrice {} -> {}, discount {} -> {}", 
                    oldOffer.getListPrice(), newOffer.getListPrice(),
                    oldOffer.getDiscount(), newOffer.getDiscount());
            }
            return true;
        }
        
        return false;
    }
    
    /**
     * 比较两个价格是否相同(考虑精度)
     */
    private boolean isSamePrice(BigDecimal price1, BigDecimal price2) {
        if (price1 == null && price2 == null) return true;
        if (price1 == null || price2 == null) return false;
        return price1.compareTo(price2) == 0;
    }
    
    /**
     * 检查价格变化是否达到最小阈值
     */
    private boolean meetChangeThreshold(ProductPriceHistory history) {
        if (history.getPriceChangeAmount() == null) {
            return true; // 没有价格变化量的情况下默认记录
        }
        
        BigDecimal threshold = BigDecimal.valueOf(priceHistoryConfig.getMinimumChangeThreshold());
        BigDecimal changeAmount = history.getPriceChangeAmount().abs();
        return changeAmount.compareTo(threshold) >= 0;
    }
    
    /**
     * 构建价格历史记录
     */
    private ProductPriceHistory buildPriceHistory(ProductDataOffers oldOffer, ProductDataOffers newOffer, 
            ProductPriceHistory.ChangeType changeType, String triggerSource) {
        
        // 对于删除操作，newOffer为null，使用oldOffer的基本信息
        ProductDataOffers sourceOffer = newOffer != null ? newOffer : oldOffer;
        
        ProductPriceHistory.ProductPriceHistoryBuilder builder = ProductPriceHistory.builder()
            .offerId(sourceOffer.getId())
            .skuId(sourceOffer.getSkuId())
            .spuId(sourceOffer.getSpuId())
            .sourcePlatform(sourceOffer.getSourcePlatform())
            .platformSpuId(sourceOffer.getPlatformSpuId())
            .platformSkuId(sourceOffer.getPlatformSkuId())
            .changeType(changeType.getCode())
            .dataChannel(sourceOffer.getDataChannel())
            .triggerSource(triggerSource)
            .priceUpdateTime(sourceOffer.getPriceUpdateTime())
            .recordTime(LocalDateTime.now());
        
        if (changeType == ProductPriceHistory.ChangeType.DELETE) {
            // 删除操作：记录删除前的价格作为旧值
            builder.oldPrice(oldOffer.getPrice())
                   .oldListPrice(oldOffer.getListPrice())
                   .oldDiscount(oldOffer.getDiscount());
        } else {
            // 新增或更新操作
            if (newOffer != null) {
                builder.newPrice(newOffer.getPrice())
                       .newListPrice(newOffer.getListPrice())
                       .newDiscount(newOffer.getDiscount());
            }
            
            // 如果是更新，设置旧值和变化量
            if (oldOffer != null && changeType == ProductPriceHistory.ChangeType.UPDATE) {
                builder.oldPrice(oldOffer.getPrice())
                       .oldListPrice(oldOffer.getListPrice())
                       .oldDiscount(oldOffer.getDiscount());
                
                // 计算价格变化
                if (oldOffer.getPrice() != null && newOffer.getPrice() != null) {
                    BigDecimal changeAmount = newOffer.getPrice().subtract(oldOffer.getPrice());
                    builder.priceChangeAmount(changeAmount);
                    
                    // 计算变化百分比
                    if (oldOffer.getPrice().compareTo(BigDecimal.ZERO) != 0) {
                        BigDecimal changePercent = changeAmount
                            .divide(oldOffer.getPrice(), 4, RoundingMode.HALF_UP)
                            .multiply(BigDecimal.valueOf(100))
                            .setScale(2, RoundingMode.HALF_UP);
                        builder.priceChangePercent(changePercent);
                    }
                }
            }
        }
        
        return builder.build();
    }
}
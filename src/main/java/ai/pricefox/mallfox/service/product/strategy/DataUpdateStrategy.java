package ai.pricefox.mallfox.service.product.strategy;

import ai.pricefox.mallfox.domain.product.ProductDataOffers;
import ai.pricefox.mallfox.domain.product.ProductDataSimplify;
import ai.pricefox.mallfox.enums.DataChannelEnum;
import ai.pricefox.mallfox.model.dto.ProductDataDTO;
import ai.pricefox.mallfox.model.dto.ProductPriceInventoryDTO;
import ai.pricefox.mallfox.service.product.util.ProductMatchingUtil;
import ai.pricefox.mallfox.service.tracking.TrackableEntity;
import ai.pricefox.mallfox.service.tracking.TrackableEntityFactory;
import ai.pricefox.mallfox.service.tracking.FieldTrackingService;
import com.alibaba.fastjson2.util.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * 数据更新策略
 * 根据数据渠道优先级进行字段更新
 *
 * <AUTHOR>
 * @since 2025-01-11
 */
@Slf4j
@Component
public class DataUpdateStrategy {

    private static TrackableEntityFactory trackableFactory;
    private static FieldTrackingService fieldTrackingService;

    @Autowired
    public void setTrackableFactory(TrackableEntityFactory factory) {
        DataUpdateStrategy.trackableFactory = factory;
        log.info("TrackableEntityFactory 注入成功: {}", factory != null ? "成功" : "失败");
    }

    @Autowired
    public void setFieldTrackingService(FieldTrackingService service) {
        DataUpdateStrategy.fieldTrackingService = service;
        log.info("FieldTrackingService 注入成功: {}", service != null ? "成功" : "失败");
    }

    /**
     * 根据数据渠道优先级更新记录
     *
     * @param existingRecord 现有数据库记录
     * @param newData 新进来的数据
     * @return 是否需要更新
     */
    public static boolean updateRecordByDataChannel(ProductDataSimplify existingRecord, ProductDataDTO newData) {
        Integer existingChannel = existingRecord.getDataChannel();
        Integer newChannel = newData.getDataChannel().getCode();

        // 默认值处理
        if (existingChannel == null) {
            existingChannel = DataChannelEnum.CRAWLER.getCode();
        }
        if (newChannel == null) {
            newChannel = DataChannelEnum.CRAWLER.getCode();
        }

        log.info("数据更新策略判断，现有渠道: {}, 新数据渠道: {}", existingChannel, newChannel);

        boolean needUpdate = false;

        // 情况一：数据库中是API数据，新进来的是爬虫数据
        if (existingChannel.equals(DataChannelEnum.API.getCode()) && 
            newChannel.equals(DataChannelEnum.CRAWLER.getCode())) {
            needUpdate = updateApiWithCrawlerData(existingRecord, newData);
        }
        // 情况二：数据库中是爬虫数据，新进来的是API数据
        else if (existingChannel.equals(DataChannelEnum.CRAWLER.getCode()) && 
                 newChannel.equals(DataChannelEnum.API.getCode())) {
            needUpdate = updateCrawlerWithApiData(existingRecord, newData);
        }
        // 情况三：相同渠道数据，使用最新数据更新
        else if (existingChannel.equals(newChannel)) {
            needUpdate = updateSameChannelData(existingRecord, newData);
        }

        if (needUpdate) {
            existingRecord.setUpdateTime(LocalDateTime.now());
            log.info("记录需要更新，SKU ID: {}", existingRecord.getSkuId());
        }

        return needUpdate;
    }

    /**
     * 根据数据渠道优先级更新 ProductDataSimplify 记录（带字段追踪）
     */
    public static boolean updateRecordByDataChannelWithTracking(ProductDataSimplify existingRecord, ProductDataDTO newData) {
        log.debug("开始带追踪的更新 ProductDataSimplify，recordId: {}, dataChannel: {}",
            existingRecord.getId(), newData.getDataChannel());

        if (trackableFactory == null) {
            log.warn("TrackableEntityFactory 未注入，回退到原有方法");
            return updateRecordByDataChannel(existingRecord, newData);
        }

        // 创建可追踪的包装器
        TrackableEntity<ProductDataSimplify> trackableRecord =
            trackableFactory.createTrackableSimplify(existingRecord, newData.getDataChannel().getCode(), newData.getSourcePlatform().getCode());

        if (trackableRecord == null) {
            log.warn("无法创建追踪包装器，回退到原有方法");
            return updateRecordByDataChannel(existingRecord, newData);
        }

        // 执行原有的更新逻辑
        boolean needUpdate = updateRecordByDataChannel(existingRecord, newData);

        // 检测并记录字段变更
        if (needUpdate) {
            log.debug("检测到字段变更，开始记录追踪信息");
            trackableRecord.detectAndRecordChanges();
        } else {
            log.debug("未检测到字段变更，跳过追踪记录");
        }

        return needUpdate;
    }

    /**
     * API数据被爬虫数据更新：API有值则不更新，API无值则更新
     * 注意：ProductDataSimplify表主要存储规格信息，价格等信息在ProductDataOffers表中
     */
    private static boolean updateApiWithCrawlerData(ProductDataSimplify existingRecord, ProductDataDTO newData) {
        boolean updated = false;

        // 规格相关字段 - 只更新API数据中为空的字段
        if (!ProductMatchingUtil.hasValue(existingRecord.getModelYear()) && ProductMatchingUtil.hasValue(newData.getModelYear())) {
            existingRecord.setModelYear(newData.getModelYear());
            updated = true;
        }

        // 图片相关字段
        if (!ProductMatchingUtil.hasValue(existingRecord.getProductMainImageUrls()) && ProductMatchingUtil.hasValue(newData.getProductMainImageUrls())) {
            existingRecord.setProductMainImageUrls(newData.getProductMainImageUrls());
            updated = true;
        }
        if (!ProductMatchingUtil.hasValue(existingRecord.getProductSpecColorUrl()) && ProductMatchingUtil.hasValue(newData.getProductSpecColorUrl())) {
            existingRecord.setProductSpecColorUrl(newData.getProductSpecColorUrl());
            updated = true;
        }

        // 硬件规格字段
        if (!ProductMatchingUtil.hasValue(existingRecord.getRamMemoryInstalledSize()) && ProductMatchingUtil.hasValue(newData.getRamMemoryInstalledSize())) {
            existingRecord.setRamMemoryInstalledSize(newData.getRamMemoryInstalledSize());
            updated = true;
        }
        if (!ProductMatchingUtil.hasValue(existingRecord.getOperatingSystem()) && ProductMatchingUtil.hasValue(newData.getOperatingSystem())) {
            existingRecord.setOperatingSystem(newData.getOperatingSystem());
            updated = true;
        }
        if (!ProductMatchingUtil.hasValue(existingRecord.getProcessor()) && ProductMatchingUtil.hasValue(newData.getProcessor())) {
            existingRecord.setProcessor(newData.getProcessor());
            updated = true;
        }
        if (!ProductMatchingUtil.hasValue(existingRecord.getCellularTechnology()) && ProductMatchingUtil.hasValue(newData.getCellularTechnology())) {
            existingRecord.setCellularTechnology(newData.getCellularTechnology());
            updated = true;
        }

        // 显示相关字段
        if (!ProductMatchingUtil.hasValue(existingRecord.getScreenSize()) && ProductMatchingUtil.hasValue(newData.getScreenSize())) {
            existingRecord.setScreenSize(newData.getScreenSize());
            updated = true;
        }
        if (!ProductMatchingUtil.hasValue(existingRecord.getResolution()) && ProductMatchingUtil.hasValue(newData.getResolution())) {
            existingRecord.setResolution(newData.getResolution());
            updated = true;
        }
        if (!ProductMatchingUtil.hasValue(existingRecord.getRefreshRate()) && ProductMatchingUtil.hasValue(newData.getRefreshRate())) {
            existingRecord.setRefreshRate(newData.getRefreshRate());
            updated = true;
        }
        if (!ProductMatchingUtil.hasValue(existingRecord.getDisplayType()) && ProductMatchingUtil.hasValue(newData.getDisplayType())) {
            existingRecord.setDisplayType(newData.getDisplayType());
            updated = true;
        }

        // 电池相关字段
        if (!ProductMatchingUtil.hasValue(existingRecord.getBatteryPower()) && ProductMatchingUtil.hasValue(newData.getBatteryPower())) {
            existingRecord.setBatteryPower(newData.getBatteryPower());
            updated = true;
        }
        if (!ProductMatchingUtil.hasValue(existingRecord.getAverageTalkTime()) && ProductMatchingUtil.hasValue(newData.getAverageTalkTime())) {
            existingRecord.setAverageTalkTime(newData.getAverageTalkTime());
            updated = true;
        }
        if (!ProductMatchingUtil.hasValue(existingRecord.getBatteryChargeTime()) && ProductMatchingUtil.hasValue(newData.getBatteryChargeTime())) {
            existingRecord.setBatteryChargeTime(newData.getBatteryChargeTime());
            updated = true;
        }

        // 摄像头相关字段
        if (!ProductMatchingUtil.hasValue(existingRecord.getFrontPhotoSensorResolution()) && ProductMatchingUtil.hasValue(newData.getFrontPhotoSensorResolution())) {
            existingRecord.setFrontPhotoSensorResolution(newData.getFrontPhotoSensorResolution());
            updated = true;
        }
        if (!ProductMatchingUtil.hasValue(existingRecord.getRearFacingCameraPhotoSensorResolution()) && ProductMatchingUtil.hasValue(newData.getRearFacingCameraPhotoSensorResolution())) {
            existingRecord.setRearFacingCameraPhotoSensorResolution(newData.getRearFacingCameraPhotoSensorResolution());
            updated = true;
        }
        if (existingRecord.getNumberOfRearFacingCameras() == null && newData.getNumberOfRearFacingCameras() != null) {
            existingRecord.setNumberOfRearFacingCameras(newData.getNumberOfRearFacingCameras());
            updated = true;
        }

        return updated;
    }

    /**
     * 爬虫数据被API数据更新：API数据全部覆盖
     * 注意：ProductDataSimplify表主要存储规格信息，价格等信息在ProductDataOffers表中
     */
    private static boolean updateCrawlerWithApiData(ProductDataSimplify existingRecord, ProductDataDTO newData) {
        // API数据优先级高，有值就覆盖
        boolean updated = false;

        // 规格相关字段 - API有值就覆盖
        if (ProductMatchingUtil.hasValue(newData.getModelYear())) {
            existingRecord.setModelYear(newData.getModelYear());
        }

        // 图片相关字段
        if (ProductMatchingUtil.hasValue(newData.getProductMainImageUrls())) {
            existingRecord.setProductMainImageUrls(newData.getProductMainImageUrls());
        }
        if (ProductMatchingUtil.hasValue(newData.getProductSpecColorUrl())) {
            existingRecord.setProductSpecColorUrl(newData.getProductSpecColorUrl());
        }

        // 硬件规格字段
        if (ProductMatchingUtil.hasValue(newData.getRamMemoryInstalledSize())) {
            existingRecord.setRamMemoryInstalledSize(newData.getRamMemoryInstalledSize());
        }
        if (ProductMatchingUtil.hasValue(newData.getOperatingSystem())) {
            existingRecord.setOperatingSystem(newData.getOperatingSystem());
        }
        if (ProductMatchingUtil.hasValue(newData.getProcessor())) {
            existingRecord.setProcessor(newData.getProcessor());
        }
        if (ProductMatchingUtil.hasValue(newData.getCellularTechnology())) {
            existingRecord.setCellularTechnology(newData.getCellularTechnology());
        }

        // 显示相关字段
        if (ProductMatchingUtil.hasValue(newData.getScreenSize())) {
            existingRecord.setScreenSize(newData.getScreenSize());
        }
        if (ProductMatchingUtil.hasValue(newData.getResolution())) {
            existingRecord.setResolution(newData.getResolution());
        }
        if (ProductMatchingUtil.hasValue(newData.getRefreshRate())) {
            existingRecord.setRefreshRate(newData.getRefreshRate());
        }
        if (ProductMatchingUtil.hasValue(newData.getDisplayType())) {
            existingRecord.setDisplayType(newData.getDisplayType());
        }

        // 电池相关字段
        if (ProductMatchingUtil.hasValue(newData.getBatteryPower())) {
            existingRecord.setBatteryPower(newData.getBatteryPower());
        }
        if (ProductMatchingUtil.hasValue(newData.getAverageTalkTime())) {
            existingRecord.setAverageTalkTime(newData.getAverageTalkTime());
        }
        if (ProductMatchingUtil.hasValue(newData.getBatteryChargeTime())) {
            existingRecord.setBatteryChargeTime(newData.getBatteryChargeTime());
        }

        // 摄像头相关字段
        if (ProductMatchingUtil.hasValue(newData.getFrontPhotoSensorResolution())) {
            existingRecord.setFrontPhotoSensorResolution(newData.getFrontPhotoSensorResolution());
        }
        if (ProductMatchingUtil.hasValue(newData.getRearFacingCameraPhotoSensorResolution())) {
            existingRecord.setRearFacingCameraPhotoSensorResolution(newData.getRearFacingCameraPhotoSensorResolution());
        }
        if (newData.getNumberOfRearFacingCameras() != null) {
            existingRecord.setNumberOfRearFacingCameras(newData.getNumberOfRearFacingCameras());
        }

        // 更新数据渠道为API
        existingRecord.setDataChannel(DataChannelEnum.API.getCode());
        updated = true;

        return updated;
    }

    /**
     * 相同渠道数据更新：使用最新数据
     * 注意：ProductDataSimplify表主要存储规格信息，价格等信息在ProductDataOffers表中
     */
    private static boolean updateSameChannelData(ProductDataSimplify existingRecord, ProductDataDTO newData) {
        // 相同渠道，使用新数据更新有值的字段
        boolean updated = false;

        // 规格相关字段
        if (ProductMatchingUtil.hasValue(newData.getModelYear())) {
            existingRecord.setModelYear(newData.getModelYear());
            updated = true;
        }

        // 图片相关字段
        if (ProductMatchingUtil.hasValue(newData.getProductMainImageUrls())) {
            existingRecord.setProductMainImageUrls(newData.getProductMainImageUrls());
            updated = true;
        }
        if (ProductMatchingUtil.hasValue(newData.getProductSpecColorUrl())) {
            existingRecord.setProductSpecColorUrl(newData.getProductSpecColorUrl());
            updated = true;
        }

        // 硬件规格字段
        if (ProductMatchingUtil.hasValue(newData.getRamMemoryInstalledSize())) {
            existingRecord.setRamMemoryInstalledSize(newData.getRamMemoryInstalledSize());
            updated = true;
        }
        if (ProductMatchingUtil.hasValue(newData.getOperatingSystem())) {
            existingRecord.setOperatingSystem(newData.getOperatingSystem());
            updated = true;
        }
        if (ProductMatchingUtil.hasValue(newData.getProcessor())) {
            existingRecord.setProcessor(newData.getProcessor());
            updated = true;
        }

        // 显示相关字段
        if (ProductMatchingUtil.hasValue(newData.getScreenSize())) {
            existingRecord.setScreenSize(newData.getScreenSize());
            updated = true;
        }
        if (ProductMatchingUtil.hasValue(newData.getResolution())) {
            existingRecord.setResolution(newData.getResolution());
            updated = true;
        }
        if (ProductMatchingUtil.hasValue(newData.getRefreshRate())) {
            existingRecord.setRefreshRate(newData.getRefreshRate());
            updated = true;
        }
        if (ProductMatchingUtil.hasValue(newData.getDisplayType())) {
            existingRecord.setDisplayType(newData.getDisplayType());
            updated = true;
        }

        // 电池相关字段
        if (ProductMatchingUtil.hasValue(newData.getBatteryPower())) {
            existingRecord.setBatteryPower(newData.getBatteryPower());
            updated = true;
        }
        if (ProductMatchingUtil.hasValue(newData.getAverageTalkTime())) {
            existingRecord.setAverageTalkTime(newData.getAverageTalkTime());
            updated = true;
        }
        if (ProductMatchingUtil.hasValue(newData.getBatteryChargeTime())) {
            existingRecord.setBatteryChargeTime(newData.getBatteryChargeTime());
            updated = true;
        }

        // 摄像头相关字段
        if (ProductMatchingUtil.hasValue(newData.getFrontPhotoSensorResolution())) {
            existingRecord.setFrontPhotoSensorResolution(newData.getFrontPhotoSensorResolution());
            updated = true;
        }
        if (ProductMatchingUtil.hasValue(newData.getRearFacingCameraPhotoSensorResolution())) {
            existingRecord.setRearFacingCameraPhotoSensorResolution(newData.getRearFacingCameraPhotoSensorResolution());
            updated = true;
        }
        if (newData.getNumberOfRearFacingCameras() != null) {
            existingRecord.setNumberOfRearFacingCameras(newData.getNumberOfRearFacingCameras());
            updated = true;
        }

        return updated;
    }

    /**
     * 根据数据渠道优先级更新 ProductDataOffers 记录
     *
     * @param existingOffer 现有数据库记录
     * @param newData 新进来的数据
     * @return 是否需要更新
     */
    public static boolean updateOffersRecordByDataChannel(ProductDataOffers existingOffer, ProductDataDTO newData) {
        Integer existingChannel = existingOffer.getDataChannel();
        Integer newChannel = newData.getDataChannel().getCode();

        // 默认值处理
        if (existingChannel == null) {
            existingChannel = DataChannelEnum.CRAWLER.getCode();
        }
        if (newChannel == null) {
            newChannel = DataChannelEnum.CRAWLER.getCode();
        }

        log.info("ProductDataOffers数据更新策略判断，现有渠道: {}, 新数据渠道: {}", existingChannel, newChannel);

        boolean needUpdate = false;

        // 情况一：数据库中是API数据，新进来的是爬虫数据
        if (existingChannel.equals(DataChannelEnum.API.getCode()) &&
            newChannel.equals(DataChannelEnum.CRAWLER.getCode())) {
            needUpdate = updateApiOffersWithCrawlerData(existingOffer, newData);
        }
        // 情况二：数据库中是爬虫数据，新进来的是API数据
        else if (existingChannel.equals(DataChannelEnum.CRAWLER.getCode()) &&
                 newChannel.equals(DataChannelEnum.API.getCode())) {
            needUpdate = updateCrawlerOffersWithApiData(existingOffer, newData);
        }
        // 情况三：相同渠道数据，使用最新数据更新
        else if (existingChannel.equals(newChannel)) {
            needUpdate = updateSameChannelOffersData(existingOffer, newData);
        }

        if (needUpdate) {
            existingOffer.setUpdateTime(LocalDateTime.now());
            log.info("ProductDataOffers记录需要更新，SKU ID: {}", existingOffer.getSkuId());
        }

        return needUpdate;
    }

    /**
     * 根据数据渠道优先级更新 ProductDataOffers 记录（带字段追踪）
     */
    public static boolean updateOffersRecordByDataChannelWithTracking(ProductDataOffers existingOffer, ProductDataDTO newData) {
        if (trackableFactory == null) {
            // 如果追踪工厂未初始化，回退到原有方法
            return updateOffersRecordByDataChannel(existingOffer, newData);
        }

        // 创建可追踪的包装器
        TrackableEntity<ProductDataOffers> trackableOffer =
            trackableFactory.createTrackableOffers(existingOffer, newData.getDataChannel().getCode(), newData.getSourcePlatform().getCode());

        if (trackableOffer == null) {
            // 如果无法创建追踪包装器，回退到原有方法
            return updateOffersRecordByDataChannel(existingOffer, newData);
        }

        // 执行原有的更新逻辑
        boolean needUpdate = updateOffersRecordByDataChannel(existingOffer, newData);

        // 检测并记录字段变更
        if (needUpdate) {
            trackableOffer.detectAndRecordChanges();
        }

        return needUpdate;
    }

    /**
     * API Offers数据被爬虫数据更新：API有值则不更新，API无值则更新
     */
    private static boolean updateApiOffersWithCrawlerData(ProductDataOffers existingOffer, ProductDataDTO newData) {
        boolean updated = false;

        // 基本信息字段 - 只更新API数据中为空的字段
        if (!ProductMatchingUtil.hasValue(existingOffer.getTitle()) && ProductMatchingUtil.hasValue(newData.getTitle())) {
            existingOffer.setTitle(newData.getTitle());
            updated = true;
        }
        if (!ProductMatchingUtil.hasValue(existingOffer.getBrand()) && ProductMatchingUtil.hasValue(newData.getBrand())) {
            existingOffer.setBrand(newData.getBrand());
            updated = true;
        }
        if (!ProductMatchingUtil.hasValue(existingOffer.getSeries()) && ProductMatchingUtil.hasValue(newData.getSeries())) {
            existingOffer.setSeries(newData.getSeries());
            updated = true;
        }
        if (!ProductMatchingUtil.hasValue(existingOffer.getUpcCode()) && ProductMatchingUtil.hasValue(newData.getUpcCode())) {
            existingOffer.setUpcCode(newData.getUpcCode());
            updated = true;
        }

        // 价格相关字段
        if (existingOffer.getPrice() == null && newData.getPrice() != null) {
            existingOffer.setPrice(newData.getPrice());
            updated = true;
        }
        if (existingOffer.getListPrice() == null && newData.getListPrice() != null) {
            existingOffer.setListPrice(newData.getListPrice());
            updated = true;
        }
        if (existingOffer.getDiscount() == null && newData.getDiscount() != null) {
            existingOffer.setDiscount(newData.getDiscount());
            updated = true;
        }

        // 库存和销量
        if (!ProductMatchingUtil.hasValue(existingOffer.getInventory()) && ProductMatchingUtil.hasValue(newData.getInventory())) {
            existingOffer.setInventory(newData.getInventory());
            existingOffer.setInventoryUpdateTime(LocalDateTime.now());
            updated = true;
        }
        if (!ProductMatchingUtil.hasValue(existingOffer.getSalesLast30Days()) && ProductMatchingUtil.hasValue(newData.getSalesLast30Days())) {
            existingOffer.setSalesLast30Days(newData.getSalesLast30Days());
            updated = true;
        }

        // 卖家信息
        if (!ProductMatchingUtil.hasValue(existingOffer.getSeller()) && ProductMatchingUtil.hasValue(newData.getSeller())) {
            existingOffer.setSeller(newData.getSeller());
            updated = true;
        }
        if (existingOffer.getMerchantRating() == null && newData.getMerchantRating() != null) {
            existingOffer.setMerchantRating(newData.getMerchantRating());
            updated = true;
        }

        return updated;
    }

    /**
     * 爬虫Offers数据被API数据更新：API数据全部覆盖
     */
    private static boolean updateCrawlerOffersWithApiData(ProductDataOffers existingOffer, ProductDataDTO newData) {
        boolean updated = false;

        // 基本信息字段 - API有值就覆盖
        if (ProductMatchingUtil.hasValue(newData.getTitle())) {
            existingOffer.setTitle(newData.getTitle());
            updated = true;
        }
        if (ProductMatchingUtil.hasValue(newData.getBrand())) {
            existingOffer.setBrand(newData.getBrand());
            updated = true;
        }
        if (ProductMatchingUtil.hasValue(newData.getSeries())) {
            existingOffer.setSeries(newData.getSeries());
            updated = true;
        }
        if (ProductMatchingUtil.hasValue(newData.getUpcCode())) {
            existingOffer.setUpcCode(newData.getUpcCode());
            updated = true;
        }

        // 价格相关字段
        if (newData.getPrice() != null) {
            existingOffer.setPrice(newData.getPrice());
            updated = true;
        }
        if (newData.getListPrice() != null) {
            existingOffer.setListPrice(newData.getListPrice());
            updated = true;
        }
        if (newData.getDiscount() != null) {
            existingOffer.setDiscount(newData.getDiscount());
            updated = true;
        }

        // 库存和销量
        if (ProductMatchingUtil.hasValue(newData.getInventory())) {
            existingOffer.setInventory(newData.getInventory());
            updated = true;
        }
        if (ProductMatchingUtil.hasValue(newData.getSalesLast30Days())) {
            existingOffer.setSalesLast30Days(newData.getSalesLast30Days());
            updated = true;
        }

        // 卖家信息
        if (ProductMatchingUtil.hasValue(newData.getSeller())) {
            existingOffer.setSeller(newData.getSeller());
            updated = true;
        }
        if (newData.getMerchantRating() != null) {
            existingOffer.setMerchantRating(newData.getMerchantRating());
            updated = true;
        }

        // 更新数据渠道为API
        existingOffer.setDataChannel(DataChannelEnum.API.getCode());
        updated = true;

        return updated;
    }

    /**
     * 相同渠道Offers数据更新：使用最新数据
     */
    private static boolean updateSameChannelOffersData(ProductDataOffers existingOffer, ProductDataDTO newData) {
        boolean updated = false;

        // 相同渠道，使用新数据更新有值的字段
        if (ProductMatchingUtil.hasValue(newData.getTitle())) {
            existingOffer.setTitle(newData.getTitle());
            updated = true;
        }
        if (ProductMatchingUtil.hasValue(newData.getBrand())) {
            existingOffer.setBrand(newData.getBrand());
            updated = true;
        }
        if (ProductMatchingUtil.hasValue(newData.getSeries())) {
            existingOffer.setSeries(newData.getSeries());
            updated = true;
        }
        if (ProductMatchingUtil.hasValue(newData.getUpcCode())) {
            existingOffer.setUpcCode(newData.getUpcCode());
            updated = true;
        }

        // 价格相关字段
        if (newData.getPrice() != null) {
            existingOffer.setPrice(newData.getPrice());
            updated = true;
        }
        if (newData.getListPrice() != null) {
            existingOffer.setListPrice(newData.getListPrice());
            updated = true;
        }
        if (newData.getDiscount() != null) {
            existingOffer.setDiscount(newData.getDiscount());
            updated = true;
        }

        // 库存和销量
        if (ProductMatchingUtil.hasValue(newData.getInventory())) {
            existingOffer.setInventory(newData.getInventory());
            existingOffer.setInventoryUpdateTime(LocalDateTime.now());
            updated = true;
        }
        if (ProductMatchingUtil.hasValue(newData.getSalesLast30Days())) {
            existingOffer.setSalesLast30Days(newData.getSalesLast30Days());
            updated = true;
        }

        // 卖家信息
        if (ProductMatchingUtil.hasValue(newData.getSeller())) {
            existingOffer.setSeller(newData.getSeller());
            updated = true;
        }
        if (newData.getMerchantRating() != null) {
            existingOffer.setMerchantRating(newData.getMerchantRating());
            updated = true;
        }

        return updated;
    }

    /**
     * 创建新的 ProductDataOffers 记录
     */
    public static ProductDataOffers createNewOffersRecord(ProductDataDTO newData, String skuId, String spuId) {
        ProductDataOffers newOffer = new ProductDataOffers();

        // 设置关联ID
        newOffer.setSkuId(skuId);
        newOffer.setSpuId(spuId);

        // 设置平台信息
        newOffer.setSourcePlatform(newData.getSourcePlatform().getCode());
        newOffer.setPlatformSpuId(newData.getPlatformSpuId());
        newOffer.setPlatformSkuId(newData.getPlatformSkuId());

        // 设置商品基本信息
        newOffer.setTitle(newData.getTitle());
        newOffer.setBrand(newData.getBrand());
        newOffer.setSeries(newData.getSeries());
        newOffer.setUpcCode(newData.getUpcCode());
        newOffer.setServiceProvider(newData.getServiceProvider());
        newOffer.setConditionNew(newData.getCondition());

        // 设置价格信息
        newOffer.setPrice(newData.getPrice());
        newOffer.setListPrice(newData.getListPrice());
        newOffer.setDiscount(newData.getDiscount());

        // 设置库存和销量
        newOffer.setInventory(newData.getInventory());
        newOffer.setInventoryUpdateTime(LocalDateTime.now());
        newOffer.setSalesLast30Days(newData.getSalesLast30Days());

        // 设置卖家信息
        newOffer.setSeller(newData.getSeller());
        newOffer.setMerchantRating(newData.getMerchantRating());

        // 设置分类信息
        newOffer.setCategoryLevel1(newData.getCategoryLevel1());
        newOffer.setCategoryLevel2(newData.getCategoryLevel2());
        newOffer.setCategoryLevel3(newData.getCategoryLevel3());

        // 设置其他信息
        newOffer.setItemUrl(newData.getItemUrl());

        // 设置数据渠道
        if (newData.getDataChannel() == null || newData.getDataChannel().getCode() == 0) {
            newOffer.setDataChannel(DataChannelEnum.CRAWLER.getCode());
        } else {
            newOffer.setDataChannel(newData.getDataChannel().getCode());
        }

        return newOffer;
    }

    /**
     * 创建新的 ProductDataOffers 记录（带字段追踪）
     */
    public static ProductDataOffers createNewOffersRecordWithTracking(ProductDataDTO newData, String skuId, String spuId) {
        // 创建新记录
        ProductDataOffers newOffer = createNewOffersRecord(newData, skuId, spuId);
        return newOffer;
    }

    /**
     * 记录新建的 ProductDataOffers 字段来源（在保存后调用）
     */
    public static void recordNewOffersFieldSources(ProductDataOffers savedOffer, Integer dataSource, String sourcePlatform) {
        log.debug("开始记录新建 ProductDataOffers 字段来源，recordId: {}, dataSource: {}",
            savedOffer != null ? savedOffer.getId() : null, dataSource);

        if (fieldTrackingService == null) {
            log.warn("FieldTrackingService 未注入，跳过字段来源记录");
            return;
        }

        if (savedOffer != null && savedOffer.getId() != null) {
            fieldTrackingService.recordNewRecordFields(savedOffer, "product_data_offers", savedOffer.getId(), dataSource, sourcePlatform);
            log.debug("已提交新建 ProductDataOffers 字段来源记录任务");
        } else {
            log.warn("ProductDataOffers 记录或ID为空，跳过字段来源记录");
        }
    }

    /**
     * 创建新的 ProductDataSimplify 记录（带字段追踪）
     */
    public static ProductDataSimplify createNewSimplifyRecordWithTracking(ProductDataDTO newData, String spuId, String skuId) {
        // 创建新记录
        ProductDataSimplify newSimplify = createNewSimplifyRecord(newData, spuId, skuId);
        return newSimplify;
    }

    /**
     * 记录新建的 ProductDataSimplify 字段来源（在保存后调用）
     */
    public static void recordNewSimplifyFieldSources(ProductDataSimplify savedSimplify, Integer dataSource, String sourcePlatform) {
        log.debug("开始记录新建 ProductDataSimplify 字段来源，recordId: {}, dataSource: {}",
            savedSimplify != null ? savedSimplify.getId() : null, dataSource);

        if (fieldTrackingService == null) {
            log.warn("FieldTrackingService 未注入，跳过字段来源记录");
            return;
        }

        if (savedSimplify != null && savedSimplify.getId() != null) {
            fieldTrackingService.recordNewRecordFields(savedSimplify, "product_data_simplify", savedSimplify.getId(), dataSource, sourcePlatform);
            log.debug("已提交新建 ProductDataSimplify 字段来源记录任务");
        } else {
            log.warn("ProductDataSimplify 记录或ID为空，跳过字段来源记录");
        }
    }

    /**
     * 创建新的 ProductDataSimplify 记录
     */
    public static ProductDataSimplify createNewSimplifyRecord(ProductDataDTO newData, String spuId, String skuId) {
        ProductDataSimplify newSimplify = new ProductDataSimplify();

        // 设置关联ID
        newSimplify.setSpuId(spuId);
        newSimplify.setSkuId(skuId);

        // 设置平台信息
        newSimplify.setSourcePlatform(newData.getSourcePlatform().getCode());
        newSimplify.setPlatformSpuId(newData.getPlatformSpuId());
        newSimplify.setPlatformSkuId(newData.getPlatformSkuId());

        // 设置商品基本信息
        newSimplify.setModel(newData.getProcessedModel());
        newSimplify.setModelBack(newData.getModel());
        newSimplify.setColor(newData.getColor());
        newSimplify.setStorage(newData.getStorage());
        newSimplify.setServiceProvider(newData.getServiceProvider());
        newSimplify.setConditionNew(newData.getCondition());

        // 设置规格信息
        newSimplify.setModelYear(newData.getModelYear());
        newSimplify.setProductMainImageUrls(newData.getProductMainImageUrls());
        newSimplify.setProductSpecColorUrl(newData.getProductSpecColorUrl());
        newSimplify.setRamMemoryInstalledSize(newData.getRamMemoryInstalledSize());
        newSimplify.setOperatingSystem(newData.getOperatingSystem());
        newSimplify.setProcessor(newData.getProcessor());
        newSimplify.setCellularTechnology(newData.getCellularTechnology());

        // 设置显示相关信息
        newSimplify.setScreenSize(newData.getScreenSize());
        newSimplify.setResolution(newData.getResolution());
        newSimplify.setRefreshRate(newData.getRefreshRate());
        newSimplify.setDisplayType(newData.getDisplayType());

        // 设置电池相关信息
        newSimplify.setBatteryPower(newData.getBatteryPower());
        newSimplify.setAverageTalkTime(newData.getAverageTalkTime());
        newSimplify.setBatteryChargeTime(newData.getBatteryChargeTime());

        // 设置摄像头相关信息
        newSimplify.setFrontPhotoSensorResolution(newData.getFrontPhotoSensorResolution());
        newSimplify.setRearFacingCameraPhotoSensorResolution(newData.getRearFacingCameraPhotoSensorResolution());
        newSimplify.setNumberOfRearFacingCameras(newData.getNumberOfRearFacingCameras());
        newSimplify.setEffectiveVideoResolution(newData.getEffectiveVideoResolution());
        newSimplify.setVideoCaptureResolution(newData.getVideoCaptureResolution());

        // 设置其他硬件信息
        newSimplify.setSimCardSlotCount(newData.getSimCardSlotCount());
        newSimplify.setConnectorType(newData.getConnectorType());
        newSimplify.setWaterResistance(newData.getWaterResistance());
        newSimplify.setDimensions(newData.getDimensions());
        newSimplify.setItemWeight(newData.getItemWeight());
        newSimplify.setBiometricSecurityFeature(newData.getBiometricSecurityFeature());
        newSimplify.setSupportedSatelliteNavigationSystem(newData.getSupportedSatelliteNavigationSystem());
        newSimplify.setFeatures(newData.getFeatures());

        // 设置商务信息
        newSimplify.setReturnPolicy(newData.getReturnPolicy());
        newSimplify.setPaymentInstallment(newData.getPaymentInstallment());
        newSimplify.setInstallPayment(newData.getInstallPayment());
        newSimplify.setWarrantyDescription(newData.getWarrantyDescription());
        newSimplify.setShippingTime(newData.getShippingTime());

        // 设置评价信息
        newSimplify.setReviewNumber(newData.getReviewNumber());
        newSimplify.setReviewScore(newData.getReviewScore());
        newSimplify.setReviewRatingDistribution(newData.getReviewRatingDistribution());
        newSimplify.setReviewDimensionalRatings(newData.getReviewDimensionalRatings());
        newSimplify.setReviewOverviewProsCons(newData.getReviewOverviewProsCons());
        newSimplify.setReviewProsConsByStar(newData.getReviewProsConsByStar());

        // 设置数据渠道
        if (newData.getDataChannel() == null || newData.getDataChannel().getCode() == 0) {
            newSimplify.setDataChannel(DataChannelEnum.CRAWLER.getCode());
        } else {
            newSimplify.setDataChannel(newData.getDataChannel().getCode());
        }

        return newSimplify;
    }

    /**
     * 根据数据渠道优先级更新 ProductDataOffers 记录（带字段追踪）- 价格库存专用版本
     */
    public static boolean updateOffersRecordByDataChannelWithTracking(ProductDataOffers existingOffer, ProductPriceInventoryDTO newData) {
        if (trackableFactory == null) {
            // 如果追踪工厂未初始化，回退到原有方法
            return updateOffersRecordByDataChannelForPriceInventory(existingOffer, newData);
        }

        // 创建可追踪的包装器
        TrackableEntity<ProductDataOffers> trackableOffer =
            trackableFactory.createTrackableOffers(existingOffer, newData.getDataChannel(), newData.getSourcePlatform());

        if (trackableOffer == null) {
            // 如果无法创建追踪包装器，回退到原有方法
            return updateOffersRecordByDataChannelForPriceInventory(existingOffer, newData);
        }

        // 执行原有的更新逻辑
        boolean needUpdate = updateOffersRecordByDataChannelForPriceInventory(existingOffer, newData);

        // 检测并记录字段变更
        if (needUpdate) {
            trackableOffer.detectAndRecordChanges();
        }

        return needUpdate;
    }

    /**
     * 根据数据渠道优先级更新 ProductDataOffers 记录 - 价格库存专用版本
     */
    public static boolean updateOffersRecordByDataChannelForPriceInventory(ProductDataOffers existingOffer, ProductPriceInventoryDTO newData) {
        Integer existingChannel = existingOffer.getDataChannel();
        Integer newChannel = newData.getDataChannel();

        // 默认值处理
        if (existingChannel == null) {
            existingChannel = DataChannelEnum.CRAWLER.getCode();
        }
        if (newChannel == null) {
            newChannel = DataChannelEnum.CRAWLER.getCode();
        }

        log.info("ProductDataOffers价格库存更新策略判断，现有渠道: {}, 新数据渠道: {}", existingChannel, newChannel);

        boolean needUpdate = false;

        // 情况一：数据库中是API数据，新进来的是爬虫数据
        if (existingChannel.equals(DataChannelEnum.API.getCode()) &&
            newChannel.equals(DataChannelEnum.CRAWLER.getCode())) {
            needUpdate = updateApiOffersWithCrawlerDataForPriceInventory(existingOffer, newData);
        }
        // 情况二：数据库中是爬虫数据，新进来的是API数据
        else if (existingChannel.equals(DataChannelEnum.CRAWLER.getCode()) &&
                 newChannel.equals(DataChannelEnum.API.getCode())) {
            needUpdate = updateCrawlerOffersWithApiDataForPriceInventory(existingOffer, newData);
        }
        // 情况三：相同渠道数据，使用最新数据更新
        else if (existingChannel.equals(newChannel)) {
            needUpdate = updateSameChannelOffersDataForPriceInventory(existingOffer, newData);
        }

        if (needUpdate) {
            existingOffer.setUpdateTime(LocalDateTime.now());
            log.info("ProductDataOffers价格库存记录需要更新，SKU ID: {}", existingOffer.getSkuId());
        }

        return needUpdate;
    }

    /**
     * API Offers数据被爬虫数据更新：API有值则不更新，API无值则更新 - 价格库存专用版本
     */
    private static boolean updateApiOffersWithCrawlerDataForPriceInventory(ProductDataOffers existingOffer, ProductPriceInventoryDTO newData) {
        boolean updated = false;

        // 价格相关字段 - 只更新API数据中为空的字段
        if (existingOffer.getPrice() == null && newData.getPrice() != null) {
            existingOffer.setPrice(newData.getPrice());
            updated = true;
        }
        if (existingOffer.getListPrice() == null && newData.getListPrice() != null) {
            existingOffer.setListPrice(newData.getListPrice());
            updated = true;
        }
        if (existingOffer.getDiscount() == null && newData.getDiscount() != null) {
            existingOffer.setDiscount(newData.getDiscount());
            updated = true;
        }

        // 库存和销量 - 只更新API数据中为空的字段
        if (!ProductMatchingUtil.hasValue(existingOffer.getInventory()) && ProductMatchingUtil.hasValue(newData.getInventory())) {
            existingOffer.setInventory(newData.getInventory());
            updated = true;
        }
        // 更新时间字段
        if (newData.getPriceUpdateTime() != null) {
            existingOffer.setPriceUpdateTime(DateUtils.format(newData.getPriceUpdateTime(), "yyyy-MM-dd HH:mm:ss"));
            updated = true;
        }
        if (newData.getInventoryUpdateTime() != null) {
            existingOffer.setInventoryUpdateTime(newData.getInventoryUpdateTime());
            updated = true;
        }

        return updated;
    }

    /**
     * 爬虫Offers数据被API数据更新：API数据全部覆盖 - 价格库存专用版本
     */
    private static boolean updateCrawlerOffersWithApiDataForPriceInventory(ProductDataOffers existingOffer, ProductPriceInventoryDTO newData) {
        boolean updated = false;

        // 价格相关字段 - API有值就覆盖
        if (newData.getPrice() != null) {
            existingOffer.setPrice(newData.getPrice());
            updated = true;
        }
        if (newData.getListPrice() != null) {
            existingOffer.setListPrice(newData.getListPrice());
            updated = true;
        }
        if (newData.getDiscount() != null) {
            existingOffer.setDiscount(newData.getDiscount());
            updated = true;
        }

        // 库存 - API有值就覆盖
        if (ProductMatchingUtil.hasValue(newData.getInventory())) {
            existingOffer.setInventory(newData.getInventory());
            updated = true;
        }

        // 更新时间字段
        if (newData.getPriceUpdateTime() != null) {
            existingOffer.setPriceUpdateTime(DateUtils.format(newData.getPriceUpdateTime(), "yyyy-MM-dd HH:mm:ss"));
            updated = true;
        }
        if (newData.getInventoryUpdateTime() != null) {
            existingOffer.setInventoryUpdateTime(newData.getInventoryUpdateTime());
            updated = true;
        }

        // 更新数据渠道为API
        existingOffer.setDataChannel(DataChannelEnum.API.getCode());
        updated = true;

        return updated;
    }

    /**
     * 相同渠道Offers数据更新：使用最新数据 - 价格库存专用版本
     */
    private static boolean updateSameChannelOffersDataForPriceInventory(ProductDataOffers existingOffer, ProductPriceInventoryDTO newData) {
        boolean updated = false;

        // 价格相关字段 - 相同渠道，使用新数据更新有值的字段
        if (newData.getPrice() != null) {
            existingOffer.setPrice(newData.getPrice());
            updated = true;
        }
        if (newData.getListPrice() != null) {
            existingOffer.setListPrice(newData.getListPrice());
            updated = true;
        }
        if (newData.getDiscount() != null) {
            existingOffer.setDiscount(newData.getDiscount());
            updated = true;
        }

        // 库存和销量
        if (ProductMatchingUtil.hasValue(newData.getInventory())) {
            existingOffer.setInventory(newData.getInventory());
            updated = true;
        }

        // 更新时间字段
        if (newData.getPriceUpdateTime() != null) {
            existingOffer.setPriceUpdateTime(DateUtils.format(newData.getPriceUpdateTime(), "yyyy-MM-dd HH:mm:ss"));
            updated = true;
        }
        if (newData.getInventoryUpdateTime() != null) {
            existingOffer.setInventoryUpdateTime(newData.getInventoryUpdateTime());
            updated = true;
        }

        return updated;
    }

}

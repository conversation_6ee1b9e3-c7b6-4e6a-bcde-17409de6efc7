package ai.pricefox.mallfox.service.product.util;

import org.springframework.util.StringUtils;

/**
 * 商品匹配工具类
 * 提供忽略大小写和空格的字段匹配功能
 *
 * <AUTHOR>
 * @since 2025-01-11
 */
public class ProductMatchingUtil {

    /**
     * 标准化字符串：去除空格并转为小写
     *
     * @param str 原始字符串
     * @return 标准化后的字符串
     */
    public static String normalizeString(String str) {
        if (!StringUtils.hasText(str)) {
            return "";
        }
        return str.replaceAll("\\s+", "").toLowerCase();
    }

    /**
     * 忽略大小写和空格的字符串匹配
     *
     * @param str1 字符串1
     * @param str2 字符串2
     * @return 是否匹配
     */
    public static boolean matchIgnoreCaseAndSpace(String str1, String str2) {
        return normalizeString(str1).equals(normalizeString(str2));
    }

    /**
     * 检查字符串是否有有效值（非空且非空白）
     *
     * @param str 待检查的字符串
     * @return 是否有有效值
     */
    public static boolean hasValue(String str) {
        return StringUtils.hasText(str) && !str.trim().isEmpty();
    }

    /**
     * 安全的字符串比较（处理null值）
     *
     * @param str1 字符串1
     * @param str2 字符串2
     * @return 是否相等
     */
    public static boolean safeEquals(String str1, String str2) {
        if (str1 == null && str2 == null) {
            return true;
        }
        if (str1 == null || str2 == null) {
            return false;
        }
        return str1.equals(str2);
    }

    /**
     * 获取非空值，优先返回第一个参数
     *
     * @param primary 主要值
     * @param fallback 备用值
     * @return 非空的值
     */
    public static String getValueOrFallback(String primary, String fallback) {
        return hasValue(primary) ? primary : fallback;
    }
}

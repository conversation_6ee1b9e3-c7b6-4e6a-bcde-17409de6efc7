package ai.pricefox.mallfox.service.rules;

import ai.pricefox.mallfox.common.constant.RedisKeyConstants;
import ai.pricefox.mallfox.common.util.CacheUtil;
import ai.pricefox.mallfox.domain.standard.*;
import ai.pricefox.mallfox.service.standard.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 标准化数据缓存服务
 * 负责将标准库和匹配规则数据缓存到Redis中，减少数据库查询
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class StandardDataCacheService {

    private final CacheUtil cacheUtil;

    // 注入所有相关的服务
    private final StandardCategoryMappingService standardCategoryMappingService;
    private final StandardCategoryService standardCategoryService;
    private final TripartitePlatformService tripartitePlatformService;
    private final StandardAttributeService standardAttributeService;
    private final StandardAttributeMappingService standardAttributeMappingService;
    private final StandardAttributeValueService standardAttributeValueService;
    private final StandardAttributeValueMappingService standardAttributeValueMappingService;

    /**
     * 获取平台分类列表
     *
     * @return 平台分类映射列表
     */
    public List<StandardCategory> getPlatformCategory() {
        String key = RedisKeyConstants.PLATFORM_CATEGORY;
        Object cached = cacheUtil.getCache(key);
        if (cached != null) {
            log.debug("从缓存中获取平台分类数据");
            return (List<StandardCategory>) cached;
        }

        log.debug("从数据库中获取平台分类映射数据");
        List<StandardCategory> data = standardCategoryService.list();
        cacheUtil.setCache(key, data, RedisKeyConstants.STANDARD_DATA_TIMEOUT, java.util.concurrent.TimeUnit.SECONDS);
        return data;
    }

    /**
     * 获取平台分类映射列表
     *
     * @return 平台分类映射列表
     */
    public List<StandardCategoryMapping> getPlatformCategoryMapping() {
        String key = RedisKeyConstants.PLATFORM_CATEGORY_MAPPING;
        Object cached = cacheUtil.getCache(key);
        if (cached != null) {
            log.debug("从缓存中获取平台分类映射数据");
            return (List<StandardCategoryMapping>) cached;
        }

        log.debug("从数据库中获取平台分类映射数据");
        List<StandardCategoryMapping> data = standardCategoryMappingService.list();
        data = data.stream().filter(s->!s.getDeleted()).toList();
        cacheUtil.setCache(key, data, RedisKeyConstants.STANDARD_DATA_TIMEOUT, java.util.concurrent.TimeUnit.SECONDS);
        return data;
    }

    /**
     * 获取属性列表
     *
     * @return 属性列表
     */
    public List<StandardAttribute> getStandardAttribute() {
        String key = RedisKeyConstants.STANDARD_ATTRIBUTE;
        Object cached = cacheUtil.getCache(key);
        if (cached != null) {
            log.debug("从缓存中获取属性数据");
            return (List<StandardAttribute>) cached;
        }

        log.debug("从数据库中获取属性数据");
        List<StandardAttribute> data = standardAttributeService.list();
        cacheUtil.setCache(key, data, RedisKeyConstants.STANDARD_DATA_TIMEOUT, java.util.concurrent.TimeUnit.SECONDS);
        return data;
    }

    /**
     * 获取属性映射列表
     *
     * @return 属性映射列表
     */
    public List<StandardAttributeMapping> getAttributeMapping() {
        String key = RedisKeyConstants.STANDARD_ATTRIBUTE_MAPPING;
        Object cached = cacheUtil.getCache(key);
        if (cached != null) {
            log.debug("从缓存中获取属性映射数据");
            return (List<StandardAttributeMapping>) cached;
        }

        log.debug("从数据库中获取属性映射数据");
        List<StandardAttributeMapping> data = standardAttributeMappingService.list();
        cacheUtil.setCache(key, data, RedisKeyConstants.STANDARD_DATA_TIMEOUT, java.util.concurrent.TimeUnit.SECONDS);
        return data;
    }

    /**
     * 获取属性值列表
     *
     * @return 属性值列表
     */
    public List<StandardAttributeValue> getAttributeValue() {
        String key = RedisKeyConstants.STANDARD_ATTRIBUTE_VALUE;
        Object cached = cacheUtil.getCache(key);
        if (cached != null) {
            log.debug("从缓存中获取属性值数据");
            return (List<StandardAttributeValue>) cached;
        }

        log.debug("从数据库中获取属性值数据");
        List<StandardAttributeValue> data = standardAttributeValueService.list();
        cacheUtil.setCache(key, data, RedisKeyConstants.STANDARD_DATA_TIMEOUT, java.util.concurrent.TimeUnit.SECONDS);
        return data;
    }

    /**
     * 获取属性值映射列表
     *
     * @return 属性值映射列表
     */
    public List<StandardAttributeValueMapping> getAttributeValueMapping() {
        String key = RedisKeyConstants.STANDARD_ATTRIBUTE_VALUE_MAPPING;
        Object cached = cacheUtil.getCache(key);
        if (cached != null) {
            log.debug("从缓存中获取属性值映射数据");
            return (List<StandardAttributeValueMapping>) cached;
        }

        log.debug("从数据库中获取属性值映射数据");
        List<StandardAttributeValueMapping> data = standardAttributeValueMappingService.list();
        cacheUtil.setCache(key, data, RedisKeyConstants.STANDARD_DATA_TIMEOUT, java.util.concurrent.TimeUnit.SECONDS);
        return data;
    }

    /**
     * 获取第三方平台列表
     *
     * @return 第三方平台列表
     */
    public List<TripartitePlatform> getTripartitePlatform() {
        String key = RedisKeyConstants.TRIPARTITE_PLATFORM;
        Object cached = cacheUtil.getCache(key);
        if (cached != null) {
            log.debug("从缓存中获取第三方平台数据");
            return (List<TripartitePlatform>) cached;
        }

        log.debug("从数据库中获取第三方平台数据");
        List<TripartitePlatform> data = tripartitePlatformService.getAll();
        cacheUtil.setCache(key, data, RedisKeyConstants.STANDARD_DATA_TIMEOUT, java.util.concurrent.TimeUnit.SECONDS);
        return data;
    }

    /**
     * 清除所有标准化数据缓存
     */
    public void clearAllStandardDataCache() {
        cacheUtil.deleteCache(RedisKeyConstants.INVALID_VALUE_PATTERNS);
        cacheUtil.deleteCache(RedisKeyConstants.NORMALIZATION_ALIAS);
        cacheUtil.deleteCache(RedisKeyConstants.NORMALIZATION_LIBRARY);
        cacheUtil.deleteCache(RedisKeyConstants.NORMALIZATION_RULES);
        cacheUtil.deleteCache(RedisKeyConstants.PLATFORM_CATEGORY_MAPPING);
        cacheUtil.deleteCache(RedisKeyConstants.STANDARD_CATEGORY);
        cacheUtil.deleteCache(RedisKeyConstants.STANDARD_CATEGORY_MAPPING);
        cacheUtil.deleteCache(RedisKeyConstants.STANDARD_ATTRIBUTE);
        cacheUtil.deleteCache(RedisKeyConstants.STANDARD_ATTRIBUTE_VALUE);
        cacheUtil.deleteCache(RedisKeyConstants.STANDARD_ATTRIBUTE_MAPPING);
        cacheUtil.deleteCache(RedisKeyConstants.STANDARD_ATTRIBUTE_VALUE_MAPPING);
        cacheUtil.deleteCache(RedisKeyConstants.TRIPARTITE_PLATFORM);
        log.info("已清除所有标准化数据缓存");
    }

    /**
     * 监听实体保存事件，清除相关缓存
     * 只有当标准化相关的实体发生变化时才清除对应缓存
     */
    @EventListener
    public void handleEntitySaveEvent(Object event) {
        // 检查事件类型，只对标准化相关的实体进行缓存清除
        if (event instanceof ai.pricefox.mallfox.domain.standard.InvalidValuePatterns) {
            cacheUtil.deleteCache(RedisKeyConstants.INVALID_VALUE_PATTERNS);
        }
        if (event instanceof ai.pricefox.mallfox.domain.standard.NormalizationAlias) {
            cacheUtil.deleteCache(RedisKeyConstants.NORMALIZATION_ALIAS);
        }
        if (event instanceof ai.pricefox.mallfox.domain.standard.NormalizationLibrary) {
            cacheUtil.deleteCache(RedisKeyConstants.NORMALIZATION_LIBRARY);
        }
        if (event instanceof ai.pricefox.mallfox.domain.standard.NormalizationRules) {
            cacheUtil.deleteCache(RedisKeyConstants.NORMALIZATION_RULES);
        }
        if (event instanceof StandardCategoryMapping) {
            cacheUtil.deleteCache(RedisKeyConstants.PLATFORM_CATEGORY_MAPPING);
        }
        if (event instanceof ai.pricefox.mallfox.domain.standard.StandardCategory) {
            cacheUtil.deleteCache(RedisKeyConstants.STANDARD_CATEGORY);
        }
        if (event instanceof ai.pricefox.mallfox.domain.standard.StandardCategoryMapping) {
            cacheUtil.deleteCache(RedisKeyConstants.STANDARD_CATEGORY_MAPPING);
        }
        if (event instanceof ai.pricefox.mallfox.domain.standard.StandardAttribute) {
            cacheUtil.deleteCache(RedisKeyConstants.STANDARD_ATTRIBUTE);
        }
        if (event instanceof ai.pricefox.mallfox.domain.standard.StandardAttributeMapping) {
            cacheUtil.deleteCache(RedisKeyConstants.STANDARD_ATTRIBUTE_MAPPING);
        }
        if (event instanceof ai.pricefox.mallfox.domain.standard.StandardAttributeValue) {
            cacheUtil.deleteCache(RedisKeyConstants.STANDARD_ATTRIBUTE_VALUE);
        }
        if (event instanceof ai.pricefox.mallfox.domain.standard.StandardAttributeValueMapping) {
            cacheUtil.deleteCache(RedisKeyConstants.STANDARD_ATTRIBUTE_VALUE_MAPPING);
        }
        if (event instanceof ai.pricefox.mallfox.domain.standard.TripartitePlatform) {
            cacheUtil.deleteCache(RedisKeyConstants.TRIPARTITE_PLATFORM);
        }
    }

}

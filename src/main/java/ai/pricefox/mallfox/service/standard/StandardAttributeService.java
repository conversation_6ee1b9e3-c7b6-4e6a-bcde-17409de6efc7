package ai.pricefox.mallfox.service.standard;

import ai.pricefox.mallfox.domain.standard.StandardAttribute;
import ai.pricefox.mallfox.vo.attribute.*;
import ai.pricefox.mallfox.vo.base.CommonResult;
import ai.pricefox.mallfox.vo.base.PageResult;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 标准属性库Service接口
 * <AUTHOR>
 * @date 2025-08-02
 */
public interface StandardAttributeService extends IService<StandardAttribute> {

    /**
     * 创建属性
     *
     * @param reqVO 创建请求
     * @return 创建结果
     */
    CommonResult<AttributeDetailRespVO> createAttribute(AttributeCreateReqVO reqVO);

    /**
     * 更新属性
     *
     * @param reqVO 更新请求
     * @return 更新结果
     */
    CommonResult<AttributeDetailRespVO> updateAttribute(AttributeUpdateReqVO reqVO);

    /**
     * 分页查询属性列表
     *
     * @param reqVO 查询条件
     * @return 分页结果
     */
    CommonResult<PageResult<AttributeDetailRespVO>> getAttributePage(AttributePageReqVO reqVO);

    /**
     * 根据ID获取属性详情
     *
     * @param id 属性ID
     * @return 属性详情
     */
    CommonResult<AttributeDetailRespVO> getAttributeById(Long id);

    /**
     * 删除属性
     *
     * @param id 属性ID
     * @return 删除结果
     */
    CommonResult<Boolean> deleteAttribute(Long id);

    /**
     * 批量删除属性
     *
     * @param reqVO 批量删除请求
     * @return 删除结果
     */
    CommonResult<String> batchDeleteAttributes(AttributeBatchDeleteReqVO reqVO);

    /**
     * 批量更新类目属性和属性值
     *
     * @param file Excel文件
     * @return 更新结果
     */
    CommonResult<AttributeBatchUpdateResultRespVO> batchUpdateAttributes(MultipartFile file);

    /**
     * 下载更新模版
     *
     * @return 模版文件信息（URL和文件名）
     */
    CommonResult<AttributeFileRespVO> downloadTemplate();

    /**
     * 导出属性数据
     *
     * @param reqVO 导出条件
     * @return 导出文件信息（URL和文件名）
     */
    CommonResult<AttributeFileRespVO> exportAttributes(AttributePageReqVO reqVO);

    // ========== 规格管理相关接口 ==========

    /**
     * 新增规格
     *
     * @param id 属性ID
     * @return 操作结果
     */
    CommonResult<Boolean> createSpec(Long id);

    /**
     * 删除规格
     *
     * @param id 属性ID
     * @return 操作结果
     */
    CommonResult<Boolean> deleteSpec(Long id);

    /**
     * 规格分页列表
     *
     * @param reqVO 查询条件
     * @return 分页结果
     */
    CommonResult<PageResult<SpecRespVO>> getSpecPage(SpecPageReqVO reqVO);

    /**
     * 属性列表（用于规格选择）
     *
     * @param reqVO 查询条件
     * @return 属性列表
     */
    CommonResult<List<AttributeListRespVO>> getAttributeList(AttributeListReqVO reqVO);
}
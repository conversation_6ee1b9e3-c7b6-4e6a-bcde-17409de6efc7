package ai.pricefox.mallfox.service.standard;

import ai.pricefox.mallfox.domain.standard.StandardAttributeValue;
import ai.pricefox.mallfox.vo.attribute.*;
import ai.pricefox.mallfox.vo.base.CommonResult;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 标准属性值Service接口
 * <AUTHOR>
 * @date 2025-08-02
 */
public interface StandardAttributeValueService extends IService<StandardAttributeValue> {

    /**
     * 创建属性值
     *
     * @param reqVO 创建请求
     * @return 创建结果
     */
    CommonResult<AttributeValueRespVO> createAttributeValue(AttributeValueCreateReqVO reqVO);

    /**
     * 更新属性值
     *
     * @param reqVO 更新请求
     * @return 更新结果
     */
    CommonResult<AttributeValueRespVO> updateAttributeValue(AttributeValueUpdateReqVO reqVO);

    /**
     * 删除属性值
     *
     * @param id 属性值ID
     * @return 删除结果
     */
    CommonResult<Boolean> deleteAttributeValue(Long id);

    /**
     * 批量删除属性值
     *
     * @param reqVO 批量删除请求
     * @return 删除结果
     */
    CommonResult<String> batchDeleteAttributeValues(AttributeValueBatchDeleteReqVO reqVO);

    /**
     * 导出属性值数据
     *
     * @param reqVO 导出条件
     * @return 导出文件信息（URL和文件名）
     */
    CommonResult<AttributeFileRespVO> exportAttributeValues(AttributeValueExportReqVO reqVO);
}
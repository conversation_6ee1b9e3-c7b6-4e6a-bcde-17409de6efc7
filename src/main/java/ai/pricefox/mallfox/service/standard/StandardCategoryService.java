package ai.pricefox.mallfox.service.standard;

import ai.pricefox.mallfox.domain.standard.StandardCategory;
import ai.pricefox.mallfox.vo.base.CommonResult;
import ai.pricefox.mallfox.vo.base.PageResult;
import ai.pricefox.mallfox.vo.category.*;
import cn.hutool.core.lang.tree.Tree;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 标准品类库Service接口
 * <AUTHOR>
 * @date 2025-07-24
 */
public interface StandardCategoryService extends IService<StandardCategory> {

    /**
     * 获取分类树形结构
     *
     * @param reqVO 查询条件
     * @return 分类树
     */
    CommonResult<List<CategoryTreeRespVO>> getCategoryTree(CategoryListReqVO reqVO);

    /**
     * 分页查询分类列表
     *
     * @param reqVO 查询条件
     * @return 分页结果
     */
    CommonResult<PageResult<CategoryDetailRespVO>> getCategoryList(CategoryListReqVO reqVO);

    /**
     * 创建分类
     *
     * @param reqVO 创建请求
     * @return 创建结果
     */
    CommonResult<CategoryDetailRespVO> createCategory(CategoryCreateReqVO reqVO);

    /**
     * 更新分类
     *
     * @param reqVO 更新请求
     * @return 更新结果
     */
    CommonResult<CategoryDetailRespVO> updateCategory(CategoryUpdateReqVO reqVO);

    /**
     * 根据ID获取分类详情
     *
     * @param id 分类ID
     * @return 分类详情
     */
    CommonResult<CategoryDetailRespVO> getCategoryById(Long id);

    /**
     * 删除分类
     *
     * @param id 分类ID
     * @return 删除结果
     */
    CommonResult<Boolean> deleteCategory(Long id);

    /**
     * 导出分类数据
     *
     * @param reqVO 导出条件
     * @return 导出文件路径
     */
    CommonResult<String> exportCategories(CategoryListReqVO reqVO);
}
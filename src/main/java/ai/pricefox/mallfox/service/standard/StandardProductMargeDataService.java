package ai.pricefox.mallfox.service.standard;

import ai.pricefox.mallfox.domain.standard.StandardProductMargeData;
import ai.pricefox.mallfox.model.param.ProductDataSearchRequest;
import ai.pricefox.mallfox.vo.product.HeaderInfoVO;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

/**
 * 标准商品合并数据服务接口
 *
 * <AUTHOR>
public interface StandardProductMargeDataService extends IService<StandardProductMargeData> {

    /**
     * 保存或更新标准商品合并数据
     *
     * @param standardProductMargeData 标准商品合并数据
     */
    StandardProductMargeData saveOrUpdateByProductIdentifier(StandardProductMargeData standardProductMargeData);

    /**
     * 获取商品数据表头信息
     *
     * @return 表头信息列表
     */
    List<HeaderInfoVO> getProductHeaders();

    /**
     * 根据动态表头分页查询商品数据
     *
     * @param request 查询条件
     * @return 分页结果
     */
    Page<Map<String, Object>> getPageByFields(ProductDataSearchRequest request);
}
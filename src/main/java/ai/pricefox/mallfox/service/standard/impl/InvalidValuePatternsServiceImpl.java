package ai.pricefox.mallfox.service.standard.impl;

import ai.pricefox.mallfox.domain.standard.InvalidValuePatterns;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import ai.pricefox.mallfox.mapper.standard.InvalidValuePatternsMapper;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
public class InvalidValuePatternsServiceImpl extends ServiceImpl<InvalidValuePatternsMapper, InvalidValuePatterns> implements IService<InvalidValuePatterns> {

}





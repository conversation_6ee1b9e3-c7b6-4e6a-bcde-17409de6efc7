package ai.pricefox.mallfox.service.standard.impl;

import ai.pricefox.mallfox.domain.standard.PlatformFieldMapping;
import ai.pricefox.mallfox.enums.TransformLogicEnum;
import ai.pricefox.mallfox.listener.PlatformFieldMappingExcelListener;
import ai.pricefox.mallfox.mapper.standard.PlatformFieldMappingMapper;
import ai.pricefox.mallfox.model.excel.PlatformFieldMappingExcelEntity;
import ai.pricefox.mallfox.model.vo.PlatformFieldMappingImportRespVO;
import ai.pricefox.mallfox.service.standard.PlatformFieldMappingImportService;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.Date;
import java.util.List;

/**
 * 平台字段映射导入服务实现
 * 使用EasyExcel进行Excel文件读取
 *
 * <AUTHOR>
 * @since 2025-07-26
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PlatformFieldMappingImportServiceImpl implements PlatformFieldMappingImportService {

    private final PlatformFieldMappingMapper platformFieldMappingMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PlatformFieldMappingImportRespVO importExcel(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            return PlatformFieldMappingImportRespVO.failure("Excel文件不能为空");
        }

        try {
            // 使用EasyExcel读取数据
            List<PlatformFieldMappingExcelEntity> excelData = parseExcelFile(file);
            
            if (excelData.isEmpty()) {
                return PlatformFieldMappingImportRespVO.failure("Excel文件中没有有效数据");
            }

            int successCount = 0;
            int failureCount = 0;
            int skippedCount = 0;
            StringBuilder errorMessages = new StringBuilder();

            for (int i = 0; i < excelData.size(); i++) {
                try {
                    PlatformFieldMappingExcelEntity entity = excelData.get(i);
                    
                    // 检查sourceFieldName，如果为空或者是"-"则跳过该行
                    if (StrUtil.isBlank(entity.getSourceFieldName()) || "-".equals(entity.getSourceFieldName().trim())) {
                        skippedCount++;
                        log.debug("第{}行sourceFieldName为空或为'-'，跳过处理", i + 2);
                        continue;
                    }
                    
                    // 验证必填字段（包括平台编码）
                    String validationError = validateExcelData(entity, i + 2); // +2 因为从第2行开始
                    if (validationError != null) {
                        failureCount++;
                        errorMessages.append(validationError).append("; ");
                        continue;
                    }

                    // 转换并保存到数据库
                    PlatformFieldMapping mapping = convertToEntity(entity);
                    platformFieldMappingMapper.insert(mapping);
                    successCount++;
                    
                } catch (Exception e) {
                    failureCount++;
                    errorMessages.append("第").append(i + 2).append("行数据保存失败: ").append(e.getMessage()).append("; ");
                    log.error("保存第{}行数据失败", i + 2, e);
                }
            }

            log.info("Excel导入完成，总数: {}, 成功: {}, 失败: {}, 跳过: {}", excelData.size(), successCount, failureCount, skippedCount);
            
            PlatformFieldMappingImportRespVO result = PlatformFieldMappingImportRespVO.success(excelData.size(), successCount, failureCount, skippedCount);
            if (failureCount > 0) {
                result.setErrorMessage(errorMessages.toString());
            }
            return result;

        } catch (Exception e) {
            log.error("Excel导入处理失败", e);
            return PlatformFieldMappingImportRespVO.failure("Excel导入处理失败: " + e.getMessage());
        }
    }

    /**
     * 使用EasyExcel解析Excel文件
     */
    private List<PlatformFieldMappingExcelEntity> parseExcelFile(MultipartFile file) throws IOException {
        PlatformFieldMappingExcelListener listener = new PlatformFieldMappingExcelListener();
        
        // 使用EasyExcel读取文件
        EasyExcel.read(file.getInputStream(), PlatformFieldMappingExcelEntity.class, listener)
                .sheet()
                .headRowNumber(1) // 表头占1行
                .doRead();
        
        return listener.getDataList();
    }

    /**
     * 验证Excel数据
     */
    private String validateExcelData(PlatformFieldMappingExcelEntity entity, int rowNum) {
        StringBuilder errors = new StringBuilder();
        
        if (!StringUtils.hasText(entity.getPlatformCode())) {
            errors.append("第").append(rowNum).append("行平台编码不能为空; ");
        }
        
        if (!StringUtils.hasText(entity.getStandardFieldCode())) {
            errors.append("第").append(rowNum).append("行字段编码不能为空; ");
        }
        
//        if (!StringUtils.hasText(entity.getSourceFieldName())) {
//            errors.append("第").append(rowNum).append("行字段名称-英文不能为空; ");
//        }
        
        if (StringUtils.hasText(entity.getTransformLogic())) {
            try {
                TransformLogicEnum.valueOf(entity.getTransformLogic().toUpperCase());
            } catch (IllegalArgumentException e) {
                errors.append("第").append(rowNum).append("行转换逻辑关系值无效: ").append(entity.getTransformLogic()).append("; ");
            }
        }
        
        return errors.length() > 0 ? errors.toString() : null;
    }

    /**
     * 转换为实体对象
     */
    private PlatformFieldMapping convertToEntity(PlatformFieldMappingExcelEntity entity) {
        PlatformFieldMapping mapping = new PlatformFieldMapping();
        
        // 设置从Excel读取的平台编码
        mapping.setPlatformCode(entity.getPlatformCode());
        
        // 设置从Excel读取的其他数据
        mapping.setCreateDate(entity.getCreateDate() != null ? entity.getCreateDate() : new Date());
        //mapping.setUpdateDate(new Date());
        mapping.setCreateUsername(entity.getCreateUsername());
        mapping.setStandardFieldCode(entity.getStandardFieldCode());
        mapping.setSourceFieldName(StrUtil.isBlank(entity.getSourceFieldName()) ? "" : entity.getSourceFieldName());
        mapping.setCustomScript(entity.getCustomScript());
        mapping.setPositionInfo(entity.getPositionInfo());
        
        // 转换逻辑关系枚举
        if (StringUtils.hasText(entity.getTransformLogic())) {
            try {
                mapping.setTransformLogic(TransformLogicEnum.valueOf(entity.getTransformLogic().toUpperCase()));
            } catch (IllegalArgumentException e) {
                mapping.setTransformLogic(TransformLogicEnum.DIRECT); // 默认值
            }
        } else {
            mapping.setTransformLogic(TransformLogicEnum.DIRECT); // 默认值
        }
        
        return mapping;
    }
}
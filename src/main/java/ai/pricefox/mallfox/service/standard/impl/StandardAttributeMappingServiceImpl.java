package ai.pricefox.mallfox.service.standard.impl;

import ai.pricefox.mallfox.domain.standard.StandardAttributeMapping;
import ai.pricefox.mallfox.mapper.standard.StandardAttributeMappingMapper;
import ai.pricefox.mallfox.service.standard.StandardAttributeMappingService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 映射属性名Service实现类
 * <AUTHOR>
 * @date 2025-08-02
 */
@Service
@AllArgsConstructor
public class StandardAttributeMappingServiceImpl extends ServiceImpl<StandardAttributeMappingMapper, StandardAttributeMapping> implements StandardAttributeMappingService {

    private final StandardAttributeMappingMapper standardAttributeMappingMapper;

}
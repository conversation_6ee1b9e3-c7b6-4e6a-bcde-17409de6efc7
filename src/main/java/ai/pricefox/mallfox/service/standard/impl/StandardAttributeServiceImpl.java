package ai.pricefox.mallfox.service.standard.impl;

import ai.pricefox.mallfox.common.exception.enums.ErrorCodeConstants;
import ai.pricefox.mallfox.common.util.IdGenerator;
import ai.pricefox.mallfox.common.util.AliOssUtil;
import ai.pricefox.mallfox.common.util.CacheUtil;
import ai.pricefox.mallfox.domain.standard.StandardAttribute;
import ai.pricefox.mallfox.domain.standard.StandardCategory;
import ai.pricefox.mallfox.domain.standard.StandardAttributeValue;
import ai.pricefox.mallfox.mapper.standard.StandardAttributeMapper;
import ai.pricefox.mallfox.mapper.standard.StandardCategoryMapper;
import ai.pricefox.mallfox.mapper.standard.StandardAttributeValueMapper;
import ai.pricefox.mallfox.model.excel.AttributeImportExcelEntity;
import ai.pricefox.mallfox.model.excel.AttributeExportExcelEntity;
import ai.pricefox.mallfox.model.excel.AttributeTemplateExcelEntity;
import ai.pricefox.mallfox.service.standard.StandardAttributeService;
import ai.pricefox.mallfox.utils.AdminTokenUtil;
import ai.pricefox.mallfox.vo.attribute.*;
import ai.pricefox.mallfox.vo.base.CommonResult;
import ai.pricefox.mallfox.vo.base.PageResult;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static ai.pricefox.mallfox.common.exception.util.ServiceExceptionUtil.exception;

/**
 * 标准属性库Service实现类
 * <AUTHOR>
 * @date 2025-08-02
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class StandardAttributeServiceImpl extends ServiceImpl<StandardAttributeMapper, StandardAttribute> implements StandardAttributeService {

    private final StandardAttributeMapper standardAttributeMapper;
    private final StandardCategoryMapper standardCategoryMapper;
    private final StandardAttributeValueMapper standardAttributeValueMapper;
    private final AliOssUtil aliOssUtil;
    private final CacheUtil cacheUtil;
    private final IdGenerator idGenerator;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<AttributeDetailRespVO> createAttribute(AttributeCreateReqVO reqVO) {
        // 1. 检查分类是否存在且激活
        StandardCategory category = standardCategoryMapper.findActiveCategoryByCode(reqVO.getCategoryCode());
        if (category == null) {
            throw exception(ErrorCodeConstants.ATTRIBUTE_CATEGORY_NOT_EXIST);
        }

        // 2. 生成属性编码（自增方式，无需检查重复）
        String attributeCode = idGenerator.generateAttributeCode();

        // 3. 构建属性实体
        StandardAttribute attribute = buildAttributeEntity(reqVO, attributeCode, category);

        // 4. 保存属性
        int result = standardAttributeMapper.insert(attribute);
        if (result <= 0) {
            throw exception(ErrorCodeConstants.ATTRIBUTE_CREATE_FAILED);
        }

        // 5. 查询并返回详情（包含属性值列表）
        AttributeDetailRespVO respVO = convertToDetailRespVO(attribute);
        return CommonResult.success(respVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<AttributeDetailRespVO> updateAttribute(AttributeUpdateReqVO reqVO) {
        // 1. 检查属性是否存在
        StandardAttribute attribute = standardAttributeMapper.selectById(reqVO.getId());
        if (attribute == null || Boolean.TRUE.equals(attribute.getDeleted())) {
            throw exception(ErrorCodeConstants.ATTRIBUTE_INFO_NOT_EXIST);
        }

        // 2. 更新属性信息
        attribute.setAttributeNameEn(reqVO.getAttributeNameEn());
        attribute.setAttributeNameCn(reqVO.getAttributeNameCn());
        attribute.setUpdateDate(LocalDateTime.now());
        attribute.setUpdateUsername(AdminTokenUtil.getCurrentUsername());

        // 3. 保存更新
        int result = standardAttributeMapper.updateById(attribute);
        if (result <= 0) {
            throw exception(ErrorCodeConstants.ATTRIBUTE_UPDATE_FAILED);
        }

        // 4. 查询并返回详情（包含属性值列表）
        AttributeDetailRespVO respVO = convertToDetailRespVO(attribute);
        return CommonResult.success(respVO);
    }

    @Override
    public CommonResult<PageResult<AttributeDetailRespVO>> getAttributePage(AttributePageReqVO reqVO) {
        // 1. 分页查询属性
        Page<StandardAttribute> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        Page<StandardAttribute> result = standardAttributeMapper.selectAttributePageList(page, reqVO);

        // 2. 转换为响应VO并构建类目路径
        List<AttributeDetailRespVO> respVOList = result.getRecords().stream()
                .map(this::convertToDetailRespVO)
                .collect(Collectors.toList());

        PageResult<AttributeDetailRespVO> pageResult = new PageResult<>(respVOList, result.getTotal());
        return CommonResult.success(pageResult);
    }

    @Override
    public CommonResult<AttributeDetailRespVO> getAttributeById(Long id) {
        StandardAttribute attribute = standardAttributeMapper.selectById(id);
        if (attribute == null || Boolean.TRUE.equals(attribute.getDeleted())) {
            throw exception(ErrorCodeConstants.ATTRIBUTE_INFO_NOT_EXIST);
        }

        // 使用包含属性值列表的转换方法
        AttributeDetailRespVO respVO = convertToDetailRespVOWithValues(attribute);
        return CommonResult.success(respVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<Boolean> deleteAttribute(Long id) {
        // 1. 检查属性是否存在
        StandardAttribute attribute = standardAttributeMapper.selectById(id);
        if (attribute == null || Boolean.TRUE.equals(attribute.getDeleted())) {
            throw exception(ErrorCodeConstants.ATTRIBUTE_INFO_NOT_EXIST);
        }

        // 2. 检查映射关系
        long mappingCount = standardAttributeMapper.countAttributeMappings(attribute.getAttributeCode());
        if (mappingCount > 0) {
            throw exception(ErrorCodeConstants.ATTRIBUTE_HAS_MAPPING);
        }

        // 3. 逻辑删除
        attribute.setDeleted(true);
        attribute.setUpdateDate(LocalDateTime.now());
        attribute.setUpdateUsername(AdminTokenUtil.getCurrentUsername());

        int result = standardAttributeMapper.updateById(attribute);
        return CommonResult.success(result > 0);
    }

    /**
     * 构建属性实体
     */
    private StandardAttribute buildAttributeEntity(AttributeCreateReqVO reqVO, String attributeCode, StandardCategory category) {
        StandardAttribute attribute = new StandardAttribute();
        attribute.setAttributeCode(attributeCode);
        attribute.setAttributeNameEn(reqVO.getAttributeNameEn());
        attribute.setAttributeNameCn(reqVO.getAttributeNameCn());
        attribute.setStandardCategoryCode(category.getCategoryCode());
        attribute.setStandardCategoryNameEn(category.getCategoryNameEn());
        attribute.setStandardCategoryNameCn(category.getCategoryNameCn());
        attribute.setDeleted(false);
        attribute.setCreateDate(LocalDateTime.now());
        attribute.setCreateUsername(AdminTokenUtil.getCurrentUsername());
        return attribute;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<String> batchDeleteAttributes(AttributeBatchDeleteReqVO reqVO) {
        // 1. 检查哪些属性有映射关系
        List<Long> idsWithMappings = standardAttributeMapper.selectIdsWithMappings(reqVO.getIds());

        // 2. 过滤掉有映射关系的属性ID
        List<Long> idsToDelete = reqVO.getIds().stream()
                .filter(id -> !idsWithMappings.contains(id))
                .collect(Collectors.toList());

        // 3. 批量删除可删除的属性
        int deletedCount = 0;
        if (!idsToDelete.isEmpty()) {
            deletedCount = standardAttributeMapper.batchLogicalDelete(idsToDelete, AdminTokenUtil.getCurrentUsername());
        }

        // 4. 构建返回消息
        String message;
        if (idsWithMappings.isEmpty()) {
            message = "批量删除成功，共删除 " + deletedCount + " 个属性";
        } else {
            message = "存在属性或属性下的属性值有映射关系，无法进行删除，已自动过滤。成功删除 " + deletedCount + " 个，过滤 " + idsWithMappings.size() + " 个";
        }

        return CommonResult.success(message);
    }

    @Override
    public CommonResult<AttributeFileRespVO> downloadTemplate() {
        try {
            // 1. 先检查缓存
            CacheUtil.AttributeTemplateCache cachedTemplate = cacheUtil.getAttributeTemplate();
            if (cachedTemplate != null) {
                log.info("从缓存获取属性模板文件: {}", cachedTemplate.getUrl());
                return CommonResult.success(new AttributeFileRespVO(cachedTemplate.getUrl(), cachedTemplate.getFileName()));
            }

            // 2. 缓存不存在，生成新的模板文件
            // 创建模板数据（空数据，仅用于生成表头）
            List<AttributeTemplateExcelEntity> templateList = new ArrayList<>();

            // 生成文件名
            String fileName = "属性批量更新模板_" + DateUtil.format(DateUtil.date(), "yyyyMMdd") + ".xlsx";

            // 使用ByteArrayOutputStream生成Excel文件到内存
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            EasyExcel.write(outputStream, AttributeTemplateExcelEntity.class)
                    .sheet("属性批量更新模板")
                    .doWrite(templateList);

            // 将ByteArrayOutputStream转换为InputStream
            ByteArrayInputStream inputStream = new ByteArrayInputStream(outputStream.toByteArray());

            // 上传到OSS
            String ossUrl = aliOssUtil.uploadInputStream(inputStream, fileName, "templates/attributes");

            // 3. 缓存文件信息（15天）
            cacheUtil.setAttributeTemplate(ossUrl, fileName);

            log.info("属性模板文件生成成功，文件已上传到OSS并缓存: {}", ossUrl);
            return CommonResult.success(new AttributeFileRespVO(ossUrl, fileName));
        } catch (Exception e) {
            log.error("生成属性模板文件失败", e);
            throw exception(ErrorCodeConstants.ATTRIBUTE_TEMPLATE_GENERATE_FAILED);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<AttributeBatchUpdateResultRespVO> batchUpdateAttributes(MultipartFile file) {
        try {
            // 1. 读取Excel文件
            List<AttributeImportExcelEntity> importList = EasyExcel.read(file.getInputStream())
                    .head(AttributeImportExcelEntity.class)
                    .sheet()
                    .doReadSync();

            if (importList.isEmpty()) {
                throw exception(ErrorCodeConstants.ATTRIBUTE_EXCEL_EMPTY);
            }

            // 2. 验证表头格式
            // TODO: 实现表头验证逻辑

            // 3. 处理数据
            int totalCount = importList.size();
            int successCount = 0;
            int failedCount = 0;
            List<String> errorMessages = new ArrayList<>();

            for (AttributeImportExcelEntity importEntity : importList) {
                try {
                    // 验证必填字段
                    if (StrUtil.isBlank(importEntity.getStandardCategoryCode())) {
                        errorMessages.add("类目编码不能为空");
                        failedCount++;
                        continue;
                    }

                    // 检查分类是否存在且激活
                    StandardCategory category = standardCategoryMapper.findActiveCategoryByCode(importEntity.getStandardCategoryCode().trim());
                    if (category == null) {
                        errorMessages.add("类目编码 " + importEntity.getStandardCategoryCode() + " 不存在或未激活");
                        failedCount++;
                        continue;
                    }

                    // 处理属性
                    boolean attributeProcessed = processAttribute(importEntity, category);

                    // 处理属性值
                    boolean valueProcessed = processAttributeValue(importEntity);

                    if (attributeProcessed || valueProcessed) {
                        successCount++;
                    } else {
                        failedCount++;
                        errorMessages.add("处理记录失败: " + importEntity.getAttributeNameEn());
                    }

                } catch (Exception e) {
                    log.error("处理导入记录时发生异常", e);
                    failedCount++;
                    errorMessages.add("处理异常: " + e.getMessage());
                }
            }

            // 4. 构建返回结果
            AttributeBatchUpdateResultRespVO result = new AttributeBatchUpdateResultRespVO();
            result.setTotalCount(totalCount);
            result.setSuccessCount(successCount);
            result.setFailedCount(failedCount);
            result.setMessage("批量更新完成，共处理 " + totalCount + " 条记录，成功 " + successCount + " 条，失败 " + failedCount + " 条");

            return CommonResult.success(result);

        } catch (Exception e) {
            log.error("批量更新属性失败", e);
            throw exception(ErrorCodeConstants.ATTRIBUTE_BATCH_UPDATE_FAILED);
        }
    }

    @Override
    public CommonResult<AttributeFileRespVO> exportAttributes(AttributePageReqVO reqVO) {
        try {
            // 1. 查询所有符合条件的属性数据（不分页）
            List<StandardAttribute> attributeList = standardAttributeMapper.selectAttributeExportList(reqVO);

            // 2. 转换为导出实体
            List<AttributeExportExcelEntity> exportList = attributeList.stream()
                    .map(this::convertToDetailRespVO)
                    .map(this::convertToExportEntity)
                    .collect(Collectors.toList());

            // 3. 生成文件名
            String fileName = "商品属性_" + DateUtil.format(DateUtil.date(), "MM_dd") + ".xlsx";

            // 4. 使用ByteArrayOutputStream生成Excel文件到内存
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            EasyExcel.write(outputStream, AttributeExportExcelEntity.class)
                    .sheet("商品属性")
                    .doWrite(exportList);

            // 5. 将ByteArrayOutputStream转换为InputStream
            ByteArrayInputStream inputStream = new ByteArrayInputStream(outputStream.toByteArray());

            // 6. 上传到OSS
            String ossUrl = aliOssUtil.uploadInputStream(inputStream, fileName, "exports/attributes");

            log.info("属性数据导出成功，文件已上传到OSS: {}", ossUrl);
            return CommonResult.success(new AttributeFileRespVO(ossUrl, fileName));
        } catch (Exception e) {
            log.error("导出属性数据失败", e);
            throw exception(ErrorCodeConstants.ATTRIBUTE_EXPORT_FAILED);
        }
    }

    /**
     * 处理属性数据
     */
    private boolean processAttribute(AttributeImportExcelEntity importEntity, StandardCategory category) {
        try {
            if (StrUtil.isNotBlank(importEntity.getAttributeCode())) {
                // 更新现有属性
                StandardAttribute existingAttribute = standardAttributeMapper.selectByAttributeCode(importEntity.getAttributeCode().trim());
                if (existingAttribute != null) {
                    if (StrUtil.isNotBlank(importEntity.getAttributeNameEn())) {
                        existingAttribute.setAttributeNameEn(importEntity.getAttributeNameEn().trim());
                    }
                    if (StrUtil.isNotBlank(importEntity.getAttributeNameCn())) {
                        existingAttribute.setAttributeNameCn(importEntity.getAttributeNameCn().trim());
                    }
                    existingAttribute.setUpdateDate(LocalDateTime.now());
                    existingAttribute.setUpdateUsername(AdminTokenUtil.getCurrentUsername());

                    return standardAttributeMapper.updateById(existingAttribute) > 0;
                }
            } else if (StrUtil.isNotBlank(importEntity.getAttributeNameEn())) {
                // 新增属性
                String attributeCode = idGenerator.generateAttributeCode();

                StandardAttribute newAttribute = new StandardAttribute();
                newAttribute.setAttributeCode(attributeCode);
                newAttribute.setAttributeNameEn(importEntity.getAttributeNameEn().trim());
                newAttribute.setAttributeNameCn(StrUtil.isNotBlank(importEntity.getAttributeNameCn()) ?
                        importEntity.getAttributeNameCn().trim() : null);
                newAttribute.setStandardCategoryCode(category.getCategoryCode());
                newAttribute.setStandardCategoryNameEn(category.getCategoryNameEn());
                newAttribute.setStandardCategoryNameCn(category.getCategoryNameCn());
                newAttribute.setDeleted(false);
                newAttribute.setCreateDate(LocalDateTime.now());
                newAttribute.setCreateUsername(AdminTokenUtil.getCurrentUsername());

                return standardAttributeMapper.insert(newAttribute) > 0;
            }
            return false;
        } catch (Exception e) {
            log.error("处理属性数据失败", e);
            return false;
        }
    }

    /**
     * 处理属性值数据
     */
    private boolean processAttributeValue(AttributeImportExcelEntity importEntity) {
        try {
            // 如果没有属性值相关数据，直接返回true
            if (StrUtil.isBlank(importEntity.getValueEn()) && StrUtil.isBlank(importEntity.getValueCode())) {
                return true;
            }

            // 需要先确定属性编码
            String attributeCode = importEntity.getAttributeCode();
            if (StrUtil.isBlank(attributeCode)) {
                // 如果没有属性编码，尝试根据属性名称查找
                if (StrUtil.isNotBlank(importEntity.getAttributeNameEn())) {
                    StandardAttribute attribute = standardAttributeMapper.selectByNameAndCategoryCode(
                            importEntity.getAttributeNameEn().trim(),
                            importEntity.getStandardCategoryCode().trim());
                    if (attribute != null) {
                        attributeCode = attribute.getAttributeCode();
                    }
                }
            }

            if (StrUtil.isBlank(attributeCode)) {
                return false;
            }

            if (StrUtil.isNotBlank(importEntity.getValueCode())) {
                // 更新现有属性值
                StandardAttributeValue existingValue = standardAttributeValueMapper.selectByValueCode(importEntity.getValueCode().trim());

                if (existingValue != null) {
                    if (StrUtil.isNotBlank(importEntity.getValueEn())) {
                        existingValue.setValueEn(importEntity.getValueEn().trim());
                    }
                    if (StrUtil.isNotBlank(importEntity.getValueCn())) {
                        existingValue.setValueCn(importEntity.getValueCn().trim());
                    }
                    existingValue.setUpdateDate(LocalDateTime.now());
                    existingValue.setUpdateUsername(AdminTokenUtil.getCurrentUsername());

                    return standardAttributeValueMapper.updateById(existingValue) > 0;
                }
            } else if (StrUtil.isNotBlank(importEntity.getValueEn())) {
                // 新增属性值
                String valueCode = idGenerator.generateAttributeValueCode();
                StandardAttributeValue newValue = new StandardAttributeValue();
                newValue.setAttributeCode(attributeCode);
                newValue.setValueCode(valueCode);
                newValue.setValueEn(importEntity.getValueEn().trim());
                newValue.setValueCn(StrUtil.isNotBlank(importEntity.getValueCn()) ?
                        importEntity.getValueCn().trim() : null);
                newValue.setDeleted(false);
                newValue.setCreateDate(LocalDateTime.now());
                newValue.setCreateUsername(AdminTokenUtil.getCurrentUsername());

                return standardAttributeValueMapper.insert(newValue) > 0;
            }

            return false;
        } catch (Exception e) {
            log.error("处理属性值数据失败", e);
            return false;
        }
    }

    /**
     * 转换为导出实体
     */
    private AttributeExportExcelEntity convertToExportEntity(AttributeDetailRespVO detailVO) {
        AttributeExportExcelEntity exportEntity = new AttributeExportExcelEntity();
        exportEntity.setCategoryCode(detailVO.getStandardCategoryCode());
        exportEntity.setCategoryNameEn(detailVO.getStandardCategoryNameEn());
        exportEntity.setCategoryNameCn(detailVO.getStandardCategoryNameCn());
        exportEntity.setCategoryPath(detailVO.getCategoryPath());
        exportEntity.setAttributeNameEn(detailVO.getAttributeNameEn());
        exportEntity.setAttributeNameCn(detailVO.getAttributeNameCn());
        exportEntity.setAttributeCode(detailVO.getAttributeCode());
        exportEntity.setCreateDate(detailVO.getCreateDate());
        return exportEntity;
    }

    /**
     * 转换为详情响应VO（不包含属性值列表，用于分页查询）
     */
    private AttributeDetailRespVO convertToDetailRespVO(StandardAttribute attribute) {
        AttributeDetailRespVO respVO = new AttributeDetailRespVO();
        respVO.setId(attribute.getId());
        respVO.setAttributeCode(attribute.getAttributeCode());
        respVO.setAttributeNameEn(attribute.getAttributeNameEn());
        respVO.setAttributeNameCn(attribute.getAttributeNameCn());
        respVO.setStandardCategoryCode(attribute.getStandardCategoryCode());
        respVO.setStandardCategoryNameEn(attribute.getStandardCategoryNameEn());
        respVO.setStandardCategoryNameCn(attribute.getStandardCategoryNameCn());
        respVO.setCreateDate(attribute.getCreateDate());
        respVO.setUpdateDate(attribute.getUpdateDate());
        respVO.setCreateUsername(attribute.getCreateUsername());
        respVO.setUpdateUsername(attribute.getUpdateUsername());

        // 构建类目路径
        String categoryPath = standardCategoryMapper.buildCategoryPath(attribute.getStandardCategoryCode());
        respVO.setCategoryPath(categoryPath);

        return respVO;
    }

    /**
     * 转换为详情响应VO（包含属性值列表，用于详情查询）
     */
    private AttributeDetailRespVO convertToDetailRespVOWithValues(StandardAttribute attribute) {
        AttributeDetailRespVO respVO = convertToDetailRespVO(attribute);

        // 查询属性值列表
        List<StandardAttributeValue> attributeValues = standardAttributeValueMapper.selectByAttributeCode(attribute.getAttributeCode());
        List<AttributeValueRespVO> valueRespVOList = attributeValues.stream()
                .map(this::convertToAttributeValueRespVO)
                .collect(Collectors.toList());

        respVO.setAttributeValues(valueRespVOList);

        return respVO;
    }

    /**
     * 转换为属性值响应VO
     */
    private AttributeValueRespVO convertToAttributeValueRespVO(StandardAttributeValue attributeValue) {
        AttributeValueRespVO respVO = new AttributeValueRespVO();
        respVO.setId(attributeValue.getId());
        respVO.setAttributeCode(attributeValue.getAttributeCode());
        respVO.setValueCode(attributeValue.getValueCode());
        respVO.setValueEn(attributeValue.getValueEn());
        respVO.setValueCn(attributeValue.getValueCn());
        return respVO;
    }

    // ========== 规格管理相关方法 ==========

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<Boolean> createSpec(Long id) {
        // 1. 检查属性是否存在
        StandardAttribute attribute = standardAttributeMapper.selectById(id);
        if (attribute == null || Boolean.TRUE.equals(attribute.getDeleted())) {
            throw exception(ErrorCodeConstants.ATTRIBUTE_INFO_NOT_EXIST);
        }

        // 2. 更新规格状态为true
        int result = standardAttributeMapper.updateSpecStatus(id, true);

        return CommonResult.success(result > 0);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<Boolean> deleteSpec(Long id) {
        // 1. 检查属性是否存在
        StandardAttribute attribute = standardAttributeMapper.selectById(id);
        if (attribute == null || Boolean.TRUE.equals(attribute.getDeleted())) {
            throw exception(ErrorCodeConstants.ATTRIBUTE_INFO_NOT_EXIST);
        }

        // 2. 更新规格状态为false
        int result = standardAttributeMapper.updateSpecStatus(id, false);

        return CommonResult.success(result > 0);
    }

    @Override
    public CommonResult<PageResult<SpecRespVO>> getSpecPage(SpecPageReqVO reqVO) {
        // 1. 分页查询规格数据
        Page<StandardAttribute> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        Page<StandardAttribute> result = standardAttributeMapper.selectSpecPageList(page, reqVO);

        // 2. 转换为响应VO并构建类目路径
        List<SpecRespVO> respVOList = result.getRecords().stream()
                .map(this::convertToSpecRespVO)
                .collect(Collectors.toList());

        PageResult<SpecRespVO> pageResult = new PageResult<>(respVOList, result.getTotal());
        return CommonResult.success(pageResult);
    }

    @Override
    public CommonResult<List<AttributeListRespVO>> getAttributeList(AttributeListReqVO reqVO) {
        // 1. 查询属性列表
        List<StandardAttribute> attributeList = standardAttributeMapper.selectAttributeList(reqVO);

        // 2. 转换为响应VO
        List<AttributeListRespVO> respVOList = attributeList.stream()
                .map(this::convertToAttributeListRespVO)
                .collect(Collectors.toList());

        return CommonResult.success(respVOList);
    }

    /**
     * 转换为规格响应VO
     */
    private SpecRespVO convertToSpecRespVO(StandardAttribute attribute) {
        SpecRespVO respVO = new SpecRespVO();
        respVO.setAttributeCode(attribute.getAttributeCode());
        respVO.setAttributeNameEn(attribute.getAttributeNameEn());
        respVO.setAttributeNameCn(attribute.getAttributeNameCn());

        // 构建类目路径
        String categoryPath = standardCategoryMapper.buildCategoryPath(attribute.getStandardCategoryCode());
        respVO.setCategoryPath(categoryPath);

        return respVO;
    }

    /**
     * 转换为属性列表响应VO
     */
    private AttributeListRespVO convertToAttributeListRespVO(StandardAttribute attribute) {
        AttributeListRespVO respVO = new AttributeListRespVO();
        respVO.setId(attribute.getId());
        respVO.setAttributeCode(attribute.getAttributeCode());
        respVO.setAttributeNameEn(attribute.getAttributeNameEn());
        return respVO;
    }
}
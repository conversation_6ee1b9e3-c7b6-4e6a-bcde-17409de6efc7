package ai.pricefox.mallfox.service.standard.impl;

import ai.pricefox.mallfox.domain.standard.StandardAttributeValueMapping;
import ai.pricefox.mallfox.mapper.standard.StandardAttributeValueMappingMapper;
import ai.pricefox.mallfox.service.standard.StandardAttributeValueMappingService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 映射属性值Service实现类
 * <AUTHOR>
 * @date 2025-08-02
 */
@Service
@AllArgsConstructor
public class StandardAttributeValueMappingServiceImpl extends ServiceImpl<StandardAttributeValueMappingMapper, StandardAttributeValueMapping> implements StandardAttributeValueMappingService {

    private final StandardAttributeValueMappingMapper standardAttributeValueMappingMapper;

}
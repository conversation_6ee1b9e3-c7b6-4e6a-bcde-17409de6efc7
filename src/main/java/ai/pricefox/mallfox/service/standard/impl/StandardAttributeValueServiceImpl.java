package ai.pricefox.mallfox.service.standard.impl;

import ai.pricefox.mallfox.common.exception.enums.ErrorCodeConstants;
import ai.pricefox.mallfox.utils.AdminTokenUtil;
import ai.pricefox.mallfox.common.util.IdGenerator;
import ai.pricefox.mallfox.common.util.AliOssUtil;
import ai.pricefox.mallfox.domain.standard.StandardAttribute;
import ai.pricefox.mallfox.domain.standard.StandardAttributeValue;
import ai.pricefox.mallfox.mapper.standard.StandardAttributeMapper;
import ai.pricefox.mallfox.mapper.standard.StandardAttributeValueMapper;
import ai.pricefox.mallfox.model.excel.AttributeValueExportExcelEntity;
import ai.pricefox.mallfox.service.standard.StandardAttributeValueService;
import ai.pricefox.mallfox.vo.attribute.*;
import ai.pricefox.mallfox.vo.base.CommonResult;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import cn.hutool.core.date.DateUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

import static ai.pricefox.mallfox.common.exception.util.ServiceExceptionUtil.exception;

/**
 * 标准属性值Service实现类
 * <AUTHOR>
 * @date 2025-08-02
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class StandardAttributeValueServiceImpl extends ServiceImpl<StandardAttributeValueMapper, StandardAttributeValue> implements StandardAttributeValueService {

    private final StandardAttributeValueMapper standardAttributeValueMapper;
    private final StandardAttributeMapper standardAttributeMapper;
    private final AliOssUtil aliOssUtil;
    private final IdGenerator idGenerator;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<AttributeValueRespVO> createAttributeValue(AttributeValueCreateReqVO reqVO) {
        // 1. 检查属性是否存在
        StandardAttribute attribute = standardAttributeMapper.selectByAttributeCode(reqVO.getAttributeCode());
        if (attribute == null) {
            throw exception(ErrorCodeConstants.ATTRIBUTE_INFO_NOT_EXIST);
        }

        // 2. 生成属性值编码（自增方式，无需检查重复）
        String valueCode = idGenerator.generateAttributeValueCode();

        // 3. 构建属性值实体
        StandardAttributeValue attributeValue = buildAttributeValueEntity(reqVO, valueCode);

        // 4. 保存属性值
        int result = standardAttributeValueMapper.insert(attributeValue);
        if (result <= 0) {
            throw exception(ErrorCodeConstants.ATTRIBUTE_VALUE_CREATE_FAILED);
        }

        // 5. 转换并返回结果
        AttributeValueRespVO respVO = convertToAttributeValueRespVO(attributeValue);
        return CommonResult.success(respVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<AttributeValueRespVO> updateAttributeValue(AttributeValueUpdateReqVO reqVO) {
        // 1. 检查属性值是否存在
        StandardAttributeValue attributeValue = standardAttributeValueMapper.selectById(reqVO.getId());
        if (attributeValue == null || Boolean.TRUE.equals(attributeValue.getDeleted())) {
            throw exception(ErrorCodeConstants.ATTRIBUTE_VALUE_NOT_EXIST);
        }

        // 2. 更新属性值信息
        attributeValue.setValueEn(reqVO.getValueEn());
        attributeValue.setValueCn(reqVO.getValueCn());
        attributeValue.setUpdateDate(LocalDateTime.now());
        attributeValue.setUpdateUsername(AdminTokenUtil.getCurrentUsername());

        // 3. 保存更新
        int result = standardAttributeValueMapper.updateById(attributeValue);
        if (result <= 0) {
            throw exception(ErrorCodeConstants.ATTRIBUTE_VALUE_UPDATE_FAILED);
        }

        // 4. 转换并返回结果
        AttributeValueRespVO respVO = convertToAttributeValueRespVO(attributeValue);
        return CommonResult.success(respVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<Boolean> deleteAttributeValue(Long id) {
        // 1. 检查属性值是否存在
        StandardAttributeValue attributeValue = standardAttributeValueMapper.selectById(id);
        if (attributeValue == null || Boolean.TRUE.equals(attributeValue.getDeleted())) {
            throw exception(ErrorCodeConstants.ATTRIBUTE_VALUE_NOT_EXIST);
        }

        // 2. 检查映射关系
        boolean hasMapping = standardAttributeValueMapper.hasValueMapping(attributeValue.getValueCode());
        if (hasMapping) {
            throw exception(ErrorCodeConstants.ATTRIBUTE_VALUE_HAS_MAPPING);
        }

        // 3. 逻辑删除
        attributeValue.setDeleted(true);
        attributeValue.setUpdateDate(LocalDateTime.now());
        attributeValue.setUpdateUsername(AdminTokenUtil.getCurrentUsername());

        int result = standardAttributeValueMapper.updateById(attributeValue);
        return CommonResult.success(result > 0);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<String> batchDeleteAttributeValues(AttributeValueBatchDeleteReqVO reqVO) {
        // 1. 检查哪些属性值有映射关系
        List<Long> idsWithMappings = standardAttributeValueMapper.selectIdsWithMappings(reqVO.getIds());

        // 2. 过滤掉有映射关系的属性值ID
        List<Long> idsToDelete = reqVO.getIds().stream()
                .filter(id -> !idsWithMappings.contains(id))
                .collect(Collectors.toList());

        // 3. 批量删除可删除的属性值
        int deletedCount = 0;
        if (!idsToDelete.isEmpty()) {
            deletedCount = standardAttributeValueMapper.batchLogicalDelete(idsToDelete, AdminTokenUtil.getCurrentUsername());
        }

        // 4. 构建返回消息
        String message;
        if (idsWithMappings.isEmpty()) {
            message = "批量删除成功，共删除 " + deletedCount + " 个属性值";
        } else {
            message = "存在属性值有映射关系，无法进行删除，已自动过滤。成功删除 " + deletedCount + " 个，过滤 " + idsWithMappings.size() + " 个";
        }

        return CommonResult.success(message);
    }

    @Override
    public CommonResult<AttributeFileRespVO> exportAttributeValues(AttributeValueExportReqVO reqVO) {
        try {
            // 1. 检查属性是否存在
            StandardAttribute attribute = standardAttributeMapper.selectByAttributeCode(reqVO.getAttributeCode());
            if (attribute == null) {
                throw exception(ErrorCodeConstants.ATTRIBUTE_INFO_NOT_EXIST);
            }

            // 2. 查询属性值数据
            List<StandardAttributeValue> attributeValueList = standardAttributeValueMapper.selectByAttributeCodeForExport(reqVO.getAttributeCode());

            // 3. 转换为导出实体
            List<AttributeValueExportExcelEntity> exportList = attributeValueList.stream()
                    .map(attributeValue -> convertToExportEntity(attribute, attributeValue))
                    .collect(Collectors.toList());

            // 4. 生成文件名
            String fileName = "属性值_" + attribute.getAttributeNameEn() + "_" + DateUtil.format(DateUtil.date(), "MM_dd") + ".xlsx";

            // 5. 使用ByteArrayOutputStream生成Excel文件到内存
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            EasyExcel.write(outputStream, AttributeValueExportExcelEntity.class)
                    .sheet("属性值")
                    .doWrite(exportList);

            // 6. 将ByteArrayOutputStream转换为InputStream
            ByteArrayInputStream inputStream = new ByteArrayInputStream(outputStream.toByteArray());

            // 7. 上传到OSS
            String ossUrl = aliOssUtil.uploadInputStream(inputStream, fileName, "exports/attribute-values");

            log.info("属性值数据导出成功，文件已上传到OSS: {}", ossUrl);
            return CommonResult.success(new AttributeFileRespVO(ossUrl, fileName));
        } catch (Exception e) {
            log.error("导出属性值数据失败", e);
            throw exception(ErrorCodeConstants.ATTRIBUTE_VALUE_EXPORT_FAILED);
        }
    }

    /**
     * 构建属性值实体
     */
    private StandardAttributeValue buildAttributeValueEntity(AttributeValueCreateReqVO reqVO, String valueCode) {
        StandardAttributeValue attributeValue = new StandardAttributeValue();
        attributeValue.setAttributeCode(reqVO.getAttributeCode());
        attributeValue.setValueCode(valueCode);
        attributeValue.setValueEn(reqVO.getValueEn());
        attributeValue.setValueCn(reqVO.getValueCn());
        attributeValue.setDeleted(false);
        attributeValue.setCreateDate(LocalDateTime.now());
        attributeValue.setCreateUsername(AdminTokenUtil.getCurrentUsername());
        return attributeValue;
    }

    /**
     * 转换为属性值响应VO
     */
    private AttributeValueRespVO convertToAttributeValueRespVO(StandardAttributeValue attributeValue) {
        AttributeValueRespVO respVO = new AttributeValueRespVO();
        respVO.setId(attributeValue.getId());
        respVO.setAttributeCode(attributeValue.getAttributeCode());
        respVO.setValueCode(attributeValue.getValueCode());
        respVO.setValueEn(attributeValue.getValueEn());
        respVO.setValueCn(attributeValue.getValueCn());
        respVO.setCreateDate(attributeValue.getCreateDate());
        respVO.setUpdateDate(attributeValue.getUpdateDate());
        respVO.setCreateUsername(attributeValue.getCreateUsername());
        respVO.setUpdateUsername(attributeValue.getUpdateUsername());
        return respVO;
    }

    /**
     * 转换为导出实体
     */
    private AttributeValueExportExcelEntity convertToExportEntity(StandardAttribute attribute, StandardAttributeValue attributeValue) {
        AttributeValueExportExcelEntity exportEntity = new AttributeValueExportExcelEntity();
        exportEntity.setAttributeNameEn(attribute.getAttributeNameEn());
        exportEntity.setAttributeNameCn(attribute.getAttributeNameCn());
        exportEntity.setAttributeCode(attribute.getAttributeCode());
        exportEntity.setValueEn(attributeValue.getValueEn());
        exportEntity.setValueCn(attributeValue.getValueCn());
        exportEntity.setValueCode(attributeValue.getValueCode());
        return exportEntity;
    }
}
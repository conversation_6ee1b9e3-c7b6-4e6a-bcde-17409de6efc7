package ai.pricefox.mallfox.service.standard.impl;

import ai.pricefox.mallfox.common.exception.enums.ErrorCodeConstants;
import ai.pricefox.mallfox.domain.standard.StandardBrand;
import ai.pricefox.mallfox.mapper.standard.StandardBrandMapper;
import ai.pricefox.mallfox.service.standard.StandardBrandService;
import ai.pricefox.mallfox.vo.base.CommonResult;
import ai.pricefox.mallfox.vo.base.PageResult;
import ai.pricefox.mallfox.vo.brand.BrandInfoCreateReqVO;
import ai.pricefox.mallfox.vo.brand.BrandInfoPageReqVO;
import ai.pricefox.mallfox.vo.brand.BrandInfoRespVO;
import ai.pricefox.mallfox.vo.brand.BrandInfoUpdateReqVO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

import static ai.pricefox.mallfox.common.exception.util.ServiceExceptionUtil.exception;

/**
 * 标准品牌库Service实现类
 * <AUTHOR>
 * @date 2025-07-24
 */
@Service
@AllArgsConstructor
public class StandardBrandServiceImpl extends ServiceImpl<StandardBrandMapper, StandardBrand> implements StandardBrandService {

    private final StandardBrandMapper standardBrandMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<BrandInfoRespVO> createBrandInfo(BrandInfoCreateReqVO reqVO) {
        // 检查品牌编码是否已存在
        LambdaQueryWrapper<StandardBrand> codeWrapper = new LambdaQueryWrapper<>();
        codeWrapper.eq(StandardBrand::getBrandCode, generateBrandCode(reqVO.getName()));
        StandardBrand existingByCode = standardBrandMapper.selectOne(codeWrapper);
        if (existingByCode != null) {
            throw exception(ErrorCodeConstants.BRAND_INFO_NAME_EXISTS);
        }

        // 检查品牌名称是否已存在
        LambdaQueryWrapper<StandardBrand> nameWrapper = new LambdaQueryWrapper<>();
        nameWrapper.eq(StandardBrand::getBrandNameEn, reqVO.getName())
                .or()
                .eq(StandardBrand::getBrandNameCn, reqVO.getName());
        StandardBrand existingByName = standardBrandMapper.selectOne(nameWrapper);
        if (existingByName != null) {
            throw exception(ErrorCodeConstants.BRAND_INFO_NAME_EXISTS);
        }

        StandardBrand standardBrand = new StandardBrand();
        standardBrand.setBrandCode(generateBrandCode(reqVO.getName()));
        standardBrand.setBrandNameEn(reqVO.getName());
        standardBrand.setBrandNameCn(reqVO.getName());
        standardBrand.setLogoUrl(reqVO.getLogoUrl());
        standardBrand.setWebsite(reqVO.getWebsite());
        standardBrand.setSort(reqVO.getSortOrder() != null ? reqVO.getSortOrder() : 0);
        standardBrand.setIsActive(reqVO.getIsActive() != null ? reqVO.getIsActive() : true);
        standardBrand.setCreateDate(LocalDateTime.now());
        standardBrand.setCreateUsername("system");
        
        // 插入数据
        standardBrandMapper.insert(standardBrand);

        BrandInfoRespVO respVO = convertToRespVO(standardBrand);
        return CommonResult.success(respVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<BrandInfoRespVO> updateBrandInfo(BrandInfoUpdateReqVO reqVO) {
        // 检查品牌是否存在
        StandardBrand existingBrand = standardBrandMapper.selectById(reqVO.getId());
        if (existingBrand == null) {
            throw exception(ErrorCodeConstants.BRAND_INFO_NOT_EXIST);
        }

        // 检查品牌名称是否已被其他品牌使用
        LambdaQueryWrapper<StandardBrand> nameWrapper = new LambdaQueryWrapper<>();
        nameWrapper.ne(StandardBrand::getId, reqVO.getId())
                .and(wrapper -> wrapper.eq(StandardBrand::getBrandNameEn, reqVO.getName())
                        .or()
                        .eq(StandardBrand::getBrandNameCn, reqVO.getName()));
        StandardBrand brandWithSameName = standardBrandMapper.selectOne(nameWrapper);
        if (brandWithSameName != null) {
            throw exception(ErrorCodeConstants.BRAND_INFO_NAME_EXISTS);
        }

        // 更新品牌信息
        existingBrand.setBrandNameEn(reqVO.getName());
        existingBrand.setBrandNameCn(reqVO.getName());
        existingBrand.setLogoUrl(reqVO.getLogoUrl());
        existingBrand.setWebsite(reqVO.getWebsite());
        existingBrand.setSort(reqVO.getSortOrder());
        existingBrand.setIsActive(reqVO.getIsActive());
        existingBrand.setUpdateDate(LocalDateTime.now());
        existingBrand.setUpdateUsername("system");
        
        standardBrandMapper.updateById(existingBrand);

        BrandInfoRespVO respVO = convertToRespVO(existingBrand);
        return CommonResult.success(respVO);
    }

    @Override
    public CommonResult<BrandInfoRespVO> getBrandInfoById(Long id) {
        StandardBrand standardBrand = standardBrandMapper.selectById(id);
        if (standardBrand == null) {
            throw exception(ErrorCodeConstants.BRAND_INFO_NOT_EXIST);
        }

        BrandInfoRespVO respVO = convertToRespVO(standardBrand);
        return CommonResult.success(respVO);
    }

    @Override
    public CommonResult<PageResult<BrandInfoRespVO>> getBrandInfoPage(BrandInfoPageReqVO reqVO) {
        LambdaQueryWrapper<StandardBrand> wrapper = new LambdaQueryWrapper<>();
        
        // 添加查询条件
        if (StringUtils.hasText(reqVO.getName())) {
            wrapper.like(StandardBrand::getBrandNameEn, reqVO.getName())
                    .or()
                    .like(StandardBrand::getBrandNameCn, reqVO.getName())
                    .or()
                    .like(StandardBrand::getBrandCode, reqVO.getName());
        }
        
        if (reqVO.getIsActive() != null) {
            wrapper.eq(StandardBrand::getIsActive, reqVO.getIsActive());
        }
        
        // 按排序字段和创建时间排序
        wrapper.orderByAsc(StandardBrand::getSort)
                .orderByDesc(StandardBrand::getCreateDate);

        Page<StandardBrand> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        Page<StandardBrand> result = standardBrandMapper.selectPage(page, wrapper);
        
        List<BrandInfoRespVO> respVOList = result.getRecords().stream()
                .map(this::convertToRespVO)
                .collect(Collectors.toList());

        PageResult<BrandInfoRespVO> pageResult = new PageResult<>(respVOList, result.getTotal());
        return CommonResult.success(pageResult);
    }

    @Override
    public CommonResult<List<BrandInfoRespVO>> getAllBrandInfos() {
        LambdaQueryWrapper<StandardBrand> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StandardBrand::getIsActive, true)
                .orderByAsc(StandardBrand::getSort)
                .orderByDesc(StandardBrand::getCreateDate);
        
        List<StandardBrand> brandList = standardBrandMapper.selectList(wrapper);
        
        List<BrandInfoRespVO> respVOList = brandList.stream()
                .map(this::convertToRespVO)
                .collect(Collectors.toList());

        return CommonResult.success(respVOList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<Boolean> deleteBrandInfo(Long id) {
        // 检查品牌是否存在
        StandardBrand standardBrand = standardBrandMapper.selectById(id);
        if (standardBrand == null) {
            throw exception(ErrorCodeConstants.BRAND_INFO_NOT_EXIST);
        }

        // 删除品牌
        int result = standardBrandMapper.deleteById(id);
        return CommonResult.success(result > 0);
    }

    /**
     * 生成品牌编码
     */
    private String generateBrandCode(String brandName) {
        // 简单的编码生成策略：品牌名称的拼音首字母 + 时间戳后4位
        String timestamp = String.valueOf(System.currentTimeMillis());
        String suffix = timestamp.substring(timestamp.length() - 4);
        return brandName.toUpperCase().replaceAll("[^A-Z0-9]", "").substring(0, Math.min(brandName.length(), 10)) + suffix;
    }

    /**
     * 转换为响应VO
     */
    private BrandInfoRespVO convertToRespVO(StandardBrand standardBrand) {
        BrandInfoRespVO respVO = new BrandInfoRespVO();
        respVO.setId(standardBrand.getId().longValue());
        respVO.setName(StringUtils.hasText(standardBrand.getBrandNameCn()) ? 
                standardBrand.getBrandNameCn() : standardBrand.getBrandNameEn());
        respVO.setLogoUrl(standardBrand.getLogoUrl());
        respVO.setWebsite(standardBrand.getWebsite());
        respVO.setSortOrder(standardBrand.getSort());
        respVO.setIsActive(standardBrand.getIsActive());
        respVO.setCreateTime(standardBrand.getCreateDate());
        respVO.setUpdateTime(standardBrand.getUpdateDate());
        return respVO;
    }
}
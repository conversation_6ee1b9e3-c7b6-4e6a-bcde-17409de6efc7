package ai.pricefox.mallfox.service.standard.impl;

import ai.pricefox.mallfox.domain.standard.StandardProductAttributeData;
import ai.pricefox.mallfox.mapper.standard.StandardProductAttributeDataMapper;
import ai.pricefox.mallfox.service.standard.StandardProductAttributeDataService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class StandardProductAttributeDataServiceImpl extends ServiceImpl<StandardProductAttributeDataMapper, StandardProductAttributeData> implements StandardProductAttributeDataService {
    private final StandardProductAttributeDataMapper standardProductAttributeDataMapper;

    public StandardProductAttributeDataServiceImpl(StandardProductAttributeDataMapper standardProductAttributeDataMapper) {
        this.standardProductAttributeDataMapper = standardProductAttributeDataMapper;
    }

    @Override
    public List<StandardProductAttributeData> getBySkuCode(String skuCode) {
        return standardProductAttributeDataMapper.selectList(new LambdaQueryWrapper<StandardProductAttributeData>().eq(StandardProductAttributeData::getSkuCode, skuCode));
    }

    @Override
    public List<StandardProductAttributeData> getBySkuCodes(List<String> skuCodes) {
        return standardProductAttributeDataMapper.selectList(new LambdaQueryWrapper<StandardProductAttributeData>().in(StandardProductAttributeData::getSkuCode, skuCodes));
    }

}

package ai.pricefox.mallfox.service.standard.impl;

import ai.pricefox.mallfox.domain.standard.StandardAttribute;
import ai.pricefox.mallfox.domain.standard.StandardProductAttributeData;
import ai.pricefox.mallfox.domain.standard.StandardProductMargeData;
import ai.pricefox.mallfox.mapper.standard.StandardProductMargeDataMapper;
import ai.pricefox.mallfox.model.param.ProductDataSearchRequest;
import ai.pricefox.mallfox.service.rules.StandardDataCacheService;
import ai.pricefox.mallfox.service.standard.StandardProductAttributeDataService;
import ai.pricefox.mallfox.service.standard.StandardProductMargeDataService;
import ai.pricefox.mallfox.vo.product.HeaderInfoVO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.io.FileInputStream;
import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.javaparser.StaticJavaParser;
import com.github.javaparser.ast.CompilationUnit;
import com.github.javaparser.ast.body.FieldDeclaration;
import com.github.javaparser.ast.body.VariableDeclarator;
import com.github.javaparser.ast.comments.Comment;
import org.springframework.stereotype.Service;

/**
 * 标准商品合并数据服务实现类
 *
 * <AUTHOR>
 */
@Service
public class StandardProductMargeDataServiceImpl extends ServiceImpl<StandardProductMargeDataMapper, StandardProductMargeData> implements StandardProductMargeDataService {

    private final StandardProductMargeDataMapper standardProductMargeDataMapper;
    private final StandardProductAttributeDataService standardProductAttributeDataService;

    private final StandardDataCacheService standardDataCacheService;

    public StandardProductMargeDataServiceImpl(StandardProductMargeDataMapper standardProductMargeDataMapper, StandardProductAttributeDataService standardProductAttributeDataService, StandardDataCacheService standardDataCacheService) {
        this.standardProductMargeDataMapper = standardProductMargeDataMapper;
        this.standardProductAttributeDataService = standardProductAttributeDataService;
        this.standardDataCacheService = standardDataCacheService;
    }

    // 字段名与注释的映射关系
    private static final Map<String, String> FIELD_COMMENTS = new HashMap<>();

    static {
        FIELD_COMMENTS.put("id", "主键");
        FIELD_COMMENTS.put("createDate", "创建时间");
        FIELD_COMMENTS.put("createUsername", "创建人");
        FIELD_COMMENTS.put("updateDate", "更新时间");
        FIELD_COMMENTS.put("updateUsername", "更新人");
        FIELD_COMMENTS.put("sourcePlatformName", "来源平台名称");
        FIELD_COMMENTS.put("sourcePlatformCode", "来源平台code");
        FIELD_COMMENTS.put("dataChannel", "数据渠道 API   爬虫");
        FIELD_COMMENTS.put("spuCode", "自建spu code");
        FIELD_COMMENTS.put("skuCode", "自建sku code");
        FIELD_COMMENTS.put("platformSpuId", "第三方平台的spuid");
        FIELD_COMMENTS.put("platformSkuId", "第三放平台skuId");
        FIELD_COMMENTS.put("step", "步骤");
        FIELD_COMMENTS.put("examineStatus", "审核状态");
        FIELD_COMMENTS.put("categoryLevel1", "一级类目");
        FIELD_COMMENTS.put("categoryLevel2", "二级类目");
        FIELD_COMMENTS.put("categoryLevel3", "三级类目");
        FIELD_COMMENTS.put("categoryLeve3Code", "三级类目code");
        FIELD_COMMENTS.put("selfOperated", "自营");
    }

    public BaseMapper<StandardProductMargeData> getMapper() {
        return standardProductMargeDataMapper;
    }

    /**
     * 保存或更新标准商品合并数据
     *
     * @param standardProductMargeData 标准商品合并数据
     */
    public StandardProductMargeData saveOrUpdateByProductIdentifier(StandardProductMargeData standardProductMargeData) {
        if (standardProductMargeData == null) {
            throw new IllegalArgumentException("标准商品数据不能为空");
        }

        // 实现保存或更新逻辑
        if (standardProductMargeData.getId() == null) {
            // 新增数据
            standardProductMargeData.setCreateDate(LocalDateTime.now());
            standardProductMargeData.setCreateUsername("system");
            standardProductMargeDataMapper.insert(standardProductMargeData);
        } else {
            // 更新数据
            standardProductMargeData.setUpdateDate(LocalDateTime.now());
            standardProductMargeData.setUpdateUsername("system");
            standardProductMargeDataMapper.updateById(standardProductMargeData);
        }
        return standardProductMargeData;
    }


    @Override
    public List<HeaderInfoVO> getProductHeaders() {
        List<HeaderInfoVO> headerInfoList = new ArrayList<>();

        // 使用反射和JavaParser获取StandardProductMargeData类的字段信息和JavaDoc注释
        Map<String, String> fieldComments = getFieldCommentsFromJavaDoc();

        // 使用反射动态获取StandardProductMargeData类的字段信息
        Field[] fields = StandardProductMargeData.class.getDeclaredFields();
        for (Field field : fields) {
            HeaderInfoVO headerInfo = new HeaderInfoVO();
            headerInfo.setFieldName(field.getName());

            // 获取字段的注释信息
            String comment = fieldComments.getOrDefault(field.getName(), field.getName());
            headerInfo.setComment(comment);

            headerInfo.setCode(field.getName());
            headerInfoList.add(headerInfo);
        }

        // 获取标准属性列表
        List<StandardAttribute> standardAttributeList = standardDataCacheService.getStandardAttribute();

        // 创建一个映射存储StandardProductMargeData类中已存在的字段名（转为小写）及其原始名称，用于忽略大小写的去重
        Map<String, String> existingFieldNamesLower = headerInfoList.stream().collect(Collectors.toMap(header -> header.getFieldName().toLowerCase(), HeaderInfoVO::getFieldName, (existing, replacement) -> existing)); // 如果有重复的（理论上不应该有），保留第一个

        // 添加标准属性，但避免与已存在的字段重复（忽略大小写）
        for (StandardAttribute attribute : standardAttributeList) {
            // 检查标准属性名是否与已有字段名相同（忽略大小写）
            String attributeNameEn = attribute.getAttributeNameEn();
            if (existingFieldNamesLower.containsKey(attributeNameEn.toLowerCase())) {
                // 获取原始字段名（保持原有大小写）
                String originalFieldName = existingFieldNamesLower.get(attributeNameEn.toLowerCase());
                // 移除已存在的同名字段（忽略大小写）
                headerInfoList.removeIf(header -> header.getFieldName().equalsIgnoreCase(originalFieldName));
            }

            // 添加标准属性
            HeaderInfoVO attributeHeader = new HeaderInfoVO();
            attributeHeader.setFieldName(attributeNameEn);
            attributeHeader.setComment(attribute.getAttributeNameCn());
            attributeHeader.setCode(attribute.getAttributeCode());
            headerInfoList.add(attributeHeader);
        }

        return headerInfoList;
    }

    @Override
    public Page<Map<String, Object>> getPageByFields(ProductDataSearchRequest request) {
        // 创建MyBatis Plus的Page对象，用于分页查询
        Page<StandardProductMargeData> page = new Page<>(request.getPageNo(), request.getPageSize());

        // 调用Mapper方法执行分页查询，获取商品合并数据
        Page<StandardProductMargeData> standardProductMargeDataPage = standardProductMargeDataMapper.selectPage(page, request);

        // 提取所有skuCode用于批量查询属性数据，提高查询效率
        Set<String> allSkuCodes = standardProductMargeDataPage.getRecords().stream().flatMap(item -> {
            // 收集主商品的skuCode
            Stream<String> mainSkuStream = item.getSkuCode() != null ? Stream.of(item.getSkuCode()) : Stream.empty();

            // 查询并收集child商品的skuCode
            LambdaQueryWrapper<StandardProductMargeData> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(StandardProductMargeData::getSourcePlatformCode, item.getSourcePlatformCode()).eq(StandardProductMargeData::getSpuCode, item.getSpuCode()).ne(StandardProductMargeData::getId, item.getId());
            List<StandardProductMargeData> childList = standardProductMargeDataMapper.selectList(queryWrapper);
            item.setChild(childList); // 同时设置child列表用于后续处理

            Stream<String> childSkuStream = childList.stream().map(StandardProductMargeData::getSkuCode).filter(Objects::nonNull);

            return Stream.concat(mainSkuStream, childSkuStream);
        }).collect(Collectors.toSet());

        // 批量查询所有需要的属性数据，避免在循环中多次查询数据库
        Map<String, List<StandardProductAttributeData>> attributeDataMap;
        if (!allSkuCodes.isEmpty()) {
            List<StandardProductAttributeData> allAttributeDataList = standardProductAttributeDataService.getBySkuCodes(new ArrayList<>(allSkuCodes));
            attributeDataMap = allAttributeDataList.stream().collect(Collectors.groupingBy(StandardProductAttributeData::getSkuCode));
        } else {
            attributeDataMap = new HashMap<>();
        }

        // 构建结果Page对象，用于返回Map格式的数据
        Page<Map<String, Object>> resultMapPage = new Page<>(page.getCurrent(), page.getSize(), page.getTotal());

        // 转换数据格式，将商品数据和属性数据合并到同一层级的Map中
        List<Map<String, Object>> resultMapList = standardProductMargeDataPage.getRecords().stream().map(item -> convertToMapWithAttributes(item, attributeDataMap)).collect(Collectors.toList());

        resultMapPage.setRecords(resultMapList);
        return resultMapPage;
    }

    /**
     * 将StandardProductMargeData对象转换为包含属性数据的Map
     *
     * @param item             商品合并数据
     * @param attributeDataMap 属性数据映射（按skuCode分组）
     * @return 包含商品数据和属性数据的Map
     */
    private Map<String, Object> convertToMapWithAttributes(StandardProductMargeData item, Map<String, List<StandardProductAttributeData>> attributeDataMap) {
        // 创建结果Map
        Map<String, Object> resultMap = new HashMap<>();

        // 使用反射将StandardProductMargeData的所有字段添加到结果Map中
        Field[] margeDataFields = StandardProductMargeData.class.getDeclaredFields();
        for (Field field : margeDataFields) {
            try {
                field.setAccessible(true);
                resultMap.put(field.getName(), field.get(item));
            } catch (IllegalAccessException e) {
                // 忽略无法访问的字段
            }
        }

        // 获取并添加当前商品的属性数据
        List<StandardProductAttributeData> itemAttributeDataList = attributeDataMap.getOrDefault(item.getSkuCode(), Collections.emptyList());
        for (StandardProductAttributeData attributeData : itemAttributeDataList) {
            resultMap.put(attributeData.getAttributeName(), attributeData.getAttributeValue());
        }

        // 处理child数据
        if (item.getChild() != null) {
            List<Map<String, Object>> childMapList = item.getChild().stream().map(childItem -> convertToMapWithAttributes(childItem, attributeDataMap)).collect(Collectors.toList());
            resultMap.put("child", childMapList);
        }

        return resultMap;
    }

    /**
     * 从Java源文件中提取字段的JavaDoc注释
     *
     * @return 字段名与注释的映射
     */
    private Map<String, String> getFieldCommentsFromJavaDoc() {
        Map<String, String> fieldComments = new HashMap<>();

        try {
            // 获取StandardProductMargeData类的源文件路径
            String sourceFilePath = "src/main/java/ai/pricefox/mallfox/domain/standard/StandardProductMargeData.java";

            // 解析Java源文件
            CompilationUnit cu = StaticJavaParser.parse(new FileInputStream(sourceFilePath));

            // 遍历所有字段声明
            List<FieldDeclaration> fieldDeclarations = cu.findAll(FieldDeclaration.class);
            for (FieldDeclaration fieldDecl : fieldDeclarations) {
                // 获取字段声明的注释
                if (fieldDecl.getComment().isPresent()) {
                    Comment c = fieldDecl.getComment().get();
                    String comment = c.getContent().trim();
                    // 简单处理注释，提取核心内容
                    comment = extractCommentContent(comment);

                    // 获取字段名
                    for (VariableDeclarator variable : fieldDecl.getVariables()) {
                        String fieldName = variable.getNameAsString();
                        if (!comment.isEmpty()) {
                            fieldComments.put(fieldName, comment);
                        }
                    }
                }
            }
        } catch (Exception e) {
            // 如果解析失败，使用默认的注释映射
            fieldComments = getDefaultFieldComments();
        }

        return fieldComments;
    }

    /**
     * 简单提取注释内容
     *
     * @param comment 原始注释
     * @return 提取后的注释内容
     */
    private String extractCommentContent(String comment) {
        // 移除JavaDoc标记符号
        comment = comment.replaceAll("[/*]", "").trim();
        // 如果是多行注释，取第一行
        if (comment.contains("\n")) {
            comment = comment.substring(0, comment.indexOf("\n")).trim();
        }
        return comment;
    }

    /**
     * 获取默认的字段注释映射
     *
     * @return 字段名与注释的映射
     */
    private Map<String, String> getDefaultFieldComments() {
        return FIELD_COMMENTS;
    }

}
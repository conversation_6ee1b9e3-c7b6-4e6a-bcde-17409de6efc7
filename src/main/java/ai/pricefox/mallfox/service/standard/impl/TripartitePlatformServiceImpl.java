package ai.pricefox.mallfox.service.standard.impl;

import ai.pricefox.mallfox.common.constant.RedisKeyConstants;
import ai.pricefox.mallfox.domain.standard.TripartitePlatform;
import ai.pricefox.mallfox.enums.DataChannelEnum;
import ai.pricefox.mallfox.enums.ProductPlatformEnum;
import ai.pricefox.mallfox.mapper.standard.TripartitePlatformMapper;
import ai.pricefox.mallfox.service.standard.TripartitePlatformService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class TripartitePlatformServiceImpl extends ServiceImpl<TripartitePlatformMapper, TripartitePlatform> implements TripartitePlatformService {

    private final TripartitePlatformMapper tripartitePlatformMapper;

    public TripartitePlatformServiceImpl(TripartitePlatformMapper tripartitePlatformMapper) {
        this.tripartitePlatformMapper = tripartitePlatformMapper;
    }

    @Override
    public TripartitePlatform getByPlatformCode(ProductPlatformEnum platformName, DataChannelEnum dataChannelEnum) {
        // 添加空值检查，防止空指针异常
        if (platformName == null || dataChannelEnum == null) {
            return null;
        }
        return tripartitePlatformMapper.selectOne(new LambdaQueryWrapper<TripartitePlatform>()
                .eq(TripartitePlatform::getPlatformName, platformName.name())
                .eq(TripartitePlatform::getEnable, Boolean.TRUE));
    }

    @Override
    @Cacheable(cacheNames = RedisKeyConstants.PLATFORM_CODE_BY_NAME + "#" + RedisKeyConstants.PRODUCT_DETAIL_TIMEOUT,
            key = "#platformName", unless = "#result == null")
    public TripartitePlatform selectByName(String platformName) {
        return tripartitePlatformMapper.selectOne(new LambdaQueryWrapper<TripartitePlatform>()
                .eq(TripartitePlatform::getPlatformName, platformName)
                .eq(TripartitePlatform::getEnable, Boolean.TRUE));

    }

    @Override
    @Cacheable(cacheNames = RedisKeyConstants.PLATFORM_NAME_BY_CODE + "#" + RedisKeyConstants.PRODUCT_DETAIL_TIMEOUT,
            key = "#platformCode", unless = "#result == null")
    public TripartitePlatform selectByCode(String platformCode) {
        return tripartitePlatformMapper.selectOne(new LambdaQueryWrapper<TripartitePlatform>()
                .eq(TripartitePlatform::getPlatformCode, platformCode)
                .eq(TripartitePlatform::getEnable, Boolean.TRUE));
    }

    @Override
    public List<TripartitePlatform> getAll() {
        return tripartitePlatformMapper.selectList(new LambdaQueryWrapper<TripartitePlatform>());
    }
}
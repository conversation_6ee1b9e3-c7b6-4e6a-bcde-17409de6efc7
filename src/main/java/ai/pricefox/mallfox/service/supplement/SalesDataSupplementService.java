package ai.pricefox.mallfox.service.supplement;

import ai.pricefox.mallfox.domain.product.ProductDataOffers;
import ai.pricefox.mallfox.domain.supplement.SalesDataSupplementRecord;
import ai.pricefox.mallfox.mapper.product.ProductDataOffersMapper;
import ai.pricefox.mallfox.mapper.product.ProductDataReviewsMapper;
import ai.pricefox.mallfox.mapper.product.ProductDataSimplifyMapper;
import ai.pricefox.mallfox.mapper.supplement.SalesDataSupplementRecordMapper;
import ai.pricefox.mallfox.model.dto.SalesDataManualSupplementRequest;
import ai.pricefox.mallfox.model.dto.SalesDataSupplementResponse;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;

/**
 * 销量数据补充服务
 * 
 * <AUTHOR>
 * @since 2025-01-14
 */
@Slf4j
@Service
public class SalesDataSupplementService {

    @Autowired
    private ProductDataOffersMapper productDataOffersMapper;

    @Autowired
    private ProductDataSimplifyMapper productDataSimplifyMapper;

    @Autowired
    private ProductDataReviewsMapper productDataReviewsMapper;

    @Autowired
    private SalesDataSupplementRecordMapper supplementRecordMapper;

    @Value("${sales.supplement.review-to-sales-ratio:0.03}")
    private BigDecimal reviewToSalesRatio;

    @Value("${sales.supplement.batch-size:1000}")
    private Integer batchSize;

    @Value("${sales.supplement.enabled:true}")
    private Boolean enabled;

    /**
     * 执行月度销量数据补充
     * 
     * @param currentMonth 当前月份 (格式: YYYY-MM)
     */
    @Transactional(rollbackFor = Exception.class)
    public void executeMonthlySupplementTask(String currentMonth) {
        if (!enabled) {
            log.info("销量数据补充功能已禁用");
            return;
        }

        log.info("开始执行月度销量数据补充任务，月份: {}", currentMonth);

        try {
            // 1. 更新已记录的offers (历史补充的数据)
            updateRecordedOffers(currentMonth);

            // 2. 查找新的空销量offers并补充
            supplementNewEmptyOffers(currentMonth);

            log.info("月度销量数据补充任务完成，月份: {}", currentMonth);
        } catch (Exception e) {
            log.error("月度销量数据补充任务执行失败，月份: {}", currentMonth, e);
            throw e;
        }
    }

    /**
     * 更新已记录的offers
     * 
     * @param currentMonth 当前月份
     */
    private void updateRecordedOffers(String currentMonth) {
        log.info("开始更新已记录的offers，月份: {}", currentMonth);

        // 查询所有历史补充记录的offer ID（不重复）
        List<Long> historyOfferIds = supplementRecordMapper.selectAllDistinctOfferIds();

        if (CollectionUtils.isEmpty(historyOfferIds)) {
            log.info("没有历史补充记录需要更新");
            return;
        }

        int updatedCount = 0;
        for (Long offerId : historyOfferIds) {
            try {
                // 重新计算并更新销量
                updateOfferSalesData(offerId, currentMonth);
                updatedCount++;
            } catch (Exception e) {
                log.error("更新历史记录失败，offerId: {}", offerId, e);
            }
        }

        log.info("更新已记录的offers完成，共更新: {} 条", updatedCount);
    }

    /**
     * 补充新的空销量offers
     * 
     * @param currentMonth 当前月份
     */
    private void supplementNewEmptyOffers(String currentMonth) {
        log.info("开始补充新的空销量offers，月份: {}", currentMonth);

        // 查询sales_last30_days为空的offers
        List<ProductDataOffers> emptyOffers = productDataOffersMapper.selectList(
            new LambdaQueryWrapper<ProductDataOffers>()
                .and(wrapper -> wrapper.isNull(ProductDataOffers::getSalesLast30Days)
                    .or()
                    .eq(ProductDataOffers::getSalesLast30Days, ""))
        );

        if (CollectionUtils.isEmpty(emptyOffers)) {
            log.info("没有新的空销量offers需要补充");
            return;
        }

        int supplementedCount = 0;
        for (ProductDataOffers offer : emptyOffers) {
            try {
                // 补充销量数据（如果已存在记录则更新，不存在则新增）
                supplementOfferSalesData(offer, currentMonth);
                supplementedCount++;
            } catch (Exception e) {
                log.error("补充新offer销量失败，offerId: {}", offer.getId(), e);
            }
        }

        log.info("补充新的空销量offers完成，共补充: {} 条", supplementedCount);
    }

    /**
     * 补充单个offer的销量数据并记录
     * 
     * @param offer 产品offer
     * @param currentMonth 当前月份
     */
    private void supplementOfferSalesData(ProductDataOffers offer, String currentMonth) {
        // 通过skuId和spuId查询上个月的评论数量
        Integer reviewNumber = getReviewNumberForLastMonth(offer.getSkuId(), offer.getSpuId());

        if (reviewNumber == null || reviewNumber <= 0) {
            log.debug("offer {} 没有有效的评论数量，跳过补充", offer.getId());
            return;
        }

        // 计算销量
        String calculatedSales = calculateSalesFromReviews(reviewNumber);

        // 更新offer的销量数据
        LambdaUpdateWrapper<ProductDataOffers> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ProductDataOffers::getId, offer.getId())
                    .set(ProductDataOffers::getSalesLast30Days, calculatedSales);

        int updateResult = productDataOffersMapper.update(null, updateWrapper);

        if (updateResult > 0) {
            // 更新或插入补充记录（如果已存在则更新，不存在则新增）
            updateOrInsertSupplementRecord(offer.getId(), currentMonth, calculatedSales, reviewNumber);
            log.debug("成功补充offer {} 的销量数据: {}", offer.getId(), calculatedSales);
        }
    }

    /**
     * 更新已有记录的offer销量数据
     * 
     * @param offerId offer ID
     * @param currentMonth 当前月份
     */
    private void updateOfferSalesData(Long offerId, String currentMonth) {
        // 重新获取最新的review_number
        ProductDataOffers offer = productDataOffersMapper.selectById(offerId);
        if (offer == null) {
            log.warn("offer {} 不存在，跳过更新", offerId);
            return;
        }

        Integer reviewNumber = getReviewNumberForLastMonth(offer.getSkuId(), offer.getSpuId());

        if (reviewNumber == null || reviewNumber <= 0) {
            log.debug("offer {} 没有有效的评论数量，跳过更新", offerId);
            return;
        }

        // 重新计算销量
        String calculatedSales = calculateSalesFromReviews(reviewNumber);

        // 更新offer的销量数据
        LambdaUpdateWrapper<ProductDataOffers> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ProductDataOffers::getId, offerId)
                    .set(ProductDataOffers::getSalesLast30Days, calculatedSales);

        int updateResult = productDataOffersMapper.update(null, updateWrapper);

        if (updateResult > 0) {
            // 更新或插入当月记录
            updateOrInsertSupplementRecord(offerId, currentMonth, calculatedSales, reviewNumber);
            log.debug("成功更新offer {} 的销量数据: {}", offerId, calculatedSales);
        }
    }

    /**
     * 通过skuId和spuId查询review_number
     * 
     * @param skuId SKU ID
     * @param spuId SPU ID
     * @return 评论数量
     */
    private Integer getReviewNumberBySkuAndSpu(String skuId, String spuId) {
        if (!StringUtils.hasText(skuId) && !StringUtils.hasText(spuId)) {
            return null;
        }

        try {
            return productDataSimplifyMapper.selectReviewNumberBySkuAndSpu(skuId, spuId);
        } catch (Exception e) {
            log.error("查询review_number失败，skuId: {}, spuId: {}", skuId, spuId, e);
            return null;
        }
    }

    /**
     * 基于评论数量计算销量
     * 
     * @param reviewNumber 评论数量
     * @return 计算的销量字符串
     */
    private String calculateSalesFromReviews(Integer reviewNumber) {
        if (reviewNumber == null || reviewNumber <= 0) {
            return "0";
        }

        // 销量 = 评论数量 ÷ 3%
        BigDecimal sales = new BigDecimal(reviewNumber)
            .divide(reviewToSalesRatio, 0, RoundingMode.HALF_UP);

        return sales.toString();
    }

    /**
     * 记录补充数据
     * 
     * @param offerId offer ID
     * @param supplementMonth 补充月份
     * @param supplementSales 补充的销量
     * @param reviewNumber 评论数量
     */
    private void recordSupplementData(Long offerId, String supplementMonth, String supplementSales, Integer reviewNumber) {
        SalesDataSupplementRecord record = new SalesDataSupplementRecord();
        record.setOfferId(offerId);
        record.setSupplementMonth(supplementMonth);
        record.setSupplementSales(supplementSales);
        record.setReviewNumber(reviewNumber);
        record.setCalculationRatio(reviewToSalesRatio);
        record.setCreateTime(LocalDateTime.now());
        record.setUpdateTime(LocalDateTime.now());

        supplementRecordMapper.insert(record);
    }

    /**
     * 更新或插入补充记录
     * 
     * @param offerId offer ID
     * @param supplementMonth 补充月份
     * @param supplementSales 补充的销量
     * @param reviewNumber 评论数量
     */
    private void updateOrInsertSupplementRecord(Long offerId, String supplementMonth, String supplementSales, Integer reviewNumber) {
        // 查询是否存在当月记录
        SalesDataSupplementRecord existingRecord = supplementRecordMapper.selectOne(
            new LambdaQueryWrapper<SalesDataSupplementRecord>()
                .eq(SalesDataSupplementRecord::getOfferId, offerId)
                .eq(SalesDataSupplementRecord::getSupplementMonth, supplementMonth)
        );

        if (existingRecord != null) {
            // 更新现有记录
            existingRecord.setSupplementSales(supplementSales);
            existingRecord.setReviewNumber(reviewNumber);
            existingRecord.setCalculationRatio(reviewToSalesRatio);
            existingRecord.setUpdateTime(LocalDateTime.now());
            supplementRecordMapper.updateById(existingRecord);
        } else {
            // 插入新记录
            recordSupplementData(offerId, supplementMonth, supplementSales, reviewNumber);
        }
    }

    /**
     * 获取当前月份字符串
     * 
     * @return 当前月份 (格式: YYYY-MM)
     */
    public String getCurrentMonth() {
        return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM"));
    }

    /**
     * 获取补充统计信息
     *
     * @param month 月份
     * @return 统计信息
     */
    public String getSupplementStatistics(String month) {
        List<SalesDataSupplementRecord> records = supplementRecordMapper.selectRecordsByMonth(month);
        return String.format("月份 %s 补充记录数: %d", month, records.size());
    }

    /**
     * 执行手动销量数据补充任务
     *
     * @param request 手动补充请求参数
     * @return 补充结果
     */
    @Transactional(rollbackFor = Exception.class)
    public SalesDataSupplementResponse executeManualSupplementTask(SalesDataManualSupplementRequest request) {
        if (!enabled) {
            throw new RuntimeException("销量数据补充功能已禁用");
        }

        log.info("开始执行手动销量数据补充任务，参数: {}", request);

        // 查询所有空销量的offers
        List<ProductDataOffers> emptyOffers = productDataOffersMapper.selectList(
            new LambdaQueryWrapper<ProductDataOffers>()
                .and(wrapper -> wrapper.isNull(ProductDataOffers::getSalesLast30Days)
                    .or()
                    .eq(ProductDataOffers::getSalesLast30Days, ""))
        );

        if (CollectionUtils.isEmpty(emptyOffers)) {
            return SalesDataSupplementResponse.success(0, 0, 0, 0, 0L, "没有需要补充的offers");
        }

        int totalProcessed = emptyOffers.size();
        int successCount = 0;
        int failureCount = 0;
        int skippedCount = 0;

        String currentMonth = getCurrentMonth();

        for (ProductDataOffers offer : emptyOffers) {
            try {
                // 根据指定参数查询评论数量
                Integer reviewNumber = getReviewNumberByTimeRangeAndPlatform(
                    offer.getSkuId(), offer.getSpuId(),
                    request.getSourcePlatform(),
                    request.getStartTime(), request.getEndTime()
                );

                if (reviewNumber == null || reviewNumber <= 0) {
                    skippedCount++;
                    log.debug("offer {} 没有有效的评论数量，跳过补充", offer.getId());
                    continue;
                }

                // 计算销量
                String calculatedSales = calculateSalesFromReviews(reviewNumber);

                // 更新offer的销量数据
                LambdaUpdateWrapper<ProductDataOffers> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.eq(ProductDataOffers::getId, offer.getId())
                            .set(ProductDataOffers::getSalesLast30Days, calculatedSales);

                int updateResult = productDataOffersMapper.update(null, updateWrapper);

                if (updateResult > 0) {
                    // 记录补充数据
                    recordSupplementData(offer.getId(), currentMonth, calculatedSales, reviewNumber);
                    successCount++;
                    log.debug("成功补充offer {} 的销量数据: {}", offer.getId(), calculatedSales);
                } else {
                    failureCount++;
                }

            } catch (Exception e) {
                failureCount++;
                log.error("补充offer {} 销量失败", offer.getId(), e);
            }
        }

        String message = String.format("手动补充完成 - 总数: %d, 成功: %d, 失败: %d, 跳过: %d",
                                     totalProcessed, successCount, failureCount, skippedCount);

        log.info("手动销量数据补充任务完成，{}", message);

        return SalesDataSupplementResponse.success(totalProcessed, successCount, failureCount, skippedCount, 0L, message);
    }

    /**
     * 获取上个月的评论数量（用于定时任务）
     *
     * @param skuId SKU ID
     * @param spuId SPU ID
     * @return 评论数量
     */
    private Integer getReviewNumberForLastMonth(String skuId, String spuId) {
        if (!StringUtils.hasText(skuId) && !StringUtils.hasText(spuId)) {
            return null;
        }

        try {
            // 计算上个月的时间范围
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime lastMonthStart = now.minusMonths(1).withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0).withNano(0);
            LocalDateTime lastMonthEnd = now.withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0).withNano(0).minusNanos(1);

            return productDataReviewsMapper.countReviewsBySkuSpuAndTimeRange(skuId, spuId, lastMonthStart, lastMonthEnd);
        } catch (Exception e) {
            log.error("查询上个月评论数量失败，skuId: {}, spuId: {}", skuId, spuId, e);
            return null;
        }
    }

    /**
     * 根据时间范围和平台查询评论数量（用于手动执行）
     *
     * @param skuId SKU ID
     * @param spuId SPU ID
     * @param sourcePlatform 来源平台
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 评论数量
     */
    private Integer getReviewNumberByTimeRangeAndPlatform(String skuId, String spuId, String sourcePlatform,
                                                        LocalDateTime startTime, LocalDateTime endTime) {
        if (!StringUtils.hasText(skuId) && !StringUtils.hasText(spuId)) {
            return null;
        }

        try {
            return productDataReviewsMapper.countReviewsBySkuSpuPlatformAndTimeRange(
                skuId, spuId, sourcePlatform, startTime, endTime);
        } catch (Exception e) {
            log.error("查询指定时间范围评论数量失败，skuId: {}, spuId: {}, platform: {}", skuId, spuId, sourcePlatform, e);
            return null;
        }
    }
}
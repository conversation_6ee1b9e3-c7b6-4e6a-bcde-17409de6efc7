package ai.pricefox.mallfox.service.system;

import ai.pricefox.mallfox.model.param.ProductTableConfigRequest;
import ai.pricefox.mallfox.model.response.ProductTableConfigResponse;
import ai.pricefox.mallfox.vo.base.CommonResult;

import java.util.List;

/**
 * 产品表格配置服务接口
 *
 * <AUTHOR>
 */
public interface ProductTableConfigService {

    /**
     * 根据类型查询配置列表
     *
     * @param type 类型：1-字段 2-配置
     * @param tag  表格标签
     * @return 配置列表
     */
    CommonResult<List<ProductTableConfigResponse>> getConfigsByType(Integer type, String tag);

    /**
     * 根据字段更新配置
     *
     * @param request 更新请求
     * @return 更新结果
     */
    CommonResult<ProductTableConfigResponse> updateByField(ProductTableConfigRequest.UpdateByFieldRequest request);

    /**
     * 批量插入配置
     *
     * @param request 批量插入请求
     * @return 批量操作结果
     */
    CommonResult<Boolean> batchInsert(List<ProductTableConfigRequest> request);

    /**
     * 更新权重
     *
     * @param request
     * @return
     */
    CommonResult<Boolean> updateWeight(ProductTableConfigRequest.UpdateWeightRequest request);
}

package ai.pricefox.mallfox.service.system;

import ai.pricefox.mallfox.domain.system.SysDict;
import ai.pricefox.mallfox.vo.base.CommonResult;
import ai.pricefox.mallfox.vo.base.PageResult;
import ai.pricefox.mallfox.vo.dict.*;

import java.util.List;

/**
 * <p>
 * 字典表 服务类
 * </p>
 *
 * <AUTHOR>
public interface SysDictService {
	/**
	 * 创建字典
	 *
	 * @param reqVO 创建请求
	 * @return 创建结果
	 */
	CommonResult<SysDictRespVO> createDict(SysDictCreateReqVO reqVO);

	/**
	 * 更新字典
	 *
	 * @param reqVO 更新请求
	 * @return 更新结果
	 */
	CommonResult<SysDictRespVO> updateDict(SysDictUpdateReqVO reqVO);

	/**
	 * 根据ID获取字典
	 *
	 * @param id 字典ID
	 * @return 字典信息
	 */
	CommonResult<SysDictRespVO> getDictById(Long id);

	/**
	 * 分页查询字典
	 *
	 * @param reqVO 分页查询请求
	 * @return 分页结果
	 */
	CommonResult<PageResult<SysDictRespVO>> getDictPage(SysDictPageReqVO reqVO);

	/**
	 * 删除字典
	 *
	 * @param id 字典ID
	 * @return 删除结果
	 */
	CommonResult<Boolean> deleteDict(Long id);

	/**
	 * 获取所有字典列表
	 *
	 * @return 字典列表
	 */
	CommonResult<List<SysDictRespVO>> getAllDicts();
}

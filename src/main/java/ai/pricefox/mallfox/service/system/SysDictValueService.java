package ai.pricefox.mallfox.service.system;

import ai.pricefox.mallfox.domain.system.SysDictValue;
import ai.pricefox.mallfox.vo.base.CommonResult;
import ai.pricefox.mallfox.vo.base.PageResult;
import ai.pricefox.mallfox.vo.dict.*;

import java.util.List;

/**
 * 字典项
 */
public interface SysDictValueService {

	/**
	 * 创建字典项
	 *
	 * @param reqVO 创建请求
	 * @return 创建结果
	 */
	CommonResult<SysDictValueRespVO> createDictValue(SysDictValueCreateReqVO reqVO);

	/**
	 * 更新字典项
	 *
	 * @param reqVO 更新请求
	 * @return 更新结果
	 */
	CommonResult<SysDictValueRespVO> updateDictValue(SysDictValueUpdateReqVO reqVO);

	/**
	 * 根据ID获取字典项
	 *
	 * @param id 字典项ID
	 * @return 字典项信息
	 */
	CommonResult<SysDictValueRespVO> getDictValueById(Long id);

	/**
	 * 分页查询字典项
	 *
	 * @param reqVO 分页查询请求
	 * @return 分页结果
	 */
	CommonResult<PageResult<SysDictValueRespVO>> getDictValuePage(SysDictValuePageReqVO reqVO);

	/**
	 * 根据类型获取字典项列表
	 *
	 * @param type 字典类型
	 * @return 字典项列表
	 */
	CommonResult<List<SysDictValueRespVO>> getDictValuesByType(String type);

	/**
	 * 根据类型获取单个字典项
	 *
	 * @param type 字典类型
	 * @return 字典项
	 */
	CommonResult<SysDictValueRespVO> getDictValueByType(String type);

	/**
	 * 删除字典项
	 *
	 * @param id 字典项ID
	 * @return 删除结果
	 */
	CommonResult<Boolean> deleteDictValue(Long id);
}

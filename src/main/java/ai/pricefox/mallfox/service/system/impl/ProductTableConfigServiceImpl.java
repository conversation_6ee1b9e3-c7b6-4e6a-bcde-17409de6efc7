package ai.pricefox.mallfox.service.system.impl;

import ai.pricefox.mallfox.common.util.JSONUtils;
import ai.pricefox.mallfox.mapper.system.ProductTableConfigMapper;
import ai.pricefox.mallfox.domain.product.ProductTableConfig;
import ai.pricefox.mallfox.model.param.ProductTableConfigRequest;
import ai.pricefox.mallfox.model.response.ProductTableConfigResponse;
import ai.pricefox.mallfox.service.system.ProductTableConfigService;
import ai.pricefox.mallfox.utils.LexoRankUtils;
import ai.pricefox.mallfox.vo.base.CommonResult;
import ai.pricefox.mallfox.common.exception.enums.ErrorCodeConstants;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Map;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

import static ai.pricefox.mallfox.common.exception.util.ServiceExceptionUtil.exception;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 产品表格配置服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class ProductTableConfigServiceImpl implements ProductTableConfigService {

    @Autowired
    private ProductTableConfigMapper productTableConfigMapper;

    @Autowired
    private ObjectMapper objectMapper;

    @Override
    public CommonResult<List<ProductTableConfigResponse>> getConfigsByType(Integer type, String tag) {
        log.info("根据类型查询配置列表，type: {}", type);

        try {
            // 验证类型值
            if (type == null) {
                return CommonResult.error("类型不能为空");
            }
            if (type != 1 && type != 2) {
                return CommonResult.error("类型值无效，只能是1（字段）或2（配置）");
            }

            List<ProductTableConfig> configs = productTableConfigMapper.selectByTypeWithSort(type, tag);

            if (CollectionUtils.isEmpty(configs)) {
                log.info("未找到类型为 {} 的配置", type);
                return CommonResult.success(new ArrayList<>());
            }

            List<ProductTableConfigResponse> result = configs.stream()
                    .map(this::convertToResponseDTO)
                    .collect(Collectors.toList());

            result.forEach(item -> item.setInfo(JSONUtils.toJsonObject(item.getInfo())));

            log.info("查询到 {} 条配置记录", result.size());
            return CommonResult.success(result);

        } catch (Exception e) {
            log.error("根据类型查询配置列表失败，type: {}", type, e);
            return CommonResult.error("查询配置列表失败: " + e.getMessage());
        }
    }

    /**
     * 根据字段更新配置
     *
     * <p>功能说明：</p>
     * <ul>
     *   <li>根据字段名称更新对应的配置信息</li>
     *   <li>支持更新类型、JSON信息、权重等字段</li>
     *   <li>更新前会验证字段是否存在</li>
     * </ul>
     *
     * @param request 更新请求，包含字段名称和要更新的配置信息
     * @return 更新结果
     * @throws ai.pricefox.mallfox.common.exception.ServiceException 当字段不存在或更新失败时抛出
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<ProductTableConfigResponse> updateByField(ProductTableConfigRequest.UpdateByFieldRequest request) {
        log.info("根据字段更新配置，field: {}", request.getField());

        // 验证参数
        if (request.getField() == null) {
            throw exception(ErrorCodeConstants.PRODUCT_TABLE_CONFIG_FIELD_NOT_EXIST);
        }
        if (request.getType() != null && request.getType() != 1 && request.getType() != 2) {
            throw exception(ErrorCodeConstants.PRODUCT_TABLE_CONFIG_TYPE_INVALID);
        }

        // 检查字段是否存在
        ProductTableConfig existingConfig = productTableConfigMapper.selectByField(request.getField());
        if (existingConfig == null) {
            log.warn("字段 {} 不存在，无法更新", request.getField());
            throw exception(ErrorCodeConstants.PRODUCT_TABLE_CONFIG_FIELD_NOT_EXIST);
        }

        // 执行更新
        int updateCount = productTableConfigMapper.updateByField(
                request.getField(),
                request.getType(),
                request.getInfo(),
                request.getWeight()
        );

        if (updateCount == 0) {
            log.warn("更新失败，field: {}", request.getField());
            throw exception(ErrorCodeConstants.PRODUCT_TABLE_CONFIG_UPDATE_FAILED);
        }

        log.info("字段 {} 更新成功", request.getField());
        return CommonResult.success();
    }

    /**
     * 批量插入配置
     *
     * <p>功能说明：</p>
     * <ul>
     *   <li>支持批量插入多个表格配置</li>
     *   <li>单次最多支持100条记录</li>
     *   <li>会验证每个配置的类型值有效性</li>
     * </ul>
     *
     * @param configs 配置列表，每个配置包含字段、类型、JSON信息等
     * @return 批量操作结果
     * @throws ai.pricefox.mallfox.common.exception.ServiceException 当配置列表为空、超过限制或类型无效时抛出
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<Boolean> batchInsert(List<ProductTableConfigRequest> configs) {
        log.info("批量插入配置，数量: {}", configs.size());

        // 验证配置列表
        if (CollectionUtil.isEmpty(configs)) {
            throw exception(ErrorCodeConstants.PRODUCT_TABLE_CONFIG_LIST_EMPTY);
        }

        if (configs.size() > 100) {
            throw exception(ErrorCodeConstants.PRODUCT_TABLE_CONFIG_BATCH_SIZE_EXCEEDED);
        }

        // 验证每个配置的类型值
        for (ProductTableConfigRequest config : configs) {
            if (config.getType() != null && config.getType() != 1 && config.getType() != 2) {
                throw exception(ErrorCodeConstants.PRODUCT_TABLE_CONFIG_TYPE_INVALID);
            }
        }

        List<ProductTableConfig> configsToInsert = new ArrayList<>();
        // 验证和转换数据
        for (ProductTableConfigRequest configRequest : configs) {
            ProductTableConfig config = convertToEntity(configRequest);
            config.setCreateTime(LocalDateTime.now());
            configsToInsert.add(config);
        }

        // 批量插入
        if (!configsToInsert.isEmpty()) {
            int insertCount = productTableConfigMapper.batchInsert(configsToInsert);
            log.info("批量插入成功，插入数量: {}", insertCount);
        }

        return CommonResult.success();
    }

    /**
     * 更新字段权重 - 支持拖拽排序和新增配置
     *
     * <p>功能说明：</p>
     * <ul>
     *   <li>当id不为空时：支持将指定字段拖拽到新位置，自动计算合适的权重值</li>
     *   <li>当id为空时：新增配置记录，根据位置信息计算权重并插入</li>
     *   <li>使用LexoRank算法确保权重值的有序性和可扩展性</li>
     *   <li>支持插入到列表开头、中间、末尾等各种位置</li>
     * </ul>
     *
     * <p>使用场景：</p>
     * <ul>
     *   <li>场景1：在两个元素之间插入 - leftId和rightId都不为null</li>
     *   <li>场景2：插入到某个元素之后（可能是末尾） - leftId不为null，rightId为null</li>
     *   <li>场景3：插入到某个元素之前（可能是开头） - leftId为null，rightId不为null</li>
     * </ul>
     *
     * <p>权重计算逻辑：</p>
     * <ul>
     *   <li>两元素之间：计算leftWeight和rightWeight的中间值</li>
     *   <li>元素之后：计算大于leftWeight的新权重</li>
     *   <li>元素之前：计算小于rightWeight的新权重</li>
     * </ul>
     *
     * @param request 更新权重请求，包含：
     *                - tag: 表格标识，用于区分不同表格的配置
     *                - id: 配置ID，为空时执行新增，不为空时执行更新
     *                - field: 字段名称（新增时必需）
     *                - type: 类型，1-字段 2-配置（新增时必需）
     *                - info: JSON信息（可选）
     *                - weight: 权重（可选，会被重新计算）
     *                - leftId: 目标位置左侧元素的ID（可为null）
     *                - rightId: 目标位置右侧元素的ID（可为null）
     * @return 更新结果，成功返回true
     * @throws ai.pricefox.mallfox.common.exception.ServiceException 当参数验证失败或操作失败时抛出
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<Boolean> updateWeight(ProductTableConfigRequest.UpdateWeightRequest request) {
        log.info("开始处理权重操作，请求参数: tag={}, id={}, field={}, type={}, leftId={}, rightId={}",
                request.getTag(), request.getId(), request.getField(), request.getType(),
                request.getLeftId(), request.getRightId());

        // 参数验证
        if (request.getTag() == null) {
            throw exception(ErrorCodeConstants.PRODUCT_TABLE_CONFIG_TAG_REQUIRED);
        }

        // 判断是新增还是更新操作
        boolean isInsert = request.getId() == null;

        if (isInsert) {
            // 新增操作的参数验证
            if (request.getType() == null) {
                throw exception(ErrorCodeConstants.PRODUCT_TABLE_CONFIG_TYPE_INVALID);
            }
            log.info("执行新增配置操作：field={}, type={}", request.getField(), request.getType());
        } else {
            // 更新操作的参数验证
            log.info("执行更新权重操作：id={}", request.getId());
        }

        String tag = request.getTag();
        Integer targetId = request.getId();
        Integer leftId = request.getLeftId();
        Integer rightId = request.getRightId();

        if (isInsert) {
            // 执行新增操作：先插入记录，再更新权重
            return executeInsertOperation(request, leftId, rightId);
        } else {
            // 执行更新操作：计算新权重并更新
            String newWeight = calculateWeight(leftId, rightId, tag);
            return executeUpdateOperation(targetId, newWeight);
        }
    }

    /**
     * 实体转换为响应DTO
     */
    private ProductTableConfigResponse convertToResponseDTO(ProductTableConfig entity) {
        if (entity == null) {
            return null;
        }

        ProductTableConfigResponse dto = new ProductTableConfigResponse();
        BeanUtils.copyProperties(entity, dto);
        if (StringUtils.isEmpty(entity.getInfo())) {
            dto.setInfo(new JSONObject());
        } else {
            dto.setInfo(JSONUtils.fromJson(entity.getInfo(), JSONObject.class));
        }
        // 设置类型描述
        ProductTableConfig.TypeEnum typeEnum = ProductTableConfig.TypeEnum.getByCode(entity.getType());
        if (typeEnum != null) {
            dto.setTypeDesc(typeEnum.getDesc());
        }

        return dto;
    }

    /**
     * 请求DTO转换为实体
     */
    private ProductTableConfig convertToEntity(ProductTableConfigRequest dto) {
        if (dto == null) {
            return null;
        }

        ProductTableConfig entity = new ProductTableConfig();
        BeanUtils.copyProperties(dto, entity);
        return entity;
    }

    /**
     * 执行新增操作：先插入记录，再更新权重
     */
    private CommonResult<Boolean> executeInsertOperation(ProductTableConfigRequest.UpdateWeightRequest request,
                                                        Integer leftId, Integer rightId) {
        // 第一步：插入新记录（使用默认权重）
        ProductTableConfig config = new ProductTableConfig();
        config.setTag(request.getTag());
        config.setField(request.getField());
        config.setType(request.getType());
        config.setInfo(request.getInfo());
        config.setWeight("V"); // 使用默认权重
        config.setCreateTime(LocalDateTime.now());

        int rowsAffected = productTableConfigMapper.insert(config);
        if (rowsAffected <= 0) {
            log.error("新增配置失败：config={}", config);
            throw exception(ErrorCodeConstants.PRODUCT_TABLE_CONFIG_UPDATE_FAILED);
        }

        // 获取新插入记录的ID
        Integer newId = config.getId();
        if (newId == null) {
            log.error("新增配置后无法获取ID：config={}", config);
            throw exception(ErrorCodeConstants.PRODUCT_TABLE_CONFIG_UPDATE_FAILED);
        }

        log.info("新增配置成功：id={}, field={}, type={}, 默认权重=V", newId, request.getField(), request.getType());

        // 第二步：更新权重（复用权重更新逻辑）
        String newWeight = calculateWeight(leftId, rightId, request.getTag());
        return executeUpdateOperation(newId, newWeight);
    }

    /**
     * 执行更新操作：同时更新权重和info中的width字段
     */
    private CommonResult<Boolean> executeUpdateOperation(Integer targetId, String newWeight) {
        try {
            // 获取当前记录的完整信息
            ProductTableConfig currentConfig = productTableConfigMapper.selectFullById(targetId);
            if (currentConfig == null) {
                log.error("目标记录不存在：targetId={}", targetId);
                throw exception(ErrorCodeConstants.PRODUCT_TABLE_CONFIG_UPDATE_FAILED);
            }

            // 同时更新权重和info字段
            int rowsAffected = productTableConfigMapper.updateWeightAndInfoById(targetId, newWeight);
            if (rowsAffected <= 0) {
                log.error("权重和info更新失败：targetId={}, newWeight={}, rowsAffected={}",
                        targetId, newWeight, rowsAffected);
                throw exception(ErrorCodeConstants.PRODUCT_TABLE_CONFIG_UPDATE_FAILED);
            }

            log.info("字段权重更新成功：targetId={}, newWeight={}",
                    targetId, newWeight);
            return CommonResult.success(true);
        } catch (Exception e) {
            log.error("更新操作失败：targetId={}, newWeight={}", targetId, newWeight, e);
            throw exception(ErrorCodeConstants.PRODUCT_TABLE_CONFIG_UPDATE_FAILED);
        }
    }

    /**
     * 计算新权重
     */
    private String calculateWeight(Integer leftId, Integer rightId, String tag) {
        String newWeight;

        // 场景1：在两个元素之间插入
        if (leftId != null && rightId != null) {
            log.info("场景1：在元素{}和{}之间插入", leftId, rightId);

            String leftWeight = productTableConfigMapper.selectById(leftId);
            String rightWeight = productTableConfigMapper.selectById(rightId);

            if (leftWeight == null) {
                throw exception(ErrorCodeConstants.PRODUCT_TABLE_CONFIG_LEFT_NOT_EXIST);
            }
            if (rightWeight == null) {
                throw exception(ErrorCodeConstants.PRODUCT_TABLE_CONFIG_RIGHT_NOT_EXIST);
            }

            // 检查两个权重是否相同
            if (leftWeight.equals(rightWeight)) {
                log.warn("检测到相同权重：leftWeight={}, rightWeight={}，触发批量重新平衡", leftWeight, rightWeight);
                // 当两个权重相同时，触发批量重新平衡
                rebalanceWeights(tag, null, leftId, rightId);
                // 重新平衡后，重新获取左右权重
                leftWeight = productTableConfigMapper.selectById(leftId);
                rightWeight = productTableConfigMapper.selectById(rightId);
                newWeight = LexoRankUtils.between(leftWeight, rightWeight);
                log.info("重新平衡后计算权重：leftWeight={}, rightWeight={}, newWeight={}",
                        leftWeight, rightWeight, newWeight);
            } else {
                newWeight = LexoRankUtils.between(leftWeight, rightWeight);
                log.info("计算两元素间权重：leftWeight={}, rightWeight={}, newWeight={}",
                        leftWeight, rightWeight, newWeight);
            }
        }
        // 场景2：插入到某个元素之后（可能是末尾）
        else if (leftId != null) {
            log.info("场景2：在元素{}之后插入", leftId);

            String leftWeight = productTableConfigMapper.selectById(leftId);
            if (leftWeight == null) {
                throw exception(ErrorCodeConstants.PRODUCT_TABLE_CONFIG_LEFT_NOT_EXIST);
            }

            newWeight = LexoRankUtils.after(leftWeight);
            log.info("计算元素后权重：leftWeight={}, newWeight={}", leftWeight, newWeight);
        }
        // 场景3：插入到某个元素之前（可能是开头）
        else if (rightId != null) {
            log.info("场景3：在元素{}之前插入", rightId);

            String rightWeight = productTableConfigMapper.selectById(rightId);
            if (rightWeight == null) {
                throw exception(ErrorCodeConstants.PRODUCT_TABLE_CONFIG_RIGHT_NOT_EXIST);
            }

            newWeight = LexoRankUtils.before(rightWeight);
            log.info("计算元素前权重：rightWeight={}, newWeight={}", rightWeight, newWeight);
        }
        // 场景4：都为空，使用默认权重策略
        else {
            log.info("场景4：leftId和rightId都为空，使用默认权重策略");
            // 获取当前tag下的最大权重，在其后插入
            String maxWeight = productTableConfigMapper.selectMaxWeightByTag(tag);
            if (maxWeight != null) {
                newWeight = LexoRankUtils.after(maxWeight);
                log.info("使用最大权重后插入：maxWeight={}, newWeight={}", maxWeight, newWeight);
            } else {
                newWeight = "V"; // 默认中间权重
                log.info("使用默认权重：newWeight={}", newWeight);
            }
        }

        return newWeight;
    }

    /**
     * 批量重新平衡权重
     * 当检测到相同权重时，重新分配整个序列的权重
     *
     * @param tag 表格标签
     * @param targetId 目标ID（新增时为null）
     * @param leftId 左侧元素ID
     * @param rightId 右侧元素ID
     */
    private void rebalanceWeights(String tag, Integer targetId, Integer leftId, Integer rightId) {
        log.info("开始批量重新平衡权重：tag={}, targetId={}, leftId={}, rightId={}",
                tag, targetId, leftId, rightId);

        try {
            // 1. 查询当前tag下的所有配置，按权重排序
            List<ProductTableConfig> allConfigs = productTableConfigMapper.selectAllByTagOrderByWeight(tag);
            if (allConfigs.isEmpty()) {
                log.warn("未找到tag={}的配置记录", tag);
                return;
            }

            log.info("查询到{}条配置记录，开始重新分配权重", allConfigs.size());

            // 2. 生成新的权重序列（保持现有权重格式）
            List<String> newWeights = generateBalancedWeights(allConfigs);

            // 3. 为每个配置分配新权重
            List<ProductTableConfig> updateConfigs = new ArrayList<>();
            for (int i = 0; i < allConfigs.size(); i++) {
                ProductTableConfig config = allConfigs.get(i);
                String newWeight = newWeights.get(i);

                // 只有权重发生变化时才更新
                if (!newWeight.equals(config.getWeight())) {
                    ProductTableConfig updateConfig = new ProductTableConfig();
                    updateConfig.setId(config.getId());
                    updateConfig.setWeight(newWeight);
                    updateConfigs.add(updateConfig);

                    log.debug("权重更新：id={}, oldWeight={}, newWeight={}",
                            config.getId(), config.getWeight(), newWeight);
                }
            }

            // 4. 批量更新权重
            if (!updateConfigs.isEmpty()) {
                int updateCount = batchUpdateWeights(updateConfigs);
                log.info("批量重新平衡完成：更新了{}条记录", updateCount);
            } else {
                log.info("所有权重已经是最优分布，无需更新");
            }

        } catch (Exception e) {
            log.error("批量重新平衡失败：tag={}", tag, e);
            throw new RuntimeException("权重重新平衡失败", e);
        }
    }

    /**
     * 生成均匀分布的权重序列，保持与现有权重相同的位数格式
     *
     * @param existingConfigs 现有配置列表
     * @return 权重列表
     */
    private List<String> generateBalancedWeights(List<ProductTableConfig> existingConfigs) {
        List<String> weights = new ArrayList<>();
        String charSet = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";

        int count = existingConfigs.size();
        if (count <= 0) {
            return weights;
        }

        // 1. 分析现有权重的位数
        int targetLength = analyzeWeightLength(existingConfigs);
        log.info("分析现有权重位数：{}, 需要生成{}个权重", targetLength, count);

        // 2. 计算当前位数的最大容量
        long maxCapacity = (long) Math.pow(charSet.length(), targetLength);

        // 3. 如果需要的权重数量超出当前位数容量，增加位数
        if (count >= maxCapacity * 0.8) { // 使用80%容量作为阈值
            targetLength++;
            log.info("权重数量接近容量上限，增加位数到：{}", targetLength);
        }

        // 4. 生成均匀分布的权重
        if (count == 1) {
            weights.add(generateMiddleWeight(targetLength));
            return weights;
        }

        // 5. 使用顺序递增方式生成权重
        weights = generateSequentialWeights(count, targetLength, charSet);

        log.info("生成{}个{}位权重：{}", count, targetLength, weights);
        return weights;
    }

    /**
     * 分析现有权重的位数
     */
    private int analyzeWeightLength(List<ProductTableConfig> configs) {
        int maxLength = 4; // 最小4位

        for (ProductTableConfig config : configs) {
            String weight = config.getWeight();
            if (weight != null && weight.length() > maxLength) {
                maxLength = weight.length();
            }
        }

        return maxLength;
    }

    /**
     * 生成中间权重
     */
    private String generateMiddleWeight(int length) {
        String charSet = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
        char middleChar = charSet.charAt(charSet.length() / 2); // 'V'
        return String.valueOf(middleChar).repeat(length);
    }

    /**
     * 生成顺序递增的权重
     * 从基础权重开始，按照字典序顺序递增生成
     */
    private List<String> generateSequentialWeights(int count, int length, String charSet) {
        List<String> weights = new ArrayList<>();

        // 生成起始权重（使用字符集的第一个字符填充）
        String startWeight = generateStartWeight(length, charSet);
        log.info("生成顺序权重，起始权重：{}, 数量：{}", startWeight, count);

        String currentWeight = startWeight;
        for (int i = 0; i < count; i++) {
            weights.add(currentWeight);
            // 生成下一个权重
            currentWeight = getNextWeight(currentWeight, charSet);
        }

        log.info("生成{}个顺序权重：{}", count, weights);
        return weights;
    }

    /**
     * 生成起始权重
     */
    private String generateStartWeight(int length, String charSet) {
        // 使用字符集的第一个字符（'0'）填充到指定长度
        char firstChar = charSet.charAt(0);
        return String.valueOf(firstChar).repeat(length);
    }

    /**
     * 获取下一个权重（字典序递增）
     */
    private String getNextWeight(String currentWeight, String charSet) {
        char[] chars = currentWeight.toCharArray();
        int length = chars.length;

        // 从右到左进行进位
        for (int i = length - 1; i >= 0; i--) {
            char currentChar = chars[i];
            int currentIndex = charSet.indexOf(currentChar);

            if (currentIndex < charSet.length() - 1) {
                // 当前位可以递增
                chars[i] = charSet.charAt(currentIndex + 1);
                return new String(chars);
            } else {
                // 当前位已经是最大值，需要进位
                chars[i] = charSet.charAt(0); // 重置为第一个字符
                // 继续处理下一位（向左进位）
            }
        }

        // 所有位都已经是最大值，需要增加长度
        return charSet.charAt(0) + currentWeight;
    }

    /**
     * 将位置转换为权重字符串
     */
    private String convertPositionToWeight(long position, int length, String charSet) {
        StringBuilder weight = new StringBuilder();
        long remaining = position;
        int base = charSet.length();

        for (int i = 0; i < length; i++) {
            int charIndex = (int) (remaining % base);
            weight.insert(0, charSet.charAt(charIndex));
            remaining /= base;
        }

        return weight.toString();
    }

    /**
     * 批量更新权重（支持事务）
     *
     * @param configs 要更新的配置列表
     * @return 更新的记录数
     */
    private int batchUpdateWeights(List<ProductTableConfig> configs) {
        if (configs.isEmpty()) {
            return 0;
        }

        // 逐条更新（确保事务安全）
        int totalUpdated = 0;
        for (ProductTableConfig config : configs) {
            int updated = productTableConfigMapper.updateWeightById(config.getId(), config.getWeight());
            totalUpdated += updated;
        }

        return totalUpdated;
    }

}

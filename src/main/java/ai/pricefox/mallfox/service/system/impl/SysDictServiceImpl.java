package ai.pricefox.mallfox.service.system.impl;

import ai.pricefox.mallfox.common.exception.enums.ErrorCodeConstants;
import ai.pricefox.mallfox.domain.system.SysDict;
import ai.pricefox.mallfox.mapper.system.SysDictMapper;
import ai.pricefox.mallfox.mapper.system.SysDictValueMapper;
import ai.pricefox.mallfox.service.system.SysDictService;
import ai.pricefox.mallfox.vo.base.CommonResult;
import ai.pricefox.mallfox.vo.base.PageResult;
import ai.pricefox.mallfox.vo.dict.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.AllArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import static ai.pricefox.mallfox.common.exception.util.ServiceExceptionUtil.exception;

/**
 * <p>
 * 字典表 服务实现类
 * </p>
 *
 * <AUTHOR>
@Service
@AllArgsConstructor
public class SysDictServiceImpl implements SysDictService {
	private final SysDictMapper sysDictMapper;
	private final SysDictValueMapper sysDictValueMapper;

	@Override
	@Transactional(rollbackFor = Exception.class)
	public CommonResult<SysDictRespVO> createDict(SysDictCreateReqVO reqVO) {
		SysDict sysDict = new SysDict();
		BeanUtils.copyProperties(reqVO, sysDict);
		// 使用 Mapper 层的插入方法
		sysDictMapper.insertDict(sysDict);

		SysDictRespVO respVO = new SysDictRespVO();
		BeanUtils.copyProperties(sysDict, respVO);
		return CommonResult.success(respVO);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public CommonResult<SysDictRespVO> updateDict(SysDictUpdateReqVO reqVO) {
		SysDict sysDict = new SysDict();
		BeanUtils.copyProperties(reqVO, sysDict);
		// 使用 Mapper 层的更新方法
		sysDictMapper.updateDictById(sysDict);

		// 同步更新字典项的类型
		sysDictValueMapper.updateDictValueTypeByDictId(sysDict.getId(), sysDict.getType());

		SysDictRespVO respVO = new SysDictRespVO();
		BeanUtils.copyProperties(sysDict, respVO);
		return CommonResult.success(respVO);
	}

	@Override
	public CommonResult<SysDictRespVO> getDictById(Long id) {
		// 使用 Mapper 层的查询方法
		SysDict sysDict = sysDictMapper.selectDictById(id);
		if (sysDict == null) {
			throw exception(ErrorCodeConstants.DICT_NOT_EXIST);
		}

		SysDictRespVO respVO = new SysDictRespVO();
		BeanUtils.copyProperties(sysDict, respVO);
		return CommonResult.success(respVO);
	}

	@Override
	public CommonResult<PageResult<SysDictRespVO>> getDictPage(SysDictPageReqVO reqVO) {
		// 使用 Mapper 层的分页查询方法
		Page<SysDict> dictPage = sysDictMapper.selectDictPage(reqVO);

		// 转换为响应VO
		PageResult<SysDictRespVO> pageResult = new PageResult<>();
		pageResult.setTotal(dictPage.getTotal());
		pageResult.setList(dictPage.getRecords().stream().map(dict -> {
			SysDictRespVO respVO = new SysDictRespVO();
			BeanUtils.copyProperties(dict, respVO);
			return respVO;
		}).toList());

		return CommonResult.success(pageResult);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public CommonResult<Boolean> deleteDict(Long id) {
		// 使用 Mapper 层的删除方法
		boolean dictDeleted = sysDictMapper.deleteDictById(id);
		// 删除相关字典项
		sysDictValueMapper.deleteDictValuesByDictId(id);
		return CommonResult.success(dictDeleted);
	}

	@Override
	public CommonResult<List<SysDictRespVO>> getAllDicts() {
		// 使用 Mapper 层的查询方法
		List<SysDict> dictList = sysDictMapper.selectAllDicts();

		List<SysDictRespVO> respVOList = dictList.stream().map(dict -> {
			SysDictRespVO respVO = new SysDictRespVO();
			BeanUtils.copyProperties(dict, respVO);
			return respVO;
		}).toList();

		return CommonResult.success(respVOList);
	}
}

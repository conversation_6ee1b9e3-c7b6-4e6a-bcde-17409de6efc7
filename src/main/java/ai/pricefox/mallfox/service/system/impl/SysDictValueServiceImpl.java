package ai.pricefox.mallfox.service.system.impl;

import ai.pricefox.mallfox.common.exception.enums.ErrorCodeConstants;
import ai.pricefox.mallfox.domain.system.SysDictValue;
import ai.pricefox.mallfox.mapper.system.SysDictValueMapper;
import ai.pricefox.mallfox.service.system.SysDictValueService;
import ai.pricefox.mallfox.vo.base.CommonResult;
import ai.pricefox.mallfox.vo.base.PageResult;
import ai.pricefox.mallfox.vo.dict.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.AllArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import static ai.pricefox.mallfox.common.exception.util.ServiceExceptionUtil.exception;
/**
 * 字典项
 *
 * <AUTHOR>
@Service
@AllArgsConstructor
public class SysDictValueServiceImpl implements SysDictValueService {
	private final SysDictValueMapper sysDictValueMapper;

	@Override
	@Transactional(rollbackFor = Exception.class)
	public CommonResult<SysDictValueRespVO> createDictValue(SysDictValueCreateReqVO reqVO) {
		SysDictValue sysDictValue = new SysDictValue();
		BeanUtils.copyProperties(reqVO, sysDictValue);
		// 使用 Mapper 层的插入方法
		sysDictValueMapper.insertDictValue(sysDictValue);

		SysDictValueRespVO respVO = new SysDictValueRespVO();
		BeanUtils.copyProperties(sysDictValue, respVO);
		return CommonResult.success(respVO);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public CommonResult<SysDictValueRespVO> updateDictValue(SysDictValueUpdateReqVO reqVO) {
		SysDictValue sysDictValue = new SysDictValue();
		BeanUtils.copyProperties(reqVO, sysDictValue);
		// 使用 Mapper 层的更新方法
		sysDictValueMapper.updateDictValueById(sysDictValue);

		SysDictValueRespVO respVO = new SysDictValueRespVO();
		BeanUtils.copyProperties(sysDictValue, respVO);
		return CommonResult.success(respVO);
	}

	@Override
	public CommonResult<SysDictValueRespVO> getDictValueById(Long id) {
		// 使用 Mapper 层的查询方法
		SysDictValue sysDictValue = sysDictValueMapper.selectDictValueById(id);
		if (sysDictValue == null) {
			throw exception(ErrorCodeConstants.DICT_VALUE_NOT_EXIST);
		}

		SysDictValueRespVO respVO = new SysDictValueRespVO();
		BeanUtils.copyProperties(sysDictValue, respVO);
		return CommonResult.success(respVO);
	}

	@Override
	public CommonResult<PageResult<SysDictValueRespVO>> getDictValuePage(SysDictValuePageReqVO reqVO) {
		// 使用 Mapper 层的分页查询方法
		Page<SysDictValue> dictValuePage = sysDictValueMapper.selectDictValuePage(reqVO);

		// 转换为响应VO
		PageResult<SysDictValueRespVO> pageResult = new PageResult<>();
		pageResult.setTotal(dictValuePage.getTotal());
		pageResult.setList(dictValuePage.getRecords().stream().map(dictValue -> {
			SysDictValueRespVO respVO = new SysDictValueRespVO();
			BeanUtils.copyProperties(dictValue, respVO);
			return respVO;
		}).toList());

		return CommonResult.success(pageResult);
	}

	@Override
	public CommonResult<List<SysDictValueRespVO>> getDictValuesByType(String type) {
		// 使用 Mapper 层的查询方法
		List<SysDictValue> dictValues = sysDictValueMapper.selectDictValuesByType(type);

		List<SysDictValueRespVO> respVOList = dictValues.stream().map(dictValue -> {
			SysDictValueRespVO respVO = new SysDictValueRespVO();
			BeanUtils.copyProperties(dictValue, respVO);
			return respVO;
		}).toList();

		return CommonResult.success(respVOList);
	}

	@Override
	public CommonResult<SysDictValueRespVO> getDictValueByType(String type) {
		// 使用 Mapper 层的查询方法
		SysDictValue sysDictValue = sysDictValueMapper.selectDictValueByType(type);

		if (sysDictValue == null) {
			throw exception(ErrorCodeConstants.DICT_VALUE_NOT_EXIST);
		}

		SysDictValueRespVO respVO = new SysDictValueRespVO();
		BeanUtils.copyProperties(sysDictValue, respVO);
		return CommonResult.success(respVO);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public CommonResult<Boolean> deleteDictValue(Long id) {
		// 使用 Mapper 层的删除方法
		boolean deleted = sysDictValueMapper.deleteDictValueById(id);
		return CommonResult.success(deleted);
	}
}

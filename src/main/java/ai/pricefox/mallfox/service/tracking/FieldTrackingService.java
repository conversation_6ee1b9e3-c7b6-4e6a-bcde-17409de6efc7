package ai.pricefox.mallfox.service.tracking;

import ai.pricefox.mallfox.config.FieldTrackingConfig;
import ai.pricefox.mallfox.domain.product.ProductDataOffers;
import ai.pricefox.mallfox.domain.product.ProductDataSimplify;
import ai.pricefox.mallfox.domain.tracking.ProductFieldSourceTracking;
import ai.pricefox.mallfox.mapper.tracking.FieldTrackingMapper;
import ai.pricefox.mallfox.model.dto.FieldChangeEvent;
import ai.pricefox.mallfox.service.product.ProductDataOffersService;
import ai.pricefox.mallfox.service.product.ProductDataSimplifyService;
import ai.pricefox.mallfox.vo.tracking.ProcessResult;
import ai.pricefox.mallfox.common.constant.RedisKeyConstants;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;

/**
 * 字段追踪服务
 *
 * <AUTHOR>
 * @since 2025-01-14
 */
@Service
@Slf4j
@AllArgsConstructor
public class FieldTrackingService {

    private FieldTrackingMapper fieldTrackingMapper;
    private FieldTrackingConfig config;
    private ProductDataOffersService offersService;
    private ProductDataSimplifyService simplifyService;
    private RedisTemplate<String, Object> redisTemplate;

    private final BlockingQueue<FieldChangeEvent> eventQueue = new LinkedBlockingQueue<>();

    /**
     * 处理product_data_offers表数据
     */
    public ProcessResult processProductDataOffers(Integer batchSize, Boolean syncMode) {
        ProcessResult result = new ProcessResult();
        int pageNo = 1;
        int processedCount = 0;
        int fieldsCreated = 0;

        while (true) {
            Page<ProductDataOffers> page = new Page<>(pageNo, batchSize);
            LambdaQueryWrapper<ProductDataOffers> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.isNotNull(ProductDataOffers::getDataChannel);

            Page<ProductDataOffers> pageResult = offersService.page(page, queryWrapper);
            List<ProductDataOffers> records = pageResult.getRecords();

            if (records.isEmpty()) {
                break;
            }

            log.info("处理product_data_offers表第{}页，记录数：{}", pageNo, records.size());

            for (ProductDataOffers record : records) {
                try {
                    // 根据dataChannel确定数据来源
                    Integer dataSource = record.getDataChannel();
                    String sourcePlatform = record.getSourcePlatform();

                    if (dataSource != null) {
                        // 根据syncMode选择同步或异步处理
                        if (syncMode) {
                            this.recordNewRecordFieldsSync(record, "product_data_offers", record.getId(), dataSource, sourcePlatform);
                        } else {
                            this.recordNewRecordFields(record, "product_data_offers", record.getId(), dataSource, sourcePlatform);
                        }

                        // 统计创建的字段数（估算）
                        fieldsCreated += countNonEmptyFields(record);
                        processedCount++;
                    }
                } catch (Exception e) {
                    log.error("处理product_data_offers记录失败，recordId={}", record.getId(), e);
                }
            }

            pageNo++;

            // 如果当前页记录数小于批次大小，说明已经是最后一页
            if (records.size() < batchSize) {
                break;
            }
        }

        result.setProcessedCount(processedCount);
        result.setFieldsCreated(fieldsCreated);
        return result;
    }

    /**
     * 处理product_data_simplify表数据
     */
    public ProcessResult processProductDataSimplify(Integer batchSize, Boolean syncMode) {
        ProcessResult result = new ProcessResult();
        int pageNo = 1;
        int processedCount = 0;
        int fieldsCreated = 0;

        while (true) {
            Page<ProductDataSimplify> page = new Page<>(pageNo, batchSize);
            LambdaQueryWrapper<ProductDataSimplify> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.isNotNull(ProductDataSimplify::getDataChannel);

            Page<ProductDataSimplify> pageResult = simplifyService.page(page, queryWrapper);
            List<ProductDataSimplify> records = pageResult.getRecords();

            if (records.isEmpty()) {
                break;
            }

            log.info("处理product_data_simplify表第{}页，记录数：{}", pageNo, records.size());

            for (ProductDataSimplify record : records) {
                try {
                    // 根据dataChannel确定数据来源
                    Integer dataSource = record.getDataChannel();
                    String sourcePlatform = record.getSourcePlatform();

                    if (dataSource != null) {
                        // 根据syncMode选择同步或异步处理
                        if (syncMode) {
                            this.recordNewRecordFieldsSync(record, "product_data_simplify", record.getId(), dataSource, sourcePlatform);
                        } else {
                            this.recordNewRecordFields(record, "product_data_simplify", record.getId(), dataSource, sourcePlatform);
                        }

                        // 统计创建的字段数（估算）
                        fieldsCreated += countNonEmptyFields(record);
                        processedCount++;
                    }
                } catch (Exception e) {
                    log.error("处理product_data_simplify记录失败，recordId={}", record.getId(), e);
                }
            }

            pageNo++;

            // 如果当前页记录数小于批次大小，说明已经是最后一页
            if (records.size() < batchSize) {
                break;
            }
        }

        result.setProcessedCount(processedCount);
        result.setFieldsCreated(fieldsCreated);
        return result;
    }

    /**
     * 统计对象中非空字段的数量
     */
    private int countNonEmptyFields(Object entity) {
        if (entity == null) {
            return 0;
        }

        int count = 0;
        java.lang.reflect.Field[] fields = entity.getClass().getDeclaredFields();

        for (java.lang.reflect.Field field : fields) {
            if (shouldCountField(field)) {
                try {
                    field.setAccessible(true);
                    Object value = field.get(entity);

                    if (value != null && !this.isEmptyValue(value)) {
                        count++;
                    }
                } catch (IllegalAccessException e) {
                    // 忽略无法访问的字段
                }
            }
        }

        return count;
    }

    /**
     * 判断是否应该统计该字段
     */
    private boolean shouldCountField(java.lang.reflect.Field field) {
        String fieldName = field.getName();

        // 排除系统字段和静态字段
        return !fieldName.equals("id") && !fieldName.equals("createTime") && !fieldName.equals("updateTime") && !fieldName.startsWith("$") && !java.lang.reflect.Modifier.isStatic(field.getModifiers());
    }


    /**
     * 异步记录单个字段更新
     */
    @Async
    public void recordFieldUpdate(String tableName, Long recordId, String fieldName, Integer dataSource, Object oldValue, Object newValue) {
        if (!config.getEnabled()) {
            return;
        }

        FieldChangeEvent event = new FieldChangeEvent(tableName, recordId, fieldName, dataSource, oldValue, newValue);
        recordFieldUpdate(event);
    }

    /**
     * 异步记录字段更新事件
     */
    @Async
    public void recordFieldUpdate(FieldChangeEvent event) {
        if (!config.getEnabled() || event == null) {
            return;
        }

        if (config.getAsyncEnabled()) {
            // 异步模式：加入队列
            if (!eventQueue.offer(event)) {
                log.warn("字段追踪队列已满，丢弃事件: table={}, recordId={}, field={}", event.getTableName(), event.getRecordId(), event.getFieldName());
            }
        } else {
            // 同步模式：直接处理
            processFieldChangeEvent(event);
        }
    }

    /**
     * 批量记录字段变更
     */
    @Async
    public void batchRecordFieldChanges(List<FieldChangeEvent> events) {
        if (!config.getEnabled() || events == null || events.isEmpty()) {
            return;
        }

        if (config.getAsyncEnabled()) {
            // 异步模式：批量加入队列
            for (FieldChangeEvent event : events) {
                if (!eventQueue.offer(event)) {
                    log.warn("字段追踪队列已满，丢弃事件: table={}, recordId={}, field={}", event.getTableName(), event.getRecordId(), event.getFieldName());
                    break;
                }
            }
        } else {
            // 同步模式：直接批量处理
            batchProcessFieldChangeEvents(events);
        }
    }

    /**
     * 处理队列中的待处理事件
     * 由定时任务调用
     */
    public void processPendingEvents() {
        if (!config.getEnabled() || !config.getAsyncEnabled()) {
            return;
        }

        List<FieldChangeEvent> events = new ArrayList<>();
        eventQueue.drainTo(events, config.getBatchSize());

        if (!events.isEmpty()) {
            try {
                batchProcessFieldChangeEvents(events);
                log.debug("批量处理字段追踪事件: {} 个", events.size());
            } catch (Exception e) {
                log.error("批量处理字段追踪事件失败", e);
                // 可以考虑将失败的事件重新加入队列或记录到死信队列
            }
        }
    }

    /**
     * 处理单个字段变更事件（JSON格式）
     */
    private void processFieldChangeEvent(FieldChangeEvent event) {
        try {
            int affectedRows = fieldTrackingMapper.updateFieldSource(event.getTableName(), event.getRecordId(), event.getFieldName(), event.getDataSource(), event.getSourcePlatform(), event.getOldValue() != null ? event.getOldValue().toString() : null, event.getNewValue() != null ? event.getNewValue().toString() : null);

            if (affectedRows == 0) {
                // 如果更新失败，可能是记录不存在，创建新记录
                ProductFieldSourceTracking tracking = new ProductFieldSourceTracking();
                tracking.setTableName(event.getTableName());
                tracking.setRecordId(event.getRecordId());
                tracking.setCreateTime(LocalDateTime.now());

                Map<String, ProductFieldSourceTracking.FieldSourceInfo> fieldSources = new HashMap<>();
                ProductFieldSourceTracking.FieldSourceInfo sourceInfo = new ProductFieldSourceTracking.FieldSourceInfo();
                sourceInfo.setDataSource(event.getDataSource());
                sourceInfo.setSourcePlatform(event.getSourcePlatform());
                sourceInfo.setLastUpdate(LocalDateTime.now());
                sourceInfo.setOldValue(event.getOldValue() != null ? event.getOldValue().toString() : null);
                sourceInfo.setNewValue(event.getNewValue() != null ? event.getNewValue().toString() : null);

                fieldSources.put(event.getFieldName(), sourceInfo);
                tracking.setFieldSources(fieldSources);

                fieldTrackingMapper.insertOrUpdate(tracking);
            }

            log.debug("处理字段变更事件: table={}, recordId={}, field={}", event.getTableName(), event.getRecordId(), event.getFieldName());
        } catch (Exception e) {
            log.error("处理字段追踪事件失败: table={}, recordId={}, field={}", event.getTableName(), event.getRecordId(), event.getFieldName(), e);
        }
    }

    /**
     * 批量处理字段变更事件
     */
    private void batchProcessFieldChangeEvents(List<FieldChangeEvent> events) {
        if (events.isEmpty()) {
            return;
        }

        try {
            // 按table_name和record_id分组
            Map<String, Map<String, ProductFieldSourceTracking.FieldSourceInfo>> groupedEvents = new HashMap<>();
            Map<String, String> tableNames = new HashMap<>();
            Map<String, Long> recordIds = new HashMap<>();

            for (FieldChangeEvent event : events) {
                String key = event.getTableName() + ":" + event.getRecordId();
                tableNames.put(key, event.getTableName());
                recordIds.put(key, event.getRecordId());

                groupedEvents.computeIfAbsent(key, k -> new HashMap<>());

                ProductFieldSourceTracking.FieldSourceInfo sourceInfo = new ProductFieldSourceTracking.FieldSourceInfo();
                sourceInfo.setDataSource(event.getDataSource());
                sourceInfo.setSourcePlatform(event.getSourcePlatform());
                sourceInfo.setLastUpdate(LocalDateTime.now());
                sourceInfo.setOldValue(event.getOldValue() != null ? event.getOldValue().toString() : null);
                sourceInfo.setNewValue(event.getNewValue() != null ? event.getNewValue().toString() : null);

                groupedEvents.get(key).put(event.getFieldName(), sourceInfo);
            }

            // 为每个记录创建JSON格式的追踪记录
            List<ProductFieldSourceTracking> trackingList = new ArrayList<>();
            for (Map.Entry<String, Map<String, ProductFieldSourceTracking.FieldSourceInfo>> entry : groupedEvents.entrySet()) {
                String key = entry.getKey();
                ProductFieldSourceTracking tracking = new ProductFieldSourceTracking();
                tracking.setTableName(tableNames.get(key));
                tracking.setRecordId(recordIds.get(key));
                tracking.setFieldSources(entry.getValue());
                tracking.setCreateTime(LocalDateTime.now());

                trackingList.add(tracking);
            }

            // 批量插入或更新
            if (!trackingList.isEmpty()) {
                int affectedRows = fieldTrackingMapper.batchInsertOrUpdate(trackingList);
                log.debug("批量处理字段追踪事件: {} 个事件, {} 个记录, 影响行数: {}", events.size(), trackingList.size(), affectedRows);
            }
        } catch (Exception e) {
            log.error("批量处理字段追踪事件失败", e);
        }
    }


    /**
     * 查询指定记录的字段来源信息（带缓存）
     */
    @Cacheable(cacheNames = RedisKeyConstants.FIELD_SOURCES + "#" + RedisKeyConstants.FIELD_SOURCES_TIMEOUT, key = "#tableName + ':' + #recordId")
    public Map<String, Integer> getFieldSources(String tableName, Long recordId) {
        try {
            Map<String, Object> result = fieldTrackingMapper.selectFieldSources(tableName, recordId);
            Map<String, Integer> fieldSources = new HashMap<>();
            for (Map.Entry<String, Object> entry : result.entrySet()) {
                String fieldName = entry.getKey();
                Object fieldInfo = entry.getValue();

                if (fieldInfo instanceof Map) {
                    @SuppressWarnings("unchecked") Map<String, Object> infoMap = (Map<String, Object>) fieldInfo;
                    Object dataSourceObj = infoMap.get("dataSource");

                    if (dataSourceObj instanceof Number) {
                        fieldSources.put(fieldName, ((Number) dataSourceObj).intValue());
                    }
                }
            }

            log.debug("查询字段来源信息: table={}, recordId={}, 找到{}个字段", tableName, recordId, fieldSources.size());
            return fieldSources;
        } catch (Exception e) {
            log.error("查询字段来源信息失败: table={}, recordId={}", tableName, recordId, e);
            return new HashMap<>();
        }
    }

    /**
     * 记录新建记录的所有字段来源
     */
    @Async
    public void recordNewRecordFields(Object entity, String tableName, Long recordId, Integer dataSource, String sourcePlatform) {
        log.info("【异步开始】记录新建记录字段来源: table={}, recordId={}, dataSource={}, thread={}, enabled={}", tableName, recordId, dataSource, Thread.currentThread().getName(), config.getEnabled());

        if (!config.getEnabled()) {
            log.info("字段追踪功能已禁用，跳过记录");
            return;
        }

        if (entity == null) {
            log.warn("实体对象为空，跳过字段来源记录");
            return;
        }

        try {
            List<FieldChangeEvent> events = new ArrayList<>();

            // 通过反射获取所有字段
            java.lang.reflect.Field[] fields = entity.getClass().getDeclaredFields();
            log.info("实体类 {} 共有 {} 个字段，表名: {}", entity.getClass().getSimpleName(), fields.length, tableName);

            // 检查配置状态
            log.info("字段追踪配置检查: enabled={}, shouldTrackTable({})={}", config.getEnabled(), tableName, config.shouldTrackTable(tableName));

            int totalFields = 0;
            int trackableFields = 0;
            int nonEmptyFields = 0;

            for (java.lang.reflect.Field field : fields) {
                totalFields++;
                if (shouldTrackFieldForNewRecord(field, tableName)) {
                    trackableFields++;
                    try {
                        field.setAccessible(true);
                        Object value = field.get(entity);

                        log.debug("检查字段: field={}, value={}, type={}", field.getName(), value, value != null ? value.getClass().getSimpleName() : "null");

                        // 只记录非空字段
                        if (value != null && !isEmptyValue(value)) {
                            nonEmptyFields++;
                            events.add(new FieldChangeEvent(tableName, recordId, field.getName(), dataSource, sourcePlatform, null, value));
                            log.info("添加字段追踪事件: field={}, value={}", field.getName(), value);
                        } else {
                            log.debug("跳过空字段: field={}, value={}", field.getName(), value);
                        }
                    } catch (IllegalAccessException e) {
                        log.debug("无法访问字段: {}", field.getName(), e);
                    }
                } else {
                    log.debug("字段不需要追踪: field={}", field.getName());
                }
            }

            log.info("字段统计: 总字段数={}, 可追踪字段数={}, 非空字段数={}", totalFields, trackableFields, nonEmptyFields);

            // 批量记录字段来源（JSON格式，一次性存储）
            if (!events.isEmpty()) {
                log.info("准备记录 {} 个字段的来源信息", events.size());

                // 将所有字段事件转换为JSON格式
                ProductFieldSourceTracking tracking = new ProductFieldSourceTracking();
                tracking.setTableName(tableName);
                tracking.setRecordId(recordId);
                tracking.setCreateTime(LocalDateTime.now());

                Map<String, ProductFieldSourceTracking.FieldSourceInfo> fieldSources = new HashMap<>();
                for (FieldChangeEvent event : events) {
                    ProductFieldSourceTracking.FieldSourceInfo sourceInfo = new ProductFieldSourceTracking.FieldSourceInfo();
                    sourceInfo.setDataSource(event.getDataSource());
                    sourceInfo.setSourcePlatform(event.getSourcePlatform());
                    sourceInfo.setLastUpdate(LocalDateTime.now());
                    sourceInfo.setOldValue(event.getOldValue() != null ? event.getOldValue().toString() : null);
                    sourceInfo.setNewValue(event.getNewValue() != null ? event.getNewValue().toString() : null);

                    fieldSources.put(event.getFieldName(), sourceInfo);
                }
                tracking.setFieldSources(fieldSources);

                int affectedRows = fieldTrackingMapper.insertOrUpdate(tracking);
                log.info("【异步完成】成功记录新建记录的字段来源: table={}, recordId={}, fields={}, 影响行数={}, thread={}", tableName, recordId, events.size(), affectedRows, Thread.currentThread().getName());
            } else {
                log.warn("没有找到需要追踪的字段: table={}, recordId={}", tableName, recordId);
            }
        } catch (Exception e) {
            log.error("【异步异常】记录新建记录字段来源失败: table={}, recordId={}, thread={}", tableName, recordId, Thread.currentThread().getName(), e);
        }
    }

    /**
     * 判断是否应该追踪新记录的字段
     */
    private boolean shouldTrackFieldForNewRecord(java.lang.reflect.Field field, String tableName) {
        String fieldName = field.getName();

        // 排除系统字段和静态字段
        if (fieldName.equals("id") || fieldName.equals("createTime") || fieldName.equals("updateTime") || fieldName.startsWith("$") || java.lang.reflect.Modifier.isStatic(field.getModifiers())) {
            return false;
        }

        return config.shouldTrackField(tableName, fieldName);
    }

    /**
     * 判断值是否为空
     */
    private boolean isEmptyValue(Object value) {
        if (value == null) {
            return true;
        }
        if (value instanceof String) {
            return ((String) value).trim().isEmpty();
        }
        return false;
    }

    /**
     * 获取队列状态信息
     */
    public String getQueueStatus() {
        return String.format("队列大小: %d/%d", eventQueue.size(), config.getQueueCapacity());
    }


    /**
     * 记录新建记录的所有字段来源（同步版本，用于初始化）
     */
    public void recordNewRecordFieldsSync(Object entity, String tableName, Long recordId, Integer dataSource, String sourcePlatform) {
        log.info("【同步开始】记录新建记录字段来源: table={}, recordId={}, dataSource={}, thread={}, enabled={}", tableName, recordId, dataSource, Thread.currentThread().getName(), config.getEnabled());

        if (!config.getEnabled()) {
            log.info("字段追踪功能已禁用，跳过记录");
            return;
        }

        if (entity == null) {
            log.warn("实体对象为空，跳过字段来源记录");
            return;
        }

        try {
            List<FieldChangeEvent> events = new ArrayList<>();
            java.lang.reflect.Field[] fields = entity.getClass().getDeclaredFields();

            int totalFields = 0;
            int trackableFields = 0;
            int nonEmptyFields = 0;

            for (java.lang.reflect.Field field : fields) {
                totalFields++;
                if (shouldTrackFieldForNewRecord(field, tableName)) {
                    trackableFields++;
                    try {
                        field.setAccessible(true);
                        Object value = field.get(entity);

                        log.debug("检查字段: field={}, value={}, type={}", field.getName(), value, value != null ? value.getClass().getSimpleName() : "null");

                        // 只记录非空字段
                        if (value != null && !isEmptyValue(value)) {
                            nonEmptyFields++;
                            events.add(new FieldChangeEvent(tableName, recordId, field.getName(), dataSource, sourcePlatform, null, value));
                            log.debug("添加字段追踪事件: field={}, value={}", field.getName(), value);
                        } else {
                            log.debug("跳过空字段: field={}, value={}", field.getName(), value);
                        }
                    } catch (IllegalAccessException e) {
                        log.debug("无法访问字段: {}", field.getName(), e);
                    }
                } else {
                    log.debug("字段不需要追踪: field={}", field.getName());
                }
            }

            log.info("字段统计: 总字段数={}, 可追踪字段数={}, 非空字段数={}", totalFields, trackableFields, nonEmptyFields);

            // 同步记录字段来源（JSON格式，一次性存储）
            if (!events.isEmpty()) {
                log.info("准备记录 {} 个字段的来源信息", events.size());

                // 将所有字段事件转换为JSON格式
                ProductFieldSourceTracking tracking = new ProductFieldSourceTracking();
                tracking.setTableName(tableName);
                tracking.setRecordId(recordId);
                tracking.setCreateTime(LocalDateTime.now());

                Map<String, ProductFieldSourceTracking.FieldSourceInfo> fieldSources = new HashMap<>();
                for (FieldChangeEvent event : events) {
                    ProductFieldSourceTracking.FieldSourceInfo sourceInfo = new ProductFieldSourceTracking.FieldSourceInfo();
                    sourceInfo.setDataSource(event.getDataSource());
                    sourceInfo.setSourcePlatform(event.getSourcePlatform());
                    sourceInfo.setLastUpdate(LocalDateTime.now());
                    sourceInfo.setOldValue(null); // 新建记录没有旧值
                    sourceInfo.setNewValue(event.getNewValue() != null ? event.getNewValue().toString() : null);

                    fieldSources.put(event.getFieldName(), sourceInfo);
                }

                tracking.setFieldSources(fieldSources);

                int affectedRows = fieldTrackingMapper.insertOrUpdate(tracking);
                log.info("【同步完成】成功记录新建记录的字段来源: table={}, recordId={}, fields={}, 影响行数={}, thread={}", tableName, recordId, events.size(), affectedRows, Thread.currentThread().getName());
            } else {
                log.warn("没有找到需要追踪的字段: table={}, recordId={}", tableName, recordId);
            }
        } catch (Exception e) {
            log.error("【同步异常】记录新建记录字段来源失败: table={}, recordId={}, thread={}", tableName, recordId, Thread.currentThread().getName(), e);
            throw e; // 同步版本抛出异常，便于调用方处理
        }
    }

    /**
     * 清除指定记录的字段来源缓存
     *
     * @param tableName 表名
     * @param recordId  记录ID
     */
    @CacheEvict(value = "field_sources", key = "#tableName + ':' + #recordId")
    public void clearFieldSourceCache(String tableName, Long recordId) {
        log.info("清除字段来源缓存: table={}, recordId={}", tableName, recordId);
    }

    /**
     * 清除所有字段来源缓存
     */
    @CacheEvict(value = "field_sources", allEntries = true)
    public void clearAllFieldSourceCache() {
        log.info("清除所有字段来源缓存");
    }

    /**
     * 按表名清除字段来源缓存
     *
     * @param tableName 表名
     * @return 清除的缓存数量
     */
    public long clearFieldSourceCacheByTable(String tableName) {
        try {
            String pattern = "field_sources::" + tableName + ":*";
            Set<String> keys = redisTemplate.keys(pattern);

            if (!keys.isEmpty()) {
                Long deletedCount = redisTemplate.delete(keys);
                log.info("按表名清除字段来源缓存: table={}, 清除数量={}", tableName, deletedCount);
                return deletedCount;
            } else {
                log.info("按表名清除字段来源缓存: table={}, 未找到匹配的缓存", tableName);
                return 0;
            }
        } catch (Exception e) {
            log.error("按表名清除字段来源缓存失败: table={}", tableName, e);
            return 0;
        }
    }

    /**
     * 按模式清除字段来源缓存
     *
     * @param pattern 缓存键模式（支持通配符）
     * @return 清除的缓存数量
     */
    public long clearFieldSourceCacheByPattern(String pattern) {
        try {
            // 确保模式以field_sources::开头
            String fullPattern = pattern.startsWith("field_sources::") ? pattern : "field_sources::" + pattern;
            Set<String> keys = redisTemplate.keys(fullPattern);

            if (!keys.isEmpty()) {
                Long deletedCount = redisTemplate.delete(keys);
                log.info("按模式清除字段来源缓存: pattern={}, 清除数量={}", fullPattern, deletedCount);
                return deletedCount;
            } else {
                log.info("按模式清除字段来源缓存: pattern={}, 未找到匹配的缓存", fullPattern);
                return 0;
            }
        } catch (Exception e) {
            log.error("按模式清除字段来源缓存失败: pattern={}", pattern, e);
            return 0;
        }
    }

    /**
     * 获取字段来源缓存统计信息
     *
     * @return 缓存统计信息
     */
    public Map<String, Object> getFieldSourceCacheStats() {
        Map<String, Object> stats = new HashMap<>();

        try {
            // 统计总缓存数量
            Set<String> allKeys = redisTemplate.keys("field_sources::*");
            stats.put("totalCacheCount", allKeys.size());

            // 按表名统计
            Map<String, Integer> tableStats = new HashMap<>();
            for (String key : allKeys) {
                // 解析键格式: field_sources::tableName:recordId
                String[] parts = key.split("::");
                if (parts.length >= 2) {
                    String[] tableAndRecord = parts[1].split(":");
                    if (tableAndRecord.length >= 1) {
                        String tableName = tableAndRecord[0];
                        tableStats.put(tableName, tableStats.getOrDefault(tableName, 0) + 1);
                    }
                }
            }
            stats.put("tableStats", tableStats);

            log.debug("字段来源缓存统计: {}", stats);
        } catch (Exception e) {
            log.error("获取字段来源缓存统计失败", e);
            stats.put("error", e.getMessage());
        }

        return stats;
    }

    /**
     * 统计指定表和记录ID的字段追踪记录数量
     *
     * @param tableName 表名
     * @param recordId  记录ID
     * @return 记录数量
     */
    public long countTrackingRecords(String tableName, Long recordId) {
        try {
            // 使用自定义查询统计特定记录的数量
            return fieldTrackingMapper.countSpecificRecord(tableName, recordId);
        } catch (Exception e) {
            log.error("统计字段追踪记录数量失败: table={}, recordId={}", tableName, recordId, e);
            return 0;
        }
    }
}

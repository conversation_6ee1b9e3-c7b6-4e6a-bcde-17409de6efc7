package ai.pricefox.mallfox.service.tracking;

import ai.pricefox.mallfox.config.FieldTrackingConfig;
import ai.pricefox.mallfox.model.dto.FieldChangeEvent;
import ai.pricefox.mallfox.model.dto.FieldTrackingContext;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.util.*;

/**
 * 可追踪的实体包装器
 * 自动追踪字段变更，无需修改现有代码
 * 
 * <AUTHOR>
 * @since 2025-01-14
 */
@Slf4j
public class TrackableEntity<T> {
    private final T originalEntity;
    private final FieldTrackingContext context;
    private final Map<String, Object> originalValues;
    private final Set<String> changedFields;
    private final FieldTrackingService trackingService;
    private final FieldTrackingConfig config;

    public TrackableEntity(T entity, FieldTrackingContext context, 
                          FieldTrackingService trackingService, FieldTrackingConfig config) {
        this.originalEntity = entity;
        this.context = context;
        this.trackingService = trackingService;
        this.config = config;
        this.originalValues = new HashMap<>();
        this.changedFields = new HashSet<>();
        
        // 捕获初始状态
        captureInitialState();
    }

    /**
     * 获取原始实体（用于正常的业务操作）
     */
    public T getEntity() {
        return originalEntity;
    }

    /**
     * 捕获当前实体的所有字段值作为初始状态
     */
    private void captureInitialState() {
        if (originalEntity == null) {
            return;
        }

        Field[] fields = originalEntity.getClass().getDeclaredFields();
        for (Field field : fields) {
            if (shouldTrackField(field)) {
                try {
                    field.setAccessible(true);
                    Object value = field.get(originalEntity);
                    originalValues.put(field.getName(), value);
                } catch (IllegalAccessException e) {
                    log.debug("无法访问字段: {}", field.getName(), e);
                }
            }
        }
    }

    /**
     * 检测并记录字段变更
     * 在业务逻辑执行后调用此方法
     */
    public void detectAndRecordChanges() {
        if (originalEntity == null || !config.getEnabled()) {
            return;
        }

        Field[] fields = originalEntity.getClass().getDeclaredFields();
        List<FieldChangeEvent> changes = new ArrayList<>();

        for (Field field : fields) {
            if (shouldTrackField(field)) {
                try {
                    field.setAccessible(true);
                    Object currentValue = field.get(originalEntity);
                    Object originalValue = originalValues.get(field.getName());

                    if (isValueChanged(originalValue, currentValue)) {
                        changes.add(new FieldChangeEvent(
                            context.getTableName(),
                            context.getRecordId(),
                            field.getName(),
                            context.getDataSource(),
                            context.getSourcePlatform(),
                            originalValue,
                            currentValue
                        ));
                        changedFields.add(field.getName());
                    }
                } catch (IllegalAccessException e) {
                    log.warn("检测字段变更失败: {}", field.getName(), e);
                }
            }
        }

        // 异步批量记录变更
        if (!changes.isEmpty()) {
            trackingService.batchRecordFieldChanges(changes);
            log.debug("检测到{}个字段变更，已提交追踪", changes.size());
        }
    }

    /**
     * 判断字段值是否发生变更
     */
    private boolean isValueChanged(Object oldValue, Object newValue) {
        if (oldValue == null && newValue == null) {
            return false;
        }
        if (oldValue == null || newValue == null) {
            return true;
        }
        return !oldValue.equals(newValue);
    }

    /**
     * 判断是否应该追踪该字段
     */
    private boolean shouldTrackField(Field field) {
        String fieldName = field.getName();
        
        // 排除系统字段和静态字段
        if (fieldName.startsWith("$") || 
            Modifier.isStatic(field.getModifiers()) ||
            !config.shouldTrackField(context.getTableName(), fieldName)) {
            return false;
        }

        // 应用上下文过滤
        return context.shouldTrackField(fieldName);
    }

    /**
     * 获取变更的字段列表
     */
    public Set<String> getChangedFields() {
        return new HashSet<>(changedFields);
    }

    /**
     * 获取字段的原始值
     */
    public Object getOriginalValue(String fieldName) {
        return originalValues.get(fieldName);
    }

    /**
     * 获取追踪上下文
     */
    public FieldTrackingContext getContext() {
        return context;
    }
}

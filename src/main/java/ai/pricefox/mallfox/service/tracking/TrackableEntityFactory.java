package ai.pricefox.mallfox.service.tracking;

import ai.pricefox.mallfox.config.FieldTrackingConfig;
import ai.pricefox.mallfox.domain.product.ProductDataOffers;
import ai.pricefox.mallfox.domain.product.ProductDataSimplify;
import ai.pricefox.mallfox.model.dto.FieldTrackingContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 可追踪实体工厂
 * 
 * <AUTHOR>
 * @since 2025-01-14
 */
@Component
@Slf4j
public class TrackableEntityFactory {

    private final FieldTrackingService fieldTrackingService;
    private final FieldTrackingConfig fieldTrackingConfig;

    public TrackableEntityFactory(FieldTrackingService fieldTrackingService, FieldTrackingConfig fieldTrackingConfig) {
        this.fieldTrackingService = fieldTrackingService;
        this.fieldTrackingConfig = fieldTrackingConfig;
    }

    /**
     * 创建ProductDataOffers的可追踪包装器
     */
    public TrackableEntity<ProductDataOffers> createTrackableOffers(ProductDataOffers offers, Integer dataSource) {
        if (offers == null || !fieldTrackingConfig.getEnabled()) {
            return null;
        }
        
        FieldTrackingContext context = FieldTrackingContext.forOffers(offers.getId(), dataSource);
        return new TrackableEntity<>(offers, context, fieldTrackingService, fieldTrackingConfig);
    }

    /**
     * 创建ProductDataOffers的可追踪包装器（带平台信息）
     */
    public TrackableEntity<ProductDataOffers> createTrackableOffers(ProductDataOffers offers, Integer dataSource, String sourcePlatform) {
        if (offers == null || !fieldTrackingConfig.getEnabled()) {
            return null;
        }
        
        FieldTrackingContext context = FieldTrackingContext.forOffers(offers.getId(), dataSource, sourcePlatform);
        return new TrackableEntity<>(offers, context, fieldTrackingService, fieldTrackingConfig);
    }

    /**
     * 创建ProductDataSimplify的可追踪包装器
     */
    public TrackableEntity<ProductDataSimplify> createTrackableSimplify(ProductDataSimplify simplify, Integer dataSource) {
        if (simplify == null || !fieldTrackingConfig.getEnabled()) {
            return null;
        }
        
        FieldTrackingContext context = FieldTrackingContext.forSimplify(simplify.getId(), dataSource);
        return new TrackableEntity<>(simplify, context, fieldTrackingService, fieldTrackingConfig);
    }

    /**
     * 创建ProductDataSimplify的可追踪包装器（带平台信息）
     */
    public TrackableEntity<ProductDataSimplify> createTrackableSimplify(ProductDataSimplify simplify, Integer dataSource, String sourcePlatform) {
        if (simplify == null || !fieldTrackingConfig.getEnabled()) {
            return null;
        }
        
        FieldTrackingContext context = FieldTrackingContext.forSimplify(simplify.getId(), dataSource, sourcePlatform);
        return new TrackableEntity<>(simplify, context, fieldTrackingService, fieldTrackingConfig);
    }

    /**
     * 通用创建方法
     */
    public <T> TrackableEntity<T> createTrackable(T entity, String tableName, Long recordId, Integer dataSource) {
        if (entity == null || !fieldTrackingConfig.getEnabled()) {
            return null;
        }
        
        FieldTrackingContext context = new FieldTrackingContext(tableName, recordId, dataSource, null, null, null);
        return new TrackableEntity<>(entity, context, fieldTrackingService, fieldTrackingConfig);
    }

    /**
     * 通用创建方法（带平台信息）
     */
    public <T> TrackableEntity<T> createTrackable(T entity, String tableName, Long recordId, Integer dataSource, String sourcePlatform) {
        if (entity == null || !fieldTrackingConfig.getEnabled()) {
            return null;
        }
        
        FieldTrackingContext context = new FieldTrackingContext(tableName, recordId, dataSource, sourcePlatform, null, null);
        return new TrackableEntity<>(entity, context, fieldTrackingService, fieldTrackingConfig);
    }
}

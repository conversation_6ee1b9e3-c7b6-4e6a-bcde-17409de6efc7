package ai.pricefox.mallfox.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.Base64;

/**
 * AES加解密工具类
 */
@Slf4j
public class AESUtil {

    /**
     * 加密算法
     */
    private static final String ALGORITHM = "AES";
    
    /**
     * 加密模式
     */
    private static final String TRANSFORMATION = "AES/ECB/PKCS5Padding";
    
    /**
     * 密钥长度
     */
    private static final int KEY_LENGTH = 128;
    
    /**
     * 默认密钥（实际项目中应该从配置文件读取）
     */
    private static final String DEFAULT_SECRET_KEY = "PriceFoxMallKey!";

    /**
     * 生成AES密钥
     *
     * @return Base64编码的密钥
     */
    public static String generateKey() {
        try {
            KeyGenerator keyGenerator = KeyGenerator.getInstance(ALGORITHM);
            keyGenerator.init(KEY_LENGTH);
            SecretKey secretKey = keyGenerator.generateKey();
            return Base64.getEncoder().encodeToString(secretKey.getEncoded());
        } catch (NoSuchAlgorithmException e) {
            log.error("生成AES密钥失败", e);
            throw new RuntimeException("生成AES密钥失败", e);
        }
    }

    /**
     * 加密
     *
     * @param plainText 明文
     * @return 加密后的Base64字符串
     */
    public static String encrypt(String plainText) {
        return encrypt(plainText, DEFAULT_SECRET_KEY);
    }

    /**
     * 加密
     *
     * @param plainText 明文
     * @param secretKey 密钥
     * @return 加密后的Base64字符串
     */
    public static String encrypt(String plainText, String secretKey) {
        if (!StringUtils.hasText(plainText)) {
            return plainText;
        }

        try {
            SecretKeySpec keySpec = new SecretKeySpec(secretKey.getBytes(StandardCharsets.UTF_8), ALGORITHM);
            Cipher cipher = Cipher.getInstance(TRANSFORMATION);
            cipher.init(Cipher.ENCRYPT_MODE, keySpec);
            
            byte[] encrypted = cipher.doFinal(plainText.getBytes(StandardCharsets.UTF_8));
            return Base64.getEncoder().encodeToString(encrypted);
        } catch (Exception e) {
            log.error("AES加密失败: plainText={}", plainText, e);
            throw new RuntimeException("AES加密失败", e);
        }
    }

    /**
     * 解密
     *
     * @param encryptedText 加密的Base64字符串
     * @return 解密后的明文
     */
    public static String decrypt(String encryptedText) {
        return decrypt(encryptedText, DEFAULT_SECRET_KEY);
    }

    /**
     * 解密
     *
     * @param encryptedText 加密的Base64字符串
     * @param secretKey 密钥
     * @return 解密后的明文
     */
    public static String decrypt(String encryptedText, String secretKey) {
        if (!StringUtils.hasText(encryptedText)) {
            return encryptedText;
        }

        try {
            SecretKeySpec keySpec = new SecretKeySpec(secretKey.getBytes(StandardCharsets.UTF_8), ALGORITHM);
            Cipher cipher = Cipher.getInstance(TRANSFORMATION);
            cipher.init(Cipher.DECRYPT_MODE, keySpec);
            
            byte[] decoded = Base64.getDecoder().decode(encryptedText);
            byte[] decrypted = cipher.doFinal(decoded);
            return new String(decrypted, StandardCharsets.UTF_8);
        } catch (Exception e) {
            log.error("AES解密失败: encryptedText={}", encryptedText, e);
            throw new RuntimeException("AES解密失败", e);
        }
    }

    /**
     * 验证密码
     *
     * @param plainPassword 明文密码
     * @param encryptedPassword 加密后的密码
     * @return 是否匹配
     */
    public static boolean verifyPassword(String plainPassword, String encryptedPassword) {
        if (!StringUtils.hasText(plainPassword) || !StringUtils.hasText(encryptedPassword)) {
            return false;
        }

        try {
            String decryptedPassword = decrypt(encryptedPassword);
            return plainPassword.equals(decryptedPassword);
        } catch (Exception e) {
            log.error("密码验证失败: plainPassword={}, encryptedPassword={}", plainPassword, encryptedPassword, e);
            return false;
        }
    }

    /**
     * 检查字符串是否为加密格式（简单判断是否为Base64格式）
     *
     * @param text 待检查的字符串
     * @return 是否为加密格式
     */
    public static boolean isEncrypted(String text) {
        if (!StringUtils.hasText(text)) {
            return false;
        }

        try {
            // 尝试Base64解码，如果成功且长度合理，认为是加密格式
            byte[] decoded = Base64.getDecoder().decode(text);
            return decoded.length > 0 && decoded.length % 16 == 0; // AES块大小为16字节
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 安全地加密密码（如果已经是加密格式则不再加密）
     *
     * @param password 密码
     * @return 加密后的密码
     */
    public static String encryptPasswordSafely(String password) {
        if (!StringUtils.hasText(password)) {
            return password;
        }

        // 如果已经是加密格式，直接返回
        if (isEncrypted(password)) {
            return password;
        }

        // 否则进行加密
        return encrypt(password);
    }

    /**
     * 测试方法
     */
    public static void main(String[] args) {
        String originalPassword = "123456";
        System.out.println("原始密码: " + originalPassword);

        String encrypted = encrypt(originalPassword);
        System.out.println("加密后: " + encrypted);

        String decrypted = decrypt(encrypted);
        System.out.println("解密后: " + decrypted);

        boolean isValid = verifyPassword(originalPassword, encrypted);
        System.out.println("密码验证: " + isValid);

        System.out.println("是否为加密格式: " + isEncrypted(encrypted));
        System.out.println("是否为加密格式: " + isEncrypted(originalPassword));
    }
}

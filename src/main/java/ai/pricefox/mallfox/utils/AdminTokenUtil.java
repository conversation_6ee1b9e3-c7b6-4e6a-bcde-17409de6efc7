package ai.pricefox.mallfox.utils;

import ai.pricefox.mallfox.common.exception.enums.ErrorCodeConstants;
import ai.pricefox.mallfox.domain.admin.AdminUser;
import ai.pricefox.mallfox.service.admin.AdminTokenService;
import cn.dev33.satoken.context.SaHolder;
import cn.dev33.satoken.stp.SaTokenInfo;
import cn.dev33.satoken.stp.StpUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import static ai.pricefox.mallfox.common.exception.util.ServiceExceptionUtil.exception;

/**
 * 后台Token工具类
 */
@Slf4j
@Component
public class AdminTokenUtil {

    private static AdminTokenService adminTokenService;

    @Autowired
    public void setAdminTokenService(AdminTokenService adminTokenService) {
        AdminTokenUtil.adminTokenService = adminTokenService;
    }

    /**
     * 后台用户登录
     *
     * @param userId 用户ID
     * @return Token信息
     */
    public static SaTokenInfo login(Long userId) {
        // 先设置SaToken的登录状态
        StpUtil.login(userId);
        // 使用自定义的Token管理服务
        SaTokenInfo tokenInfo = adminTokenService.generateAndSaveToken(userId);
        return tokenInfo;
    }

    /**
     * 后台用户登出
     */
    public static void logout() {
        Long userId = getCurrentUserIdOrNull();
        if (userId != null) {
            // 清除数据库和Redis中的Token
            adminTokenService.removeToken(userId);
        }
        // 清除SaToken的登录状态
        StpUtil.logout();
    }

    /**
     * 获取当前登录后台用户ID
     *
     * @return 用户ID
     */
    public static Long getCurrentUserId() {
        try {
            // 先尝试从请求头中获取Token
            String token = getTokenFromRequest();
            if (StringUtils.hasText(token)) {
                AdminUser adminUser = adminTokenService.getUserByToken(token);
                if (adminUser != null) {
                    return adminUser.getId();
                }
            }
            
            // 如果自定义Token验证失败，尝试SaToken
            return StpUtil.getLoginIdAsLong();
        } catch (Exception e) {
            throw exception(ErrorCodeConstants.AUTH_TOKEN_INVALID);
        }
    }

    /**
     * 获取当前登录的用户名称
     */
    public static String getCurrentUsername() {
        try {
            // 先尝试从请求头中获取Token
            String token = getTokenFromRequest();
            if (StringUtils.hasText(token)) {
                AdminUser adminUser = adminTokenService.getUserByToken(token);
                if (adminUser != null) {
                    return adminUser.getUsername();
                }
            }
            return  StpUtil.getLoginIdAsString();
        } catch (Exception e) {
            throw exception(ErrorCodeConstants.AUTH_TOKEN_INVALID);
        }
    }

    /**
     * 获取当前登录后台用户ID（可为空）
     *
     * @return 用户ID，未登录返回null
     */
    public static Long getCurrentUserIdOrNull() {
        try {
            // 先尝试从请求头中获取Token
            String token = getTokenFromRequest();
            if (StringUtils.hasText(token)) {
                AdminUser adminUser = adminTokenService.getUserByToken(token);
                if (adminUser != null) {
                    return adminUser.getId();
                }
            }
            
            // 如果自定义Token验证失败，尝试SaToken
            if (StpUtil.isLogin()) {
                return StpUtil.getLoginIdAsLong();
            }
            return null;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 检查是否已登录
     *
     * @return 是否已登录
     */
    public static boolean isLogin() {
        try {
            // 先尝试从请求头中获取Token
            String token = getTokenFromRequest();
            if (StringUtils.hasText(token)) {
                AdminUser adminUser = adminTokenService.getUserByToken(token);
                if (adminUser != null) {
                    return true;
                }
            }
            
            // 如果自定义Token验证失败，尝试SaToken
            return StpUtil.isLogin();
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 检查登录状态，未登录抛出异常
     */
    public static void checkLogin() {
        if (!isLogin()) {
            throw exception(ErrorCodeConstants.AUTH_TOKEN_INVALID, "后台用户未登录");
        }
    }

    /**
     * 获取Token信息
     *
     * @return Token信息
     */
    public static SaTokenInfo getTokenInfo() {
        return StpUtil.getTokenInfo();
    }

    /**
     * 获取Token值
     *
     * @return Token值
     */
    public static String getTokenValue() {
        return StpUtil.getTokenValue();
    }

    /**
     * 踢下线指定后台用户
     *
     * @param userId 用户ID
     */
    public static void kickout(Long userId) {
        // 使用自定义的Token管理服务踢下线
        adminTokenService.kickoutUser(userId);
    }

    /**
     * 封禁指定后台用户
     *
     * @param userId 用户ID
     * @param time   封禁时长（秒）
     */
    public static void disable(Long userId, long time) {
        StpUtil.disable(userId, time);
    }

    /**
     * 解封指定后台用户
     *
     * @param userId 用户ID
     */
    public static void untieDisable(Long userId) {
        StpUtil.untieDisable(userId);
    }

    /**
     * 检查指定后台用户是否被封禁
     *
     * @param userId 用户ID
     * @return 是否被封禁
     */
    public static boolean isDisable(Long userId) {
        return StpUtil.isDisable(userId);
    }

    /**
     * 获取Token剩余有效时间
     *
     * @return 剩余有效时间（秒）
     */
    public static long getTokenTimeout() {
        return StpUtil.getTokenTimeout();
    }

    /**
     * 续签Token
     *
     * @param timeout 续签时长（秒）
     */
    public static void renewTimeout(long timeout) {
        StpUtil.renewTimeout(timeout);
    }

    /**
     * 刷新Token
     */
    public static void refreshToken() {
        StpUtil.renewTimeout(StpUtil.getTokenTimeout());
    }

    /**
     * 获取当前会话的所有Token
     *
     * @return Token列表
     */
    public static java.util.List<String> getTokenValueListByLoginId() {
        return StpUtil.getTokenValueListByLoginId(getCurrentUserId());
    }

    /**
     * 注销指定Token
     *
     * @param tokenValue Token值
     */
    public static void logoutByTokenValue(String tokenValue) {
        // 根据Token获取用户信息并清除
        AdminUser adminUser = adminTokenService.getUserByToken(tokenValue);
        if (adminUser != null) {
            adminTokenService.removeToken(adminUser.getId());
        }
        StpUtil.logoutByTokenValue(tokenValue);
    }

    /**
     * 刷新Token
     *
     * @param userId 用户ID
     * @param refreshToken 刷新令牌
     * @return 新的Token信息
     */
    public static SaTokenInfo refreshToken(Long userId, String refreshToken) {
        return adminTokenService.refreshToken(userId, refreshToken);
    }

    /**
     * 验证Token有效性
     *
     * @param userId 用户ID
     * @param accessToken 访问令牌
     * @return 是否有效
     */
    public static boolean validateToken(Long userId, String accessToken) {
        return adminTokenService.validateToken(userId, accessToken);
    }

    /**
     * 根据Token获取后台用户信息
     *
     * @param accessToken 访问令牌
     * @return 用户信息
     */
    public static AdminUser getUserByToken(String accessToken) {
        return adminTokenService.getUserByToken(accessToken);
    }

    /**
     * 自动续期Token（如果需要）
     *
     * @param userId 用户ID
     * @param accessToken 访问令牌
     * @return 是否续期成功
     */
    public static boolean renewTokenIfNeeded(Long userId, String accessToken) {
        return adminTokenService.renewTokenIfNeeded(userId, accessToken);
    }

    /**
     * 从请求中获取Token
     *
     * @return Token值
     */
    private static String getTokenFromRequest() {
        try {
            // 从SaToken上下文中获取请求
            String tokenValue = SaHolder.getRequest().getHeader("Authorization");
            if (StringUtils.hasText(tokenValue)) {
                // 移除 "Bearer " 前缀
                if (tokenValue.startsWith("Bearer ")) {
                    return tokenValue.substring(7);
                }
                return tokenValue;
            }
            
            // 也可以从参数中获取
            tokenValue = SaHolder.getRequest().getParam("token");
            if (StringUtils.hasText(tokenValue)) {
                return tokenValue;
            }
            
            return null;
        } catch (Exception e) {
            log.warn("获取后台Token失败: {}", e.getMessage());
            return null;
        }
    }
}

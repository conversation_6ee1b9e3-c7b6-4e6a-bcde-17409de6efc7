package ai.pricefox.mallfox.utils;

import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @desc 字段映射工具类
 * @since 2025/6/29
 */
public class FieldMappingUtil {

    /**
     * 驼峰字段名到数据库字段名的映射
     */
    private static final Map<String, String> FIELD_MAPPING = new HashMap<>();

    static {
        // SPU相关字段
        FIELD_MAPPING.put("spuId", "s.spu_id");
        FIELD_MAPPING.put("skuId", "s.sku_id");
        FIELD_MAPPING.put("offerId", "s.id");
        FIELD_MAPPING.put("model", "s.model");
        FIELD_MAPPING.put("modelYear", "s.model_year");
        FIELD_MAPPING.put("color", "s.color");
        FIELD_MAPPING.put("storage", "s.storage");
        FIELD_MAPPING.put("serviceProvider", "s.service_provider");
        FIELD_MAPPING.put("conditionNew", "s.condition_new");
        FIELD_MAPPING.put("sourcePlatform", "s.source_platform");
        FIELD_MAPPING.put("platformSpuId", "s.platform_spu_id");
        FIELD_MAPPING.put("platformSkuId", "s.platform_sku_id");
        FIELD_MAPPING.put("productMainImageUrls", "s.product_main_image_urls");
        FIELD_MAPPING.put("productSpecColorUrl", "s.product_spec_color_url");
        FIELD_MAPPING.put("ramMemoryInstalledSize", "s.ram_memory_installed_size");
        FIELD_MAPPING.put("operatingSystem", "s.operating_system");
        FIELD_MAPPING.put("processor", "s.processor");
        FIELD_MAPPING.put("cellularTechnology", "s.cellular_technology");
        FIELD_MAPPING.put("screenSize", "s.screen_size");
        FIELD_MAPPING.put("resolution", "s.resolution");
        FIELD_MAPPING.put("refreshRate", "s.refresh_rate");
        FIELD_MAPPING.put("displayType", "s.display_type");
        FIELD_MAPPING.put("batteryPower", "s.battery_power");
        FIELD_MAPPING.put("averageTalkTime", "s.average_talk_time");
        FIELD_MAPPING.put("batteryChargeTime", "s.battery_charge_time");
        FIELD_MAPPING.put("frontPhotoSensorResolution", "s.front_photo_sensor_resolution");
        FIELD_MAPPING.put("rearFacingCameraPhotoSensorResolution", "s.rear_facing_camera_photo_sensor_resolution");
        FIELD_MAPPING.put("numberOfRearFacingCameras", "s.number_of_rear_facing_cameras");
        FIELD_MAPPING.put("effectiveVideoResolution", "s.effective_video_resolution");
        FIELD_MAPPING.put("videoCaptureResolution", "s.video_capture_resolution");
        FIELD_MAPPING.put("simCardSlotCount", "s.sim_card_slot_count");
        FIELD_MAPPING.put("connectorType", "s.connector_type");
        FIELD_MAPPING.put("waterResistance", "s.water_resistance");
        FIELD_MAPPING.put("dimensions", "s.dimensions");
        FIELD_MAPPING.put("itemWeight", "s.item_weight");
        FIELD_MAPPING.put("biometricSecurityFeature", "s.biometric_security_feature");
        FIELD_MAPPING.put("supportedSatelliteNavigationSystem", "s.supported_satellite_navigation_system");
        FIELD_MAPPING.put("features", "s.features");
        FIELD_MAPPING.put("returnPolicy", "s.return_policy");
        FIELD_MAPPING.put("paymentInstallment", "s.payment_installment");
        FIELD_MAPPING.put("installPayment", "s.install_payment");
        FIELD_MAPPING.put("warrantyDescription", "s.warranty_description");
        FIELD_MAPPING.put("reviewNumber", "s.review_number");
        FIELD_MAPPING.put("reviewScore", "s.review_score");
        FIELD_MAPPING.put("reviewRatingDistribution", "s.review_rating_distribution");
        FIELD_MAPPING.put("reviewDimensionalRatings", "s.review_dimensional_ratings");
        FIELD_MAPPING.put("reviewOverviewProsCons", "s.review_overview_pros_cons");
        FIELD_MAPPING.put("reviewProsConsByStar", "s.review_pros_cons_by_star");
        FIELD_MAPPING.put("mark", "s.mark");
        FIELD_MAPPING.put("simplifyCreateTime", "s.create_time");

        // 新增字段映射
        FIELD_MAPPING.put("specificationAiDescription", "s.specification_ai_description");
        FIELD_MAPPING.put("specificationAiCategory", "s.specification_ai_category");
        FIELD_MAPPING.put("reviewOneStar", "s.review_one_star");
        FIELD_MAPPING.put("reviewSpecialDescription1", "s.review_special_description_1");
        FIELD_MAPPING.put("reviewUnsatisfiedDescription", "s.review_unsatisfied_description");
        FIELD_MAPPING.put("reviewUnsatisfiedDescription2", "s.review_unsatisfied_description_2");
        FIELD_MAPPING.put("reviewSecondaryDescription", "s.review_secondary_description");
        FIELD_MAPPING.put("reviewPrimaryDescription", "s.review_primary_description");
        FIELD_MAPPING.put("reviewPrimaryDescription2", "s.review_primary_description_2");
        FIELD_MAPPING.put("reviewCustomerDescription", "s.review_customer_description");
        FIELD_MAPPING.put("reviewTags", "s.review_tags");
        FIELD_MAPPING.put("expertReview", "s.expert_review");
        FIELD_MAPPING.put("rankingCategoryRating", "s.ranking_category_rating");
        FIELD_MAPPING.put("rankingCategoryScore", "s.ranking_category_score");
        FIELD_MAPPING.put("accessoryADescription", "s.accessory_a_description");
        FIELD_MAPPING.put("accessoryACategory", "s.accessory_a_category");
        FIELD_MAPPING.put("accessoryADescription2", "s.accessory_a_description_2");
        FIELD_MAPPING.put("reviewOneToFiveStarRating", "s.review_one_to_five_star_rating");
        FIELD_MAPPING.put("reviewDataAnalysisRating", "s.review_data_analysis_rating");
        FIELD_MAPPING.put("reviewSpecialDescription", "s.review_special_description");
        FIELD_MAPPING.put("reviewUnsatisfiedDescription3", "s.review_unsatisfied_description_3");
        FIELD_MAPPING.put("reviewSecondaryDescription2", "s.review_secondary_description_2");
        FIELD_MAPPING.put("reviewPrimaryDescription3", "s.review_primary_description_3");
        FIELD_MAPPING.put("reviewCustomerDescription2", "s.review_customer_description_2");
        FIELD_MAPPING.put("reviewTags2", "s.review_tags_2");
        FIELD_MAPPING.put("expertReview2", "s.expert_review_2");
        FIELD_MAPPING.put("rankingCategoryRating2", "s.ranking_category_rating_2");
        FIELD_MAPPING.put("rankingCategoryScore2", "s.ranking_category_score_2");
        FIELD_MAPPING.put("rankingCategoryRating3", "s.ranking_category_rating_3");
        FIELD_MAPPING.put("rankingCategoryScore3", "s.ranking_category_score_3");
        FIELD_MAPPING.put("encyclopediaCategoryDescription", "s.encyclopedia_category_description");
        FIELD_MAPPING.put("encyclopediaCategoryDescription2", "s.encyclopedia_category_description_2");
        FIELD_MAPPING.put("encyclopediaPopularDescription", "s.encyclopedia_popular_description");
        FIELD_MAPPING.put("encyclopediaPopularDescription2", "s.encyclopedia_popular_description_2");
        FIELD_MAPPING.put("encyclopediaBannerDescription", "s.encyclopedia_banner_description");
        FIELD_MAPPING.put("editorUnsatisfiedDescription", "s.editor_unsatisfied_description");
        FIELD_MAPPING.put("editorUnsatisfiedDescription2", "s.editor_unsatisfied_description_2");
        FIELD_MAPPING.put("editorUnsatisfiedDescription3", "s.editor_unsatisfied_description_3");
        FIELD_MAPPING.put("editorUnsatisfiedDescription4", "s.editor_unsatisfied_description_4");
        FIELD_MAPPING.put("editorUnsatisfiedDescription5", "s.editor_unsatisfied_description_5");
        FIELD_MAPPING.put("editorUnsatisfiedDescription6", "s.editor_unsatisfied_description_6");
        FIELD_MAPPING.put("editorUnsatisfiedDescription7", "s.editor_unsatisfied_description_7");
        FIELD_MAPPING.put("editorUnsatisfiedDescription8", "s.editor_unsatisfied_description_8");
        FIELD_MAPPING.put("editorUnsatisfiedDescription9", "s.editor_unsatisfied_description_9");
        FIELD_MAPPING.put("editorUnsatisfiedDescription10", "s.editor_unsatisfied_description_10");
        FIELD_MAPPING.put("complaintColorSummary", "s.complaint_color_summary");
        FIELD_MAPPING.put("installPayment2", "s.install_payment_2");
        FIELD_MAPPING.put("priceUpdateTime2", "s.price_update_time_2");
        FIELD_MAPPING.put("categoryLevel2Extended", "s.category_level_2_extended");
        FIELD_MAPPING.put("productMainImage4", "s.product_main_image_4");
        FIELD_MAPPING.put("productMainImage5", "s.product_main_image_5");
        FIELD_MAPPING.put("productMainImage6", "s.product_main_image_6");
        FIELD_MAPPING.put("productMainImage7", "s.product_main_image_7");
        FIELD_MAPPING.put("productMainImage8", "s.product_main_image_8");
        FIELD_MAPPING.put("productMainImage9", "s.product_main_image_9");
        FIELD_MAPPING.put("productMainImage10", "s.product_main_image_10");
        FIELD_MAPPING.put("cellPhones", "s.cell_phones");

        // Offer相关字段
        FIELD_MAPPING.put("series", "o.series");
        FIELD_MAPPING.put("upcCode", "o.upc_code");
        FIELD_MAPPING.put("itemUrl", "o.item_url");
        FIELD_MAPPING.put("title", "o.title");
        FIELD_MAPPING.put("brand", "o.brand");
        FIELD_MAPPING.put("price", "o.price");
        FIELD_MAPPING.put("listPrice", "o.list_price");
        FIELD_MAPPING.put("discount", "o.discount");
        FIELD_MAPPING.put("inventory", "o.inventory");
        FIELD_MAPPING.put("salesLast30Days", "o.sales_last30_days");
        FIELD_MAPPING.put("seller", "o.seller");
        FIELD_MAPPING.put("merchantRating", "o.merchant_rating");
        FIELD_MAPPING.put("categoryLevel1", "o.category_level1");
        FIELD_MAPPING.put("categoryLevel2", "o.category_level2");
        FIELD_MAPPING.put("categoryLevel3", "o.category_level3");
        FIELD_MAPPING.put("offerUpdateTime", "o.update_time");

        // 默认排序字段
        FIELD_MAPPING.put("createTime", "s.create_time");
        FIELD_MAPPING.put("updateTime", "o.update_time");
    }

    /**
     * 将驼峰格式的字段名转换为数据库字段名
     *
     * @param camelCaseField 驼峰格式字段名
     * @return 数据库字段名，如果找不到映射则返回默认值
     */
    public static String convertToDbField(String camelCaseField) {
        if (!StringUtils.hasText(camelCaseField)) {
            return "s.spu_id"; // 默认按spuId排序
        }

        String dbField = FIELD_MAPPING.get(camelCaseField);
        if (dbField != null) {
            return dbField;
        }

        // 如果没有找到映射，尝试自动转换驼峰到下划线
        String underscoreField = camelToUnderscore(camelCaseField);

        // 默认假设是simplify表的字段
        return "s." + underscoreField;
    }

    /**
     * 将驼峰格式的字段名转换为适合GROUP BY查询的数据库字段名
     * 对于非GROUP BY字段，会自动添加MIN()聚合函数
     *
     * @param camelCaseField 驼峰格式字段名
     * @return 适合GROUP BY查询的数据库字段名
     */
    public static String convertToDbFieldForGroupBy(String camelCaseField) {
        if (!StringUtils.hasText(camelCaseField)) {
            return "s.spu_id"; // 默认按spuId排序
        }

        String dbField = convertToDbField(camelCaseField);

        // spuId 是 GROUP BY 字段，不需要聚合函数
        if ("s.spu_id".equals(dbField)) {
            return dbField;
        }

        // 其他字段需要使用MIN()聚合函数以符合GROUP BY规则
        return "MIN(" + dbField + ")";
    }

    /**
     * 验证排序方向
     *
     * @param sortOrder 排序方向
     * @return 有效的排序方向（ASC或DESC）
     */
    public static String validateSortOrder(String sortOrder) {
        if ("desc".equalsIgnoreCase(sortOrder) || "DESC".equalsIgnoreCase(sortOrder)) {
            return "DESC";
        }
        return "ASC";
    }

    /**
     * 驼峰转下划线
     *
     * @param camelCase 驼峰格式字符串
     * @return 下划线格式字符串
     */
    private static String camelToUnderscore(String camelCase) {
        if (!StringUtils.hasText(camelCase)) {
            return "";
        }

        StringBuilder result = new StringBuilder();
        for (int i = 0; i < camelCase.length(); i++) {
            char c = camelCase.charAt(i);
            if (Character.isUpperCase(c)) {
                if (i > 0) {
                    result.append('_');
                }
                result.append(Character.toLowerCase(c));
            } else {
                result.append(c);
            }
        }
        return result.toString();
    }

    /**
     * 获取所有支持的排序字段
     *
     * @return 支持的排序字段列表
     */
    public static Map<String, String> getSupportedSortFields() {
        return new HashMap<>(FIELD_MAPPING);
    }
}

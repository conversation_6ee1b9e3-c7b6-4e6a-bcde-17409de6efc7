package ai.pricefox.mallfox.utils;

import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

@Component
public class LexoRankUtils {
    private static final String CHAR_SET = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
    private static final char MIN_CHAR = '0';
    private static final char MAX_CHAR = 'z';
    private static final char MID_CHAR = 'V';
    private static final int MAX_LENGTH = 15;

    public static String between(String prev, String next) {
        if (!StringUtils.hasLength(prev) && !StringUtils.hasLength(next)) {
            return String.valueOf(MID_CHAR);
        }

        boolean swapped = false;
        if (StringUtils.hasLength(prev) && StringUtils.hasLength(next) && prev.compareTo(next) > 0) {
            String temp = prev;
            prev = next;
            next = temp;
            swapped = true;
        }

        int len = Math.max(
                StringUtils.hasLength(prev) ? prev.length() : 0,
                StringUtils.hasLength(next) ? next.length() : 0
        );
        prev = StringUtils.hasLength(prev) ? padRight(prev, len, MIN_CHAR) : String.valueOf(MIN_CHAR).repeat(len);
        next = StringUtils.hasLength(next) ? padRight(next, len, MIN_CHAR) : String.valueOf(MAX_CHAR).repeat(len);

        int diffIndex = findFirstDifference(prev, next);
        if (diffIndex == -1) {
            return swapped ? prev : next;
        }

        char prevChar = prev.charAt(diffIndex);
        char nextChar = next.charAt(diffIndex);
        int prevOrd = CHAR_SET.indexOf(prevChar);
        int nextOrd = CHAR_SET.indexOf(nextChar);

        if (nextOrd - prevOrd > 1) {
            int midOrd = (prevOrd + nextOrd) / 2;
            String newRank = prev.substring(0, diffIndex) + CHAR_SET.charAt(midOrd);
            return trimTrailingChars(padRight(newRank, len, MIN_CHAR));
        }

        String prevSuffix = prev.substring(diffIndex + 1);
        String nextSuffix = next.substring(diffIndex + 1);

        if (isMaxString(prevSuffix) && isMinString(nextSuffix)) {
            String newRank = prev + MID_CHAR;
            if (newRank.length() > MAX_LENGTH) {
                throw new IllegalStateException("Rank length exceeds maximum limit of " + MAX_LENGTH);
            }
            return newRank;
        }

        String newSuffix;
        if (prevSuffix.isEmpty()) {
            newSuffix = between(String.valueOf(MIN_CHAR), nextSuffix);
        } else {
            newSuffix = between(prevSuffix, String.valueOf(MAX_CHAR).repeat(prevSuffix.length()));
        }

        String newRank = prev.substring(0, diffIndex + 1) + newSuffix;
        return trimTrailingChars(newRank);
    }

    public static String before(String first) {
        if (!StringUtils.hasLength(first)) {
            return "0000";
        }
        String minRank = String.valueOf(MIN_CHAR).repeat(first.length());
        if (minRank.equals(first)) {
            if (first.length() > 1) {
                return minRank.substring(0, first.length() - 1);
            } else {
                throw new IllegalStateException("Cannot generate rank before minimum value");
            }
        }
        return between(minRank, first);
    }

    public static String after(String last) {
        if (!StringUtils.hasLength(last)) {
            return "0000";
        }
        String maxRank = String.valueOf(MAX_CHAR).repeat(last.length());
        if (maxRank.equals(last)) {
            String newRank = last + MID_CHAR;
            if (newRank.length() > MAX_LENGTH) {
                throw new IllegalStateException("Rank length exceeds maximum limit of 15");
            }
            return newRank;
        }

        return between(last, maxRank);
    }

    private static String padRight(String str, int length, char padChar) {
        if (str.length() >= length) {
            return str;
        }
        StringBuilder sb = new StringBuilder(str);
        while (sb.length() < length) {
            sb.append(padChar);
        }
        return sb.toString();
    }

    private static String trimTrailingChars(String str) {
        int endIndex = str.length();
        while (endIndex > 0 && str.charAt(endIndex - 1) == MIN_CHAR) {
            endIndex--;
        }
        return endIndex > 0 ? str.substring(0, endIndex) : String.valueOf(MIN_CHAR);
    }

    private static int findFirstDifference(String a, String b) {
        int len = Math.min(a.length(), b.length());
        for (int i = 0; i < len; i++) {
            if (a.charAt(i) != b.charAt(i)) {
                return i;
            }
        }
        return len < a.length() || len < b.length() ? len : -1;
    }

    private static boolean isMaxString(String str) {
        return str.chars().allMatch(c -> c == MAX_CHAR);
    }

    private static boolean isMinString(String str) {
        return str.chars().allMatch(c -> c == MIN_CHAR);
    }
}

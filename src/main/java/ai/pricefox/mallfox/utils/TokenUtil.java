package ai.pricefox.mallfox.utils;

import ai.pricefox.mallfox.common.exception.enums.ErrorCodeConstants;
import ai.pricefox.mallfox.domain.auth.User;
import ai.pricefox.mallfox.service.auth.TokenService;
import cn.dev33.satoken.context.SaHolder;
import cn.dev33.satoken.stp.SaTokenInfo;
import cn.dev33.satoken.stp.StpUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import static ai.pricefox.mallfox.common.exception.util.ServiceExceptionUtil.exception;

/**
 * Token 工具类
 */
@Slf4j
@Component
public class TokenUtil {

    private static TokenService tokenService;

    @Autowired
    public void setTokenService(TokenService tokenService) {
        TokenUtil.tokenService = tokenService;
    }

    /**
     * 用户登录
     *
     * @param userId 用户ID
     * @return Token信息
     */
    public static SaTokenInfo login(Long userId) {
        // 先设置SaToken的登录状态
        StpUtil.login(userId);
        // 使用自定义的Token管理服务
        SaTokenInfo tokenInfo = tokenService.generateAndSaveToken(userId);
        return tokenInfo;
    }

    /**
     * 用户登出
     */
    public static void logout() {
        Long userId = getCurrentUserIdOrNull();
        if (userId != null) {
            // 清除数据库和Redis中的Token
            tokenService.removeToken(userId);
        }
        // 清除SaToken的登录状态
        StpUtil.logout();
    }

    /**
     * 获取当前登录用户ID
     *
     * @return 用户ID
     */
    public static Long getCurrentUserId() {
        try {
            // 先尝试从请求头中获取Token
            String token = getTokenFromRequest();
            log.info("从请求头中获取后台Token: {}", token);
            if (StringUtils.hasText(token)) {
                User user = tokenService.getUserByToken(token);
                if (user != null) {
                    return user.getId();
                }
            }

            // 如果自定义Token验证失败，尝试SaToken
            log.info("从SaToken中获取后台用户ID");
            return StpUtil.getLoginIdAsLong();
        } catch (Exception e) {
            log.error("获取后台用户ID失败：{}", e.getMessage());
            throw exception(ErrorCodeConstants.AUTH_TOKEN_INVALID);
        }
    }

    /**
     * 获取当前登录用户ID（可为空）
     *
     * @return 用户ID，未登录返回null
     */
    public static Long getCurrentUserIdOrNull() {
        try {
            // 先尝试从请求头中获取Token
            String token = getTokenFromRequest();
            if (StringUtils.hasText(token)) {
                User user = tokenService.getUserByToken(token);
                if (user != null) {
                    return user.getId();
                }
            }

            // 如果自定义Token验证失败，尝试SaToken
            if (StpUtil.isLogin()) {
                return StpUtil.getLoginIdAsLong();
            }
            return null;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 检查是否已登录
     *
     * @return 是否已登录
     */
    public static boolean isLogin() {
        try {
            // 先尝试从请求头中获取Token
            String token = getTokenFromRequest();
            if (StringUtils.hasText(token)) {
                User user = tokenService.getUserByToken(token);
                if (user != null) {
                    return true;
                }
            }

            // 如果自定义Token验证失败，尝试SaToken
            return StpUtil.isLogin();
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 检查登录状态，未登录抛出异常
     */
    public static void checkLogin() {
        if (!isLogin()) {
            throw exception(ErrorCodeConstants.AUTH_TOKEN_INVALID, "用户未登录");
        }
    }

    /**
     * 获取Token信息
     *
     * @return Token信息
     */
    public static SaTokenInfo getTokenInfo() {
        return StpUtil.getTokenInfo();
    }

    /**
     * 获取Token值
     *
     * @return Token值
     */
    public static String getTokenValue() {
        return StpUtil.getTokenValue();
    }

    /**
     * 踢下线指定用户
     *
     * @param userId 用户ID
     */
    public static void kickout(Long userId) {
        // 使用自定义的Token管理服务踢下线
        tokenService.kickoutUser(userId);
    }

    /**
     * 封禁指定用户
     *
     * @param userId 用户ID
     * @param time   封禁时长（秒）
     */
    public static void disable(Long userId, long time) {
        StpUtil.disable(userId, time);
    }

    /**
     * 解封指定用户
     *
     * @param userId 用户ID
     */
    public static void untieDisable(Long userId) {
        StpUtil.untieDisable(userId);
    }

    /**
     * 检查指定用户是否被封禁
     *
     * @param userId 用户ID
     * @return 是否被封禁
     */
    public static boolean isDisable(Long userId) {
        return StpUtil.isDisable(userId);
    }

    /**
     * 获取Token剩余有效时间
     *
     * @return 剩余有效时间（秒）
     */
    public static long getTokenTimeout() {
        return StpUtil.getTokenTimeout();
    }

    /**
     * 续签Token
     *
     * @param timeout 续签时长（秒）
     */
    public static void renewTimeout(long timeout) {
        StpUtil.renewTimeout(timeout);
    }

    /**
     * 刷新Token
     */
    public static void refreshToken() {
        StpUtil.renewTimeout(StpUtil.getTokenTimeout());
    }

    /**
     * 获取当前会话的所有Token
     *
     * @return Token列表
     */
    public static java.util.List<String> getTokenValueListByLoginId() {
        return StpUtil.getTokenValueListByLoginId(getCurrentUserId());
    }

    /**
     * 注销指定Token
     *
     * @param tokenValue Token值
     */
    public static void logoutByTokenValue(String tokenValue) {
        // 根据Token获取用户信息并清除
        User user = tokenService.getUserByToken(tokenValue);
        if (user != null) {
            tokenService.removeToken(user.getId());
        }
        StpUtil.logoutByTokenValue(tokenValue);
    }

    /**
     * 刷新Token
     *
     * @param userId 用户ID
     * @param refreshToken 刷新令牌
     * @return 新的Token信息
     */
    public static SaTokenInfo refreshToken(Long userId, String refreshToken) {
        return tokenService.refreshToken(userId, refreshToken);
    }

    /**
     * 验证Token有效性
     *
     * @param userId 用户ID
     * @param accessToken 访问令牌
     * @return 是否有效
     */
    public static boolean validateToken(Long userId, String accessToken) {
        return tokenService.validateToken(userId, accessToken);
    }

    /**
     * 根据Token获取用户信息
     *
     * @param accessToken 访问令牌
     * @return 用户信息
     */
    public static User getUserByToken(String accessToken) {
        return tokenService.getUserByToken(accessToken);
    }

    /**
     * 自动续期Token（如果需要）
     *
     * @param userId 用户ID
     * @param accessToken 访问令牌
     * @return 是否续期成功
     */
    public static boolean renewTokenIfNeeded(Long userId, String accessToken) {
        return tokenService.renewTokenIfNeeded(userId, accessToken);
    }

    /**
     * 从请求中获取Token
     *
     * @return Token值
     */
    private static String getTokenFromRequest() {
        try {
            // 从SaToken上下文中获取请求
            String tokenValue = SaHolder.getRequest().getHeader("Authorization");
            if (StringUtils.hasText(tokenValue)) {
                // 移除 "Bearer " 前缀
                if (tokenValue.startsWith("Bearer ")) {
                    return tokenValue.substring(7);
                }
                return tokenValue;
            }

            // 也可以从参数中获取
            tokenValue = SaHolder.getRequest().getParam("token");
            if (StringUtils.hasText(tokenValue)) {
                return tokenValue;
            }

            return null;
        } catch (Exception e) {
            log.warn("获取Token失败: {}", e.getMessage());
            return null;
        }
    }
}

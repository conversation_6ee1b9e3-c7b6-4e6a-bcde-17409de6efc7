package ai.pricefox.mallfox.vo.admin;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 后台认证响应VO
 */
@Data
@Schema(description = "后台认证响应")
public class AdminAuthRespVO {

    @Schema(description = "访问令牌", example = "ADMIN_AT_xxx")
    private String accessToken;

    @Schema(description = "刷新令牌", example = "ADMIN_RT_xxx")
    private String refreshToken;

    @Schema(description = "令牌类型", example = "Bearer")
    private String tokenType = "Bearer";

    @Schema(description = "过期时间（秒）", example = "7200")
    private Long expiresIn;

    @Schema(description = "用户ID", example = "1")
    private Long userId;

    @Schema(description = "用户名", example = "admin")
    private String username;

    @Schema(description = "昵称", example = "管理员")
    private String nickname;

    @Schema(description = "头像", example = "https://example.com/avatar.jpg")
    private String avatar;
}

package ai.pricefox.mallfox.vo.admin;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

/**
 * 后台发送验证码请求VO
 */
@Data
@Schema(description = "后台发送验证码请求")
public class AdminSendCodeReqVO {

    @Schema(description = "验证码类型", example = "SMS", allowableValues = {"SMS", "EMAIL"})
    @NotNull(message = "验证码类型不能为空")
    private CodeType codeType;

    @Schema(description = "业务类型", example = "LOGIN", allowableValues = {"LOGIN", "RESET_PASSWORD"})
    @NotNull(message = "业务类型不能为空")
    private BusinessType businessType;

    @Schema(description = "手机号", example = "***********")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String phone;

    @Schema(description = "邮箱", example = "<EMAIL>")
    @Email(message = "邮箱格式不正确")
    private String email;

    /**
     * 验证码类型枚举
     */
    public enum CodeType {
        SMS,    // 短信验证码
        EMAIL   // 邮箱验证码
    }

    /**
     * 业务类型枚举
     */
    public enum BusinessType {
        LOGIN,          // 登录
        RESET_PASSWORD  // 重置密码
    }
}

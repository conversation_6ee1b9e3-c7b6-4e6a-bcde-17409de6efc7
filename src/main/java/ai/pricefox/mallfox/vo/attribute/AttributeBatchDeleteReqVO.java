package ai.pricefox.mallfox.vo.attribute;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 属性批量删除请求VO
 */
@Data
@Schema(description = "属性批量删除请求")
public class AttributeBatchDeleteReqVO {

    @Schema(description = "属性ID列表（必填）", example = "[1, 2, 3]")
    @NotEmpty(message = "属性ID列表不能为空")
    private List<Long> ids;
}

package ai.pricefox.mallfox.vo.attribute;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 属性批量更新结果响应VO
 */
@Data
@Schema(description = "属性批量更新结果响应")
public class AttributeBatchUpdateResultRespVO {

    @Schema(description = "更新总数量", example = "100")
    private Integer totalCount;

    @Schema(description = "成功数量", example = "95")
    private Integer successCount;

    @Schema(description = "失败数量", example = "5")
    private Integer failedCount;

    @Schema(description = "处理详情信息", example = "批量更新完成，共处理100条记录，成功95条，失败5条")
    private String message;
}

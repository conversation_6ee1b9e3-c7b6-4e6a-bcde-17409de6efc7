package ai.pricefox.mallfox.vo.attribute;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;

/**
 * 属性创建请求VO
 */
@Data
@Schema(description = "属性创建请求")
public class AttributeCreateReqVO {

    @Schema(description = "类目编码（必填）", example = "CATG12345678")
    @NotBlank(message = "类目编码不能为空")
    private String categoryCode;

    @Schema(description = "属性英文名称（必填）", example = "Color")
    @NotBlank(message = "属性英文名称不能为空")
    private String attributeNameEn;

    @Schema(description = "属性中文名称", example = "颜色")
    private String attributeNameCn;
}

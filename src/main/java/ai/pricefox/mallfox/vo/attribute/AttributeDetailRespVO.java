package ai.pricefox.mallfox.vo.attribute;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 属性详情响应VO
 */
@Data
@Schema(description = "属性详情响应")
public class AttributeDetailRespVO {

    @Schema(description = "属性ID", example = "1")
    private Long id;

    @Schema(description = "属性编码", example = "ATTR1234567")
    private String attributeCode;

    @Schema(description = "属性英文名称", example = "Color")
    private String attributeNameEn;

    @Schema(description = "属性中文名称", example = "颜色")
    private String attributeNameCn;

    @Schema(description = "标准分类编码", example = "CATG12345678")
    private String standardCategoryCode;

    @Schema(description = "标准分类英文名称", example = "Smartphone")
    private String standardCategoryNameEn;

    @Schema(description = "标准分类中文名称", example = "智能手机")
    private String standardCategoryNameCn;

    @Schema(description = "类目路径", example = "/Electronics/Mobile/Smartphone")
    private String categoryPath;

    @Schema(description = "创建时间", example = "2023-01-01 12:00:00")
    private LocalDateTime createDate;

    @Schema(description = "更新时间", example = "2023-01-01 12:00:00")
    private LocalDateTime updateDate;

    @Schema(description = "创建人", example = "admin")
    private String createUsername;

    @Schema(description = "更新人", example = "admin")
    private String updateUsername;

    @Schema(description = "属性值列表")
    private List<AttributeValueRespVO> attributeValues;
}

package ai.pricefox.mallfox.vo.attribute;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 属性文件响应VO
 * 用于返回文件URL和文件名称
 */
@Data
@Schema(description = "属性文件响应")
public class AttributeFileRespVO {

    @Schema(description = "文件URL", example = "https://oss.example.com/templates/attribute_template_20250103.xlsx")
    private String url;

    @Schema(description = "文件名称", example = "属性批量更新模板_20250103.xlsx")
    private String fileName;

    public AttributeFileRespVO() {}

    public AttributeFileRespVO(String url, String fileName) {
        this.url = url;
        this.fileName = fileName;
    }
}

package ai.pricefox.mallfox.vo.attribute;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 属性列表查询请求VO
 */
@Data
@Schema(description = "属性列表查询请求")
public class AttributeListReqVO {

    @Schema(description = "属性编码（精确查询）", example = "ATTR0000001")
    private String attributeCode;

    @Schema(description = "属性英文名称（模糊查询）", example = "Color")
    private String attributeNameEn;
}

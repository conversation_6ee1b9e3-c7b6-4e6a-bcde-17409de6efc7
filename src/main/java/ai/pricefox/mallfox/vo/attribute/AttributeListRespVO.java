package ai.pricefox.mallfox.vo.attribute;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 属性列表响应VO
 */
@Data
@Schema(description = "属性列表响应")
public class AttributeListRespVO {

    @Schema(description = "主键ID", example = "1")
    private Long id;

    @Schema(description = "属性编码", example = "ATTR0000001")
    private String attributeCode;

    @Schema(description = "属性英文名称", example = "Color")
    private String attributeNameEn;
}

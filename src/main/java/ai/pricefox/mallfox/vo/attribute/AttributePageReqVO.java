package ai.pricefox.mallfox.vo.attribute;

import ai.pricefox.mallfox.vo.base.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 属性分页查询请求VO
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "属性分页查询请求")
public class AttributePageReqVO extends PageParam {

    @Schema(description = "标准分类英文名称", example = "Smartphone")
    private String standardCategoryNameEn;

    @Schema(description = "属性编码", example = "ATTR1234567")
    private String attributeCode;

    @Schema(description = "属性英文名称", example = "Color")
    private String attributeNameEn;

    @Schema(description = "属性中文名称", example = "颜色")
    private String attributeNameCn;
}

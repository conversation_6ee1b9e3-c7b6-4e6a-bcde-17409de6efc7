package ai.pricefox.mallfox.vo.attribute;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 属性更新请求VO
 */
@Data
@Schema(description = "属性更新请求")
public class AttributeUpdateReqVO {

    @Schema(description = "主键ID（必填）", example = "1")
    @NotNull(message = "主键ID不能为空")
    private Long id;

    @Schema(description = "属性英文名称（必填）", example = "Color")
    @NotBlank(message = "属性英文名称不能为空")
    private String attributeNameEn;

    @Schema(description = "属性中文名称", example = "颜色")
    private String attributeNameCn;
}

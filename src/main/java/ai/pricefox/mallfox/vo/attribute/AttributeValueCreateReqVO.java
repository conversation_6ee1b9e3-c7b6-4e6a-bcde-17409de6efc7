package ai.pricefox.mallfox.vo.attribute;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;

/**
 * 属性值创建请求VO
 */
@Data
@Schema(description = "属性值创建请求")
public class AttributeValueCreateReqVO {

    @Schema(description = "属性编码（必填）", example = "ATTR1234567")
    @NotBlank(message = "属性编码不能为空")
    private String attributeCode;

    @Schema(description = "属性值英文名称（必填）", example = "Red")
    @NotBlank(message = "属性值英文名称不能为空")
    private String valueEn;

    @Schema(description = "属性值中文名称", example = "红色")
    private String valueCn;
}

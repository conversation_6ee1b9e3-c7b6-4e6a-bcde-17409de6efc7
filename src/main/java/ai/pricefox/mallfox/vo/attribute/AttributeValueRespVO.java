package ai.pricefox.mallfox.vo.attribute;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 属性值响应VO
 */
@Data
@Schema(description = "属性值响应")
public class AttributeValueRespVO {

    @Schema(description = "属性值ID", example = "1")
    private Long id;

    @Schema(description = "属性编码", example = "ATTR1234567")
    private String attributeCode;

    @Schema(description = "属性值编码", example = "VAL1234567")
    private String valueCode;

    @Schema(description = "属性值英文名称", example = "Red")
    private String valueEn;

    @Schema(description = "属性值中文名称", example = "红色")
    private String valueCn;

    @Schema(description = "创建时间", example = "2023-01-01 12:00:00")
    private LocalDateTime createDate;

    @Schema(description = "更新时间", example = "2023-01-01 12:00:00")
    private LocalDateTime updateDate;

    @Schema(description = "创建人", example = "admin")
    private String createUsername;

    @Schema(description = "更新人", example = "admin")
    private String updateUsername;
}

package ai.pricefox.mallfox.vo.attribute;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 属性值更新请求VO
 */
@Data
@Schema(description = "属性值更新请求")
public class AttributeValueUpdateReqVO {

    @Schema(description = "主键ID（必填）", example = "1")
    @NotNull(message = "主键ID不能为空")
    private Long id;

    @Schema(description = "属性值英文名称（必填）", example = "Dark Red")
    @NotBlank(message = "属性值英文名称不能为空")
    private String valueEn;

    @Schema(description = "属性值中文名称", example = "深红色")
    private String valueCn;
}

package ai.pricefox.mallfox.vo.attribute;

import ai.pricefox.mallfox.vo.base.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 规格分页查询请求VO
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "规格分页查询请求")
public class SpecPageReqVO extends PageParam {

    @Schema(description = "标准分类英文名称", example = "Smartphone")
    private String standardCategoryNameEn;

    @Schema(description = "属性编码", example = "ATTR0000001")
    private String attributeCode;

    @Schema(description = "属性英文名称", example = "Color")
    private String attributeNameEn;

    @Schema(description = "属性中文名称", example = "颜色")
    private String attributeNameCn;
}

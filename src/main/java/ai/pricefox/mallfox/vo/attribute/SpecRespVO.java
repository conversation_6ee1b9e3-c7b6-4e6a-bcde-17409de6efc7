package ai.pricefox.mallfox.vo.attribute;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 规格响应VO
 */
@Data
@Schema(description = "规格响应")
public class SpecRespVO {

    @Schema(description = "属性编码", example = "ATTR0000001")
    private String attributeCode;

    @Schema(description = "属性英文名称", example = "Color")
    private String attributeNameEn;

    @Schema(description = "属性中文名称", example = "颜色")
    private String attributeNameCn;

    @Schema(description = "类目路径", example = "/Electronics/Mobile/Smartphone")
    private String categoryPath;
}

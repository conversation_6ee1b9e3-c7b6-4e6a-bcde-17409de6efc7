package ai.pricefox.mallfox.vo.auth;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

/**
 * 用户登录请求VO
 */
@Data
@Schema(description = "用户登录请求")
public class LoginReqVO {

    @Schema(description = "登录类型", example = "USERNAME_PASSWORD", allowableValues = {"USERNAME_PASSWORD", "PHONE_PASSWORD", "EMAIL_PASSWORD", "PHONE_CODE"})
    @NotNull(message = "登录类型不能为空")
    private LoginType loginType;

    @Schema(description = "用户名", example = "admin")
    private String username;

    @Schema(description = "手机号", example = "13800138000")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String phone;

    @Schema(description = "邮箱", example = "<EMAIL>")
    @Email(message = "邮箱格式不正确")
    private String email;

    @Schema(description = "密码", example = "123456")
    private String password;

    @Schema(description = "验证码", example = "123456")
    private String verifyCode;

    /**
     * 登录类型枚举
     */
    public enum LoginType {
        USERNAME_PASSWORD,  // 用户名密码登录
        PHONE_PASSWORD,     // 手机号密码登录
        EMAIL_PASSWORD,     // 邮箱密码登录
        PHONE_CODE         // 手机号验证码登录
    }
}

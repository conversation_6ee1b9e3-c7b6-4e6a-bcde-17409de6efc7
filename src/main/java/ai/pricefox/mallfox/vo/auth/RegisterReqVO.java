package ai.pricefox.mallfox.vo.auth;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

/**
 * 用户注册请求VO
 */
@Data
@Schema(description = "用户注册请求")
public class RegisterReqVO {

    @Schema(description = "注册类型", example = "EMAIL", allowableValues = {"EMAIL", "PHONE"})
    @NotNull(message = "注册类型不能为空")
    private RegisterType registerType;

    @Schema(description = "手机号", example = "13800138000")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String phone;

    @Schema(description = "邮箱", example = "<EMAIL>")
    @Email(message = "邮箱格式不正确")
    private String email;

    @Schema(description = "昵称", example = "用户昵称")
    @NotBlank(message = "昵称不能为空")
    private String nickname;

    @Schema(description = "密码", example = "123456")
    @NotBlank(message = "密码不能为空")
    private String password;

    @Schema(description = "验证码", example = "123456")
    @NotBlank(message = "验证码不能为空")
    private String verifyCode;

    /**
     * 注册类型枚举
     */
    public enum RegisterType {
        EMAIL, PHONE
    }
}

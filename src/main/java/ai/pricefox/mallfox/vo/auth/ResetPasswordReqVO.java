package ai.pricefox.mallfox.vo.auth;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 重置密码请求VO
 */
@Data
@Schema(description = "重置密码请求")
public class ResetPasswordReqVO {

    @Schema(description = "邮箱", example = "<EMAIL>")
    @Email(message = "邮箱格式不正确")
    @NotBlank(message = "邮箱不能为空")
    private String email;

    @Schema(description = "重置令牌", example = "abc123def456")
    @NotBlank(message = "重置令牌不能为空")
    private String resetToken;

    @Schema(description = "新密码", example = "newPassword123")
    @NotBlank(message = "新密码不能为空")
    private String newPassword;
}

package ai.pricefox.mallfox.vo.bestbuy;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * BestBuy 产品详情响应 VO
 */
@Data
@Schema(description = "BestBuy 产品详情响应")
public class BestBuyProductDetailRespVO {

    @Schema(description = "SKU", example = "6418599")
    private String sku;

    @Schema(description = "产品名称", example = "Apple - iPhone 13 5G 128GB")
    private String name;

    @Schema(description = "产品类型", example = "Game")
    private String type;

    @Schema(description = "开始日期", example = "2021-09-24")
    private String startDate;

    @Schema(description = "是否为新产品", example = "true")
    private Boolean isNew;

    @Schema(description = "是否活跃", example = "true")
    private Boolean active;

    @Schema(description = "低价保证", example = "true")
    private Boolean lowPriceGuarantee;

    @Schema(description = "活跃更新日期", example = "2021-09-24T00:00:00")
    private String activeUpdateDate;

    @Schema(description = "常规价格", example = "699.99")
    private BigDecimal regularPrice;

    @Schema(description = "销售价格", example = "629.99")
    private BigDecimal salePrice;

    @Schema(description = "是否清仓", example = "false")
    private Boolean clearance;

    @Schema(description = "数字交付", example = "false")
    private Boolean digitalDelivery;

    @Schema(description = "扩展保修", example = "true")
    private Boolean extendedWarranty;

    @Schema(description = "免费配送", example = "true")
    private Boolean freeShipping;

    @Schema(description = "免费配送符合条件", example = "true")
    private Boolean freeShippingEligible;

    @Schema(description = "安装服务", example = "false")
    private Boolean installation;

    @Schema(description = "店内可用性", example = "true")
    private Boolean inStoreAvailability;

    @Schema(description = "店内可用性文本", example = "Available")
    private String inStoreAvailabilityText;

    @Schema(description = "店内可用性更新日期", example = "2021-09-24T00:00:00")
    private String inStoreAvailabilityUpdateDate;

    @Schema(description = "商品更新日期", example = "2021-09-24T00:00:00")
    private String itemUpdateDate;

    @Schema(description = "在线可用性", example = "true")
    private Boolean onlineAvailability;

    @Schema(description = "在线可用性文本", example = "Available")
    private String onlineAvailabilityText;

    @Schema(description = "在线可用性更新日期", example = "2021-09-24T00:00:00")
    private String onlineAvailabilityUpdateDate;

    @Schema(description = "发布日期", example = "2021-09-24")
    private String releaseDate;

    @Schema(description = "短期销售排名", example = "1")
    private Integer salesRankShortTerm;

    @Schema(description = "中期销售排名", example = "2")
    private Integer salesRankMediumTerm;

    @Schema(description = "长期销售排名", example = "3")
    private Integer salesRankLongTerm;

    @Schema(description = "特殊订单", example = "false")
    private Boolean specialOrder;

    @Schema(description = "短描述", example = "iPhone 13 features...")
    private String shortDescription;

    @Schema(description = "长描述", example = "Detailed description of iPhone 13...")
    private String longDescription;

    @Schema(description = "类别路径")
    private List<CategoryPathVO> categoryPath;

    @Schema(description = "客户评价平均分", example = "4.5")
    private BigDecimal customerReviewAverage;

    @Schema(description = "客户评价数量", example = "1250")
    private Integer customerReviewCount;

    @Schema(description = "制造商", example = "Apple")
    private String manufacturer;

    @Schema(description = "型号", example = "MLPF3LL/A")
    private String modelNumber;

    @Schema(description = "主图片", example = "https://pisces.bbystatic.com/image2/BestBuy_US/images/products/6418/6418599_sd.jpg")
    private String image;

    @Schema(description = "大图片", example = "https://pisces.bbystatic.com/image2/BestBuy_US/images/products/6418/6418599_sd.jpg")
    private String largeFrontImage;

    @Schema(description = "缩略图", example = "https://pisces.bbystatic.com/image2/BestBuy_US/images/products/6418/6418599_s.gif")
    private String thumbnailImage;

    @Schema(description = "大缩略图", example = "https://pisces.bbystatic.com/image2/BestBuy_US/images/products/6418/6418599_sb.jpg")
    private String largeImage;

    @Schema(description = "替代视图图片", example = "https://pisces.bbystatic.com/image2/BestBuy_US/images/products/6418/6418599cv1d.jpg")
    private String alternateViewsImage;

    @Schema(description = "角度图片", example = "https://pisces.bbystatic.com/image2/BestBuy_US/images/products/6418/6418599cv2d.jpg")
    private String angleImage;

    @Schema(description = "背面图片", example = "https://pisces.bbystatic.com/image2/BestBuy_US/images/products/6418/6418599cv3d.jpg")
    private String backViewImage;

    @Schema(description = "能源指南", example = "https://pisces.bbystatic.com/image2/BestBuy_US/images/products/6418/6418599_eg.pdf")
    private String energyGuide;

    @Schema(description = "左侧图片", example = "https://pisces.bbystatic.com/image2/BestBuy_US/images/products/6418/6418599cv4d.jpg")
    private String leftViewImage;

    @Schema(description = "配件图片", example = "https://pisces.bbystatic.com/image2/BestBuy_US/images/products/6418/6418599cv5d.jpg")
    private String accessoriesImage;

    @Schema(description = "遥控器图片", example = "https://pisces.bbystatic.com/image2/BestBuy_US/images/products/6418/6418599cv6d.jpg")
    private String remoteControlImage;

    @Schema(description = "右侧图片", example = "https://pisces.bbystatic.com/image2/BestBuy_US/images/products/6418/6418599cv7d.jpg")
    private String rightViewImage;

    @Schema(description = "顶部图片", example = "https://pisces.bbystatic.com/image2/BestBuy_US/images/products/6418/6418599cv8d.jpg")
    private String topViewImage;

    @Schema(description = "详情图片", example = "https://pisces.bbystatic.com/image2/BestBuy_US/images/products/6418/6418599cv9d.jpg")
    private String detailImage;

    @Schema(description = "特征图片", example = "https://pisces.bbystatic.com/image2/BestBuy_US/images/products/6418/6418599cv10d.jpg")
    private String featuresImage;

    @Schema(description = "包含物品图片", example = "https://pisces.bbystatic.com/image2/BestBuy_US/images/products/6418/6418599cv11d.jpg")
    private String includedItemsImage;

    @Schema(description = "生活方式图片", example = "https://pisces.bbystatic.com/image2/BestBuy_US/images/products/6418/6418599cv12d.jpg")
    private String lifestyleImage;

    @Schema(description = "UPC", example = "194252707883")
    private String upc;

    @Schema(description = "产品URL", example = "https://api.bestbuy.com/click/-/6418599/pdp")
    private String url;

    @Schema(description = "360度旋转URL", example = "https://spin360.bbystatic.com/6418599/")
    private String spin360Url;

    @Schema(description = "移动URL", example = "https://api.bestbuy.com/click/-/6418599/pdp")
    private String mobileUrl;

    @Schema(description = "附属品")
    private List<String> accessories;

    @Schema(description = "相关产品")
    private List<String> relatedProducts;

    @Schema(description = "技术规格")
    private List<String> technicalSpecifications;

    @Schema(description = "颜色", example = "Black")
    private String color;

    @Schema(description = "颜色分类", example = "Black")
    private String colorCategory;

    @Schema(description = "数量限制", example = "5")
    private Integer quantityLimit;

    @Schema(description = "店内取货", example = "true")
    private Boolean inStorePickup;

    @Schema(description = "家庭配送", example = "true")
    private Boolean homeDelivery;

    @Schema(description = "GTIN", example = "194252707234")
    private String gtin;

    @Schema(description = "ISBN", example = "9781234567890")
    private String isbn;

    @Schema(description = "MPN (制造商零件号)", example = "MHQM3LL/A")
    private String mpn;

    @Schema(description = "节省金额", example = "100.00")
    private BigDecimal dollarSavings;

    @Schema(description = "节省百分比", example = "15.5")
    private BigDecimal percentSavings;

    @Schema(description = "MSRP (建议零售价)", example = "799.99")
    private BigDecimal msrp;

    @Schema(description = "促销中", example = "true")
    private Boolean onSale;

    @Schema(description = "计划标题", example = "Geek Squad Protection")
    private String planTitle;

    @Schema(description = "二手商品", example = "false")
    private Boolean preowned;

    @Schema(description = "会员专享商品", example = "false")
    private Boolean membersOnlyItem;

    @Schema(description = "客户最高评价", example = "true")
    private Boolean customerTopRated;

    @Schema(description = "特色产品")
    private List<String> features;

    @Schema(description = "包含物品列表")
    private List<String> includedItemList;

    @Schema(description = "配送成本", example = "5.99")
    private BigDecimal shippingCost;

    @Schema(description = "配送重量", example = "1.5")
    private BigDecimal shippingWeight;

    @Schema(description = "保修劳工", example = "1 year")
    private String warrantyLabor;

    @Schema(description = "保修零件", example = "1 year")
    private String warrantyParts;

    @Schema(description = "重量", example = "0.5")
    private BigDecimal weight;

    @Schema(description = "宽度", example = "2.8")
    private BigDecimal width;

    @Schema(description = "高度", example = "5.8")
    private BigDecimal height;

    @Schema(description = "深度", example = "0.3")
    private BigDecimal depth;

    @Schema(description = "条件", example = "New")
    private String condition;

    @Schema(description = "条件ID", example = "1")
    private String conditionId;

    @Schema(description = "市场", example = "false")
    private Boolean marketplace;

    @Schema(description = "市场ID", example = "bestbuy")
    private String marketplaceId;

    /**
     * 类别路径 VO
     */
    @Data
    @Schema(description = "类别路径")
    public static class CategoryPathVO {
        @Schema(description = "类别ID", example = "pcmcat209400050001")
        private String id;

        @Schema(description = "类别名称", example = "Cell Phones")
        private String name;
    }
}

package ai.pricefox.mallfox.vo.brand;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 品牌信息创建请求VO
 */
@Data
@Schema(description = "品牌信息创建请求")
public class BrandInfoCreateReqVO {

    @Schema(description = "品牌官方名称", example = "Apple")
    @NotBlank(message = "品牌名称不能为空")
    private String name;

    @Schema(description = "品牌Logo图片地址", example = "https://example.com/logo.png")
    private String logoUrl;

    @Schema(description = "品牌官网", example = "https://www.apple.com")
    private String website;

    @Schema(description = "排序", example = "1")
    private Integer sortOrder;

    @Schema(description = "是否激活", example = "true")
    private Boolean isActive;
}

package ai.pricefox.mallfox.vo.brand;

import ai.pricefox.mallfox.vo.base.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 品牌信息分页查询请求VO
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "品牌信息分页查询请求")
public class BrandInfoPageReqVO extends PageParam {

    @Schema(description = "品牌名称", example = "Apple")
    private String name;

    @Schema(description = "是否激活", example = "true")
    private Boolean isActive;
}

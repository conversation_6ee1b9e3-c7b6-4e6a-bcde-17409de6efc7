package ai.pricefox.mallfox.vo.brand;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 品牌信息响应VO
 */
@Data
@Schema(description = "品牌信息响应")
public class BrandInfoRespVO {

    @Schema(description = "品牌ID", example = "1")
    private Long id;

    @Schema(description = "品牌官方名称", example = "Apple")
    private String name;

    @Schema(description = "品牌Logo图片地址", example = "https://example.com/logo.png")
    private String logoUrl;

    @Schema(description = "品牌官网", example = "https://www.apple.com")
    private String website;

    @Schema(description = "排序", example = "1")
    private Integer sortOrder;

    @Schema(description = "是否激活", example = "true")
    private Boolean isActive;

    @Schema(description = "创建时间", example = "2023-01-01 12:00:00")
    private LocalDateTime createTime;

    @Schema(description = "更新时间", example = "2023-01-01 12:00:00")
    private LocalDateTime updateTime;
}

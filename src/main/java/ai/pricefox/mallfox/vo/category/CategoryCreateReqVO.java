package ai.pricefox.mallfox.vo.category;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;

/**
 * 分类创建请求VO
 */
@Data
@Schema(description = "分类创建请求")
public class CategoryCreateReqVO {

    @Schema(description = "品类英文名称（必填）", example = "Smartphone")
    @NotBlank(message = "品类英文名称不能为空")
    private String categoryNameEn;

    @Schema(description = "品类中文名称", example = "智能手机")
    private String categoryNameCn;

    @Schema(description = "父级ID（0表示一级分类）", example = "0")
    private Long parent;

    @Schema(description = "分类图标", example = "https://example.com/icon.png")
    private String iconUrl;
}

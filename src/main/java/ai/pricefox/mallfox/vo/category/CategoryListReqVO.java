package ai.pricefox.mallfox.vo.category;

import ai.pricefox.mallfox.vo.base.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 分类列表查询请求VO
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "分类列表查询请求")
public class CategoryListReqVO extends PageParam {

    @Schema(description = "品类编码", example = "CATG12345678")
    private String categoryCode;

    @Schema(description = "品类英文名称", example = "Smartphone")
    private String categoryNameEn;

    @Schema(description = "品类中文名称", example = "智能手机")
    private String categoryNameCn;
}

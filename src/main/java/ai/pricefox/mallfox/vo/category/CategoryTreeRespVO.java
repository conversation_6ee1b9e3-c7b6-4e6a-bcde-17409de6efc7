package ai.pricefox.mallfox.vo.category;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 分类树形结构响应VO
 */
@Data
@Schema(description = "分类树形结构响应")
public class CategoryTreeRespVO {

    @Schema(description = "分类ID", example = "1")
    private Long id;

    @Schema(description = "品类编码", example = "CATG12345678")
    private String categoryCode;

    @Schema(description = "品类英文名称", example = "Smartphone")
    private String categoryNameEn;

    @Schema(description = "品类中文名称", example = "智能手机")
    private String categoryNameCn;

    @Schema(description = "级别", example = "1")
    private Integer level;

    @Schema(description = "分类图标", example = "https://example.com/icon.png")
    private String iconUrl;

    @Schema(description = "排序", example = "1")
    private Integer sort;

    @Schema(description = "是否激活", example = "true")
    private Boolean isActive;

    @Schema(description = "父级ID", example = "0")
    private Long parent;

    @Schema(description = "创建时间", example = "2023-01-01 12:00:00")
    private LocalDateTime createDate;

    @Schema(description = "更新时间", example = "2023-01-01 12:00:00")
    private LocalDateTime updateDate;

    @Schema(description = "子分类列表")
    private List<CategoryTreeRespVO> children;
}

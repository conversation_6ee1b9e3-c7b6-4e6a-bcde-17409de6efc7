package ai.pricefox.mallfox.vo.category;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 分类更新请求VO
 */
@Data
@Schema(description = "分类更新请求")
public class CategoryUpdateReqVO {

    @Schema(description = "主键ID（必填）", example = "1")
    @NotNull(message = "主键ID不能为空")
    private Long id;

    @Schema(description = "品类英文名称（必填）", example = "Smartphone")
    @NotBlank(message = "品类英文名称不能为空")
    private String categoryNameEn;

    @Schema(description = "品类中文名称", example = "智能手机")
    private String categoryNameCn;

    @Schema(description = "是否激活", example = "true")
    private Boolean isActive;

    @Schema(description = "父级ID", example = "0")
    private Long parent;

    @Schema(description = "分类图标", example = "https://example.com/icon.png")
    private String iconUrl;
}

package ai.pricefox.mallfox.vo.dict;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 字典创建请求VO
 */
@Data
@Schema(description = "字典创建请求")
public class SysDictCreateReqVO {

    @Schema(description = "字典类型", example = "user_status")
    @NotBlank(message = "字典类型不能为空")
    private String type;

    @Schema(description = "字典描述", example = "用户状态")
    @NotBlank(message = "字典描述不能为空")
    private String description;

    @Schema(description = "备注信息", example = "用户状态字典")
    private String remarks;
}

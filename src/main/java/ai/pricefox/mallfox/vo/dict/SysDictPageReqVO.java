package ai.pricefox.mallfox.vo.dict;

import ai.pricefox.mallfox.vo.base.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 字典分页查询请求VO
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "字典分页查询请求")
public class SysDictPageReqVO extends PageParam {

    @Schema(description = "字典类型", example = "user_status")
    private String type;

    @Schema(description = "字典描述", example = "用户状态")
    private String description;
}

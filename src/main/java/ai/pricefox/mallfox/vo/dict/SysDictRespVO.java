package ai.pricefox.mallfox.vo.dict;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 字典响应VO
 */
@Data
@Schema(description = "字典响应")
public class SysDictRespVO {

    @Schema(description = "字典ID", example = "1")
    private Long id;

    @Schema(description = "字典类型", example = "user_status")
    private String type;

    @Schema(description = "字典描述", example = "用户状态")
    private String description;

    @Schema(description = "备注信息", example = "用户状态字典")
    private String remarks;

    @Schema(description = "创建时间", example = "2023-01-01 12:00:00")
    private LocalDateTime createTime;

    @Schema(description = "更新时间", example = "2023-01-01 12:00:00")
    private LocalDateTime updateTime;

    @Schema(description = "删除标记", example = "0")
    private String delFlag;
}

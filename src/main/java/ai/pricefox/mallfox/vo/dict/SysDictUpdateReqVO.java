package ai.pricefox.mallfox.vo.dict;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 字典更新请求VO
 */
@Data
@Schema(description = "字典更新请求")
public class SysDictUpdateReqVO {

    @Schema(description = "字典ID", example = "1")
    @NotNull(message = "字典ID不能为空")
    private Long id;

    @Schema(description = "字典类型", example = "user_status")
    @NotBlank(message = "字典类型不能为空")
    private String type;

    @Schema(description = "字典描述", example = "用户状态")
    @NotBlank(message = "字典描述不能为空")
    private String description;

    @Schema(description = "备注信息", example = "用户状态字典")
    private String remarks;
}

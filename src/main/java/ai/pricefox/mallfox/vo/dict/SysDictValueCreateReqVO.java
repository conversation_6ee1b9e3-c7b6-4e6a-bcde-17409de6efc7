package ai.pricefox.mallfox.vo.dict;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 字典项创建请求VO
 */
@Data
@Schema(description = "字典项创建请求")
public class SysDictValueCreateReqVO {

    @Schema(description = "字典ID", example = "1")
    @NotNull(message = "字典ID不能为空")
    private Long dictId;

    @Schema(description = "数据值", example = "1")
    @NotBlank(message = "数据值不能为空")
    private String value;

    @Schema(description = "标签名", example = "正常")
    @NotBlank(message = "标签名不能为空")
    private String label;

    @Schema(description = "类型", example = "user_status")
    @NotBlank(message = "类型不能为空")
    private String type;

    @Schema(description = "描述", example = "用户正常状态")
    private String description;

    @Schema(description = "排序", example = "1")
    private Integer sort;

    @Schema(description = "备注信息", example = "用户正常状态")
    private String remarks;
}

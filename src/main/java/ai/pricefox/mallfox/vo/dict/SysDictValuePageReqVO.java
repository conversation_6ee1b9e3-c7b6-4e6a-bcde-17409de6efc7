package ai.pricefox.mallfox.vo.dict;

import ai.pricefox.mallfox.vo.base.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 字典项分页查询请求VO
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "字典项分页查询请求")
public class SysDictValuePageReqVO extends PageParam {

    @Schema(description = "字典ID", example = "1")
    private Long dictId;

    @Schema(description = "数据值", example = "1")
    private String value;

    @Schema(description = "标签名", example = "正常")
    private String label;

    @Schema(description = "类型", example = "user_status")
    private String type;

    @Schema(description = "描述", example = "用户正常状态")
    private String description;
}

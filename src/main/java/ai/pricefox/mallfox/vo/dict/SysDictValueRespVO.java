package ai.pricefox.mallfox.vo.dict;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 字典项响应VO
 */
@Data
@Schema(description = "字典项响应")
public class SysDictValueRespVO {

    @Schema(description = "字典项ID", example = "1")
    private Long id;

    @Schema(description = "字典ID", example = "1")
    private Long dictId;

    @Schema(description = "数据值", example = "1")
    private String value;

    @Schema(description = "标签名", example = "正常")
    private String label;

    @Schema(description = "类型", example = "user_status")
    private String type;

    @Schema(description = "描述", example = "用户正常状态")
    private String description;

    @Schema(description = "排序", example = "1")
    private Integer sort;

    @Schema(description = "备注信息", example = "用户正常状态")
    private String remarks;

    @Schema(description = "创建时间", example = "2023-01-01 12:00:00")
    private LocalDateTime createTime;

    @Schema(description = "更新时间", example = "2023-01-01 12:00:00")
    private LocalDateTime updateTime;

    @Schema(description = "删除标记", example = "0")
    private String delFlag;
}

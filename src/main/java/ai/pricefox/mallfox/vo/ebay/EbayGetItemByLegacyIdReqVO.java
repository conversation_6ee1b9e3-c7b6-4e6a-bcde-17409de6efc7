package ai.pricefox.mallfox.vo.ebay;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Positive;
import java.util.List;

/**
 * eBay 通过传统ID获取商品详情请求VO
 * 对应 eBay Browse API 的 getItemByLegacyId 接口
 */
@Data
@Schema(description = "eBay 通过传统ID获取商品详情请求")
public class EbayGetItemByLegacyIdReqVO {

    @Schema(description = "传统商品ID", 
            example = "110325312345", 
            requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "传统商品ID不能为空")
    private String legacyItemId;

    @Schema(description = "传统变体ID - 用于多变体商品中的特定变体", 
            example = "123456789012")
    private String legacyVariationId;

    @Schema(description = "传统变体SKU - 卖家创建的商品唯一标识符", 
            example = "V123456789M")
    private String legacyVariationSku;

    @Schema(description = "字段组 - 控制返回的字段内容", 
            example = "[\"PRODUCT\", \"ADDITIONAL_SELLER_DETAILS\"]",
            allowableValues = {"PRODUCT", "ADDITIONAL_SELLER_DETAILS"})
    private List<String> fieldgroups;

    @Schema(description = "配送估算数量 - 用于计算配送费用的商品数量", 
            example = "1")
    @Positive(message = "配送估算数量必须为正数")
    private Integer quantityForShippingEstimate;

    /**
     * 验证参数组合的有效性
     * 根据eBay API文档，当指定legacy_variation_id或legacy_variation_sku时，必须同时提供legacy_item_id
     */
    public boolean isValidParameterCombination() {
        // legacy_item_id 是必需的
        if (legacyItemId == null || legacyItemId.trim().isEmpty()) {
            return false;
        }
        
        // 如果同时提供了 legacy_variation_id 和 legacy_variation_sku，这是无效的
        if (legacyVariationId != null && !legacyVariationId.trim().isEmpty() &&
            legacyVariationSku != null && !legacyVariationSku.trim().isEmpty()) {
            return false;
        }
        
        return true;
    }

    /**
     * 获取字段组的逗号分隔字符串
     */
    public String getFieldgroupsAsString() {
        if (fieldgroups == null || fieldgroups.isEmpty()) {
            return null;
        }
        return String.join(",", fieldgroups);
    }
}

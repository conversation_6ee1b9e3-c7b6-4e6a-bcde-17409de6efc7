package ai.pricefox.mallfox.vo.ebay;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * eBay 商品详情请求 VO
 */
@Data
@Schema(description = "eBay 商品详情请求")
public class EbayItemDetailReqVO {

    @Schema(description = "eBay 商品 ID", example = "v1|1234567890|0", required = true)
    @NotBlank(message = "商品 ID 不能为空")
    private String itemId;

    @Schema(description = "字段组", example = "PRODUCT,EXTENDED")
    private String fieldGroups = "PRODUCT,EXTENDED";

    @Schema(description = "是否同步数据到产品数据库", example = "false")
    private Boolean syncData = false;
}

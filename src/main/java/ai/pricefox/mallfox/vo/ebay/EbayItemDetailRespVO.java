package ai.pricefox.mallfox.vo.ebay;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * eBay 商品详情响应 VO
 */
@Data
@Schema(description = "eBay 商品详情响应")
public class EbayItemDetailRespVO {

    @Schema(description = "商品 ID", example = "v1|1234567890|0")
    private String itemId;

    @Schema(description = "传统商品 ID", example = "1234567890")
    private String legacyItemId;

    @Schema(description = "商品标题", example = "Sample Item")
    private String title;

    @Schema(description = "商品简短描述", example = "Summary")
    private String shortDescription;

    @Schema(description = "商品详细描述", example = "Detailed description")
    private String description;

    @Schema(description = "品牌", example = "test")
    private String brand;

    @Schema(description = "制造商零件号", example = "DCA800SSK2C")
    private String mpn;

    @Schema(description = "价格", example = "29.00")
    private String price;

    @Schema(description = "货币", example = "USD")
    private String currency;

    @Schema(description = "分类路径", example = "Home & Garden|Patio, Lawn & Garden")
    private String categoryPath;

    @Schema(description = "分类 ID", example = "261019")
    private String categoryId;

    @Schema(description = "商品状态", example = "New")
    private String condition;

    @Schema(description = "商品状态 ID", example = "1000")
    private String conditionId;

    @Schema(description = "商品所在城市", example = "New York")
    private String itemLocationCity;

    @Schema(description = "商品所在州/省", example = "NY")
    private String itemLocationState;

    @Schema(description = "商品所在国家", example = "US")
    private String itemLocationCountry;

    @Schema(description = "商品所在邮编", example = "10001")
    private String itemLocationPostalCode;

    @Schema(description = "商品图片 URL", example = "https://i.ebayimg.com/images/g/Z**********l/s-l1600.jpg")
    private String imageUrl;

    @Schema(description = "卖家用户名", example = "seller123")
    private String sellerUsername;

    @Schema(description = "卖家反馈百分比", example = "98.6")
    private String sellerFeedbackPercentage;

    @Schema(description = "卖家反馈分数", example = "16632")
    private Integer sellerFeedbackScore;

    @Schema(description = "库存状态", example = "IN_STOCK")
    private String availabilityStatus;

    @Schema(description = "可用数量", example = "2")
    private Integer availableQuantity;

    @Schema(description = "已售数量", example = "0")
    private Integer soldQuantity;

    @Schema(description = "配送服务代码", example = "Economy Shipping")
    private String shippingServiceCode;

    @Schema(description = "配送类型", example = "Economy Shipping")
    private String shippingType;

    @Schema(description = "配送费用", example = "950.00")
    private String shippingCost;

    @Schema(description = "配送费用货币", example = "USD")
    private String shippingCurrency;

    @Schema(description = "最早配送日期", example = "2020-11-06T10:00:00.000Z")
    private String minDeliveryDate;

    @Schema(description = "最晚配送日期", example = "2020-11-06T10:00:00.000Z")
    private String maxDeliveryDate;

    @Schema(description = "是否接受退货", example = "true")
    private Boolean returnsAccepted;

    @Schema(description = "退款方式", example = "MONEY_BACK")
    private String refundMethod;

    @Schema(description = "退货运费承担方", example = "BUYER")
    private String returnShippingCostPayer;

    @Schema(description = "退货期限值", example = "14")
    private Integer returnPeriodValue;

    @Schema(description = "退货期限单位", example = "CALENDAR_DAY")
    private String returnPeriodUnit;

    @Schema(description = "评价数量", example = "0")
    private Integer reviewCount;

    @Schema(description = "平均评分", example = "0.0")
    private String averageRating;

    @Schema(description = "是否优先列表", example = "true")
    private Boolean priorityListing;

    @Schema(description = "是否顶级购买体验", example = "false")
    private Boolean topRatedBuyingExperience;

    @Schema(description = "购买选项", example = "FIXED_PRICE")
    private String buyingOptions;

    @Schema(description = "商品网页 URL", example = "https://www.ebay.com/itm/Sample-Item/1234567890")
    private String itemWebUrl;

    @Schema(description = "是否立即支付", example = "true")
    private Boolean immediatePay;

    @Schema(description = "是否支持访客结账", example = "false")
    private Boolean enabledForGuestCheckout;

    @Schema(description = "是否支持内联结账", example = "true")
    private Boolean eligibleForInlineCheckout;

    @Schema(description = "批次大小", example = "0")
    private Integer lotSize;

    @Schema(description = "是否仅限成人", example = "false")
    private Boolean adultOnly;

    @Schema(description = "支付方式", example = "WALLET,CREDIT_CARD")
    private String paymentMethods;

    @Schema(description = "型号", example = "DCA800SSK2C")
    private String model;

    @Schema(description = "颜色", example = "Black")
    private String color;

    @Schema(description = "尺寸", example = "Large")
    private String size;

    @Schema(description = "材质", example = "Metal")
    private String material;

    @Schema(description = "特性", example = "Waterproof")
    private String features;

    @Schema(description = "存储", example = "128GB")
    private String storage;

    @Schema(description = "税务信息", example = "Tax included")
    private String taxInfo;

    @Schema(description = "备注", example = "Additional notes")
    private String remarks;

    @Schema(description = "数据获取时间", example = "2023-01-01 12:00:00")
    private LocalDateTime fetchTime;

    // 新增字段 - 拍卖相关
    @Schema(description = "出价次数", example = "5")
    private String bidCount;

    @Schema(description = "当前出价", example = "150.00")
    private String currentBidPrice;

    @Schema(description = "是否达到保留价", example = "true")
    private Boolean reserveMet;

    @Schema(description = "最高出价者", example = "bidder123")
    private String highBidder;

    @Schema(description = "商品结束日期", example = "2023-12-31T23:59:59.000Z")
    private String itemEndDate;

    // 新增字段 - 时间信息
    @Schema(description = "商品创建日期", example = "2023-01-01T00:00:00.000Z")
    private String itemCreationDate;

    // 新增字段 - 市场和促销
    @Schema(description = "市场ID", example = "EBAY_US")
    private String listingMarketplaceId;

    @Schema(description = "可用优惠券列表", example = "SAVE10,FREESHIP")
    private String availableCoupons;

    @Schema(description = "关注数量", example = "25")
    private String watchCount;

    @Schema(description = "副标题", example = "Limited Edition")
    private String subtitle;

    // 新增字段 - 营销价格
    @Schema(description = "原价", example = "199.99")
    private String originalPrice;

    @Schema(description = "折扣百分比", example = "20")
    private String discountPercentage;

    @Schema(description = "折扣金额", example = "40.00")
    private String discountAmount;

    // 新增字段 - 处理信息
    @Schema(description = "是否快速处理", example = "true")
    private Boolean fastNHandling;

    @Schema(description = "处理时间", example = "1 business day")
    private String handlingTime;

    @Schema(description = "状态描述", example = "Item shows minimal wear")
    private String conditionDescription;

    @Schema(description = "状态显示名称", example = "Excellent - Refurbished")
    private String conditionDisplayName;

    // 新增字段 - 链接信息
    @Schema(description = "联盟链接", example = "https://www.ebay.com/itm/123456789?afepn=5337259887")
    private String itemAffiliateWebUrl;

    // 新增字段 - 多图片信息
    @Schema(description = "额外图片URL列表", example = "https://img1.jpg,https://img2.jpg")
    private String additionalImageUrls;

    @Schema(description = "缩略图URL列表", example = "https://thumb1.jpg,https://thumb2.jpg")
    private String thumbnailImageUrls;

    // 新增字段 - 项目信息
    @Schema(description = "符合的项目", example = "FAST_N_FREE,TOP_RATED")
    private String qualifiedPrograms;

    // 新增字段 - 增强配送信息
    @Schema(description = "是否保证送达", example = "true")
    private Boolean guaranteedDelivery;

    @Schema(description = "配送承运商", example = "FedEx")
    private String shippingCarrier;

    @Schema(description = "是否加急配送", example = "true")
    private Boolean expeditedShipping;

    @Schema(description = "是否免费配送", example = "true")
    private Boolean freeShipping;

    // 新增字段 - 增强库存信息
    @Schema(description = "库存阈值", example = "LOW_STOCK")
    private String availabilityThreshold;

    @Schema(description = "预计剩余数量", example = "5")
    private String estimatedRemainingQuantity;

    @Schema(description = "补货日期", example = "2023-12-15T00:00:00.000Z")
    private String restockDate;

    // 补充新增字段

    // 变体相关信息
    @Schema(description = "商品组ID", example = "group123")
    private String itemGroupId;

    @Schema(description = "父商品ID", example = "parent456")
    private String parentItemId;

    @Schema(description = "变体ID", example = "variation789")
    private String variationId;

    @Schema(description = "商品变体列表", example = "var1:red,var2:blue")
    private String itemVariations;

    @Schema(description = "商品组类型", example = "VARIATION_GROUP")
    private String itemGroupType;

    @Schema(description = "是否有变体", example = "true")
    private Boolean hasVariations;

    @Schema(description = "默认变体ID", example = "default123")
    private String defaultVariationId;

    // 商品规格和属性
    @Schema(description = "商品规格", example = "Size: Large, Weight: 1kg")
    private String itemSpecifics;

    @Schema(description = "兼容性属性", example = "iPhone 15,iPhone 14")
    private String compatibilityProperties;

    @Schema(description = "商品状态详细描述", example = "Minor scratches on back")
    private String itemConditionDescription;

    @Schema(description = "卖家备注", example = "Fast shipping available")
    private String sellerNotes;

    // 真品保证
    @Schema(description = "真品保证", example = "true")
    private Boolean authenticityGuarantee;

    @Schema(description = "真品验证", example = "VERIFIED_BY_EBAY")
    private String authenticityVerification;

    // 警告和免责声明
    @Schema(description = "警告信息", example = "Age restriction,Import duties may apply")
    private String warnings;

    @Schema(description = "免责声明", example = "Seller not responsible for delays")
    private String disclaimers;

    // 从 LocalizedAspects 中提取的独立字段
    @Schema(description = "处理器", example = "Hexa Core")
    private String processor;

    @Schema(description = "屏幕尺寸", example = "6.12 in")
    private String screenSize;

    @Schema(description = "内存卡类型", example = "MicroSD")
    private String memoryCardType;

    @Schema(description = "网络连接", example = "Wi-Fi, Bluetooth")
    private String internetConnectivity;

    @Schema(description = "锁定状态", example = "Fully Unlocked")
    private String lockStatus;

    @Schema(description = "型号", example = "A3108")
    private String modelNumber;

    @Schema(description = "SIM卡槽", example = "Dual SIM")
    private String simCardSlot;

    @Schema(description = "颜色（另一字段）", example = "Black Titanium")
    private String colour;

    @Schema(description = "状态/等级", example = "Excellent - Refurbished")
    private String conditionGrade;

    @Schema(description = "网络", example = "Fully Unlocked")
    private String network;

    @Schema(description = "连接性", example = "Bluetooth, Wi-Fi")
    private String connectivity;

    @Schema(description = "样式", example = "Bar")
    private String style;

    @Schema(description = "操作系统", example = "iOS")
    private String operatingSystem;

    @Schema(description = "存储容量", example = "128 GB")
    private String storageCapacity;

    @Schema(description = "合约", example = "Without Contract")
    private String contract;

    @Schema(description = "内存", example = "8GB")
    private String ram;

    // 基于eBay官方文档补充的缺失字段
    @Schema(description = "全球贸易项目编号", example = "0123456789012")
    private String gtin;

    @Schema(description = "eBay产品ID", example = "12345678")
    private String epid;

    @Schema(description = "推断的eBay产品ID", example = "87654321")
    private String inferredEpid;

    @Schema(description = "年龄组", example = "Adult")
    private String ageGroup;

    @Schema(description = "性别", example = "Unisex")
    private String gender;

    @Schema(description = "能效等级", example = "A++")
    private String energyEfficiencyClass;

    @Schema(description = "轮胎标签图片URL", example = "https://example.com/tyre-label.jpg")
    private String tyreLabelImageUrl;

    @Schema(description = "单价", example = "7.99")
    private String unitPrice;

    @Schema(description = "单价货币", example = "GBP")
    private String unitPriceCurrency;

    @Schema(description = "单价计量单位", example = "100g")
    private String unitPricingMeasure;

    @Schema(description = "环保参与费", example = "0.50")
    private String ecoParticipationFee;

    @Schema(description = "环保参与费货币", example = "EUR")
    private String ecoParticipationFeeCurrency;

    @Schema(description = "独特竞拍者数量", example = "15")
    private Integer uniqueBidderCount;

    @Schema(description = "卖家用户ID", example = "seller_12345")
    private String sellerUserId;

    @Schema(description = "图片高度", example = "400")
    private Integer imageHeight;

    @Schema(description = "图片宽度", example = "400")
    private Integer imageWidth;

    @Schema(description = "退货方式", example = "EXCHANGE")
    private String returnMethod;

    @Schema(description = "配送承运商代码", example = "USPS")
    private String shippingCarrierCode;

    @Schema(description = "商标符号", example = "®")
    private String trademarkSymbol;

    @Schema(description = "配送地址国家", example = "US")
    private String shipToLocationCountry;

    @Schema(description = "配送地址邮编", example = "90210")
    private String shipToLocationPostalCode;

    @Schema(description = "库存阈值类型", example = "MORE_THAN")
    private String availabilityThresholdType;

    @Schema(description = "产品标识符类型", example = "UPC")
    private String productIdentifierType;

    @Schema(description = "产品标识符值", example = "123456789012")
    private String productIdentifierValue;

    @Schema(description = "状态描述符", example = "Condition details")
    private String conditionDescriptors;

    @Schema(description = "危险材料信号词", example = "WARNING")
    private String hazardSignalWord;

    @Schema(description = "危险材料额外信息", example = "Keep away from heat")
    private String hazardAdditionalInfo;

    @Schema(description = "附加服务", example = "AUTHENTICITY_GUARANTEE")
    private String addonServices;

    @Schema(description = "真品保证描述", example = "Authenticity guaranteed by eBay")
    private String authenticityGuaranteeDescription;

    @Schema(description = "真品保证条款URL", example = "https://pages.ebay.com/authenticity-guarantee")
    private String authenticityGuaranteeTermsUrl;

    @Schema(description = "真品验证描述", example = "Item verified by professional authenticators")
    private String authenticityVerificationDescription;

    @Schema(description = "真品验证条款URL", example = "https://pages.ebay.com/authenticity-verification")
    private String authenticityVerificationTermsUrl;

    @Schema(description = "优惠券兑换码", example = "SAVE10NOW")
    private String couponRedemptionCode;

    @Schema(description = "优惠券折扣类型", example = "ITEM_PRICE")
    private String couponDiscountType;

    @Schema(description = "优惠券折扣金额", example = "10.00")
    private String couponDiscountAmount;

    @Schema(description = "优惠券消息", example = "Save $10 on your purchase")
    private String couponMessage;

    @Schema(description = "优惠券条款URL", example = "https://pages.ebay.com/coupon-terms")
    private String couponTermsUrl;

    @Schema(description = "优惠券过期日期", example = "2024-12-31T23:59:59.000Z")
    private String couponExpirationDate;
}

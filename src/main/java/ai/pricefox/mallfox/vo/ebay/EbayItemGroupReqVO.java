package ai.pricefox.mallfox.vo.ebay;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * eBay 商品项目组请求 VO
 */
@Data
@Schema(description = "eBay 商品项目组请求参数")
public class EbayItemGroupReqVO {

    @Schema(description = "商品组 ID", example = "1234567890", required = true)
    @NotBlank(message = "商品组 ID 不能为空")
    private String itemGroupId;

    @Schema(description = "字段组", example = "PRODUCT,EXTENDED")
    private String fieldGroups = "PRODUCT,EXTENDED";
}

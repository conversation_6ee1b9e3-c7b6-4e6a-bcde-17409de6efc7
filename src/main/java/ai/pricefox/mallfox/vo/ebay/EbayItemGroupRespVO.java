package ai.pricefox.mallfox.vo.ebay;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * eBay 商品项目组响应 VO
 */
@Data
@Schema(description = "eBay 商品项目组响应")
public class EbayItemGroupRespVO {

    @Schema(description = "商品列表")
    private List<EbayItemDetailRespVO> items;

    @Schema(description = "通用描述列表")
    private List<CommonDescriptionVO> commonDescriptions;

    @Schema(description = "数据获取时间")
    private LocalDateTime fetchTime;

    @Data
    @Schema(description = "通用描述")
    public static class CommonDescriptionVO {
        @Schema(description = "描述内容")
        private String description;

        @Schema(description = "关联的商品ID列表")
        private List<String> itemIds;
    }
}

package ai.pricefox.mallfox.vo.ebay;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * eBay OAuth Token 响应 VO
 */
@Data
@Schema(description = "eBay OAuth Token 响应")
public class EbayOAuthTokenRespVO {

    @Schema(description = "访问令牌", example = "v^1.1#i^1#p^1#r^0#I^3#f^0#t^H4s...")
    private String accessToken;

    @Schema(description = "令牌类型", example = "Application Access Token")
    private String tokenType;

    @Schema(description = "过期时间（秒）", example = "7200")
    private Integer expiresIn;

    @Schema(description = "刷新令牌", example = "refresh_token_value")
    private String refreshToken;

    @Schema(description = "授权范围", example = "https://api.ebay.com/oauth/api_scope")
    private String scope;

    @Schema(description = "令牌获取时间", example = "2023-01-01 12:00:00")
    private LocalDateTime tokenTime;

    @Schema(description = "令牌过期时间", example = "2023-01-01 14:00:00")
    private LocalDateTime expiryTime;

    @Schema(description = "是否有效", example = "true")
    private Boolean isValid;
}

package ai.pricefox.mallfox.vo.ebay;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * eBay 搜索请求 VO - 增强版
 * 支持 eBay Browse API 的完整搜索功能
 */
@Data
@Schema(description = "eBay 搜索请求")
public class EbaySearchReqVO {

    // === 基础搜索参数 ===
    @Schema(description = "搜索关键字", example = "iPhone 15 Pro", required = true)
    //@NotBlank(message = "搜索关键字不能为空")
    private String query;

    @Schema(description = "返回数量限制", example = "10")
    @Min(value = 1, message = "返回数量不能小于1")
    @Max(value = 200, message = "返回数量不能大于200")
    private Integer limit = 10;

    @Schema(description = "偏移量", example = "0")
    @Min(value = 0, message = "偏移量不能小于0")
    private Integer offset = 0;

    // === 分类和字段组 ===
    @Schema(description = "分类ID列表，多个用逗号分隔", example = "179697,182080")
    private String categoryIds;

    @Schema(description = "字段组，控制返回数据详细程度",
            example = "EXTENDED,ADDITIONAL_SELLER_DETAILS,PRODUCT",
            allowableValues = {"COMPACT", "EXTENDED", "MATCHING_ITEMS", "PRODUCT", "ADDITIONAL_SELLER_DETAILS"})
    private String fieldgroups = "EXTENDED,ADDITIONAL_SELLER_DETAILS,PRODUCT"; // 默认获取最详细信息

    // === 价格相关过滤 ===
    @Schema(description = "最低价格", example = "100.00")
    @Min(value = 0, message = "最低价格不能小于0")
    private BigDecimal minPrice;

    @Schema(description = "最高价格", example = "1000.00")
    @Min(value = 0, message = "最高价格不能小于0")
    private BigDecimal maxPrice;

    @Schema(description = "货币代码", example = "USD")
    @Pattern(regexp = "^[A-Z]{3}$", message = "货币代码必须是3位大写字母")
    private String currency = "USD";

    // === 商品条件过滤 ===
    @Schema(description = "商品条件ID列表", 
            example = "1000,1500,2000",
            allowableValues = {"1000", "1500", "2000", "2500", "3000", "4000", "5000", "6000"})
    private List<Integer> conditionIds;

    // === 购买选项过滤 ===
    @Schema(description = "购买选项", 
            example = "FIXED_PRICE,AUCTION",
            allowableValues = {"FIXED_PRICE", "AUCTION", "BEST_OFFER"})
    private List<String> buyingOptions;

    // === 配送和位置过滤 ===
    @Schema(description = "是否免费配送", example = "true")
    private Boolean freeShipping;

    @Schema(description = "最大配送费用", example = "10.00")
    @Min(value = 0, message = "最大配送费用不能小于0")
    private BigDecimal maxDelivyCost;

    @Schema(description = "配送至国家", example = "US")
    @Pattern(regexp = "^[A-Z]{2}$", message = "国家代码必须是2位大写字母")
    private String deliveryCountry;

    @Schema(description = "卖家位置国家", example = "US")
    @Pattern(regexp = "^[A-Z]{2}$", message = "国家代码必须是2位大写字母")
    private String itemLocationCountry;

    // === 品牌和型号过滤 ===
    @Schema(description = "品牌列表", example = "Apple,Samsung")
    private List<String> brands;

    @Schema(description = "产品型号", example = "iPhone 15")
    private String model;

    @Schema(description = "商品特征过滤", example = "Brand:Apple")
    private String aspectFilter;

    // === 排序选项 ===
    @Schema(description = "排序方式", 
            example = "price",
            allowableValues = {"price", "-price", "distance", "newlyListed", "endingSoonest", "bestMatch"})
    private String sort;

    // === 高级搜索选项 ===
    @Schema(description = "GTIN (UPC/EAN) 搜索", example = "888462652872")
    private String gtin;

    @Schema(description = "eBay 产品ID (ePID)", example = "12345678")
    private String epid;

    @Schema(description = "慈善机构ID", example = "80840")
    private String charityId;

    @Schema(description = "市场ID", example = "EBAY_US")
    private String marketplaceId = "EBAY_US";

    // === 兼容性过滤 ===
    @Schema(description = "产品兼容性过滤", example = "Year:2020;Make:Honda;Model:Accord")
    private String compatibilityFilter;

    // === 自定义过滤器 ===
    @Schema(description = "自定义过滤条件", example = "conditionIds:{1000},maxDeliveryCost:0")
    private String customFilter;

    // === 搜索设置 ===
    @Schema(description = "是否包含已售出商品", example = "false")
    private Boolean includeSoldItems = false;

    @Schema(description = "是否仅显示有库存商品", example = "true")
    private Boolean availableOnly = true;

    @Schema(description = "搜索精确匹配", example = "false")
    private Boolean exactMatch = false;

    @Schema(description = "是否同步数据", example = "false")
    private Boolean syncData = false;

    /**
     * 构建完整的过滤器字符串
     * 根据设置的各种过滤条件生成 eBay API 需要的 filter 参数
     */
    public String buildFilterString() {
        StringBuilder filterBuilder = new StringBuilder();
        
        // 价格范围过滤
        if (minPrice != null || maxPrice != null) {
            if (minPrice != null && maxPrice != null) {
                filterBuilder.append("price:[").append(minPrice).append("..").append(maxPrice).append("]");
            } else if (minPrice != null) {
                filterBuilder.append("price:[").append(minPrice).append("..]");
            } else {
                filterBuilder.append("price:[..").append(maxPrice).append("]");
            }
            filterBuilder.append(",");
        }
        
        // 商品条件过滤
        if (conditionIds != null && !conditionIds.isEmpty()) {
            filterBuilder.append("conditionIds:{");
            for (int i = 0; i < conditionIds.size(); i++) {
                if (i > 0) filterBuilder.append("|");
                filterBuilder.append(conditionIds.get(i));
            }
            filterBuilder.append("},");
        }
        
        // 购买选项过滤
        if (buyingOptions != null && !buyingOptions.isEmpty()) {
            filterBuilder.append("buyingOptions:{");
            for (int i = 0; i < buyingOptions.size(); i++) {
                if (i > 0) filterBuilder.append("|");
                filterBuilder.append(buyingOptions.get(i));
            }
            filterBuilder.append("},");
        }
        
        // 免费配送过滤
        if (Boolean.TRUE.equals(freeShipping)) {
            filterBuilder.append("maxDeliveryCost:0,");
        } else if (maxDelivyCost != null) {
            filterBuilder.append("maxDeliveryCost:").append(maxDelivyCost).append(",");
        }
        
        // 配送国家过滤
        if (deliveryCountry != null) {
            filterBuilder.append("deliveryCountry:").append(deliveryCountry).append(",");
        }
        
        // 卖家位置过滤
        if (itemLocationCountry != null) {
            filterBuilder.append("itemLocationCountry:").append(itemLocationCountry).append(",");
        }
        
        // 自定义过滤器
        if (customFilter != null && !customFilter.trim().isEmpty()) {
            filterBuilder.append(customFilter).append(",");
        }
        
        // 移除末尾的逗号
        if (filterBuilder.length() > 0 && filterBuilder.charAt(filterBuilder.length() - 1) == ',') {
            filterBuilder.setLength(filterBuilder.length() - 1);
        }
        
        return filterBuilder.toString();
    }

    /**
     * 构建品牌过滤的 aspect_filter 字符串
     */
    public String buildAspectFilter() {
        if (aspectFilter != null && !aspectFilter.trim().isEmpty()) {
            return aspectFilter;
        }
        
        StringBuilder aspectBuilder = new StringBuilder();
        
        // 品牌过滤
        if (brands != null && !brands.isEmpty()) {
            if (categoryIds != null && !categoryIds.trim().isEmpty()) {
                aspectBuilder.append("categoryId:").append(categoryIds.split(",")[0]).append(",");
            }
            aspectBuilder.append("Brand:{");
            for (int i = 0; i < brands.size(); i++) {
                if (i > 0) aspectBuilder.append("|");
                aspectBuilder.append(brands.get(i));
            }
            aspectBuilder.append("}");
        }
        
        return aspectBuilder.toString();
    }
}

package ai.pricefox.mallfox.vo.ebay;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * eBay 搜索响应 VO
 */
@Data
@Schema(description = "eBay 搜索响应")
public class EbaySearchRespVO {

    @Schema(description = "请求链接", example = "https://api.ebay.com/buy/browse/v1/item_summary/search?q=drone&limit=3")
    private String href;

    @Schema(description = "总数量", example = "260202")
    private Integer total;

    @Schema(description = "下一页链接", example = "https://api.ebay.com/buy/browse/v1/item_summary/search?q=drone&limit=3&offset=3")
    private String next;

    @Schema(description = "上一页链接")
    private String prev;

    @Schema(description = "返回数量限制", example = "3")
    private Integer limit;

    @Schema(description = "偏移量", example = "0")
    private Integer offset;

    @Schema(description = "商品摘要列表")
    private List<ItemSummaryVO> itemSummaries;

    @Schema(description = "警告信息列表", example = "Some filters may not be available")
    private List<String> warnings;

    @Schema(description = "搜索时间", example = "2023-01-01 12:00:00")
    private LocalDateTime searchTime;

    @Data
    @Schema(description = "商品摘要")
    public static class ItemSummaryVO {

        @Schema(description = "商品ID", example = "v1|1234567890|0")
        private String itemId;

        @Schema(description = "传统商品ID", example = "1234567890")
        private String legacyItemId;

        @Schema(description = "商品标题", example = "Syma X5SW-V3 Wifi FPV RC Drone Quadcopter")
        private String title;

        @Schema(description = "副标题", example = "Limited Edition")
        private String subtitle;

        @Schema(description = "商品详细描述", example = "Detailed product description")
        private String description;

        @Schema(description = "卖家商品修订版本", example = "1")
        private String sellerItemRevision;

        @Schema(description = "商品状态", example = "New")
        private String condition;

        @Schema(description = "商品状态ID", example = "1000")
        private String conditionId;

        @Schema(description = "价格", example = "59.99")
        private String price;

        @Schema(description = "货币", example = "USD")
        private String currency;

        @Schema(description = "原价", example = "74.99")
        private String originalPrice;

        @Schema(description = "折扣百分比", example = "20")
        private String discountPercentage;

        @Schema(description = "折扣金额", example = "15.00")
        private String discountAmount;

        @Schema(description = "主图片URL", example = "https://i.ebayimg.com/thumbs/images/g/abc/s-l225.jpg")
        private String imageUrl;

        @Schema(description = "卖家用户名", example = "seller123")
        private String sellerUsername;

        @Schema(description = "卖家反馈百分比", example = "98.6")
        private String sellerFeedbackPercentage;

        @Schema(description = "卖家反馈分数", example = "130000")
        private Integer sellerFeedbackScore;

        @Schema(description = "商品网页URL", example = "https://www.ebay.com/itm/1234567890")
        private String itemWebUrl;

        @Schema(description = "商品联盟链接", example = "https://rover.ebay.com/rover/1/711-53200-19255-0/1?icep_ff3=2&pub=5575378759&campid=5338273189&customid=&icep_item=1234567890")
        private String itemAffiliateWebUrl;

        @Schema(description = "商品API链接", example = "https://api.ebay.com/buy/browse/v1/item/v1|1234567890|0")
        private String itemHref;

        @Schema(description = "购买选项", example = "FIXED_PRICE,BEST_OFFER")
        private String buyingOptions;

        @Schema(description = "配送费用", example = "0.00")
        private String shippingCost;

        @Schema(description = "配送费用货币", example = "USD")
        private String shippingCurrency;

        @Schema(description = "最早配送日期", example = "2022-11-19T08:00:00.000Z")
        private String minEstimatedDeliveryDate;

        @Schema(description = "最晚配送日期", example = "2022-11-21T08:00:00.000Z")
        private String maxEstimatedDeliveryDate;

        @Schema(description = "商品所在国家", example = "US")
        private String itemLocationCountry;

        @Schema(description = "商品所在邮编", example = "12345")
        private String itemLocationPostalCode;

        @Schema(description = "是否仅限成人", example = "false")
        private Boolean adultOnly;

        @Schema(description = "是否有优惠券", example = "false")
        private Boolean availableCoupons;

        @Schema(description = "是否顶级购买体验", example = "true")
        private Boolean topRatedBuyingExperience;

        @Schema(description = "是否优先列表", example = "true")
        private Boolean priorityListing;

        @Schema(description = "商品创建时间", example = "2022-12-25T07:14:44.000Z")
        private String itemCreationDate;

        @Schema(description = "市场ID", example = "EBAY_US")
        private String listingMarketplaceId;

        @Schema(description = "商品组:商品组是指在颜色、尺寸、存储容量等各个方面存在差异的商品", example = "https://api.ebay.com/buy/browse/v1/item_group/v1|1234567890|0")
        private String itemGroupHref;
        @Schema(description = "商品组类型:目前仅SELLER_DEFINED_VARIATIONS支持", example = "SELLER_DEFINED_VARIATIONS")
        private String itemGroupType;

        @Schema(description = "当前出价（拍卖商品）", example = "150.00")
        private String currentBidPrice;

        // EXTENDED fieldgroups 新增字段
        @Schema(description = "商品简短描述", example = "Brand new drone with camera")
        private String shortDescription;

        @Schema(description = "叶子分类ID列表", example = "182186,123456")
        private String leafCategoryIds;

        @Schema(description = "分类信息", example = "Camera Drones > Quadcopters")
        private String categories;

        @Schema(description = "缩略图URL列表", example = "https://thumb1.jpg,https://thumb2.jpg")
        private String thumbnailImages;

        @Schema(description = "额外图片URL列表", example = "https://img1.jpg,https://img2.jpg")
        private String additionalImages;

        @Schema(description = "商品起源日期", example = "2022-12-20T07:14:44.000Z")
        private String itemOriginDate;

        @Schema(description = "关注数量", example = "25")
        private String watchCount;

        @Schema(description = "出价次数", example = "5")
        private String bidCount;

        @Schema(description = "是否达到保留价", example = "true")
        private String reserveMet;

        @Schema(description = "最高出价者", example = "bidder123")
        private String highBidder;

        // ADDITIONAL_SELLER_DETAILS fieldgroups 新增字段
        @Schema(description = "卖家用户ID", example = "seller_user_123")
        private String sellerUserId;

        @Schema(description = "卖家账户类型", example = "BUSINESS")
        private String sellerAccountType;

        @Schema(description = "是否为顶级卖家", example = "true")
        private Boolean sellerTopRated;

        @Schema(description = "反馈评级星级", example = "YELLOW_STAR")
        private String sellerFeedbackRatingStar;

        @Schema(description = "正面反馈百分比", example = "98")
        private Integer sellerPositiveFeedbackPercent;

        // 额外的商品详细信息
        @Schema(description = "单价信息", example = "5.99")
        private String unitPrice;

        @Schema(description = "单价计量单位", example = "per piece")
        private String unitPricingMeasure;

        @Schema(description = "符合的程序", example = "FAST_N_FREE,TOP_RATED")
        private String qualifiedPrograms;

        @Schema(description = "真品保障", example = "true")
        private Boolean authenticityGuarantee;

        @Schema(description = "真品验证", example = "true")
        private Boolean authenticityVerification;

        @Schema(description = "可用优惠券列表", example = "SAVE10,FREESHIP")
        private String availableCouponsList;

        @Schema(description = "能效等级", example = "A++")
        private String energyEfficiencyClass;

        @Schema(description = "危险品信息", example = "Contains lithium battery")
        private String hazmatItems;

        @Schema(description = "批次大小", example = "10")
        private String lotSize;

        @Schema(description = "自提选项", example = "AVAILABLE")
        private String pickupOptions;

        @Schema(description = "退货条款", example = "30 days return accepted")
        private String returnTerms;

        @Schema(description = "快速免费配送", example = "true")
        private Boolean fastAndFreeShipping;

        @Schema(description = "税务信息", example = "Tax included in price")
        private String taxes;

        @Schema(description = "预计可用性", example = "IN_STOCK")
        private String estimatedAvailabilities;

        @Schema(description = "分类路径", example = "Electronics > Cameras & Photo > Drones")
        private String categoryPath;

        @Schema(description = "产品信息", example = "Brand: DJI, Model: Mini 2")
        private String products;

        @Schema(description = "兼容性匹配", example = "Compatible with iPhone 12")
        private String compatibilityMatch;

        // ItemLocation 扩展字段
        @Schema(description = "商品所在城市", example = "New York")
        private String itemLocationCity;

        @Schema(description = "商品所在州或省", example = "NY")
        private String itemLocationStateOrProvince;

        @Schema(description = "商品所在县名", example = "Manhattan")
        private String itemLocationCountyName;

        @Schema(description = "商品所在地址行1", example = "123 Main St")
        private String itemLocationAddressLine1;

        @Schema(description = "商品所在地址行2", example = "Apt 4B")
        private String itemLocationAddressLine2;

        // 通过fieldgroups可获取的额外字段
        @Schema(description = "全球贸易项目编号", example = "0190198462930")
        private String gtin;

        @Schema(description = "eBay产品ID", example = "238226387")
        private String epid;

        @Schema(description = "推断的eBay产品ID", example = "238226388")
        private String inferredEpid;

        @Schema(description = "商品结束日期", example = "2023-01-31T12:00:00.000Z")
        private String itemEndDate;

        @Schema(description = "分类ID", example = "261019")
        private String categoryId;

        @Schema(description = "分类ID路径", example = "159907|159912|29518|261019")
        private String categoryIdPath;

        @Schema(description = "商品数量限制", example = "5")
        private Integer quantityLimitPerBuyer;

        // 基本商品信息（部分可通过PRODUCT fieldgroup获取）
        @Schema(description = "型号", example = "iPhone 13")
        private String model;

        @Schema(description = "尺寸", example = "6.1 inch")
        private String size;

        @Schema(description = "特性", example = "Face ID, Wireless Charging")
        private String features;

        @Schema(description = "存储", example = "128GB")
        private String storage;

        @Schema(description = "备注", example = "Brand new condition")
        private String remarks;

        @Schema(description = "商品规格", example = "Color: Blue, Storage: 128GB")
        private String itemSpecifics;

        @Schema(description = "商品状态详细描述", example = "Brand new in original packaging")
        private String itemConditionDescription;

        @Schema(description = "卖家备注", example = "Fast shipping available")
        private String sellerNotes;

        // 图片信息（扩展）
        @Schema(description = "图片高度", example = "400")
        private Integer imageHeight;

        @Schema(description = "图片宽度", example = "400")
        private Integer imageWidth;

        // 变体相关
        @Schema(description = "商品组ID", example = "group123456")
        private String itemGroupId;

        // 评价和反馈（通过PRODUCT可获取）
        @Schema(description = "评价数量", example = "150")
        private Integer reviewCount;

        @Schema(description = "平均评分", example = "4.5")
        private String averageRating;
    }
}

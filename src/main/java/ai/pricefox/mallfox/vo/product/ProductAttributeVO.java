package ai.pricefox.mallfox.vo.product;

import lombok.Data;

/**
 * <AUTHOR>
 * @description 商品属性VO
 * @date 2025-08-01 13:35:03
 */
@Data
public class ProductAttributeVO {
    /**
     * 电商平台
     */
    private String platform;

    /**
     * SPU编码
     */
    private String spuId;

    /**
     * SKU编码
     */
    private String skuId;

    /**
     * 品牌
     */
    private String brand;

    /**
     * 商品标题
     */
    private String title;

    /**
     * 商品型号
     */
    private String model;

    /**
     * UPC编码
     */
    private String upcCode;

    /**
     * 颜色
     */
    private String color;

    /**
     * 内存
     */
    private String storage;

    /**
     * 服务商
     */
    private String serviceProvider;

    /**
     * 商品状态
     */
    private String condition;

    /**
     * SKU价格
     */
    private Double skuPriceUsd;

    /**
     * 过去30天销量
     */
    private Integer salesLast30Days;

    /**
     * 评论数量
     */
    private Integer reviewNumber;

    /**
     * 商品评分
     */
    private Double reviewScore;

    /**
     * 商品链接
     */
    private String itemUrl;

    private String categoryLevel1;

    /**
     * 二级类目
     */
    private String categoryLevel2;

    /**
     * 三级类目
     */
    private String categoryLevel3;

    /**
     * 系列
     */
    private String series;

    /**
     * 卖家名称
     */
    private String merchant;

    /**
     * 库存（有库存/无库存）
     */
    private String inventoryRetail;

    /**
     * 操作系统
     */
    private String operatingSystem;

    /**
     * 蜂窝技术
     */
    private String cellularTechnology;

    /**
     * 已安装的随机存取存储器（RAM）大小
     */
    private String ramMemoryInstalledSize;

    /**
     * 处理器
     */
    private String processor;

    /**
     * 屏幕尺寸
     */
    private String screenSize;

    /**
     * 分辨率
     */
    private String resolution;

    /**
     * 屏幕刷新频率
     */
    private String refreshRate;

    /**
     * 显示类型
     */
    private String displayType;

    /**
     * 电池容量
     */
    private String batteryPower;

    /**
     * 平均通话时长
     */
    private String averageTalkTime;

    /**
     * 电池充电时间
     */
    private String batteryChargeTime;

    /**
     * 特点；特性；功能；特征
     */
    private String features;

    /**
     * 前置照片传感器分辨率
     */
    private String frontPhotoSensorResolution;

    /**
     * 后置摄像头照片传感器分辨率
     */
    private String rearFacingCameraPhotoSensorResolution;

    /**
     * 后置摄像头数量
     */
    private Integer numberOfRearFacingCameras;

    /**
     * 手机长宽高
     */
    private String dimensions;

    /**
     * 商品重量
     */
    private String itemWeight;

    /**
     * 生物识别安全功能
     */
    private String biometricSecurityFeature;

    /**
     * 连接技术
     */
    private String connectivityTech;

    /**
     * 支持的卫星导航系统
     */
    private String supportedSatelliteNavigationSystem;

    /**
     * 有效视频分辨率
     */
    private String effectiveVideoResolution;

    /**
     * 视频捕获分辨率
     */
    private String videoCaptureResolution;

    /**
     * SIM卡卡槽数量
     */
    private Integer simCardSlotCount;

    /**
     * 连接器类型
     */
    private String connectorType;

    /**
     * 防水性能
     */
    private String waterResistance;

    /**
     * 款式年份
     */
    private String modelYear;

    /**
     * 保修说明
     */
    private String warrantyDescription;

    /**
     * 商品评分后标签
     */
    private String reviewComment;

    /**
     * 商品销售价
     */
    private String priceUsd;

    /**
     * 商品零售价
     */
    private String listPrice;

    /**
     * 商品折扣
     */
    private String discount;

    /**
     * 价格趋势 最低价
     */
    private String priceHistoryLowestPrice;

    /**
     * 价格趋势 平均成交价
     */
    private String priceHistoryAveragePrice;

    /**
     * 比价模块 后面标签
     */
    private String priceComparisonComment;

    /**
     * 卖家类型
     */
    private String sellerType;

    /**
     * 发货时间
     */
    private String shippingTime;

    /**
     * 退换货政策
     */
    private String returnPolicy;

    /**
     * 付款方式 如信用卡
     */
    private String paymentMethods;

    /**
     * 分期信息
     */
    private String installPayment;

    /**
     * 卖家评分
     */
    private String merchantRating;

    /**
     * Price history-区间最低价
     */
    private String priceHistoryLowestPriceRange;

    /**
     * Price history-当前最低价
     */
    private String priceHistoryLowestPriceNow;

    /**
     * 规格-参数信息
     */
    private String specificationInfo;

    /**
     * 规格-排名
     */
    private String specificationRanking;

    /**
     * 规格-专家测评
     */
    private String specificationExpertReview;

    /**
     * 规格-AI描述
     */
    private String specificationAiDescription;

    /**
     * 规格-AI案例
     */
    private String specificationAiCase;

    /**
     * 评论-1-5星评分
     */
    private String review1To5StarNumbers;

    /**
     * 评论-整体的好坏标签
     */
    private String reviewOverviewProsCons;

    /**
     * Review-蛛网图的几个二级版块
     */
    private String reviewWebChartSubsections;

    /**
     * Review-不同星级评分
     */
    private String reviewDifferentStarRatings;

    /**
     * Review-不同星级 提及率
     */
    private String reviewStarRatingMentionRate;

    /**
     * Review-二级版块pros cons
     */
    private String reviewSubsectionProsCons;

    /**
     * Review-二级版块 AI description
     */
    private String reviewSubsectionAiDescription;

    /**
     * Review-标签
     */
    private String reviewTags;

    /**
     * 专家测评
     */
    private String expertReview;

    /**
     * Ranking 列表页 -参数
     */
    private String rankingListParams;

    /**
     * Ranking 列表页 -参数打分
     */
    private String rankingListParamScoring;

    /**
     * Ranking 列表页 -参数排名
     */
    private String rankingListParamRanking;

    /**
     * 商品主图1
     */
    private String mainImage1;

    /**
     * 商品主图2
     */
    private String mainImage2;

    /**
     * 商品主图3
     */
    private String mainImage3;

    /**
     * 商品主图4
     */
    private String mainImage4;

    /**
     * 商品主图5
     */
    private String mainImage5;

    /**
     * 商品主图6
     */
    private String mainImage6;

    /**
     * 商品主图7
     */
    private String mainImage7;

    /**
     * 商品主图8
     */
    private String mainImage8;

    /**
     * 商品主图9
     */
    private String mainImage9;

    /**
     * 商品主图10
     */
    private String mainImage10;

    /**
     * 规格 颜色的图片
     */
    private String specificationColorImage;

    /**
     * 最新价格 库存的获取时间
     */
    private String priceUpdateTime;

    /**
     * 商品SKU数量
     */
    private Integer skuNumber;

    /**
     * 上一次的SKU价格
     */
    private String skuPriceLastUpdated;

    /**
     * 上一次价格 库存更新时间
     */
    private String priceLastUpdatedTime;
}
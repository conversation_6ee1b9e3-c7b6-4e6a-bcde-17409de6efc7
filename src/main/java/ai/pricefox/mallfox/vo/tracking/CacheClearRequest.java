package ai.pricefox.mallfox.vo.tracking;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.Data;

/**
 * 缓存清除请求类
 */
@Data
public class CacheClearRequest {
    @Parameter(description = "清除类型：all(全部)、table(按表)、record(按记录)、pattern(按模式)", example = "all")
    private String clearType;

    @Parameter(description = "表名（clearType为table或record时必填）", example = "product_data_offers")
    private String tableName;

    @Parameter(description = "记录ID（clearType为record时必填）", example = "123")
    private Long recordId;

    @Parameter(description = "缓存键模式（clearType为pattern时必填，支持通配符*）", example = "product_data_*")
    private String pattern;
}
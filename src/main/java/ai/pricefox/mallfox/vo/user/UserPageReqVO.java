package ai.pricefox.mallfox.vo.user;

import ai.pricefox.mallfox.vo.base.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 用户分页查询请求VO
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "用户分页查询请求")
public class UserPageReqVO extends PageParam {

    @Schema(description = "用户名", example = "admin")
    private String username;

    @Schema(description = "邮箱", example = "<EMAIL>")
    private String email;

    @Schema(description = "手机号", example = "13800138000")
    private String phone;

    @Schema(description = "昵称", example = "管理员")
    private String nickname;

    @Schema(description = "性别", example = "1")
    private Integer sex;

    @Schema(description = "状态", example = "1")
    private Integer status;
}

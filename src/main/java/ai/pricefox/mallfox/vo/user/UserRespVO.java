package ai.pricefox.mallfox.vo.user;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户响应VO
 */
@Data
@Schema(description = "用户响应")
public class UserRespVO {

    @Schema(description = "用户ID", example = "1")
    private Long id;

    @Schema(description = "用户名", example = "admin")
    private String username;

    @Schema(description = "邮箱", example = "<EMAIL>")
    private String email;

    @Schema(description = "手机号", example = "13800138000")
    private String phone;

    @Schema(description = "昵称", example = "管理员")
    private String nickname;

    @Schema(description = "头像", example = "https://example.com/avatar.jpg")
    private String avatar;

    @Schema(description = "性别", example = "1")
    private Integer sex;

    @Schema(description = "状态", example = "1")
    private Integer status;

    @Schema(description = "备注", example = "系统管理员")
    private String remark;

    @Schema(description = "创建时间", example = "2023-01-01 12:00:00")
    private LocalDateTime createTime;

    @Schema(description = "更新时间", example = "2023-01-01 12:00:00")
    private LocalDateTime updateTime;

    @Schema(description = "删除标记", example = "0")
    private String delFlag;
}

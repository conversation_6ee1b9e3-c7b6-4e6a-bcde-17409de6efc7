# 日志级别配置
logging.level.ai.pricefox.mallfox.config.SaTokenConfigure=DEBUG
logging.level.cn.dev33.satoken=DEBUG

# MyBatis-Plus SQL日志配置
# 使用 SLF4J 日志，这样可以更好地控制日志格式
mybatis-plus.configuration.log-impl=org.apache.ibatis.logging.slf4j.Slf4jImpl

# 数据库相关日志
logging.level.ai.pricefox.mallfox.mapper=DEBUG
logging.level.com.baomidou.mybatisplus=DEBUG

# 请求日志配置
logging.level.ai.pricefox.mallfox.config.RequestLoggingInterceptor=INFO
logging.level.ai.pricefox.mallfox.config.ApiLoggingAspect=INFO

# SQL 执行日志（显示具体的 SQL 语句和参数）
logging.level.org.apache.ibatis=DEBUG
logging.level.java.sql=DEBUG
logging.level.java.sql.Statement=DEBUG
logging.level.java.sql.PreparedStatement=DEBUG
logging.level.java.sql.ResultSet=DEBUG

# 应用日志配置
app.logging.enable-request-logging=true
app.logging.enable-api-logging=true
app.logging.enable-sql-logging=true
app.logging.log-request-headers=true
app.logging.log-request-parameters=true
app.logging.log-request-body=true
app.logging.log-response-body=true
app.logging.max-request-body-length=1000
app.logging.max-response-body-length=1000


# 密码加密配置
app.password.enable-encryption=true
app.password.secret-key=PriceFoxMallKey!
app.password.log-encryption=false
app.password.min-length=6
app.password.max-length=20

#mysql??
spring.datasource.url=*************************************************************************************************************************************************************
spring.datasource.type=com.alibaba.druid.pool.DruidDataSource
spring.datasource.username=pricefox
spring.datasource.password=pf6a4Kh#OA@
spring.datasource.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.druid.initial-size=30
spring.datasource.druid.max-active=1000
spring.datasource.druid.min-idle=30
spring.datasource.druid.max-wait=100000
spring.datasource.druid.use-unfair-lock=true
spring.datasource.druid.pool-prepared-statements=false
spring.datasource.druid.validation-query=select 1
spring.datasource.druid.validation-query-timeout=2
spring.datasource.druid.keep-alive=true
spring.datasource.druid.test-on-borrow=false
spring.datasource.druid.test-on-return=false
spring.datasource.druid.test-while-idle=true
spring.datasource.druid.min-evictable-idle-time-millis=43200000
spring.datasource.druid.max-evictable-idle-time-millis=86400000

#redis配置
spring.data.redis.host=*************
spring.data.redis.port=6379
spring.data.redis.password=pf46dhsv33fdv843fer
spring.data.redis.database=1
spring.data.redis.timeout=10000

# 邮件配置-https://outlook.office.com/
mail.host=smtp.office365.com
mail.port=587
mail.auth=true
mail.user=<EMAIL>
mail.pass=your-password
mail.ssl-enable=true
mail.timeout=10000

#阿里云存储
aliyun.oss.endpoint=oss-us-west-1.aliyuncs.com
aliyun.oss.access-key-id=LTAI5t9girPswJTTfbfjcqhH
aliyun.oss.access-key-secret=******************************
aliyun.oss.bucket-name=pricefox

# Google OAuth 配置
google.client-id=************-dpb9c1nel2ivd8pl19d90qps55rkdgse.apps.googleusercontent.com
google.client-secret=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
google.redirect-uri=http://*************:28888/api/oauth/google/callback

amazon.api.key=
amazon.api.secret=

# ===========================================
# 开发环境配置
# ===========================================
spring:
  config:
    activate:
      on-profile: dev  # 激活开发环境
  datasource:
    url: ************************************************************************************************************************************************************************************************************************************************************************ # 开发数据库URL
    username: root  # 开发数据库用户
    password: PassW0rd2025.  # 开发数据库密码
    driver-class-name: com.mysql.cj.jdbc.Driver  # MySQL驱动
    hikari:
      connection-timeout: 60000  # 连接超时时间(毫秒)
      idle-timeout: 500000  # 空闲超时时间(毫秒)
      max-lifetime: 540000  # 最大生命周期(毫秒)
      maximum-pool-size: 12  # 最大连接池大小
      minimum-idle: 10  # 最小空闲连接数
      connection-test-query: SELECT 1  # 连接测试查询
  data:
    redis:
      host: *************  # Redis开发主机
      port: 6379  # Redis端口
      password: PassW0rd2025.  # Redis密码
      database: 0  # Redis库索引
      timeout: 10000  # 连接超时(毫秒)
    # MongoDB开发环境配置
    mongodb:
      uri: ******************************************************************************
      auto-index-creation: true
  mail:
    host: smtp.office365.com  # 邮件服务器
    port: 587  # 邮件端口
    username: <EMAIL>  # 邮件账号
    password: your-password  # 邮件密码
    properties:
      mail:
        smtp:
          ssl:
            enable: true  # 启用SSL

# ===========================================
# 开发环境日志配置
# ===========================================
logging:
  level:
    # 项目自定义包DEBUG日志
    ai.pricefox.mallfox: DEBUG
    
    # SaToken调试
    ai.pricefox.mallfox.config.SaTokenConfigure: DEBUG
    cn.dev33.satoken: DEBUG
    
    # MyBatis调试
    ai.pricefox.mallfox.mapper: DEBUG
    com.baomidou.mybatisplus: DEBUG
    
    # MongoDB调试
    org.springframework.data.mongodb: DEBUG
    org.mongodb.driver: DEBUG
    
    # 请求日志
    ai.pricefox.mallfox.config.RequestLoggingInterceptor: INFO
    ai.pricefox.mallfox.config.ApiLoggingAspect: INFO
    
    # SQL详细日志
    org.apache.ibatis: DEBUG
    java.sql: DEBUG
    java.sql.Statement: DEBUG
    java.sql.PreparedStatement: DEBUG
    java.sql.ResultSet: DEBUG

# ===========================================
# 开发环境应用日志配置
# ===========================================
app:
  logging:
    enable-request-logging: true  # 启用请求日志
    enable-api-logging: true  # 启用API日志
    enable-sql-logging: true  # 启用SQL日志
    log-request-headers: true  # 记录请求头
    log-request-parameters: true  # 记录请求参数
    log-request-body: true  # 记录请求体
    log-response-body: true  # 记录响应体
    max-request-body-length: 1000  # 请求体最大长度
    max-response-body-length: 1000  # 响应体最大长度

# ===========================================
# OAuth配置 (开发环境)
# ===========================================
google:
  client-id: 928277519574-dpb9c1nel2ivd8pl19d90qps55rkdgse.apps.googleusercontent.com  # Google客户端ID
  client-secret: 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  # Google客户端密钥
  redirect-uri: http://49.234.206.92:28888/api/oauth/google/callback  # 开发环境回调地址

# ===========================================
# 其他服务配置 (开发环境)
# ===========================================
amazon:
  api:
    key:  # 开发环境Amazon API Key
    secret:  # 开发环境Amazon API Secret

# ===========================================
# MyBatis-Plus 配置
# ===========================================
mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl  # 控制台输出SQL日志
  global-config:
    db-config:
      logic-delete-value: 1
      logic-not-delete-value: 0
spring.application.name=mallfox
spring.jackson.date-format=yyyy-MM-dd HH:mm:ss
spring.jackson.time-zone=GMT+8
spring.jackson.default-property-inclusion=non_null
spring.jackson.serialization.FAIL_ON_EMPTY_BEANS=false
spring.jackson.serialization.write-dates-as-timestamps=false
spring.jackson.deserialization.fail-on-unknown-properties=false
spring.main.allow-circular-references=true
server.port=28888
server.error.whitelabel.enabled=false
# 全局API前缀配置
server.servlet.context-path=/api
spring.profiles.active=dev

# Sa-Token配置
# token 名称（同时也是 cookie 名称）
sa-token.token-name=Authorization
# token 有效期（单位：秒） 默认30天，-1 代表永久有效
sa-token.timeout=2592000
# token 最低活跃频率（单位：秒），如果 token 超过此时间没有访问系统就会被冻结，默认-1 代表不限制，永不冻结
sa-token.active-timeout=-1
# 是否允许同一账号多地同时登录 （为 true 时允许一起登录, 为 false 时新登录挤掉旧登录）
sa-token.is-concurrent=true
# 在多人登录同一账号时，是否共用一个 token （为 true 时所有登录共用一个 token, 为 false 时每次登录新建一个 token）
sa-token.is-share=true
# token 风格（默认可取值：uuid、simple-uuid、random-32、random-64、random-128、tik）
sa-token.token-style=uuid
# 是否输出操作日志
sa-token.is-log=true

# 销量数据补充配置
# 销量补充功能开关
sales.supplement.enabled=true
# 评论到销量的转换比例（默认3%）
sales.supplement.review-to-sales-ratio=0.03
# 批处理大小
sales.supplement.batch-size=1000

# 日志级别配置
logging.level.ai.pricefox.mallfox.config.SaTokenConfigure=DEBUG
logging.level.cn.dev33.satoken=DEBUG

# MyBatis-Plus SQL日志配置
# 使用 SLF4J 日志，这样可以更好地控制日志格式
mybatis-plus.configuration.log-impl=org.apache.ibatis.logging.slf4j.Slf4jImpl

# 数据库相关日志
logging.level.ai.pricefox.mallfox.mapper=DEBUG
logging.level.com.baomidou.mybatisplus=DEBUG

# 请求日志配置
logging.level.ai.pricefox.mallfox.config.RequestLoggingInterceptor=INFO
logging.level.ai.pricefox.mallfox.config.ApiLoggingAspect=INFO

# SQL 执行日志（显示具体的 SQL 语句和参数）
logging.level.org.apache.ibatis=DEBUG
logging.level.java.sql=DEBUG
logging.level.java.sql.Statement=DEBUG
logging.level.java.sql.PreparedStatement=DEBUG
logging.level.java.sql.ResultSet=DEBUG

# 应用日志配置
app.logging.enable-request-logging=true
app.logging.enable-api-logging=true
app.logging.enable-sql-logging=true
app.logging.log-request-headers=true
app.logging.log-request-parameters=true
app.logging.log-request-body=true
app.logging.log-response-body=true
app.logging.max-request-body-length=1000
app.logging.max-response-body-length=1000

# 密码加密配置
app.password.enable-encryption=true
app.password.secret-key=PriceFoxMallKey!
app.password.log-encryption=false
app.password.min-length=6
app.password.max-length=20

#mysql??
spring.datasource.url=*************************************************************************************************************************************************************
spring.datasource.type=com.alibaba.druid.pool.DruidDataSource
spring.datasource.username=pricefox
spring.datasource.password=pf6a4Kh#OA@
spring.datasource.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.druid.initial-size=30
spring.datasource.druid.max-active=1000
spring.datasource.druid.min-idle=30
spring.datasource.druid.max-wait=100000
spring.datasource.druid.use-unfair-lock=true
spring.datasource.druid.pool-prepared-statements=false
spring.datasource.druid.validation-query=select 1
spring.datasource.druid.validation-query-timeout=2
spring.datasource.druid.keep-alive=true
spring.datasource.druid.test-on-borrow=false
spring.datasource.druid.test-on-return=false
spring.datasource.druid.test-while-idle=true
spring.datasource.druid.min-evictable-idle-time-millis=43200000
spring.datasource.druid.max-evictable-idle-time-millis=86400000

# Redis连接池配置
spring.data.redis.lettuce.pool.max-active=8
spring.data.redis.lettuce.pool.max-wait=-1
spring.data.redis.lettuce.pool.max-idle=8
spring.data.redis.lettuce.pool.min-idle=0

# Microsoft Graph API 邮件配置
microsoft.graph.client-id=565f1679-80ad-4cce-a3d1-4ee74f1a85b2
microsoft.graph.tenant-id=93ba8227-f02f-451c-a4d9-331cb03ee414
microsoft.graph.client-secret=****************************************
microsoft.graph.sender-email=<EMAIL>

# 备用 SMTP 配置（如果需要）
spring.mail.host=smtp-mail.outlook.com
spring.mail.port=587
spring.mail.username=<EMAIL>
spring.mail.password=backup-password
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true
spring.mail.default-encoding=UTF-8

# 自定义邮件配置（用于EmailUtil中的@Value注入）
mail.user=<EMAIL>

# 备用邮件配置示例（如果Office365不可用）
# Gmail配置示例：
# spring.mail.host=smtp.gmail.com
# spring.mail.port=587
# spring.mail.username=<EMAIL>
# spring.mail.password=your-app-password

# QQ邮箱配置示例：
# spring.mail.host=smtp.qq.com
# spring.mail.port=587
# spring.mail.username=<EMAIL>
# spring.mail.password=your-authorization-code

# eBay API 配置
# 生产环境
ebay.api.client-id=yunfeiwa-i-PRD-f8e9408e1-f02fd8c9
ebay.api.client-secret=PRD-8e9408e1e474-194e-4133-9052-4b69
ebay.api.base-url=https://api.ebay.com/buy/browse/v1
# 沙盒环境
ebay.api.sandbox-base-client-id=yunfeiwa-i-PRD-f8e9408e1-f02fd8c9
ebay.api.sandbox-base-client-secret=PRD-8e9408e1e474-194e-4133-9052-4b69
ebay.api.sandbox-base-url=https://api.sandbox.ebay.com/buy/browse/v1
ebay.api.scope=https://api.ebay.com/oauth/api_scope
# 是否使用沙盒环境
ebay.api.sandbox=false
ebay.api.mock-mode=false
ebay.api.timeout=30000
ebay.api.max-retries=3
ebay.api.retry-delay=1000

#阿里云存储
aliyun.oss.endpoint=oss-us-west-1.aliyuncs.com
aliyun.oss.access-key-id=LTAI5t9girPswJTTfbfjcqhH
aliyun.oss.access-key-secret=******************************
aliyun.oss.bucket-name=pricefox

# Google OAuth 配置
google.client-id=928277519574-dpb9c1nel2ivd8pl19d90qps55rkdgse.apps.googleusercontent.com
google.client-secret=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
google.redirect-uri=http://*************:28888/api/oauth/google/callback


amazon.api.key=
amazon.api.secret=

# BestBuy API 配置
bestbuy.api.key=vcuVaClMGT7uYQANxlBoLybw
bestbuy.api.base-url=https://api.bestbuy.com/v1
bestbuy.api.timeout=30000
bestbuy.api.max-retries=3
bestbuy.api.retry-delay=1000

# 线程池配置
thread.pool.core-size=10
thread.pool.max-size=20
thread.pool.queue-capacity=200
thread.pool.keep-alive=60
thread.pool.thread-name-prefix=async-task-

# 文件上传配置
spring.servlet.multipart.enabled=true
spring.servlet.multipart.max-file-size=500MB
spring.servlet.multipart.max-request-size=1000MB
spring.servlet.multipart.file-size-threshold=2KB
spring.servlet.multipart.resolve-lazily=false

# 字段追踪配置
field.tracking.enabled=true
field.tracking.async-enabled=true
field.tracking.batch-size=1000
field.tracking.queue-capacity=10000
field.tracking.process-interval=5000

# 价格历史记录配置
price.history.enabled=true
price.history.async-enabled=true
price.history.batch-size=500
price.history.retention-days=365
price.history.cleanup-enabled=true
price.history.cleanup-interval=86400000
price.history.record-create=true
price.history.record-update=true
price.history.record-delete=true
price.history.minimum-change-threshold=0.01
price.history.debug-log-enabled=false

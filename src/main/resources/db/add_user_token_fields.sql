-- =====================================================
-- 为用户表添加Token相关字段
-- 执行时间：请根据实际情况调整
-- 说明：添加用户Token持久化所需的字段
-- =====================================================

-- 1. 添加Token相关字段
ALTER TABLE `user` 
ADD COLUMN `access_token` VARCHAR(255) NULL COMMENT '访问令牌' AFTER `update_time`,
ADD COLUMN `refresh_token` VARCHAR(255) NULL COMMENT '刷新令牌' AFTER `access_token`,
ADD COLUMN `token_expire_time` DATETIME NULL COMMENT '令牌过期时间' AFTER `refresh_token`,
ADD COLUMN `refresh_token_expire_time` DATETIME NULL COMMENT '刷新令牌过期时间' AFTER `token_expire_time`,
ADD COLUMN `last_login_time` DATETIME NULL COMMENT '最后登录时间' AFTER `refresh_token_expire_time`;

-- 2. 为Token字段添加索引以提高查询性能
CREATE INDEX `idx_user_access_token` ON `user` (`access_token`);
CREATE INDEX `idx_user_refresh_token` ON `user` (`refresh_token`);
CREATE INDEX `idx_user_token_expire_time` ON `user` (`token_expire_time`);
CREATE INDEX `idx_user_last_login_time` ON `user` (`last_login_time`);

-- 3. 验证字段是否添加成功
-- SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_COMMENT 
-- FROM INFORMATION_SCHEMA.COLUMNS 
-- WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'user' 
-- AND COLUMN_NAME IN ('access_token', 'refresh_token', 'token_expire_time', 'refresh_token_expire_time', 'last_login_time');

-- 4. 验证索引是否创建成功
-- SHOW INDEX FROM `user` WHERE Key_name IN ('idx_user_access_token', 'idx_user_refresh_token', 'idx_user_token_expire_time', 'idx_user_last_login_time');

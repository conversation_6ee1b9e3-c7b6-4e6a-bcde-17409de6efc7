-- =====================================================
-- 创建后台用户表
-- 说明：用于后台管理系统的用户管理
-- =====================================================

CREATE TABLE `admin_user` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `username` VARCHAR(50) NOT NULL COMMENT '用户名',
  `password` VARCHAR(255) NOT NULL COMMENT '密码',
  `email` VARCHAR(100) NULL COMMENT '邮箱',
  `phone` VARCHAR(20) NULL COMMENT '手机号',
  `nickname` VARCHAR(50) NULL COMMENT '昵称',
  `avatar` VARCHAR(255) NULL COMMENT '头像',
  `sex` TINYINT(1) NULL DEFAULT 0 COMMENT '性别(0:未知 1:男 2:女)',
  `status` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '状态(0:禁用 1:启用)',
  `access_token` VARCHAR(255) NULL COMMENT '访问令牌',
  `refresh_token` VARCHAR(255) NULL COMMENT '刷新令牌',
  `token_expire_time` DATETIME NULL COMMENT '令牌过期时间',
  `refresh_token_expire_time` DATETIME NULL COMMENT '刷新令牌过期时间',
  `last_login_time` DATETIME NULL COMMENT '最后登录时间',
  `last_login_ip` VARCHAR(50) NULL COMMENT '最后登录IP',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` BIGINT(20) NULL COMMENT '创建人',
  `update_by` BIGINT(20) NULL COMMENT '更新人',
  `remark` VARCHAR(500) NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_username` (`username`),
  UNIQUE KEY `uk_email` (`email`),
  UNIQUE KEY `uk_phone` (`phone`),
  INDEX `idx_status` (`status`),
  INDEX `idx_access_token` (`access_token`),
  INDEX `idx_refresh_token` (`refresh_token`),
  INDEX `idx_token_expire_time` (`token_expire_time`),
  INDEX `idx_last_login_time` (`last_login_time`),
  INDEX `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='后台用户表';

-- 插入默认管理员账号
INSERT INTO `admin_user` (`username`, `password`, `email`, `phone`, `nickname`, `status`, `create_time`, `update_time`, `remark`) 
VALUES ('admin', '123456', '<EMAIL>', '13800138000', '系统管理员', 1, NOW(), NOW(), '系统默认管理员账号');


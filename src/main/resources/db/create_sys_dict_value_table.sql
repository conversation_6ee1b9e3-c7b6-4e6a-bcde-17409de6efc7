-- =====================================================
-- 创建字典项表
-- 说明：用于存储字典的具体数据项
-- =====================================================

CREATE TABLE `sys_dict_value` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `dict_id` BIGINT(20) NOT NULL COMMENT '字典ID',
  `value` VARCHAR(100) NOT NULL COMMENT '数据值',
  `label` VARCHAR(100) NOT NULL COMMENT '标签名',
  `type` VARCHAR(50) NULL COMMENT '类型',
  `description` VARCHAR(500) NULL COMMENT '描述',
  `sort` INT(11) NULL DEFAULT 0 COMMENT '排序',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remarks` VARCHAR(500) NULL COMMENT '备注信息',
  `del_flag` VARCHAR(1) NOT NULL DEFAULT '0' COMMENT '删除标记(0:正常 1:删除)',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_dict_value` (`dict_id`, `value`),
  INDEX `idx_dict_id` (`dict_id`),
  INDEX `idx_value` (`value`),
  INDEX `idx_label` (`label`),
  INDEX `idx_type` (`type`),
  INDEX `idx_sort` (`sort`),
  INDEX `idx_del_flag` (`del_flag`),
  INDEX `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='字典项表';

-- 插入示例数据
INSERT INTO `sys_dict_value` (`dict_id`, `value`, `label`, `type`, `description`, `sort`, `remarks`) VALUES
(1, '0', '禁用', 'status', '用户状态-禁用', 1, '用户状态字典项'),
(1, '1', '启用', 'status', '用户状态-启用', 2, '用户状态字典项'),
(2, '0', '未知', 'sex', '性别-未知', 1, '性别字典项'),
(2, '1', '男', 'sex', '性别-男', 2, '性别字典项'),
(2, '2', '女', 'sex', '性别-女', 3, '性别字典项'),
(3, 'new', '全新', 'condition', '商品状态-全新', 1, '商品状态字典项'),
(3, 'used', '二手', 'condition', '商品状态-二手', 2, '商品状态字典项'),
(3, 'refurbished', '翻新', 'condition', '商品状态-翻新', 3, '商品状态字典项'),
(4, 'pending', '待处理', 'status', '订单状态-待处理', 1, '订单状态字典项'),
(4, 'processing', '处理中', 'status', '订单状态-处理中', 2, '订单状态字典项'),
(4, 'completed', '已完成', 'status', '订单状态-已完成', 3, '订单状态字典项'),
(4, 'cancelled', '已取消', 'status', '订单状态-已取消', 4, '订单状态字典项');

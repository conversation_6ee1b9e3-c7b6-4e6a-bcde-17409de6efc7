-- =====================================================
-- 加密现有用户表中的明文密码
-- 说明：此脚本用于将现有的明文密码转换为加密密码
-- 注意：执行前请备份数据库！
-- =====================================================

-- 查看当前用户表中的密码情况
SELECT id, username, password, 
       CASE 
           WHEN LENGTH(password) > 20 AND password REGEXP '^[A-Za-z0-9+/]*={0,2}$' THEN '可能已加密'
           ELSE '明文密码'
       END AS password_status
FROM user 
WHERE password IS NOT NULL;

-- 查看当前后台用户表中的密码情况（如果表存在）
SELECT id, username, password,
       CASE 
           WHEN LENGTH(password) > 20 AND password REGEXP '^[A-Za-z0-9+/]*={0,2}$' THEN '可能已加密'
           ELSE '明文密码'
       END AS password_status
FROM admin_user 
WHERE password IS NOT NULL;

-- 注意：由于SQL无法直接调用Java的AES加密方法，
-- 实际的密码加密需要通过应用程序来完成。
-- 
-- 建议的迁移步骤：
-- 1. 创建一个临时的数据迁移接口
-- 2. 通过接口批量读取用户数据
-- 3. 对明文密码进行加密
-- 4. 更新数据库中的密码字段
--
-- 或者可以在应用启动时自动检测并加密明文密码

-- 临时标记需要加密的用户（可选）
-- ALTER TABLE user ADD COLUMN password_encrypted TINYINT(1) DEFAULT 0 COMMENT '密码是否已加密(0:未加密 1:已加密)';
-- ALTER TABLE admin_user ADD COLUMN password_encrypted TINYINT(1) DEFAULT 0 COMMENT '密码是否已加密(0:未加密 1:已加密)';

-- 创建用户社交账号关联表
CREATE TABLE `user_social_account` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `social_type` tinyint(4) NOT NULL COMMENT '社交平台类型（1:Google, 2:Facebook, 3:Twitter, 4:LinkedIn, 5:GitHub, 6:Apple, 7:Microsoft, 8:Amazon）',
  `social_id` varchar(255) NOT NULL COMMENT '社交平台用户唯一标识',
  `social_name` varchar(255) DEFAULT NULL COMMENT '社交平台用户名或昵称',
  `social_avatar` varchar(1000) DEFAULT NULL COMMENT '社交平台用户头像',
  `social_email` varchar(255) DEFAULT NULL COMMENT '社交平台用户邮箱',
  `access_token` varchar(1000) DEFAULT NULL COMMENT '社交平台访问令牌',
  `token_expire_time` datetime DEFAULT NULL COMMENT '令牌过期时间',
  `refresh_token` varchar(1000) DEFAULT NULL COMMENT '刷新令牌',
  `extra_data` text DEFAULT NULL COMMENT '额外数据（JSON格式）',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_social_type_social_id` (`social_type`, `social_id`),
  KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户社交账号关联表'; 
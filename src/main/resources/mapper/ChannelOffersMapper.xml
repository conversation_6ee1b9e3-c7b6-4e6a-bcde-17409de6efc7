<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.pricefox.mallfox.mapper.product.ChannelOffersMapper">


    <!--查找比当前价格更低的所有有效报价，并按价格升序排列 -->
    <select id="findLowerPriceOffers" resultType="ai.pricefox.mallfox.domain.product.ChannelOffers">
        SELECT *
        FROM channel_offers
        WHERE sku_code = #{skuCode}
          AND inventory = 'In Stock'
          AND price &lt; #{currentPriceInUsd}
        ORDER BY price ASC
    </select>

</mapper>
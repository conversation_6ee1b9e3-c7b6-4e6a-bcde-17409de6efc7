<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.pricefox.mallfox.mapper.integration.DataCalibrationTagsMapper">

    <resultMap id="BaseResultMap" type="ai.pricefox.mallfox.domain.integration.DataCalibrationTags">
        <id property="id" column="id"/>
        <result property="targetTable" column="target_table"/>
        <result property="targetId" column="target_id"/>
        <result property="fieldName" column="field_name"/>
        <result property="tagStatus" column="tag_status"/>
        <result property="operatorId" column="operator_id"/>
        <result property="remark" column="remark"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,target_table,target_id,field_name,tag_status,operator_id,
        remark,create_time,update_time
    </sql>

    <insert id="batchInsertOrUpdate">
        INSERT INTO data_calibration_tags (target_table, target_id, field_name, tag_status, operator_id, remark)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.targetTable}, #{item.targetId}, #{item.fieldName}, #{item.tagStatus}, #{item.operatorId},
            #{item.remark})
        </foreach>
        ON DUPLICATE KEY UPDATE
        tag_status = VALUES(tag_status),
        operator_id = VALUES(operator_id),
        remark = VALUES(remark),
        update_time = NOW()
    </insert>
</mapper>
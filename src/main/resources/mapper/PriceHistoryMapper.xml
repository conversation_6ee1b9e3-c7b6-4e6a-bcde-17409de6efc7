<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="ai.pricefox.mallfox.mapper.product.PriceHistoryMapper">

    <!-- 查询图表数据点 -->
    <select id="findDataPoints" resultType="ai.pricefox.mallfox.model.response.PriceHistoryResponseDTO$DataPoint">
        <choose>
            <!-- 如果查询全平台聚合数据 -->
            <when test="platforms != null and platforms.size() == 1 and platforms[0] == 'ALL'">
                SELECT
                ph.record_date AS date,
                ph.lowest_price AS lowestPrice,
                ph.average_price AS averagePrice,
                ph.offer_count AS offerCount
                FROM price_history ph
                WHERE ph.sku_code = #{skuCode}
                AND ph.record_date BETWEEN #{startDate} AND #{endDate}
                ORDER BY ph.record_date ASC
            </when>
            <!-- 如果查询特定平台数据 -->
            <otherwise>
                SELECT
                ph.record_date AS date,
                MIN(ph.lowest_price) AS lowestPrice,
                AVG(ph.average_price) AS averagePrice,
                SUM(ph.offer_count) AS offerCount
                FROM price_history ph
                INNER JOIN channel_offers co ON ph.sku_code = co.sku_code
                WHERE ph.sku_code = #{skuCode}
                AND ph.record_date BETWEEN #{startDate} AND #{endDate}
                <if test="platforms != null and platforms.size() > 0">
                    AND co.platform_code IN
                    <foreach item="item" collection="platforms" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                GROUP BY ph.record_date
                ORDER BY ph.record_date ASC
            </otherwise>
        </choose>
    </select>

    <!-- 查询周期内最低价 -->
    <select id="findLowestPriceInPeriod" resultType="ai.pricefox.mallfox.model.response.PriceHistoryResponseDTO$PricePoint">
        <choose>
            <!-- 如果查询全平台聚合数据 -->
            <when test="platforms != null and platforms.size() == 1 and platforms[0] == 'ALL'">
                SELECT
                ph.lowest_price AS price,
                ph.record_date AS sourceInfo
                FROM price_history ph
                WHERE ph.sku_code = #{skuCode}
                AND ph.record_date BETWEEN #{startDate} AND #{endDate}
                ORDER BY ph.lowest_price ASC, ph.record_date DESC
                LIMIT 1
            </when>
            <!-- 如果查询特定平台数据 -->
            <otherwise>
                SELECT
                ph.lowest_price AS price,
                CONCAT(co.platform_code, '_', ph.record_date) AS sourceInfo
                FROM price_history ph
                INNER JOIN channel_offers co ON ph.sku_code = co.sku_code
                WHERE ph.sku_code = #{skuCode}
                AND ph.record_date BETWEEN #{startDate} AND #{endDate}
                <if test="platforms != null and platforms.size() > 0">
                    AND co.platform_code IN
                    <foreach item="item" collection="platforms" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                ORDER BY ph.lowest_price ASC, ph.record_date DESC
                LIMIT 1
            </otherwise>
        </choose>
    </select>

    <!-- 查询最新价格 (这里简化为查询最近一天的历史记录) -->
    <select id="findLatestPrice" resultType="ai.pricefox.mallfox.model.response.PriceHistoryResponseDTO$PricePoint">
        <choose>
            <!-- 如果查询全平台聚合数据 -->
            <when test="platforms != null and platforms.size() == 1 and platforms[0] == 'ALL'">
                SELECT
                ph.lowest_price AS price,
                'ALL' AS sourceInfo
                FROM price_history ph
                WHERE ph.sku_code = #{skuCode}
                ORDER BY ph.record_date DESC
                LIMIT 1
            </when>
            <!-- 如果查询特定平台数据 -->
            <otherwise>
                SELECT
                ph.lowest_price AS price,
                co.platform_code AS sourceInfo
                FROM price_history ph
                INNER JOIN channel_offers co ON ph.sku_code = co.sku_code
                WHERE ph.sku_code = #{skuCode}
                <if test="platforms != null and platforms.size() > 0">
                    AND co.platform_code IN
                    <foreach item="item" collection="platforms" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                ORDER BY ph.record_date DESC
                LIMIT 1
            </otherwise>
        </choose>
    </select>

</mapper>
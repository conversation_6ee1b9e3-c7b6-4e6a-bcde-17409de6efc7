<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.pricefox.mallfox.mapper.product.ProductAttributeMapper">

    <resultMap id="BaseResultMap" type="ai.pricefox.mallfox.domain.product.ProductAttribute">
            <id property="id" column="id" />
            <result property="categoryId" column="category_id" />
            <result property="name" column="name" />
            <result property="inputType" column="input_type" />
            <result property="values" column="values" />
            <result property="sort" column="sort" />
            <result property="isFilter" column="is_filter" />
            <result property="isRequired" column="is_required" />
            <result property="createTime" column="create_time" />
            <result property="updateTime" column="update_time" />
    </resultMap>

    <sql id="Base_Column_List">
        id,category_id,name,input_type,values,sort,
        is_filter,is_required,create_time,update_time
    </sql>
</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.pricefox.mallfox.mapper.product.ProductAttributeValueMapper">

    <resultMap id="BaseResultMap" type="ai.pricefox.mallfox.domain.product.ProductAttributeValue">
            <id property="id" column="id" />
            <result property="productId" column="product_id" />
            <result property="attributeId" column="attribute_id" />
            <result property="value" column="value" />
            <result property="createTime" column="create_time" />
    </resultMap>

    <sql id="Base_Column_List">
        id,product_id,attribute_id,value,create_time
    </sql>
</mapper>

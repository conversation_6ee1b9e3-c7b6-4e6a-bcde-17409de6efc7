<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.pricefox.mallfox.mapper.product.ProductCategoryMapper">

    <resultMap id="BaseResultMap" type="ai.pricefox.mallfox.domain.product.ProductCategory">
            <id property="id" column="id" />
            <result property="parentId" column="parent_id" />
            <result property="name" column="name" />
            <result property="level" column="level" />
            <result property="sort" column="sort" />
            <result property="icon" column="icon" />
            <result property="isDisplay" column="is_display" />
            <result property="createTime" column="create_time" />
            <result property="updateTime" column="update_time" />
    </resultMap>

    <sql id="Base_Column_List">
        id,parent_id,name,level,sort,icon,
        is_display,create_time,update_time
    </sql>
</mapper>

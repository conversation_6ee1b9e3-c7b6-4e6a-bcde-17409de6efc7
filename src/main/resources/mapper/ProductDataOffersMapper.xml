<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.pricefox.mallfox.mapper.product.ProductDataOffersMapper">

    <resultMap id="BaseResultMap" type="ai.pricefox.mallfox.domain.product.ProductDataOffers">
            <id property="id" column="id" />
            <result property="sku_id" column="sku_id" />
            <result property="source_platform" column="source_platform" />
            <result property="platform_spu_id" column="platform_spu_id" />
            <result property="platform_sku_id" column="platform_sku_id" />
            <result property="item_url" column="item_url" />
            <result property="list_price" column="list_price" />
            <result property="price" column="price" />
            <result property="discount" column="discount" />
            <result property="inventory" column="inventory" />
            <result property="sales_last_30_days" column="sales_last_30_days" />
            <result property="seller" column="seller" />
            <result property="merchant_rating" column="merchant_rating" />
            <result property="brand" column="brand" />
            <result property="title" column="title" />
            <result property="series" column="series" />
            <result property="upc_code" column="upc_code" />
            <result property="service_provider" column="service_provider" />
            <result property="condition" column="condition" />
            <result property="category_level_1" column="category_level_1" />
            <result property="category_level_2" column="category_level_2" />
            <result property="category_level_3" column="category_level_3" />
            <result property="create_time" column="create_time" />
            <result property="update_time" column="update_time" />
    </resultMap>

    <sql id="Base_Column_List">
        id,sku_id,source_platform,platform_spu_id,platform_sku_id,item_url,
        list_price,price,discount,inventory,sales_last_30_days,
        seller,merchant_rating,brand,title,series,
        upc_code,service_provider,condition,category_level_1,category_level_2,
        category_level_3,create_time,update_time
    </sql>
</mapper>

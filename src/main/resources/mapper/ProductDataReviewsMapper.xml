<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.pricefox.mallfox.mapper.product.ProductDataReviewsMapper">

    <resultMap id="BaseResultMap" type="ai.pricefox.mallfox.domain.product.ProductDataReviews">
            <id property="id" column="id" />
            <result property="review_id" column="review_id" />
            <result property="create_time" column="create_time" />
            <result property="update_time" column="update_time" />
            <result property="sku_id" column="sku_id" />
            <result property="spu_id" column="spu_id" />
            <result property="source_platform" column="source_platform" />
            <result property="platform_spu_id" column="platform_spu_id" />
            <result property="platform_sku_id" column="platform_sku_id" />
            <result property="review_score" column="review_score" />
            <result property="review_user_name" column="review_user_name" />
            <result property="review_title" column="review_title" />
            <result property="review_content" column="review_content" />
            <result property="review_time" column="review_time" />
            <result property="is_helpful_or_not" column="is_helpful_or_not" />
            <result property="review_image_url" column="review_image_url" />
    </resultMap>

    <sql id="Base_Column_List">
        id,review_id,create_time,update_time,sku_id,spu_id,
        source_platform,platform_spu_id,platform_sku_id,review_score,review_user_name,
        review_title,review_content,review_time,is_helpful_or_not,review_image_url
    </sql>
</mapper>

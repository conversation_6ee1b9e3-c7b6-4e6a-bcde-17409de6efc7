<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.pricefox.mallfox.mapper.product.ProductDataSimplifyMapper">

    <resultMap id="BaseResultMap" type="ai.pricefox.mallfox.domain.product.ProductDataSimplify">
            <id property="id" column="id" />
            <result property="source_platform" column="source_platform" />
            <result property="spu_id" column="spu_id" />
            <result property="sku_id" column="sku_id" />
            <result property="platform_spu_id" column="platform_spu_id" />
            <result property="platform_sku_id" column="platform_sku_id" />
            <result property="model" column="model" />
            <result property="model_year" column="model_year" />
            <result property="color" column="color" />
            <result property="storage" column="storage" />
            <result property="product_main_image_urls" column="product_main_image_urls" />
            <result property="product_spec_color_url" column="product_spec_color_url" />
            <result property="ram_memory_installed_size" column="ram_memory_installed_size" />
            <result property="operating_system" column="operating_system" />
            <result property="processor" column="processor" />
            <result property="cellular_technology" column="cellular_technology" />
            <result property="screen_size" column="screen_size" />
            <result property="resolution" column="resolution" />
            <result property="refresh_rate" column="refresh_rate" />
            <result property="display_type" column="display_type" />
            <result property="battery_power" column="battery_power" />
            <result property="average_talk_time" column="average_talk_time" />
            <result property="battery_charge_time" column="battery_charge_time" />
            <result property="front_photo_sensor_resolution" column="front_photo_sensor_resolution" />
            <result property="rear_facing_camera_photo_sensor_resolution" column="rear_facing_camera_photo_sensor_resolution" />
            <result property="number_of_rear_facing_cameras" column="number_of_rear_facing_cameras" />
            <result property="effective_video_resolution" column="effective_video_resolution" />
            <result property="video_capture_resolution" column="video_capture_resolution" />
            <result property="sIm_card_slot_count" column="sIm_card_slot_count" />
            <result property="connector_type" column="connector_type" />
            <result property="water_resistance" column="water_resistance" />
            <result property="dimensions" column="dimensions" />
            <result property="item_weight" column="item_weight" />
            <result property="biometric_security_feature" column="biometric_security_feature" />
            <result property="supported_satellite_navigation_system" column="supported_satellite_navigation_system" />
            <result property="features" column="features" />
            <result property="return_policy" column="return_policy" />
            <result property="payment_installment" column="payment_installment" />
            <result property="install_payment" column="install_payment" />
            <result property="warranty_description" column="warranty_description" />
            <result property="review_number" column="review_number" />
            <result property="review_score" column="review_score" />
            <result property="review_rating_distribution" column="review_rating_distribution" />
            <result property="review_dimensional_ratings" column="review_dimensional_ratings" />
            <result property="review_overview_pros_cons" column="review_overview_pros_cons" />
            <result property="review_pros_cons_by_star" column="review_pros_cons_by_star" />
            <result property="create_time" column="create_time" />
            <result property="update_time" column="update_time" />
    </resultMap>

    <sql id="Base_Column_List">
        id,source_platform,spu_id,sku_id,platform_spu_id,platform_sku_id,
        model,model_back,model_year,color,storage,product_main_image_urls,
        product_spec_color_url,ram_memory_installed_size,operating_system,processor,cellular_technology,
        screen_size,resolution,refresh_rate,display_type,battery_power,
        average_talk_time,battery_charge_time,front_photo_sensor_resolution,rear_facing_camera_photo_sensor_resolution,number_of_rear_facing_cameras,
        effective_video_resolution,video_capture_resolution,sIm_card_slot_count,connector_type,water_resistance,
        dimensions,item_weight,biometric_security_feature,supported_satellite_navigation_system,features,
        return_policy,payment_installment,install_payment,warranty_description,review_number,
        review_score,review_rating_distribution,review_dimensional_ratings,review_overview_pros_cons,review_pros_cons_by_star,
        create_time,update_time
    </sql>
    <select id="pageSelectProductData" resultType="ai.pricefox.mallfox.domain.product.ProductDataSimplify">
        select
        s.id,s.source_platform,s.spu_id,s.sku_id,s.platform_spu_id,s.platform_sku_id,s.
        model,s.model_year,s.color,s.storage,s.product_main_image_urls,s.
        product_spec_color_url,s.ram_memory_installed_size,s.operating_system,s.processor,s.cellular_technology,s.
        screen_size,s.resolution,s.refresh_rate,s.display_type,s.battery_power,s.
        average_talk_time,s.battery_charge_time,s.front_photo_sensor_resolution,s.rear_facing_camera_photo_sensor_resolution,s.number_of_rear_facing_cameras,s.
        effective_video_resolution,s.video_capture_resolution,s.sIm_card_slot_count,s.connector_type,s.water_resistance,s.
        dimensions,s.item_weight,s.biometric_security_feature,s.supported_satellite_navigation_system,s.features,s.
        return_policy,s.payment_installment,s.install_payment,s.warranty_description,s.review_number,s.
        review_score,s.review_rating_distribution,s.review_dimensional_ratings,s.review_overview_pros_cons,s.review_pros_cons_by_star,s.
        create_time,s.update_time

        ,o.sku_id,o.source_platform,o.platform_spu_id,o.platform_sku_id,o.item_url,o.
        list_price,o.price,o.discount,o.inventory,o.sales_last_30_days,o.
        seller,o.merchant_rating,o.brand,o.title,o.series,o.
        upc_code,o.service_provider,o.condition,o.category_level_1,o.category_level_2,o.
        category_level_3
        from product_data_offers o left join product_data_simplify s on o.platform_sku_id=s.platform_sku_id
        <where>
            <if test="dataSimplify.spuId != null">
                and s.spu_id=#{dataSimplify.spuId}
            </if>
        </where>

    </select>

    <!-- 根据数据渠道查询不重复的model列表
         dataChannel: DataChannelEnum.CRAWLER(1)=爬虫, DataChannelEnum.API(2)=API -->
    <select id="selectDistinctModelsByDataChannel" resultType="java.lang.String">
        SELECT DISTINCT model
        FROM product_data_simplify
        WHERE data_channel = #{dataChannel}
        AND model IS NOT NULL
        AND model != ''
        ORDER BY model
    </select>

    <!-- 分页查询需要处理型号的商品数据（关联品牌信息） -->
    <select id="selectProductModelProcessData" resultType="ai.pricefox.mallfox.model.dto.ProductModelProcessDTO">
        SELECT
            s.id,
            s.sku_id as skuId,
            s.model as originalModel,
            o.brand,
            s.model as processedModel,
            'PENDING' as status
        FROM product_data_simplify s
        LEFT JOIN product_data_offers o ON s.sku_id = o.sku_id
        WHERE s.model IS NOT NULL
        AND s.model != ''
        AND (s.model_back IS NULL or s.model_back = '')
        <if test="sourcePlatform != null and sourcePlatform != ''">
            AND s.source_platform = #{sourcePlatform}
        </if>
        <if test="onlyWithBrand != null and onlyWithBrand == true">
            AND o.brand IS NOT NULL
            AND o.brand != ''
        </if>
        ORDER BY s.id ASC
    </select>

    <!-- 分页查询需要合并的商品型号数据 -->
    <select id="selectProductModelMergeData" resultType="ai.pricefox.mallfox.model.dto.ProductModelMergeDTO">
        SELECT
            s.id,
            s.sku_id as skuId,
            s.spu_id as spuId,
            s.source_platform as sourcePlatform,
            s.model,
            REPLACE(LOWER(s.model), ' ', '') as normalizedModel,
            CASE WHEN LOWER(s.source_platform) = 'amazon' THEN 1 ELSE 0 END as isAmazon
        FROM product_data_simplify s
        WHERE s.model IS NOT NULL
        AND s.model != ''
        <if test="sourcePlatform != null and sourcePlatform != ''">
            AND s.source_platform = #{sourcePlatform}
        </if>
        <if test="targetModel != null and targetModel != ''">
            AND REPLACE(LOWER(s.model), ' ', '') = REPLACE(LOWER(#{targetModel}), ' ', '')
        </if>
        ORDER BY s.model ASC, s.source_platform ASC, s.id ASC
    </select>

    <!-- 分页查询需要合并的商品SKU数据 -->
    <select id="selectProductSkuMergeData" resultType="ai.pricefox.mallfox.model.dto.ProductSkuMergeDTO">
        SELECT
            s.id,
            s.sku_id as skuId,
            s.spu_id as spuId,
            s.source_platform as sourcePlatform,
            s.color,
            s.storage,
            REPLACE(LOWER(s.storage), ' ', '') as normalizedStorage,
            s.condition_new as conditionNew,
            s.service_provider as serviceProvider,
            CASE WHEN LOWER(s.source_platform) = 'amazon' THEN 1 ELSE 0 END as isAmazon,
            CONCAT(s.spu_id, '|', LOWER(s.color), '|', REPLACE(LOWER(s.storage), ' ', ''), '|', LOWER(s.condition_new), '|', LOWER(s.service_provider)) as skuGroupKey
        FROM product_data_simplify s
        WHERE s.spu_id IS NOT NULL
        AND s.spu_id != ''
        AND s.color IS NOT NULL
        AND s.color != ''
        AND s.storage IS NOT NULL
        AND s.storage != ''
        AND s.condition_new IS NOT NULL
        AND s.condition_new != ''
        AND s.service_provider IS NOT NULL
        AND s.service_provider != ''
        <if test="sourcePlatform != null and sourcePlatform != ''">
            AND s.source_platform = #{sourcePlatform}
        </if>
        <if test="targetSpuId != null and targetSpuId != ''">
            AND s.spu_id = #{targetSpuId}
        </if>
        ORDER BY s.spu_id ASC, s.color ASC, s.storage ASC, s.condition_new ASC, s.service_provider ASC, s.source_platform ASC, s.id ASC
    </select>

    <!-- 查询所有不重复的SPU ID列表 -->
    <select id="selectDistinctSpuIds" resultType="java.lang.String">
        SELECT DISTINCT spu_id
        FROM product_data_simplify
        WHERE spu_id IS NOT NULL
        AND spu_id != ''
        AND color IS NOT NULL
        AND color != ''
        AND storage IS NOT NULL
        AND storage != ''
        AND condition_new IS NOT NULL
        AND condition_new != ''
        AND service_provider IS NOT NULL
        AND service_provider != ''
        <if test="sourcePlatform != null and sourcePlatform != ''">
            AND source_platform = #{sourcePlatform}
        </if>
        ORDER BY spu_id ASC
    </select>

    <!-- 根据SPU ID查询UPC信息 -->
    <select id="selectUpcInfoBySpuIds" resultType="ai.pricefox.mallfox.model.dto.ProductModelUpcDTO">
        SELECT DISTINCT
            s.spu_id as spuId,
            s.model,
            REPLACE(LOWER(s.model), ' ', '') as normalizedModel,
            o.upc_code as upcCode,
            s.source_platform as sourcePlatform
        FROM product_data_simplify s
        LEFT JOIN product_data_offers o ON s.spu_id = o.spu_id
        WHERE s.spu_id IN
        <foreach collection="spuIds" item="spuId" open="(" separator="," close=")">
            #{spuId}
        </foreach>
        AND s.model IS NOT NULL
        AND s.model != ''
        AND o.upc_code IS NOT NULL
        AND o.upc_code != ''
        ORDER BY s.spu_id ASC, s.model ASC
    </select>

    <!-- 根据标准化型号查询SPU记录（忽略大小写和空格） -->
    <select id="selectByNormalizedModel" resultType="ai.pricefox.mallfox.domain.product.ProductDataSimplify">
        SELECT <include refid="Base_Column_List"/>
        FROM product_data_simplify
        WHERE REPLACE(LOWER(model), ' ', '') = #{normalizedModel}
        AND spu_id IS NOT NULL
        AND spu_id != ''
        ORDER BY id ASC
    </select>

    <!-- 根据标准化字段查询SKU记录（忽略大小写和空格） -->
    <select id="selectByNormalizedFields" resultType="ai.pricefox.mallfox.domain.product.ProductDataSimplify">
        SELECT <include refid="Base_Column_List"/>
        FROM product_data_simplify
        WHERE REPLACE(LOWER(model), ' ', '') = #{normalizedModel}
        AND REPLACE(LOWER(color), ' ', '') = #{normalizedColor}
        AND REPLACE(LOWER(storage), ' ', '') = #{normalizedStorage}
        AND REPLACE(LOWER(service_provider), ' ', '') = #{normalizedServiceProvider}
        AND REPLACE(LOWER(condition_new), ' ', '') = #{normalizedCondition}
        ORDER BY id ASC
        LIMIT 1
    </select>

</mapper>

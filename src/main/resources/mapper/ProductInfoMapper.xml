<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.pricefox.mallfox.mapper.product.ProductInfoMapper">

    <select id="findProductCardList" resultType="ai.pricefox.mallfox.model.response.ProductCardDTO">
        SELECT
        sp.master_img_url AS imageUrl,
        pv.sku_code AS skuCode,
        pv.title AS title,
        pv.lowest_price AS price,
        pv.highest_price AS listPrice,
        pv.review_rating AS rating,
        pv.installment_info AS installmentInfo
        FROM
        product_variants pv
        JOIN
        standard_product sp ON pv.spu_code = sp.spu_code
        <if test="brandName != null and brandName != ''">
            JOIN standard_brand sb ON sp.brand_code = sb.brand_code
        </if>
        <where>
           sp.published = 1
            <if test="brandName != null and brandName != ''">
                AND sb.name = #{brandName}
            </if>
            <if test="keyword != null and keyword != ''">
                AND pv.title LIKE CONCAT('%', #{keyword}, '%')
            </if>
        </where>
        ORDER BY ${orderByClause}
        LIMIT #{limit}
    </select>

</mapper>
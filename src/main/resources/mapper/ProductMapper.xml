<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.pricefox.mallfox.mapper.product.ProductMapper">

    <resultMap id="BaseResultMap" type="ai.pricefox.mallfox.domain.product.Product">
            <id property="id" column="id" />
            <result property="categoryId" column="category_id" />
            <result property="brandId" column="brand_id" />
            <result property="name" column="name" />
            <result property="subTitle" column="sub_title" />
            <result property="mainImage" column="main_image" />
            <result property="subImages" column="sub_images" />
            <result property="description" column="description" />
            <result property="price" column="price" />
            <result property="status" column="status" />
            <result property="sales" column="sales" />
            <result property="reviewCount" column="review_count" />
            <result property="createTime" column="create_time" />
            <result property="updateTime" column="update_time" />
    </resultMap>

    <sql id="Base_Column_List">
        id,category_id,brand_id,name,sub_title,main_image,
        sub_images,description,price,status,sales,
        review_count,create_time,update_time
    </sql>
</mapper>

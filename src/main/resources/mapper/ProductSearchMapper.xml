<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.pricefox.mallfox.mapper.product.ProductSearchMapper">

    <select id="searchProducts" resultType="ai.pricefox.mallfox.model.response.ProductSearchItemDTO">
        SELECT
        sp.spu_code as spuCode,
        pv.sku_code as skuCode,
        pv.title as title,
        sb.brand_name_en as brand,
        psm.model,
        sp.master_img_url as mainImageUrl,
        pv.lowest_price as price,
        pv.highest_price as listPrice,
        CASE
        WHEN pv.highest_price > pv.lowest_price
        THEN ROUND((pv.highest_price - pv.lowest_price) * 100 / NULLIF(pv.highest_price, 0), 2)
        ELSE 0
        END as discount,
        pv.review_rating as rating,
        pv.total_reviews as reviewCount,
        -- 从 option_json 中提取销售属性
        JSON_UNQUOTE(JSON_EXTRACT(pv.option_json, '$.color')) as color,
        JSON_UNQUOTE(JSON_EXTRACT(pv.option_json, '$.storage')) as storage,
        -- 从 SPU 规格表中提取规格
        psm.screen_size_inch as screenSize,
        psm.ram_size_gb as ramMemory,
        psm.battery_capacity_mah as batteryCapacity,
        psm.operating_system_family as operatingSystem,
        psm.processor_name as processor,
        CASE WHEN pv.inventory_quantity > 0 THEN 'In Stock' ELSE 'Out of Stock' END as inventory,
        pv.sales_last_30_days as salesLast30Days,
        pv.installment_info as installmentInfo
        FROM
        standard_product sp
        INNER JOIN
        product_variants pv ON sp.spu_code = pv.spu_code
        LEFT JOIN
        product_spec_mobile psm ON sp.spu_code = psm.spu_code
        LEFT JOIN
        standard_brand sb ON sp.brand_code = sb.brand_code
        <where>
            sp.published = 1
            <!-- 关键词搜索 -->
            <if test="request.keyword != null and request.keyword != ''">
                AND (
                sp.title LIKE CONCAT('%', #{request.keyword}, '%')
                OR sb.brand_name_en LIKE CONCAT('%', #{request.keyword}, '%')
                OR psm.model LIKE CONCAT('%', #{request.keyword}, '%')
                )
            </if>
            <!-- 价格筛选 -->
            <if test="request.minPrice != null">
                AND pv.lowest_price >= #{request.minPrice}
            </if>
            <if test="request.maxPrice != null">
                <![CDATA[AND pv.lowest_price <= #{request.maxPrice}]]>
            </if>
            <!-- 评分筛选 -->
            <if test="request.minRating != null">
                AND pv.review_rating >= #{request.minRating}
            </if>
            <!-- 品牌筛选 -->
            <if test="request.brandCodes != null and !request.brandCodes.isEmpty()">
                AND sp.brand_code IN
                <foreach collection="request.brandCodes" item="brandCode" open="(" separator="," close=")">
                    #{brandCode}
                </foreach>
            </if>

            <!-- === SPU 规格筛选 (查询 product_spec_mobile 表) === -->
            <if test="request.minScreenSize != null">
                <![CDATA[AND psm.screen_size_inch >= #{request.minScreenSize}]]>
            </if>
            <if test="request.maxScreenSize != null">
                <![CDATA[AND psm.screen_size_inch <= #{request.maxScreenSize}]]>
            </if>
            <if test="request.minBatteryCapacity != null">
                <![CDATA[AND psm.battery_capacity_mah >= #{request.minBatteryCapacity}]]>
            </if>
            <if test="request.maxBatteryCapacity != null">
                <![CDATA[AND psm.battery_capacity_mah <= #{request.maxBatteryCapacity}]]>
            </if>
            <if test="request.ramSizes != null and !request.ramSizes.isEmpty()">
                AND psm.ram_size_gb IN
                <foreach collection="request.ramSizes" item="ramSize" open="(" separator="," close=")">
                    #{ramSize}
                </foreach>
            </if>
            <if test="request.modelYears != null and !request.modelYears.isEmpty()">
                AND psm.model_year IN
                <foreach collection="request.modelYears" item="year" open="(" separator="," close=")">
                    #{year}
                </foreach>
            </if>

            <!-- === SKU 销售属性筛选 (查询 product_variants.option_json) === -->
            <if test="request.colors != null and !request.colors.isEmpty()">
                AND JSON_UNQUOTE(JSON_EXTRACT(pv.option_json, '$.color')) IN
                <foreach collection="request.colors" item="color" open="(" separator="," close=")">
                    #{color}
                </foreach>
            </if>
            <if test="request.storages != null and !request.storages.isEmpty()">
                AND JSON_UNQUOTE(JSON_EXTRACT(pv.option_json, '$.storage')) IN
                <foreach collection="request.storages" item="storage" open="(" separator="," close=")">
                    #{storage}
                </foreach>
            </if>
        </where>

        <!-- 动态排序 -->
        <choose>
            <when test="request.sortBy == 'price_asc'">ORDER BY pv.lowest_price ASC</when>
            <when test="request.sortBy == 'price_desc'">ORDER BY pv.lowest_price DESC</when>
            <when test="request.sortBy == 'rating_desc'">ORDER BY pv.review_rating DESC, pv.total_reviews DESC</when>
            <when test="request.sortBy == 'newest'">ORDER BY pv.create_date DESC</when>
            <otherwise>
                <!-- 默认相关性排序 -->
                <if test="request.keyword != null and request.keyword != ''">
                    ORDER BY
                    CASE WHEN sp.title LIKE CONCAT(#{request.keyword}, '%') THEN 0 ELSE 1 END, -- 关键词在开头的优先
                    CASE WHEN sp.title LIKE CONCAT('%', #{request.keyword}, '%') THEN 0 ELSE 1 END,
                    pv.sales_last_30_days DESC,
                    pv.review_rating DESC
                </if>
                <if test="request.keyword == null or request.keyword == ''">
                    ORDER BY pv.sales_last_30_days DESC, pv.review_rating DESC
                </if>
            </otherwise>
        </choose>
    </select>

    <!-- 获取可用品牌选项 -->
    <select id="getAvailableBrands" resultType="ai.pricefox.mallfox.model.response.FilterOption">
        SELECT
            sb.id as value,
            sb.name as label,
            COUNT(DISTINCT sp.spu_code) as count
        FROM standard_product sp
        INNER JOIN product_variants pv ON sp.spu_code = pv.spu_code
        LEFT JOIN product_spec_mobile psm ON sp.spu_code = psm.spu_code
        LEFT JOIN standard_brand sb ON sp.brand_code = sb.brand_code
        <where>
            sp.status = 1 AND pv.status = 1 AND sb.name IS NOT NULL AND sb.name != ''
            <if test="request.keyword != null and request.keyword != ''">
                AND (
                    sp.name LIKE CONCAT('%', #{request.keyword}, '%')
                    OR sb.name LIKE CONCAT('%', #{request.keyword}, '%')
                    OR psm.model LIKE CONCAT('%', #{request.keyword}, '%')
                )
            </if>
        </where>
        GROUP BY sb.id, sb.name
        HAVING count > 0
        ORDER BY count DESC, bi.name ASC
    </select>

    <!-- 获取价格统计信息 -->
    <select id="getPriceStatistics" resultType="map">
        SELECT
            MIN(pv.lowest_price) as minPrice,
            MAX(pv.highest_price) as maxPrice,
            AVG(pv.lowest_price) as avgPrice,
            COUNT(DISTINCT sp.spu_code) as productCount
        FROM standard_product sp
        INNER JOIN product_variants pv ON sp.spu_code = pv.spu_code
        LEFT JOIN product_spec_mobile psm ON sp.spu_code = psm.spu_code
        LEFT JOIN standard_brand sb ON sp.brand_code = sb.brand_code
        <where>
            sp.status = 1 AND pv.status = 1 AND pv.lowest_price IS NOT NULL AND pv.lowest_price > 0
            <if test="request.keyword != null and request.keyword != ''">
                AND (
                    sp.name LIKE CONCAT('%', #{request.keyword}, '%')
                    OR sb.name LIKE CONCAT('%', #{request.keyword}, '%')
                    OR psm.model LIKE CONCAT('%', #{request.keyword}, '%')
                )
            </if>
        </where>
    </select>

    <!-- 获取评分分布 -->
    <select id="getRatingDistribution" resultType="ai.pricefox.mallfox.model.response.FilterOption">
        SELECT
            CONCAT(FLOOR(pv.average_rating), ' Stars &amp; Up') as label,
            CONCAT(FLOOR(pv.average_rating)) as value,
            COUNT(DISTINCT sp.spu_code) as count
        FROM standard_product sp
        INNER JOIN product_variants pv ON sp.spu_code = pv.spu_code
        LEFT JOIN product_spec_mobile psm ON sp.spu_code = psm.spu_code
        LEFT JOIN standard_brand sb ON sp.brand_code = sb.brand_code
        <where>
            sp.status = 1 AND pv.status = 1 AND pv.average_rating IS NOT NULL AND pv.average_rating > 0
            <if test="request.keyword != null and request.keyword != ''">
                AND (
                    sp.name LIKE CONCAT('%', #{request.keyword}, '%')
                    OR sb.name LIKE CONCAT('%', #{request.keyword}, '%')
                    OR psm.model LIKE CONCAT('%', #{request.keyword}, '%')
                )
            </if>
        </where>
        GROUP BY FLOOR(pv.review_rating)
        HAVING count > 0
        ORDER BY FLOOR(pv.review_rating) DESC
    </select>

    <!-- 获取可用存储容量选项 -->
    <select id="getAvailableStorageOptions" resultType="ai.pricefox.mallfox.model.response.FilterOption">
        SELECT
            JSON_UNQUOTE(JSON_EXTRACT(pv.attributes, '$.storage')) as value,
            JSON_UNQUOTE(JSON_EXTRACT(pv.attributes, '$.storage')) as label,
            COUNT(DISTINCT sp.spu_code) as count
        FROM standard_product sp
        INNER JOIN product_variants pv ON sp.spu_code = pv.spu_code
        LEFT JOIN product_spec_mobile psm ON sp.spu_code = psm.spu_code
        LEFT JOIN standard_brand sb ON sp.brand_code = sb.brand_code
        <where>
            sp.status = 1 AND pv.status = 1
            AND JSON_UNQUOTE(JSON_EXTRACT(pv.attributes, '$.storage')) IS NOT NULL
            AND JSON_UNQUOTE(JSON_EXTRACT(pv.attributes, '$.storage')) != ''
            <if test="request.keyword != null and request.keyword != ''">
                AND (
                    sp.name LIKE CONCAT('%', #{request.keyword}, '%')
                    OR sb.name LIKE CONCAT('%', #{request.keyword}, '%')
                    OR psm.model LIKE CONCAT('%', #{request.keyword}, '%')
                )
            </if>
        </where>
        GROUP BY JSON_UNQUOTE(JSON_EXTRACT(pv.attributes, '$.storage'))
        HAVING count > 0
        ORDER BY
            CASE
                WHEN JSON_UNQUOTE(JSON_EXTRACT(pv.attributes, '$.storage')) REGEXP '^[0-9]+'
                THEN CAST(SUBSTRING_INDEX(JSON_UNQUOTE(JSON_EXTRACT(pv.attributes, '$.storage')), 'GB', 1) AS UNSIGNED)
                ELSE 999999
            END ASC
    </select>

    <!-- 获取可用颜色选项 -->
    <select id="getAvailableColorOptions" resultType="ai.pricefox.mallfox.model.response.FilterOption">
        SELECT
            JSON_UNQUOTE(JSON_EXTRACT(pv.attributes, '$.color')) as value,
            JSON_UNQUOTE(JSON_EXTRACT(pv.attributes, '$.color')) as label,
            COUNT(DISTINCT sp.spu_code) as count
        FROM standard_product sp
        INNER JOIN product_variants pv ON sp.spu_code = pv.spu_code
        LEFT JOIN product_spec_mobile psm ON sp.spu_code = psm.spu_code
        LEFT JOIN standard_brand sb ON sp.brand_code = sb.brand_code
        <where>
            sp.status = 1 AND pv.status = 1
            AND JSON_UNQUOTE(JSON_EXTRACT(pv.attributes, '$.color')) IS NOT NULL
            AND JSON_UNQUOTE(JSON_EXTRACT(pv.attributes, '$.color')) != ''
            <if test="request.keyword != null and request.keyword != ''">
                AND (
                    sp.name LIKE CONCAT('%', #{request.keyword}, '%')
                    OR sb.name LIKE CONCAT('%', #{request.keyword}, '%')
                    OR psm.model LIKE CONCAT('%', #{request.keyword}, '%')
                )
            </if>
        </where>
        GROUP BY JSON_UNQUOTE(JSON_EXTRACT(pv.attributes, '$.color'))
        HAVING count > 0
        ORDER BY count DESC, JSON_UNQUOTE(JSON_EXTRACT(pv.attributes, '$.color')) ASC
    </select>

    <!-- 获取可用屏幕尺寸选项 -->
    <select id="getAvailableScreenSizeOptions" resultType="ai.pricefox.mallfox.model.response.FilterOption">
        SELECT
            CONCAT(psm.screen_size_inch, ' inches') as value,
            CONCAT(psm.screen_size_inch, ' inches') as label,
            COUNT(DISTINCT sp.spu_code) as count
        FROM standard_product sp
        INNER JOIN product_variants pv ON sp.spu_code = pv.spu_code
        LEFT JOIN product_spec_mobile psm ON sp.spu_code = psm.spu_code
        LEFT JOIN standard_brand sb ON sp.brand_code = sb.brand_code
        <where>
            sp.status = 1 AND pv.status = 1 AND psm.screen_size_inch IS NOT NULL
            <if test="request.keyword != null and request.keyword != ''">
                AND (
                    sp.name LIKE CONCAT('%', #{request.keyword}, '%')
                    OR sb.name LIKE CONCAT('%', #{request.keyword}, '%')
                    OR psm.model LIKE CONCAT('%', #{request.keyword}, '%')
                )
            </if>
        </where>
        GROUP BY psm.screen_size_inch
        HAVING count > 0
        ORDER BY psm.screen_size_inch ASC
    </select>

    <!-- 获取可用RAM选项 -->
    <select id="getAvailableRamOptions" resultType="ai.pricefox.mallfox.model.response.FilterOption">
        SELECT
            CONCAT(psm.ram_size_gb, 'GB') as value,
            CONCAT(psm.ram_size_gb, 'GB') as label,
            COUNT(DISTINCT sp.spu_code) as count
        FROM standard_product sp
        INNER JOIN product_variants pv ON sp.spu_code = pv.spu_code
        LEFT JOIN product_spec_mobile psm ON sp.spu_code = psm.spu_code
        LEFT JOIN standard_brand sb ON sp.brand_code = sb.brand_code
        <where>
            sp.status = 1 AND pv.status = 1 AND psm.ram_size_gb IS NOT NULL
            <if test="request.keyword != null and request.keyword != ''">
                AND (
                    sp.name LIKE CONCAT('%', #{request.keyword}, '%')
                    OR sb.name LIKE CONCAT('%', #{request.keyword}, '%')
                    OR psm.model LIKE CONCAT('%', #{request.keyword}, '%')
                )
            </if>
        </where>
        GROUP BY psm.ram_size_gb
        HAVING count > 0
        ORDER BY psm.ram_size_gb ASC
    </select>

    <!-- 获取可用电池容量选项 -->
    <select id="getAvailableBatteryOptions" resultType="ai.pricefox.mallfox.model.response.FilterOption">
        SELECT
            CONCAT(psm.battery_capacity_mah, ' mAh') as value,
            CONCAT(psm.battery_capacity_mah, ' mAh') as label,
            COUNT(DISTINCT sp.spu_code) as count
        FROM standard_product sp
        INNER JOIN product_variants pv ON sp.spu_code = pv.spu_code
        LEFT JOIN product_spec_mobile psm ON sp.spu_code = psm.spu_code
        LEFT JOIN standard_brand sb ON sp.brand_code = sb.brand_code
        <where>
            sp.status = 1 AND pv.status = 1 AND psm.battery_capacity_mah IS NOT NULL
            <if test="request.keyword != null and request.keyword != ''">
                AND (
                    sp.name LIKE CONCAT('%', #{request.keyword}, '%')
                    OR sb.name LIKE CONCAT('%', #{request.keyword}, '%')
                    OR psm.model LIKE CONCAT('%', #{request.keyword}, '%')
                )
            </if>
        </where>
        GROUP BY psm.battery_capacity_mah
        HAVING count > 0
        ORDER BY psm.battery_capacity_mah ASC
    </select>

</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.pricefox.mallfox.mapper.product.ProductSkuMapper">

    <resultMap id="BaseResultMap" type="ai.pricefox.mallfox.domain.product.ProductSku">
            <id property="id" column="id" />
            <result property="productId" column="product_id" />
            <result property="skuCode" column="sku_code" />
            <result property="specs" column="specs" />
            <result property="price" column="price" />
            <result property="costPrice" column="cost_price" />
            <result property="originalPrice" column="original_price" />
            <result property="stock" column="stock" />
            <result property="lowStock" column="low_stock" />
            <result property="image" column="image" />
            <result property="weight" column="weight" />
            <result property="volume" column="volume" />
            <result property="status" column="status" />
            <result property="createTime" column="create_time" />
            <result property="updateTime" column="update_time" />
    </resultMap>

    <sql id="Base_Column_List">
        id,product_id,sku_code,specs,price,cost_price,
        original_price,stock,low_stock,image,weight,
        volume,status,create_time,update_time
    </sql>
</mapper>

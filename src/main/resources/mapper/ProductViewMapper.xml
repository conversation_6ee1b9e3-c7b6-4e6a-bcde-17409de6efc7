<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.pricefox.mallfox.mapper.product.ProductViewMapper">

    <!-- 公共查询字段 -->
    <sql id="productDataFields">
    o.id as id,
    o.id AS offerId,
    o.spu_id,
    o.source_platform,
    o.platform_spu_id,
    o.platform_sku_id,
    o.item_url,
    o.price,
    o.list_price,
    o.inventory,
    o.inventory_update_time,
    o.seller,
    o.merchant_rating,
    o.brand,
    o.title,
    o.series,
    o.service_provider,
    o.condition_new,
    o.category_level1,
    o.category_level2,
    o.category_level3,
    o.sales_last30_days,
    o.upc_code,
    o.discount,
    o.update_time AS offerUpdateTime,
    s.spu_id,
    s.sku_id,
    s.model,
    s.model_year,
    s.color,
    s.storage,
    s.product_main_image_urls,
    s.product_spec_color_url,
    s.ram_memory_installed_size,
    s.operating_system,
    s.processor,
    s.cellular_technology,
    s.screen_size,
    s.resolution,
    s.refresh_rate,
    s.display_type,
    s.battery_power,
    s.average_talk_time,
    s.battery_charge_time,
    s.front_photo_sensor_resolution,
    s.rear_facing_camera_photo_sensor_resolution,
    s.number_of_rear_facing_cameras,
    s.effective_video_resolution,
    s.video_capture_resolution,
    s.sim_card_slot_count,
    s.connector_type,
    s.water_resistance,
    s.dimensions,
    s.item_weight,
    s.biometric_security_feature,
    s.supported_satellite_navigation_system,
    s.features,
    s.return_policy,
    s.payment_installment,
    s.install_payment,
    s.warranty_description,
    s.review_number,
    s.review_score,
    s.review_rating_distribution,
    s.review_dimensional_ratings,
    s.review_overview_pros_cons,
    s.review_pros_cons_by_star,
    s.create_time AS simplifyCreateTime,
    s.mark,
    s.shipping_time,
    o.price_update_time,
    o.data_channel
    </sql>

    <!-- 公共查询字段 -->
    <sql id="productSimplifyDataFields">
        s.spu_id,
        MIN(s.id) AS id,
        MIN(o.id) AS offerId,
        MIN(s.id) AS simplifyId,
        MIN(s.sku_id) AS skuId,
        MIN(s.model) AS model,
        MIN(s.model_year) AS modelYear,
        MIN(s.color) AS color,
        MIN(s.storage) AS storage,
        MIN(s.service_provider) AS serviceProvider,
        MIN(s.condition_new) AS conditionNew,
        MIN(o.series) AS series,
        MIN(o.upc_code) AS upcCode,
        MIN(s.source_platform) AS sourcePlatform,
        MIN(s.platform_spu_id) AS platformSpuId,
        MIN(s.platform_sku_id) AS platformSkuId,
        MIN(o.item_url) AS itemUrl,
        MIN(o.title) AS title,
        MIN(o.brand) AS brand,
        MIN(o.price) AS price,
        MIN(o.list_price) AS listPrice,
        MIN(o.discount) AS discount,
        MIN(o.inventory) AS inventory,
        MIN(o.inventory_update_time) AS inventoryUpdateTime,
        MIN(o.sales_last30_days) AS salesLast30Days,
        MIN(o.seller) AS seller,
        MIN(o.merchant_rating) AS merchantRating,
        MIN(s.product_main_image_urls) AS productMainImageUrls,
        MIN(s.product_spec_color_url) AS productSpecColorUrl,
        MIN(s.ram_memory_installed_size) AS ramMemoryInstalledSize,
        MIN(s.operating_system) AS operatingSystem,
        MIN(s.processor) AS processor,
        MIN(s.cellular_technology) AS cellularTechnology,
        MIN(s.screen_size) AS screenSize,
        MIN(s.resolution) AS resolution,
        MIN(s.refresh_rate) AS refreshRate,
        MIN(s.display_type) AS displayType,
        MIN(s.battery_power) AS batteryPower,
        MIN(s.average_talk_time) AS averageTalkTime,
        MIN(s.battery_charge_time) AS batteryChargeTime,
        MIN(s.front_photo_sensor_resolution) AS frontPhotoSensorResolution,
        MIN(s.rear_facing_camera_photo_sensor_resolution) AS rearFacingCameraPhotoSensorResolution,
        MIN(s.number_of_rear_facing_cameras) AS numberOfRearFacingCameras,
        MIN(s.effective_video_resolution) AS effectiveVideoResolution,
        MIN(s.video_capture_resolution) AS videoCaptureResolution,
        MIN(s.sim_card_slot_count) AS simCardSlotCount,
        MIN(s.connector_type) AS connectorType,
        MIN(s.water_resistance) AS waterResistance,
        MIN(s.dimensions) AS dimensions,
        MIN(s.item_weight) AS itemWeight,
        MIN(s.biometric_security_feature) AS biometricSecurityFeature,
        MIN(s.supported_satellite_navigation_system) AS supportedSatelliteNavigationSystem,
        MIN(s.features) AS features,
        MIN(s.return_policy) AS returnPolicy,
        MIN(s.payment_installment) AS paymentInstallment,
        MIN(s.install_payment) AS installPayment,
        MIN(s.warranty_description) AS warrantyDescription,
        MIN(s.review_number) AS reviewNumber,
        MIN(s.review_score) AS reviewScore,
        MIN(s.review_rating_distribution) AS reviewRatingDistribution,
        MIN(s.review_dimensional_ratings) AS reviewDimensionalRatings,
        MIN(s.review_overview_pros_cons) AS reviewOverviewProsCons,
        MIN(s.review_pros_cons_by_star) AS reviewProsConsByStar,
        MIN(o.category_level1) AS categoryLevel1,
        MIN(o.category_level2) AS categoryLevel2,
        MIN(o.category_level3) AS categoryLevel3,
        MIN(s.mark) AS mark,
        MIN(o.update_time) AS offerUpdateTime,
        MIN(s.create_time) AS simplifyCreateTime,
        MIN(s.shipping_time) AS shippingTime,
        MIN(o.price_update_time) AS priceUpdateTime
    </sql>

    <!-- 公共查询字段 -->
    <sql id="productOfferDataFields">
        o.id as id,
        o.id AS offerId,
        o.spu_id,
        o.source_platform,
        o.platform_spu_id,
        o.platform_sku_id,
        o.item_url,
        o.price,
        o.list_price,
        o.inventory,
        o.inventory_update_time,
        o.seller,
        o.merchant_rating,
        o.brand,
        o.title,
        o.series,
        o.service_provider,
        o.condition_new,
        o.category_level1,
        o.category_level2,
        o.category_level3,
        o.sales_last30_days,
        o.upc_code,
        o.discount,
        o.update_time AS offerUpdateTime,
        s.id as simplify_id,
        s.spu_id,
        s.sku_id,
        s.model,
        s.model_year,
        s.color,
        s.storage,
        s.product_main_image_urls,
        s.product_spec_color_url,
        s.ram_memory_installed_size,
        s.operating_system,
        s.processor,
        s.cellular_technology,
        s.screen_size,
        s.resolution,
        s.refresh_rate,
        s.display_type,
        s.battery_power,
        s.average_talk_time,
        s.battery_charge_time,
        s.front_photo_sensor_resolution,
        s.rear_facing_camera_photo_sensor_resolution,
        s.number_of_rear_facing_cameras,
        s.effective_video_resolution,
        s.video_capture_resolution,
        s.sim_card_slot_count,
        s.connector_type,
        s.water_resistance,
        s.dimensions,
        s.item_weight,
        s.biometric_security_feature,
        s.supported_satellite_navigation_system,
        s.features,
        s.return_policy,
        s.payment_installment,
        s.install_payment,
        s.warranty_description,
        s.review_number,
        s.review_score,
        s.review_rating_distribution,
        s.review_dimensional_ratings,
        s.review_overview_pros_cons,
        s.review_pros_cons_by_star,
        s.create_time AS simplifyCreateTime,
        s.mark,
        s.shipping_time,
        o.price_update_time,
        o.data_channel
    </sql>


    <select id="selectAmazonMasterSkus" resultType="ai.pricefox.mallfox.model.response.SkuMasterViewDTO">
        SELECT
        <include refid="productDataFields"/>
        FROM
        product_data_offers o
        LEFT JOIN
        product_data_simplify s ON o.sku_id = s.sku_id
        <where>
            o.source_platform = 'Amazon'
            <if test="request.brand != null and request.brand != ''">
                AND o.brand = #{request.brand}
            </if>
            <if test="request.model != null and request.model != ''">
                AND s.model LIKE CONCAT('%', #{request.model}, '%')
            </if>
            <if test="request.series != null and request.series != ''">
                AND o.series = #{request.series}
            </if>
            <if test="request.spuId != null and request.spuId != ''">
                AND s.spu_id = #{request.spuId}
            </if>
        </where>
        ORDER BY s.spu_id ASC, s.sku_id ASC, o.price ASC
    </select>



    <!-- 分页查询 product_data_simplify 表中的 SPU 列表 -->
    <select id="selectSpuList" resultType="ai.pricefox.mallfox.model.response.SpuGroupViewResponse">
        SELECT
            <include refid="productSimplifyDataFields"/>
        FROM product_data_simplify s
        LEFT JOIN product_data_offers o ON s.sku_id = o.sku_id
        <where>
            <if test="request.brand != null and request.brand != ''">
                AND o.brand = #{request.brand}
            </if>
            <if test="request.model != null and request.model != ''">
                AND s.model LIKE CONCAT('%', #{request.model}, '%')
            </if>
            <if test="request.series != null and request.series != ''">
                AND o.series = #{request.series}
            </if>
            <if test="request.spuId != null and request.spuId != ''">
                AND s.spu_id = #{request.spuId}
            </if>
            <if test="request.skuId != null and request.skuId != ''">
                AND s.sku_id = #{request.skuId}
            </if>
            <if test="request.sourcePlatform != null and request.sourcePlatform != ''">
                AND s.source_platform = #{request.sourcePlatform}
            </if>
            <if test="request.title != null and request.title != ''">
                AND o.title LIKE CONCAT('%', #{request.title}, '%')
            </if>
            <if test="request.color != null and request.color != ''">
                AND s.color = #{request.color}
            </if>
            <if test="request.storage != null and request.storage != ''">
                AND REPLACE(LOWER(s.storage), ' ', '')   LIKE CONCAT('%', #{request.storage}, '%')
            </if>
            <if test="request.conditionNew != null and request.conditionNew != ''">
                AND s.condition_new = #{request.conditionNew}
            </if>
            <if test="request.upcCode != null and request.upcCode != ''">
                AND o.upc_code = #{request.upcCode}
            </if>
            <if test="request.serviceProvider != null and request.serviceProvider != ''">
                AND  REPLACE(LOWER(s.service_provider), ' ', '')  like CONCAT('%', #{request.serviceProvider}, '%')
            </if>
        </where>
        GROUP BY s.spu_id
        <choose>
            <when test="request.sortField != null and request.sortField != ''">
                ORDER BY ${request.dbSortFieldForGroupBy} ${request.dbSortOrder}
            </when>
            <otherwise>
                ORDER BY s.spu_id ASC
            </otherwise>
        </choose>
    </select>

    <!-- 根据 SPU ID 列表查询对应的 SKU 列表 -->
    <select id="selectSkuListBySpuIds" resultType="ai.pricefox.mallfox.model.response.SkuMasterViewDTO">
        SELECT
        <include refid="productDataFields"/>
        FROM
        product_data_offers o
        LEFT JOIN
        product_data_simplify s ON o.sku_id = s.sku_id
        WHERE s.spu_id IN
        <foreach collection="spuIds" item="spuId" open="(" separator="," close=")">
            #{spuId}
        </foreach>
        ORDER BY s.spu_id ASC, s.sku_id ASC, o.price ASC
    </select>

    <!-- 分页查询指定 SPU ID 下的 SKU 列表 -->
    <select id="selectSkuListBySpuIdWithPage" resultType="ai.pricefox.mallfox.model.response.SkuMasterViewDTO">
        SELECT
        <include refid="productDataFields"/>,
        s.id as simplifyId
        FROM
        product_data_offers o
        LEFT JOIN
        product_data_simplify s ON o.sku_id = s.sku_id AND o.platform_sku_id = s.platform_sku_id
        WHERE s.spu_id = #{spuId}
        AND o.id = (
            SELECT o2.id
            FROM product_data_offers o2
            WHERE o2.sku_id = o.sku_id
            ORDER BY o2.price ASC, o2.id ASC
            LIMIT 1
        )
        ORDER BY s.sku_id ASC, o.platform_sku_id ASC
    </select>

    <select id="selectAllPlatformOffers" resultType="ai.pricefox.mallfox.model.response.ProductDataViewResponse">
        SELECT
            <include refid="productOfferDataFields"/>
        FROM
        product_data_offers o
        INNER JOIN
        product_data_simplify s ON o.sku_id = s.sku_id AND o.platform_sku_id = s.platform_sku_id
        WHERE o.sku_id = #{skuId}
        ORDER BY o.price ASC
    </select>
    <select id="selectAllPlatformOffersBySkuIds" resultType="ai.pricefox.mallfox.model.response.ProductDataViewResponse">
        SELECT
        <include refid="productOfferDataFields"/>
        FROM
            product_data_offers o
                INNER JOIN
            product_data_simplify s ON o.sku_id = s.sku_id
        WHERE
            s.sku_id IN
            <foreach collection="skuIds" item="skuId" open="(" separator="," close=")">
                #{skuId}
            </foreach>
    </select>

</mapper>
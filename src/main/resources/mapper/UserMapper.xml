<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.pricefox.mallfox.mapper.auth.UserMapper">

    <resultMap id="BaseResultMap" type="ai.pricefox.mallfox.domain.auth.User">
            <id property="id" column="id" />
            <result property="username" column="username" />
            <result property="password" column="password" />
            <result property="phone" column="phone" />
            <result property="email" column="email" />
            <result property="avatar" column="avatar" />
            <result property="gender" column="gender" />
            <result property="birthday" column="birthday" />
            <result property="status" column="status" />
            <result property="lastLoginTime" column="last_login_time" />
            <result property="createTime" column="create_time" />
            <result property="updateTime" column="update_time" />
    </resultMap>

    <sql id="Base_Column_List">
        id,username,password,phone,email,avatar,
        gender,birthday,status,last_login_time,create_time,
        update_time
    </sql>
</mapper>

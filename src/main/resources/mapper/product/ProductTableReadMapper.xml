<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.pricefox.mallfox.mapper.product.ProductTableReadMapper">

    <insert id="batchInsertOrUpdate" parameterType="java.util.List">
        INSERT INTO product_table_read (target_table, target_id, is_read, create_time)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.targetTable}, #{item.targetId}, #{item.isRead}, #{item.createTime})
        </foreach>
        ON DUPLICATE KEY UPDATE
        is_read = VALUES(is_read),
        create_time = VALUES(create_time)
    </insert>

    <select id="selectByTargetTableAndIds" resultType="ai.pricefox.mallfox.domain.product.ProductTableRead">
        SELECT * FROM product_table_read
        WHERE target_table = #{targetTable}
        AND target_id IN
        <foreach collection="targetIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND is_read = 1
    </select>

</mapper>
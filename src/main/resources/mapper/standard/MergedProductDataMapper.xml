<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.pricefox.mallfox.mapper.standard.MergedProductDataMapper">

    <resultMap id="BaseResultMap" type="ai.pricefox.mallfox.domain.standard.MergedProductData">
        <id property="id" column="id" />
        <result property="createDate" column="create_date" />
        <result property="createUsername" column="create_username"/>
        <result property="updateDate" column="update_date"/>
        <result property="updateUsername" column="update_username"/>
         <result property="skuId" column="sku_id" />
        <result property="platformCode" column="platform_code" />
        <result property="mergedData" column="merged_data" />
        <result property="dataId" column="data_id" />
        <result property="mergeStatus" column="merge_status" />
    </resultMap>

    <sql id="Base_Column_List">
        id,create_date,create_username,update_date,update_username,sku_id,platform_code,merged_data,data_id,merge_status
    </sql>
</mapper>

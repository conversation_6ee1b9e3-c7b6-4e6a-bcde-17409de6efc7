<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.pricefox.mallfox.mapper.standard.NormalizationAliasMapper">

    <resultMap id="BaseResultMap" type="ai.pricefox.mallfox.domain.standard.NormalizationAlias">
            <id property="id" column="id" />
            <result property="entityType" column="entity_type" />
            <result property="aliasName" column="alias_name" />
            <result property="standardValue" column="standard_value" />
            <result property="sourcePlatform" column="source_platform" />
    </resultMap>

    <sql id="Base_Column_List">
        id,entity_type,alias_name,standard_value,source_platform
    </sql>
</mapper>

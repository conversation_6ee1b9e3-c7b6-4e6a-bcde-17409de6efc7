<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.pricefox.mallfox.mapper.standard.NormalizationLibraryMapper">

    <resultMap id="BaseResultMap" type="ai.pricefox.mallfox.domain.standard.NormalizationLibrary">
            <id property="id" column="id" />
            <result property="createDate" column="create_date" />
            <result property="updateDate" column="update_date" />
            <result property="createUsername" column="create_username" />
            <result property="updateUsername" column="update_username" />
            <result property="normCode" column="norm_code" />
            <result property="valueType" column="value_type" />
            <result property="originalValue" column="original_value" />
            <result property="normalizedValue" column="normalized_value" />
            <result property="equivalentValues" column="equivalent_values" />
    </resultMap>

    <sql id="Base_Column_List">
        id,create_date,update_date,create_username,update_username,norm_code,
        value_type,original_value,normalized_value,equivalent_values
    </sql>
</mapper>

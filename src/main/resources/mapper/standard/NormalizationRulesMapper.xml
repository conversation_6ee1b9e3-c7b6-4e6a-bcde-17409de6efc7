<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.pricefox.mallfox.mapper.standard.NormalizationRulesMapper">

    <resultMap id="BaseResultMap" type="ai.pricefox.mallfox.domain.standard.NormalizationRules">
            <id property="id" column="id" />
            <result property="createDate" column="create_date" />
            <result property="updateDate" column="update_date" />
            <result property="createUsername" column="create_username" />
            <result property="updateUsername" column="update_username" />
            <result property="pattern" column="pattern" />
            <result property="replacement" column="replacement" />
            <result property="applyTo" column="apply_to" />
    </resultMap>

    <sql id="Base_Column_List">
        id,create_date,update_date,create_username,update_username,pattern,
        replacement,apply_to
    </sql>
</mapper>

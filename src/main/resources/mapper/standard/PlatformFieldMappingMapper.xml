<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.pricefox.mallfox.mapper.standard.PlatformFieldMappingMapper">

    <resultMap id="BaseResultMap" type="ai.pricefox.mallfox.domain.standard.PlatformFieldMapping">
            <id property="id" column="id" />
            <result property="createDate" column="create_date" />
            <result property="updateDate" column="update_date" />
            <result property="createUsername" column="create_username" />
            <result property="updateUsername" column="update_username" />
            <result property="platformCode" column="platform_code" />
            <result property="sourceFieldName" column="source_field_name" />
            <result property="standardFieldCode" column="standard_field_code" />
            <result property="transformLogic" column="transform_logic" />
            <result property="positionInfo" column="position_info" />
            <result property="customScript" column="custom_script" />
    </resultMap>

    <sql id="Base_Column_List">
        id,create_date,update_date,create_username,update_username,platform_code,
        source_field_name,standard_field_code,transform_logic,position_info,custom_script
    </sql>
</mapper>

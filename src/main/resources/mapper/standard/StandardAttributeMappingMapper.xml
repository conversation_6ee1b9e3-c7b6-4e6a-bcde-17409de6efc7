<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.pricefox.mallfox.mapper.standard.StandardAttributeMappingMapper">

    <!-- 属性映射分页查询结果映射 -->
    <resultMap id="AttributeMappingResponseMap" type="ai.pricefox.mallfox.model.response.AttributeMappingResponse">
        <id column="id" property="id"/>
        <result column="attribute_code" property="attributeCode"/>
        <result column="attribute_name_cn" property="attributeNameCn"/>
        <result column="attribute_name_en" property="attributeNameEn"/>
        <result column="mapping_names" property="mappingNames"/>
        <result column="category_name" property="categoryName"/>
        <result column="category_name_cn" property="categoryNameCn"/>
        <result column="create_date" property="createDate"/>
        <result column="update_date" property="updateDate"/>
        <result column="create_username" property="createUsername"/>
        <result column="update_username" property="updateUsername"/>
    </resultMap>

    <!-- 属性映射详情查询结果映射 -->
    <resultMap id="AttributeMappingDetailResponseMap"
               type="ai.pricefox.mallfox.model.response.AttributeMappingDetailResponse">
        <id column="id" property="id"/>
        <result column="attribute_code" property="attributeCode"/>
        <result column="attribute_name_cn" property="attributeNameCn"/>
        <result column="attribute_name_en" property="attributeNameEn"/>
        <result column="category_code" property="categoryCode"/>
        <result column="category_name" property="categoryName"/>
        <result column="category_name_cn" property="categoryNameCn"/>
        <result column="create_date" property="createDate"/>
        <result column="update_date" property="updateDate"/>
        <result column="create_username" property="createUsername"/>
        <result column="update_username" property="updateUsername"/>
        <collection property="mappingInfos"
                    ofType="ai.pricefox.mallfox.model.response.AttributeMappingDetailResponse$MappingInfo">
            <id column="mapping_id" property="id"/>
            <result column="mapping_name" property="mappingName"/>
            <result column="mapping_create_date" property="createDate"/>
        </collection>
    </resultMap>

    <!-- 分页查询属性映射列表 -->
    <select id="selectAttributeMappingPage" resultMap="AttributeMappingResponseMap">
        SELECT
        sa.id,
        sa.attribute_code,
        sa.attribute_name_cn,
        sa.attribute_name_en,
        GROUP_CONCAT(sam.mapping_name SEPARATOR '/') as mapping_names,
        sa.standard_category_code as category_code ,
        sc.category_name_en as category_name,
        sc.category_name_cn,
        sa.create_date,
        sa.update_date,
        sa.create_username,
        sa.update_username
        FROM standard_attribute sa
        LEFT JOIN standard_attribute_mapping sam ON sa.attribute_code = sam.attribute_code AND sam.deleted = 0
        LEFT JOIN standard_category sc ON sa.standard_category_code = sc.category_code AND sc.deleted = 0
        WHERE sa.deleted = 0
        <if test="request.attributeCode != null and request.attributeCode != ''">
            AND sa.attribute_code = #{request.attributeCode}
        </if>
        <if test="request.attributeName != null and request.attributeName != ''">
            AND sa.attribute_name_en LIKE CONCAT('%', #{request.attributeName}, '%')
        </if>
        <if test="request.attributeNameCn != null and request.attributeNameCn != ''">
            AND sa.attribute_name_cn LIKE CONCAT('%', #{request.attributeNameCn}, '%')
        </if>
        <if test="request.categoryNames != null and request.categoryNames.size() > 0">
            AND sc.category_name_en IN
            <foreach collection="request.categoryNames" item="categoryName" open="(" separator="," close=")">
                #{categoryName}
            </foreach>
        </if>
        <if test="request.categoryNameCn != null and request.categoryNameCn != ''">
            AND sc.category_name_cn LIKE CONCAT('%', #{request.categoryNameCn}, '%')
        </if>
        GROUP BY sa.id, sa.attribute_code, sa.attribute_name_cn, sa.attribute_name_en,
        sc.category_name_en, sc.category_name_cn, sa.create_date, sa.update_date,
        sa.create_username, sa.update_username
        ORDER BY sa.create_date DESC
    </select>

    <!-- 查询属性映射列表（不分页，用于导出） -->
    <select id="selectAttributeMappingList" resultMap="AttributeMappingResponseMap">
        SELECT
        sa.id,
        sa.attribute_code,
        sa.attribute_name_cn,
        sa.attribute_name_en,
        GROUP_CONCAT(sam.mapping_name SEPARATOR '、') as mapping_names,
        sc.category_name_en as category_name,
        sc.category_name_cn,
        sa.create_date,
        sa.update_date,
        sa.create_username,
        sa.update_username
        FROM standard_attribute sa
        LEFT JOIN standard_attribute_mapping sam ON sa.attribute_code = sam.attribute_code AND sam.deleted = 0
        LEFT JOIN standard_category sc ON sa.standard_category_code = sc.category_code AND sc.deleted = 0
        WHERE sa.deleted = 0
        <if test="request.attributeCode != null and request.attributeCode != ''">
            AND sa.attribute_code = #{request.attributeCode}
        </if>
        <if test="request.attributeName != null and request.attributeName != ''">
            AND sa.attribute_name_en LIKE CONCAT('%', #{request.attributeName}, '%')
        </if>
        <if test="request.attributeNameCn != null and request.attributeNameCn != ''">
            AND sa.attribute_name_cn LIKE CONCAT('%', #{request.attributeNameCn}, '%')
        </if>
        <if test="request.categoryNames != null and request.categoryNames.size() > 0">
            AND sc.category_name_en IN
            <foreach collection="request.categoryNames" item="categoryName" open="(" separator="," close=")">
                #{categoryName}
            </foreach>
        </if>
        <if test="request.categoryNameCn != null and request.categoryNameCn != ''">
            AND sc.category_name_cn LIKE CONCAT('%', #{request.categoryNameCn}, '%')
        </if>
        GROUP BY sa.id, sa.attribute_code, sa.attribute_name_cn, sa.attribute_name_en,
        sc.category_name_en, sc.category_name_cn, sa.create_date, sa.update_date,
        sa.create_username, sa.update_username
        ORDER BY sa.create_date DESC
    </select>

    <!-- 根据ID查询属性映射详情 -->
    <select id="selectAttributeMappingDetailById" resultMap="AttributeMappingDetailResponseMap">
        SELECT sa.id,
               sa.attribute_code,
               sa.attribute_name_cn,
               sa.attribute_name_en,
               sc.category_code,
               sc.category_name_en as category_name,
               sc.category_name_cn,
               sa.create_date,
               sa.update_date,
               sa.create_username,
               sa.update_username,
               sam.id              as mapping_id,
               sam.mapping_name,
               sam.create_date     as mapping_create_date
        FROM standard_attribute sa
                 LEFT JOIN standard_category sc ON sa.standard_category_code = sc.category_code AND sc.deleted = 0
                 LEFT JOIN standard_attribute_mapping sam ON sa.attribute_code = sam.attribute_code AND sam.deleted = 0
        WHERE sa.id = #{id}
          AND sa.deleted = 0
        ORDER BY sam.create_date DESC
    </select>

    <!-- 其他原有方法保持不变 -->
    <resultMap id="BaseResultMap" type="ai.pricefox.mallfox.domain.standard.StandardAttributeMapping">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate"/>
        <result column="update_date" jdbcType="TIMESTAMP" property="updateDate"/>
        <result column="create_username" jdbcType="VARCHAR" property="createUsername"/>
        <result column="update_username" jdbcType="VARCHAR" property="updateUsername"/>
        <result column="attribute_code" jdbcType="VARCHAR" property="attributeCode"/>
        <result column="mapping_name" jdbcType="VARCHAR" property="mappingName"/>
        <result column="deleted" jdbcType="BIT" property="deleted"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        , create_date, update_date, create_username, update_username, attribute_code, mapping_name, deleted
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from standard_attribute_mapping
        where id = #{id,jdbcType=BIGINT}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from standard_attribute_mapping
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="ai.pricefox.mallfox.domain.standard.StandardAttributeMapping">
        insert into standard_attribute_mapping (id, create_date, update_date,
                                                create_username, update_username, attribute_code,
                                                mapping_name, deleted)
        values (#{id,jdbcType=BIGINT}, #{createDate,jdbcType=TIMESTAMP}, #{updateDate,jdbcType=TIMESTAMP},
                #{createUsername,jdbcType=VARCHAR}, #{updateUsername,jdbcType=VARCHAR},
                #{attributeCode,jdbcType=VARCHAR},
                #{mappingName,jdbcType=VARCHAR}, #{deleted,jdbcType=BIT})
    </insert>

    <insert id="insertSelective" parameterType="ai.pricefox.mallfox.domain.standard.StandardAttributeMapping">
        insert into standard_attribute_mapping
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="createDate != null">
                create_date,
            </if>
            <if test="updateDate != null">
                update_date,
            </if>
            <if test="createUsername != null">
                create_username,
            </if>
            <if test="updateUsername != null">
                update_username,
            </if>
            <if test="attributeCode != null">
                attribute_code,
            </if>
            <if test="mappingName != null">
                mapping_name,
            </if>
            <if test="deleted != null">
                deleted,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="createDate != null">
                #{createDate,jdbcType=TIMESTAMP},
            </if>
            <if test="updateDate != null">
                #{updateDate,jdbcType=TIMESTAMP},
            </if>
            <if test="createUsername != null">
                #{createUsername,jdbcType=VARCHAR},
            </if>
            <if test="updateUsername != null">
                #{updateUsername,jdbcType=VARCHAR},
            </if>
            <if test="attributeCode != null">
                #{attributeCode,jdbcType=VARCHAR},
            </if>
            <if test="mappingName != null">
                #{mappingName,jdbcType=VARCHAR},
            </if>
            <if test="deleted != null">
                #{deleted,jdbcType=BIT},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective"
            parameterType="ai.pricefox.mallfox.domain.standard.StandardAttributeMapping">
        update standard_attribute_mapping
        <set>
            <if test="createDate != null">
                create_date = #{createDate,jdbcType=TIMESTAMP},
            </if>
            <if test="updateDate != null">
                update_date = #{updateDate,jdbcType=TIMESTAMP},
            </if>
            <if test="createUsername != null">
                create_username = #{createUsername,jdbcType=VARCHAR},
            </if>
            <if test="updateUsername != null">
                update_username = #{updateUsername,jdbcType=VARCHAR},
            </if>
            <if test="attributeCode != null">
                attribute_code = #{attributeCode,jdbcType=VARCHAR},
            </if>
            <if test="mappingName != null">
                mapping_name = #{mappingName,jdbcType=VARCHAR},
            </if>
            <if test="deleted != null">
                deleted = #{deleted,jdbcType=BIT},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateByPrimaryKey" parameterType="ai.pricefox.mallfox.domain.standard.StandardAttributeMapping">
        update standard_attribute_mapping
        set create_date     = #{createDate,jdbcType=TIMESTAMP},
            update_date     = #{updateDate,jdbcType=TIMESTAMP},
            create_username = #{createUsername,jdbcType=VARCHAR},
            update_username = #{updateUsername,jdbcType=VARCHAR},
            attribute_code  = #{attributeCode,jdbcType=VARCHAR},
            mapping_name    = #{mappingName,jdbcType=VARCHAR},
            deleted         = #{deleted,jdbcType=BIT}
        where id = #{id,jdbcType=BIGINT}
    </update>
</mapper>
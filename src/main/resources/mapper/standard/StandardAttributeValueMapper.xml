<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.pricefox.mallfox.mapper.standard.StandardAttributeValueMapper">

    <resultMap id="BaseResultMap" type="ai.pricefox.mallfox.domain.standard.StandardAttributeValue">
            <id property="id" column="id" />
            <result property="createDate" column="create_date" />
            <result property="updateDate" column="update_date" />
            <result property="createUsername" column="create_username" />
            <result property="updateUsername" column="update_username" />
            <result property="attributeCode" column="attribute_code" />
            <result property="valueCode" column="value_code" />
            <result property="valueCn" column="value_cn" />
            <result property="valueEn" column="value_en" />
            <result property="deleted" column="deleted" />
    </resultMap>

    <sql id="Base_Column_List">
        id,create_date,update_date,create_username,update_username,attribute_code,
        value_code,value_cn,value_en,deleted
    </sql>

    <!-- 检查属性值映射关系 -->
    <select id="hasValueMapping" resultType="boolean">
        SELECT CASE WHEN COUNT(*) > 0 THEN 1 ELSE 0 END
        FROM standard_attribute_value_mapping
        WHERE value_code = #{valueCode} AND deleted = 0
    </select>
</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.pricefox.mallfox.mapper.standard.StandardAttributeValueMappingMapper">

    <resultMap id="BaseResultMap" type="ai.pricefox.mallfox.domain.standard.StandardAttributeValueMapping">
        <id property="id" column="id"/>
        <result property="createDate" column="create_date"/>
        <result property="updateDate" column="update_date"/>
        <result property="createUsername" column="create_username"/>
        <result property="updateUsername" column="update_username"/>
        <result property="attributeCode" column="attribute_code"/>
        <result property="valueCode" column="value_code"/>
        <result property="valueName" column="value_name"/>
        <result property="deleted" column="deleted"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,create_date,update_date,create_username,update_username,attribute_code,
        value_code,value_name,deleted
    </sql>
    <insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="ai.pricefox.mallfox.domain.standard.StandardAttributeValueMapping"></insert>

    <!-- 根据属性映射ID获取页面基础信息 -->
    <select id="selectPageInfoByAttributeId"
            resultType="ai.pricefox.mallfox.model.response.AttributeValueMappingPageInfo">
        SELECT sa.attribute_name_en as attributeName,
               sa.attribute_name_cn as attributeNameCn,
               sa.attribute_code    as attributeCode,
               sc.category_name_en  as categoryName,
               sc.category_name_cn  as categoryNameCn,
               sc.category_code     as categoryCode
        FROM standard_attribute_mapping sam
                 LEFT JOIN standard_attribute sa ON sam.attribute_code = sa.attribute_code AND sa.deleted = false
                 LEFT JOIN standard_category sc ON sa.standard_category_code = sc.category_code AND sc.deleted = false
        WHERE sam.id = #{attributeId}
          AND sam.deleted = false
    </select>

    <!-- 分页查询属性值映射列表 -->
    <select id="selectAttributeValueMappingPage"
            resultType="ai.pricefox.mallfox.model.response.AttributeValueMappingResponse">
        SELECT
        sav.value_name_en as valueName,
        sav.value_name_cn as valueNameCn,
        sav.value_code as valueCode,
        GROUP_CONCAT(savm.value_name SEPARATOR '、') as mappingValueNames
        FROM standard_attribute_value sav
        LEFT JOIN standard_attribute_value_mapping savm ON sav.value_code = savm.value_code AND savm.deleted = false
        LEFT JOIN standard_attribute sa ON sav.attribute_code = sa.attribute_code AND sa.deleted = false
        LEFT JOIN standard_attribute_mapping sam ON sa.attribute_code = sam.attribute_code AND sam.deleted = false
        WHERE sam.id = #{request.attributeId}
        AND sav.deleted = false
        <if test="request.valueCode != null and request.valueCode != ''">
            AND sav.value_code = #{request.valueCode}
        </if>
        <if test="request.valueName != null and request.valueName != ''">
            AND sav.value_name_en LIKE CONCAT('%', #{request.valueName}, '%')
        </if>
        <if test="request.valueNameCn != null and request.valueNameCn != ''">
            AND sav.value_name_cn LIKE CONCAT('%', #{request.valueNameCn}, '%')
        </if>
        GROUP BY sav.id, sav.value_name_en, sav.value_name_cn, sav.value_code
        ORDER BY sav.create_date DESC
    </select>

    <!-- 获取属性值选项 -->
    <select id="selectAttributeValueOptions" resultType="ai.pricefox.mallfox.model.response.AttributeValueOption">
        SELECT
        sav.value_code as valueCode,
        sav.value_name_en as valueName,
        sav.value_name_cn as valueNameCn,
        CONCAT(sav.value_code, '｜', sav.value_name_en) as displayText
        FROM standard_attribute_value sav
        WHERE sav.attribute_code = #{attributeCode}
        AND sav.deleted = false
        <if test="valueName != null and valueName != ''">
            AND (sav.value_name_en LIKE CONCAT('%', #{valueName}, '%')
            OR sav.value_code = #{valueName})
        </if>
        ORDER BY sav.create_date DESC
    </select>

    <!-- 获取属性值映射详情 -->
    <select id="selectAttributeValueMappingDetail"
            resultType="ai.pricefox.mallfox.model.response.AttributeValueMappingDetailResponse">
        SELECT sav.value_name_en    as valueName,
               sav.value_name_cn    as valueNameCn,
               sav.value_code       as valueCode,
               sa.attribute_name_en as attributeName,
               sa.attribute_name_cn as attributeNameCn,
               sa.attribute_code    as attributeCode,
               sc.category_name_en  as categoryName,
               sc.category_name_cn  as categoryNameCn,
               sc.category_code     as categoryCode
        FROM standard_attribute_value sav
                 LEFT JOIN standard_attribute sa ON sav.attribute_code = sa.attribute_code AND sa.deleted = false
                 LEFT JOIN standard_category sc ON sa.standard_category_code = sc.category_code AND sc.deleted = false
        WHERE sav.value_code = #{valueCode}
          AND sav.deleted = false
    </select>

    <!-- 根据属性值编码删除映射 -->
    <delete id="deleteByValueCode">
        DELETE
        FROM standard_attribute_value_mapping
        WHERE value_code = #{valueCode}
    </delete>

    <!-- 查询导出数据 -->
    <select id="selectExportData" resultType="ai.pricefox.mallfox.model.excel.AttributeValueMappingExportExcelEntity">
        SELECT
        sc.category_code as categoryCode,
        sc.category_name_en as categoryName,
        sc.category_name_cn as categoryNameCn,
        sc.category_path as categoryPath,
        sa.attribute_code as attributeCode,
        sa.attribute_name_en as attributeName,
        sa.attribute_name_cn as attributeNameCn,
        sav.value_code as valueCode,
        sav.value_name_en as valueName,
        sav.value_name_cn as valueNameCn,
        savm.value_name as mappingValueName
        FROM standard_attribute_value sav
        LEFT JOIN standard_attribute sa ON sav.attribute_code = sa.attribute_code AND sa.deleted = false
        LEFT JOIN standard_category sc ON sa.standard_category_code = sc.category_code AND sc.deleted = false
        LEFT JOIN standard_attribute_mapping sam ON sa.attribute_code = sam.attribute_code AND sam.deleted = false
        LEFT JOIN standard_attribute_value_mapping savm ON sav.value_code = savm.value_code AND savm.deleted = false
        WHERE sam.id = #{request.attributeId}
        AND sav.deleted = false
        <if test="request.valueCode != null and request.valueCode != ''">
            AND sav.value_code = #{request.valueCode}
        </if>
        <if test="request.valueName != null and request.valueName != ''">
            AND sav.value_name_en LIKE CONCAT('%', #{request.valueName}, '%')
        </if>
        <if test="request.valueNameCn != null and request.valueNameCn != ''">
            AND sav.value_name_cn LIKE CONCAT('%', #{request.valueNameCn}, '%')
        </if>
        ORDER BY sav.create_date DESC
    </select>

</mapper>
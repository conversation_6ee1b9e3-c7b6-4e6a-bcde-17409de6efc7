<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.pricefox.mallfox.mapper.standard.StandardBrandMapper">

    <resultMap id="BaseResultMap" type="ai.pricefox.mallfox.domain.standard.StandardBrand">
            <id property="id" column="id" />
            <result property="createDate" column="create_date" />
            <result property="updateDate" column="update_date" />
            <result property="createUsername" column="create_username" />
            <result property="updateUsername" column="update_username" />
            <result property="brandCode" column="brand_code" />
            <result property="brandNameEn" column="brand_name_en" />
            <result property="brandNameCn" column="brand_name_cn" />
            <result property="logoUrl" column="logo_url" />
            <result property="website" column="website" />
            <result property="sort" column="sort" />
            <result property="isActive" column="is_active" />
    </resultMap>

    <sql id="Base_Column_List">
        id,create_date,update_date,create_username,update_username,brand_code,
        brand_name_en,brand_name_cn,logo_url,website,sort,
        is_active
    </sql>
</mapper>

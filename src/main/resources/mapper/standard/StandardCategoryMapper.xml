<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.pricefox.mallfox.mapper.standard.StandardCategoryMapper">

    <resultMap id="BaseResultMap" type="ai.pricefox.mallfox.domain.standard.StandardCategory">
            <id property="id" column="id" />
            <result property="createDate" column="create_date" />
            <result property="updateDate" column="update_date" />
            <result property="createUsername" column="create_username" />
            <result property="updateUsername" column="update_username" />
            <result property="categoryCode" column="category_code" />
            <result property="categoryNameCn" column="category_name_cn" />
            <result property="categoryNameEn" column="category_name_en" />
            <result property="level" column="level" />
            <result property="iconUrl" column="icon_url" />
            <result property="sort" column="sort" />
            <result property="isActive" column="is_active" />
            <result property="parent" column="parent" />
    </resultMap>

    <sql id="Base_Column_List">
        id,create_date,update_date,create_username,update_username,category_code,
        category_name_cn,category_name_en,level,icon_url,sort,
        is_active,parent
    </sql>
</mapper>

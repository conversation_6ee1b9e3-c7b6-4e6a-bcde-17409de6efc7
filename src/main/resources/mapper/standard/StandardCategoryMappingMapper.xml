<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.pricefox.mallfox.mapper.standard.StandardCategoryMappingMapper">

    <resultMap id="BaseResultMap" type="ai.pricefox.mallfox.domain.standard.StandardCategoryMapping">
            <id property="id" column="id" />
            <result property="createDate" column="create_date" />
            <result property="updateDate" column="update_date" />
            <result property="createUsername" column="create_username" />
            <result property="updateUsername" column="update_username" />
            <result property="platformCode" column="platform_code" />
            <result property="standardCategoryCode" column="standard_category_code" />
            <result property="standardCategoryName" column="standard_category_name" />
            <result property="standardLevel" column="standard_level" />
            <result property="platformCategoryName" column="platform_category_name" />
            <result property="platformLevel" column="platform_level" />
    </resultMap>

    <sql id="Base_Column_List">
        id,create_date,update_date,create_username,update_username,platform_code,
        standard_category_code,standard_category_name,standard_level,platform_category_name,platform_level
    </sql>

    <!-- 查询类目映射数据用于导出 -->
    <select id="selectCategoryMappingForExport" resultType="ai.pricefox.mallfox.model.response.CategoryMappingResponse">
        SELECT 
            sc.category_code,
            sc.category_name_en as category_name,
            sc.category_name_cn,
            sc.level,
            GROUP_CONCAT(
                CONCAT(scm.platform_code, ':', scm.platform_category_name) 
                ORDER BY scm.platform_code SEPARATOR '|'
            ) as platform_mappings
        FROM standard_category sc
        LEFT JOIN standard_category_mapping scm ON sc.category_code = scm.standard_category_code 
            AND scm.deleted = 0
        WHERE sc.deleted = 0
        <if test="categoryCode != null and categoryCode != ''">
            AND sc.category_code = #{categoryCode}
        </if>
        <if test="categoryName != null and categoryName != ''">
            AND sc.category_name_en LIKE CONCAT('%', #{categoryName}, '%')
        </if>
        <if test="categoryNameCn != null and categoryNameCn != ''">
            AND sc.category_name_cn LIKE CONCAT('%', #{categoryNameCn}, '%')
        </if>
        GROUP BY sc.id, sc.category_code, sc.category_name_en, sc.category_name_cn, sc.level
        ORDER BY sc.level ASC, sc.category_name_en ASC
    </select>

</mapper>

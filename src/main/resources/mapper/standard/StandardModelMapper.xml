<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.pricefox.mallfox.mapper.standard.StandardModelMapper">

    <resultMap id="BaseResultMap" type="ai.pricefox.mallfox.domain.standard.StandardModel">
            <id property="id" column="id" />
            <result property="createDate" column="create_date" />
            <result property="updateDate" column="update_date" />
            <result property="createUsername" column="create_username" />
            <result property="updateUsername" column="update_username" />
            <result property="modelCode" column="model_code" />
            <result property="modelName" column="model_name" />
            <result property="modelSpuCount" column="model_spu_count" />
            <result property="salesCount" column="sales_count" />
            <result property="commentCount" column="comment_count" />
            <result property="upcCode" column="upc_code" />
    </resultMap>

    <sql id="Base_Column_List">
        id,create_date,update_date,create_username,update_username,model_code,
        model_name,model_spu_count,sales_count,comment_count,upc_code
    </sql>
</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.pricefox.mallfox.mapper.standard.StandardProductAttributeDataMapper">
  <resultMap id="BaseResultMap" type="ai.pricefox.mallfox.domain.standard.StandardProductAttributeData">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="create_username" jdbcType="VARCHAR" property="createUsername" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="update_username" jdbcType="VARCHAR" property="updateUsername" />
    <result column="attribute_name" jdbcType="VARCHAR" property="attributeName" />
    <result column="attribute_value" jdbcType="VARCHAR" property="attributeValue" />
    <result column="sku_code" jdbcType="VARCHAR" property="skuCode" />
    <result column="is_match" jdbcType="BIT" property="isMatch" />
  </resultMap>
  <sql id="Base_Column_List">
    id, create_date, create_username, update_date, update_username, attribute_name, attribute_value, 
    sku_code, is_match
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from standard_product_attribute_data
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from standard_product_attribute_data
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="ai.pricefox.mallfox.domain.standard.StandardProductAttributeData">
    insert into standard_product_attribute_data (id, create_date, create_username, 
      update_date, update_username, attribute_name, 
      attribute_value, sku_code, is_match
      )
    values (#{id,jdbcType=BIGINT}, #{createDate,jdbcType=TIMESTAMP}, #{createUsername,jdbcType=VARCHAR}, 
      #{updateDate,jdbcType=TIMESTAMP}, #{updateUsername,jdbcType=VARCHAR}, #{attributeName,jdbcType=VARCHAR}, 
      #{attributeValue,jdbcType=VARCHAR}, #{skuCode,jdbcType=VARCHAR}, #{isMatch,jdbcType=BIT}
      )
  </insert>
  <insert id="insertSelective" parameterType="ai.pricefox.mallfox.domain.standard.StandardProductAttributeData">
    insert into standard_product_attribute_data
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="createDate != null">
        create_date,
      </if>
      <if test="createUsername != null">
        create_username,
      </if>
      <if test="updateDate != null">
        update_date,
      </if>
      <if test="updateUsername != null">
        update_username,
      </if>
      <if test="attributeName != null">
        attribute_name,
      </if>
      <if test="attributeValue != null">
        attribute_value,
      </if>
      <if test="skuCode != null">
        sku_code,
      </if>
      <if test="isMatch != null">
        is_match,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="createDate != null">
        #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createUsername != null">
        #{createUsername,jdbcType=VARCHAR},
      </if>
      <if test="updateDate != null">
        #{updateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUsername != null">
        #{updateUsername,jdbcType=VARCHAR},
      </if>
      <if test="attributeName != null">
        #{attributeName,jdbcType=VARCHAR},
      </if>
      <if test="attributeValue != null">
        #{attributeValue,jdbcType=VARCHAR},
      </if>
      <if test="skuCode != null">
        #{skuCode,jdbcType=VARCHAR},
      </if>
      <if test="isMatch != null">
        #{isMatch,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="ai.pricefox.mallfox.domain.standard.StandardProductAttributeData">
    update standard_product_attribute_data
    <set>
      <if test="createDate != null">
        create_date = #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createUsername != null">
        create_username = #{createUsername,jdbcType=VARCHAR},
      </if>
      <if test="updateDate != null">
        update_date = #{updateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUsername != null">
        update_username = #{updateUsername,jdbcType=VARCHAR},
      </if>
      <if test="attributeName != null">
        attribute_name = #{attributeName,jdbcType=VARCHAR},
      </if>
      <if test="attributeValue != null">
        attribute_value = #{attributeValue,jdbcType=VARCHAR},
      </if>
      <if test="skuCode != null">
        sku_code = #{skuCode,jdbcType=VARCHAR},
      </if>
      <if test="isMatch != null">
        is_match = #{isMatch,jdbcType=BIT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="ai.pricefox.mallfox.domain.standard.StandardProductAttributeData">
    update standard_product_attribute_data
    set create_date = #{createDate,jdbcType=TIMESTAMP},
      create_username = #{createUsername,jdbcType=VARCHAR},
      update_date = #{updateDate,jdbcType=TIMESTAMP},
      update_username = #{updateUsername,jdbcType=VARCHAR},
      attribute_name = #{attributeName,jdbcType=VARCHAR},
      attribute_value = #{attributeValue,jdbcType=VARCHAR},
      sku_code = #{skuCode,jdbcType=VARCHAR},
      is_match = #{isMatch,jdbcType=BIT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>
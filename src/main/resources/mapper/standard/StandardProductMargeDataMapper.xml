<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.pricefox.mallfox.mapper.standard.StandardProductMargeDataMapper">
    <resultMap id="BaseResultMap" type="ai.pricefox.mallfox.domain.standard.StandardProductMargeData">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate"/>
        <result column="create_username" jdbcType="VARCHAR" property="createUsername"/>
        <result column="update_date" jdbcType="TIMESTAMP" property="updateDate"/>
        <result column="update_username" jdbcType="VARCHAR" property="updateUsername"/>
        <result column="source_platform_name" jdbcType="VARCHAR" property="sourcePlatformName"/>
        <result column="source_platform_code" jdbcType="VARCHAR" property="sourcePlatformCode"/>
        <result column="data_channel" jdbcType="VARCHAR" property="dataChannel"/>
        <result column="spu_code" jdbcType="VARCHAR" property="spuCode"/>
        <result column="sku_code" jdbcType="VARCHAR" property="skuCode"/>
        <result column="platform_spu_id" jdbcType="VARCHAR" property="platformSpuId"/>
        <result column="platform_sku_id" jdbcType="VARCHAR" property="platformSkuId"/>
        <result column="step" jdbcType="VARCHAR" property="step"/>
        <result column="examine_status" jdbcType="VARCHAR" property="examineStatus"/>
        <result column="product_identifier" jdbcType="VARCHAR" property="productIdentifier"/>
        <result column="category_level1" jdbcType="VARCHAR" property="categoryLevel1"/>
        <result column="category_level2" jdbcType="VARCHAR" property="categoryLevel2"/>
        <result column="category_level3" jdbcType="VARCHAR" property="categoryLevel3"/>
        <result column="category_leve3_code" jdbcType="VARCHAR" property="categoryLeve3Code"/>
        <result column="self_operated" jdbcType="BIT" property="selfOperated"/>
        <result column="is_match" jdbcType="BIT" property="isMatch"/>
        <!-- 新增字段映射 -->
        <result column="brand" jdbcType="VARCHAR" property="brand"/>
        <result column="model" jdbcType="VARCHAR" property="model"/>
        <result column="title" jdbcType="VARCHAR" property="title"/>
        <result column="upc_code" jdbcType="VARCHAR" property="upcCode"/>
        <result column="color" jdbcType="VARCHAR" property="color"/>
        <result column="series" jdbcType="VARCHAR" property="series"/>
        <result column="condition_new" jdbcType="VARCHAR" property="conditionNew"/>
        <result column="price" jdbcType="DECIMAL" property="price"/>
    </resultMap>
    <!-- 通用字段集合 -->
    <sql id="Base_Column_List">id, create_date, create_username, update_date, update_username, source_platform_name, source_platform_code, data_channel, spu_code, sku_code, platform_spu_id, platform_sku_id, product_identifier, step, examine_status, category_level1, category_level2, category_level3, category_leve3_code, self_operated, is_match, brand, model, title, upc_code, color, series, `condition_new`, price</sql>

    <!-- 新增记录并返回主键ID -->
    <insert id="insert" parameterType="ai.pricefox.mallfox.domain.standard.StandardProductMargeData"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO standard_product_marge_data (create_date, create_username, update_date, update_username,
                                                 source_platform_name, source_platform_code, data_channel, spu_code,
                                                 sku_code,
                                                 platform_spu_id, platform_sku_id, product_identifier, step,
                                                 examine_status,
                                                 category_level1, category_level2, category_level3, category_leve3_code,
                                                 self_operated, is_match, brand, model, title, upc_code, color,
                                                 series, `condition_new`, price)
                                                 VALUES (#{createDate},
                                                 #{createUsername},
                                                 #{updateDate},
                                                 #{updateUsername},
                                                 #{sourcePlatformName},
                                                 #{sourcePlatformCode},
                                                 #{dataChannel},
                                                 #{spuCode},
                                                 #{skuCode},
                                                 #{platformSpuId},
                                                 #{platformSkuId},
                                                 #{productIdentifier},
                                                 #{step},
                                                 #{examineStatus},
                                                 #{categoryLevel1},
                                                 #{categoryLevel2},
                                                 #{categoryLevel3},
                                                 #{categoryLeve3Code},
                                                 #{selfOperated},
                                                 #{isMatch},
                                                 #{brand},
                                                 #{model},
                                                 #{title},
                                                 #{upcCode},
                                                 #{color},
                                                 #{series},
                                                 #{conditionNew},
                                                 #{price})
    </insert>

    <!-- 根据ID更新记录 -->
    <update id="updateById" parameterType="ai.pricefox.mallfox.domain.standard.StandardProductMargeData">
        UPDATE standard_product_marge_data
        <set>
            <if test="et.updateDate != null">
                update_date = #{et.updateDate},
            </if>
            <if test="et.updateUsername != null">
                update_username = #{et.updateUsername},
            </if>
            <if test="et.sourcePlatformName != null">
                source_platform_name = #{et.sourcePlatformName},
            </if>
            <if test="et.sourcePlatformCode != null">
                source_platform_code = #{et.sourcePlatformCode},
            </if>
            <if test="et.dataChannel != null">
                data_channel = #{et.dataChannel},
            </if>
            <if test="et.spuCode != null">
                spu_code = #{et.spuCode},
            </if>
            <if test="et.skuCode != null">
                sku_code = #{et.skuCode},
            </if>
            <if test="et.platformSpuId != null">
                platform_spu_id = #{et.platformSpuId},
            </if>
            <if test="et.platformSkuId != null">
                platform_sku_id = #{et.platformSkuId},
            </if>
            <if test="et.productIdentifier != null">
                product_identifier = #{et.productIdentifier},
            </if>
            <if test="et.step != null">
                step = #{et.step},
            </if>
            <if test="et.examineStatus != null">
                examine_status = #{et.examineStatus},
            </if>
            <if test="et.categoryLevel1 != null">
                category_level1 = #{et.categoryLevel1},
            </if>
            <if test="et.categoryLevel2 != null">
                category_level2 = #{et.categoryLevel2},
            </if>
            <if test="et.categoryLevel3 != null">
                category_level3 = #{et.categoryLevel3},
            </if>
            <if test="et.categoryLeve3Code != null">
                category_leve3_code = #{et.categoryLeve3Code},
            </if>
            <if test="et.selfOperated != null">
                self_operated = #{et.selfOperated},
            </if>
            <if test="et.isMatch != null">
                is_match = #{et.isMatch},
            </if>
            <!-- 新增字段更新 -->
            <if test="et.brand != null">
                brand = #{et.brand},
            </if>
            <if test="et.model != null">
                model = #{et.model},
            </if>
            <if test="et.title != null">
                title = #{et.title},
            </if>
            <if test="et.upcCode != null">
                upc_code = #{et.upcCode},
            </if>
            <if test="et.color != null">
                color = #{et.color},
            </if>
            <if test="et.series != null">
                series = #{et.series},
            </if>
            <if test="et.conditionNew != null">
                `condition_new` = #{et.conditionNew},
            </if>
            <if test="et.price != null">
                price = #{et.price},
            </if>
        </set>
        WHERE id = #{et.id}
    </update>

    <!-- 根据ID删除记录 -->
    <delete id="deleteById" parameterType="java.lang.Long">
        DELETE
        FROM standard_product_marge_data
        WHERE id = #{id}
    </delete>

    <!-- 根据ID查询记录 -->
    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM standard_product_marge_data
        WHERE id = #{id}
    </select>

    <!-- 查询所有记录 -->
    <select id="selectAll" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM standard_product_marge_data
    </select>

    <!-- 根据唯一键查询 -->
    <select id="selectBySpuPlatform" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM standard_product_marge_data
        WHERE spu_code = #{spuCode} AND source_platform_code = #{platformCode}
    </select>

    <select id="selectBySkuPlatform" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM standard_product_marge_data
        WHERE sku_code = #{skuCode} AND source_platform_code = #{platformCode}
    </select>

    <select id="selectByIdentifier" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM standard_product_marge_data
        WHERE product_identifier = #{productIdentifier}
    </select>
    <select id="selectByProductIdentifier"
            resultType="ai.pricefox.mallfox.domain.standard.StandardProductMargeData" parameterType="java.lang.String">
        SELECT *
        FROM standard_product_marge_data
        WHERE product_identifier = #{productIdentifier}
    </select>

    <!-- 根据动态表头分页查询商品数据 -->
    <select id="selectPage" resultType="ai.pricefox.mallfox.domain.standard.StandardProductMargeData">
        SELECT
        id, create_date, create_username, update_date, update_username, source_platform_name, source_platform_code,
        data_channel, spu_code, sku_code, platform_spu_id, platform_sku_id, product_identifier, step, examine_status,
        category_level1, category_level2, category_level3, category_leve3_code, self_operated, is_match, brand, model,
        title, upc_code, color, series, `condition_new`, price
        FROM standard_product_marge_data
        <where>
            <!-- 这里添加动态查询条件 -->
            <if test="request.spuId != null and request.spuId != ''">
                AND spu_code LIKE CONCAT('%', #{request.spuId}, '%')
            </if>
            <if test="request.skuId != null and request.skuId != ''">
                AND sku_code LIKE CONCAT('%', #{request.skuId}, '%')
            </if>
            <if test="request.sourcePlatform != null">
                AND source_platform_name = #{request.sourcePlatform}
            </if>
            <if test="request.channel != null">
                AND data_channel = #{request.channel}
            </if>
            <if test="request.isMatch != null">
                AND is_match = #{request.isMatch}
            </if>
            <if test="request.type != null">
                AND step = #{request.type}
            </if>
            <if test="request.brand != null">
                AND brand LIKE CONCAT('%', #{request.brand}, '%')
            </if>
            <if test="request.model != null">
                AND model LIKE CONCAT('%', #{request.model}, '%')
            </if>
            <if test="request.title != null">
                AND title LIKE CONCAT('%', #{request.title}, '%')
            </if>
            <if test="request.upcCode != null">
                AND upc_code LIKE CONCAT('%', #{request.upcCode}, '%')
            </if>
            <if test="request.color != null">
                AND color LIKE CONCAT('%', #{request.color}, '%')
            </if>
            <if test="request.series != null">
                AND series LIKE CONCAT('%', #{request.series}, '%')
            </if>
            <if test="request.conditionNew != null">
                AND `condition_new` LIKE CONCAT('%', #{request.conditionNew}, '%')
            </if>
            <!-- 可以根据需要添加更多查询条件 -->
        </where>
        GROUP BY source_platform_code, platform_spu_id
        <if test="request.sortField != null">
            <if test="request.sortOrder != null">
                ORDER BY ${request.sortField} ${request.sortOrder}
            </if>
        </if>
    </select>

</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.pricefox.mallfox.mapper.tracking.FieldTrackingMapper">

    <resultMap id="BaseResultMap" type="ai.pricefox.mallfox.domain.tracking.ProductFieldSourceTracking">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="tableName" column="table_name" jdbcType="VARCHAR"/>
        <result property="recordId" column="record_id" jdbcType="BIGINT"/>
        <result property="fieldSources" column="field_sources" jdbcType="OTHER"
                typeHandler="ai.pricefox.mallfox.config.handler.CustomJacksonTypeHandler"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, table_name, record_id, field_sources, create_time, update_time
    </sql>

    <!-- 批量插入或更新（JSON格式） -->
    <insert id="batchInsertOrUpdate" parameterType="java.util.List">
        INSERT INTO product_field_source_tracking
        (table_name, record_id, field_sources, create_time)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.tableName}, #{item.recordId},
             #{item.fieldSources, typeHandler=ai.pricefox.mallfox.config.handler.CustomJacksonTypeHandler},
             #{item.createTime})
        </foreach>
        ON DUPLICATE KEY UPDATE
        field_sources = VALUES(field_sources),
        update_time = CURRENT_TIMESTAMP
    </insert>

    <!-- 查询字段来源信息（从JSON解析） -->
    <select id="selectFieldSources" resultType="java.util.Map">
        SELECT
            field_sources
        FROM product_field_source_tracking
        WHERE table_name = #{tableName} AND record_id = #{recordId}
        LIMIT 1
    </select>

    <!-- 查询指定字段来源信息（从JSON提取） -->
    <select id="selectFieldSource" resultType="java.util.Map">
        SELECT
            JSON_EXTRACT(field_sources, CONCAT('$.', #{fieldName})) as fieldSourceInfo
        FROM product_field_source_tracking
        WHERE table_name = #{tableName} AND record_id = #{recordId}
        LIMIT 1
    </select>

    <!-- 插入或更新（JSON格式） -->
    <insert id="insertOrUpdate" parameterType="ai.pricefox.mallfox.domain.tracking.ProductFieldSourceTracking">
        INSERT INTO product_field_source_tracking
        (table_name, record_id, field_sources, create_time)
        VALUES
        (#{tableName}, #{recordId},
         #{fieldSources, typeHandler=ai.pricefox.mallfox.config.handler.CustomJacksonTypeHandler},
         #{createTime})
        ON DUPLICATE KEY UPDATE
        field_sources = VALUES(field_sources),
        update_time = CURRENT_TIMESTAMP
    </insert>

    <!-- 更新JSON中的特定字段来源信息 -->
    <update id="updateFieldSource">
        UPDATE product_field_source_tracking
        SET field_sources = JSON_SET(
            IFNULL(field_sources, JSON_OBJECT()),
            CONCAT('$.', #{fieldName}),
            JSON_OBJECT(
                'dataSource', #{dataSource},
                'sourcePlatform', #{sourcePlatform},
                'lastUpdate', NOW(),
                'oldValue', #{oldValue},
                'newValue', #{newValue}
            )
        ),
        update_time = CURRENT_TIMESTAMP
        WHERE table_name = #{tableName} AND record_id = #{recordId}
    </update>

    <!-- 删除记录的所有字段追踪信息 -->
    <delete id="deleteByRecord">
        DELETE FROM product_field_source_tracking
        WHERE table_name = #{tableName} AND record_id = #{recordId}
    </delete>

    <!-- 删除过期的字段追踪数据 -->
    <delete id="deleteExpiredData">
        DELETE FROM product_field_source_tracking
        WHERE create_time &lt; DATE_SUB(NOW(), INTERVAL #{daysToKeep} DAY)
    </delete>

    <!-- 统计字段追踪记录数量 -->
    <select id="countTrackingRecords" resultType="long">
        SELECT COUNT(*)
        FROM product_field_source_tracking
        <where>
            <if test="tableName != null and tableName != ''">
                AND table_name = #{tableName}
            </if>
        </where>
    </select>

    <!-- 统计指定表和记录ID的字段追踪记录数量 -->
    <select id="countSpecificRecord" resultType="long">
        SELECT COUNT(*)
        FROM product_field_source_tracking
        WHERE table_name = #{tableName} AND record_id = #{recordId}
    </select>

</mapper>

// 文件: src/main/resources/rules/entity/CommonExtraction.drl
// 功能: 通用字段处理规则
package rules.entity;

import ai.pricefox.mallfox.utils.RuleLogUtil;
import ai.pricefox.mallfox.model.dto.DynamicStandardProduct;
import java.util.Map;
import ai.pricefox.mallfox.domain.standard.StandardField;

/**
 * 规则名称：Process Common Fields
 * 功能描述：处理通用字段的标准化
 * 触发条件：
 *   1. 存在原始数据Map对象
 *   2. 存在DynamicStandardProduct标准产品对象
 * 处理流程：
 *   1. 通过字段映射服务获取标准字段映射
 *   2. 处理标题、UPC码、商品URL等通用字段
 * 处理优先级：salience 70
 */
rule "CommonExtraction.drl"
    no-loop true
    salience 90
    when
        // 匹配原始数据Map和标准产品对象
        $standardProduct: DynamicStandardProduct()
    then {
            RuleLogUtil.info("进入 StandardizeFieldNamesRule 规则文件");
        }
end
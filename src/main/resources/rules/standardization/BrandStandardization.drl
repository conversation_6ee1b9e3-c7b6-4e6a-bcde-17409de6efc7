// 文件: src/main/resources/rules/standardization/BrandStandardization.drl
// 功能: 品牌标准化处理规则
package rules.standardization;

import ai.pricefox.mallfox.utils.RuleLogUtil;
import ai.pricefox.mallfox.model.dto.DynamicStandardProduct;
import ai.pricefox.mallfox.domain.standard.StandardBrand;
import ai.pricefox.mallfox.domain.standard.StandardField;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern
import ai.pricefox.mallfox.domain.standard.PlatformFieldMapping
import com.alibaba.fastjson.JSONObject;
import ai.pricefox.mallfox.service.rules.StandardDataCacheService;

/**
 * 规则名称：Process Brand Standardization
 * 功能描述：实现品牌字段的多级标准化处理
 * 触发条件：
 *   1. 存在原始数据Map对象
 *   2. 存在DynamicStandardProduct标准产品对象
 *   3. 存在StandardBrand标准品牌库列表
 * 处理流程：
 *   1. 优先使用标准库精确匹配
 *   2. 其次从标题中提取品牌信息
 *   3. 最后使用原始品牌数据
 * 处理优先级：salience 100（最高优先级）
 */
rule "BrandStandardization.drl"
//    agenda-group "debug"
    no-loop true
    when
        // 匹配原始数据Map和标准产品对象
        $standardProduct: DynamicStandardProduct()
        $originalData: Map() from $standardProduct.getOriginData()
        $brandList: List() from collect(StandardBrand())
    then {
            RuleLogUtil.info("进入 Process Brand Standardization 规则文件");
            // 定义品牌code
            String BRAND_CODE = "Raw0000004";
        }
    end
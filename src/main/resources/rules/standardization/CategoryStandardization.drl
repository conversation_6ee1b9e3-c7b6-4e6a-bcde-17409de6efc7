// 文件: src/main/resources/rules/standardization/CategoryStandardization.drl
// 功能: 品类标准化处理规则
package rules.standardization;

import ai.pricefox.mallfox.utils.RuleLogUtil;
import ai.pricefox.mallfox.model.dto.DynamicStandardProduct;
import ai.pricefox.mallfox.domain.standard.StandardCategory;
import java.util.List;
import java.util.Map;

/**
 * 规则名称：Process Category Standardization
 * 功能描述：实现品类字段的多级标准化处理
 * 触发条件：
 *   1. 存在原始数据Map对象
 *   2. 存在DynamicStandardProduct标准产品对象
 *   3. 存在StandardCategory标准品类库列表
 * 处理流程：
 *   1. 优先使用标准库精确匹配
 *   2. 其次从标题中提取品类信息
 *   3. 最后使用原始品类数据
 * 处理优先级：salience 100（最高优先级）
 */
rule "CategoryStandardization.drl"
    no-loop true
    when
        // 匹配原始数据Map和标准产品对象
        $standardProduct: DynamicStandardProduct()
        $originalData: Map() from $standardProduct.getOriginData()
        // 获取所有标准品类库
        $categoryList: List() from collect(StandardCategory())
    then {
        RuleLogUtil.info("进入 Process Category Standardization 规则文件",String.valueOf($categoryList));
         }
    end
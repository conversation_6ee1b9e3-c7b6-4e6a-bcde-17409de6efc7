// 文件: src/main/resources/rules/standardization/ColorStandardization.drl
// 功能: 颜色标准化处理规则
package rules.standardization;

import ai.pricefox.mallfox.utils.RuleLogUtil;
import ai.pricefox.mallfox.model.dto.DynamicStandardProduct;
import ai.pricefox.mallfox.domain.standard.StandardColor;
import java.util.List;
import java.util.Map;

/**
 * 规则名称：ColorStandardization.drl
 * 功能描述：实现颜色字段的多级标准化处理
 * 处理流程：
 *   1. 优先使用标准库精确匹配（匹配codeNameLevel1和codeNameLevel2）
 *   2. 其次从标题中提取颜色信息（模糊匹配）
 *   3. 默认为空（不使用原始颜色数据）
 */
rule "ColorStandardization.drl"
    no-loop true
    salience 100
    when
        // 匹配标准产品对象和当前数据
        $standardProduct: DynamicStandardProduct()
        // 原始数据
        $originalData: Map() from $standardProduct.getOriginData()
        // 当前数据
        $currentData: Map() from $standardProduct.getCurrentData()
    then {
            RuleLogUtil.info("[颜色标准化规则] 开始处理颜色标准化");
        }
    end

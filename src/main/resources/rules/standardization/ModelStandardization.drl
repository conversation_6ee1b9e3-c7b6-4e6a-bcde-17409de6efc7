// 文件: src/main/resources/rules/standardization/ModelStandardization.drl
// 功能: 型号标准化处理规则
package rules.standardization;

import ai.pricefox.mallfox.utils.RuleLogUtil;
import ai.pricefox.mallfox.model.dto.DynamicStandardProduct;
import ai.pricefox.mallfox.domain.standard.StandardModel;
import java.util.List;
import java.util.Map;

/**
 * 规则名称：ModelStandardization.drl
 * 功能描述：实现型号字段的多级标准化处理
 * 触发条件：
 *   1. 存在原始数据Map对象
 *   2. 存在DynamicStandardProduct标准产品对象
 *   3. 存在StandardModel标准型号库列表
 * 处理流程：
 *   1. 优先使用标准库精确匹配
 *   2. 其次从标题中提取型号信息
 *   3. 最后使用原始型号数据
 * 处理优先级：salience 100（最高优先级）
 */
rule "ModelStandardization.drl"
    no-loop true
    when
        // 匹配原始数据Map和标准产品对象
        $standardProduct: DynamicStandardProduct()
        $originalData: Map() from $standardProduct.getOriginalData()
        // 获取所有标准型号库
        $modelList: List() from collect(StandardModel())
    then {
            RuleLogUtil.info("进入 Process Model Standardization 规则文件");
         }
    end
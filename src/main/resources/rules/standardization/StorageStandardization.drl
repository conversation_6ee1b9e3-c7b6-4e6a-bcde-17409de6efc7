// 文件: src/main/resources/rules/standardization/StorageStandardization.drl
// 功能: 存储标准化处理规则
package rules.standardization;

import ai.pricefox.mallfox.utils.RuleLogUtil;
import ai.pricefox.mallfox.model.dto.ProductDataDTO;
import ai.pricefox.mallfox.model.dto.DynamicStandardProduct;
import java.util.List;
import java.util.Map;

/**
 * 规则名称：Process Storage Standardization
 * 功能描述：实现存储字段的多级标准化处理
 * 触发条件：
 *   1. 存在原始数据Map对象
 *   2. 存在DynamicStandardProduct标准产品对象
 *   3. 存在StandardStorage标准存储库列表
 * 处理流程：
 *   1. 优先使用标准库精确匹配
 *   2. 其次从标题中提取存储信息
 *   3. 最后使用原始存储数据
 * 处理优先级：salience 100（最高优先级）
 */
rule "Process Storage Standardization"
    no-loop true
    when
        // 匹配原始数据Map和标准产品对象
        $originalData: Map()
        $standardProduct: DynamicStandardProduct()
    then {
            RuleLogUtil.info("进入 Process Storage Standardization 规则文件");
        }
end
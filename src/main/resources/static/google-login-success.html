<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录成功</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            width: 100%;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        h1 {
            color: #34a853;
            margin-bottom: 20px;
            text-align: center;
        }
        .success-icon {
            text-align: center;
            font-size: 64px;
            color: #34a853;
            margin: 20px 0;
        }
        .token-container {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            word-break: break-all;
            font-family: monospace;
        }
        .back-link {
            display: block;
            text-align: center;
            margin-top: 20px;
            color: #1a73e8;
            text-decoration: none;
        }
        .back-link:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>登录成功</h1>
        <div class="success-icon">✓</div>
        
        <p>您已成功通过Google账号登录系统。您的身份已被验证。</p>
        
        <h2>您的访问令牌：</h2>
        <div class="token-container" id="tokenContainer">
            正在获取...
        </div>
        
        <h2>用户信息：</h2>
        <div class="token-container" id="userInfo">
            正在获取用户信息...
        </div>
        
        <a href="/api/google-login.html" class="back-link">返回登录页</a>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 从URL获取token参数
            const urlParams = new URLSearchParams(window.location.search);
            const token = urlParams.get('token');
            
            if (token) {
                // 显示token
                document.getElementById('tokenContainer').textContent = token;
                
                // 使用token获取用户信息
                fetch('/api/v1/auth/me', {
                    headers: {
                        'Authorization': 'Bearer ' + token
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data && data.data) {
                        // 格式化显示用户信息
                        const user = data.data;
                        const userInfoHtml = `
                            用户ID: ${user.userId}<br>
                            用户名: ${user.username}<br>
                            昵称: ${user.nickname || '未设置'}<br>
                            ${user.avatar ? `<img src="${user.avatar}" width="100" style="margin-top:10px"><br>` : ''}
                        `;
                        document.getElementById('userInfo').innerHTML = userInfoHtml;
                    } else {
                        document.getElementById('userInfo').textContent = '无法获取用户信息';
                    }
                })
                .catch(error => {
                    document.getElementById('userInfo').textContent = '获取用户信息失败: ' + error.message;
                });
            } else {
                document.getElementById('tokenContainer').textContent = '未获取到令牌';
                document.getElementById('userInfo').textContent = '无法获取用户信息';
            }
        });
    </script>
</body>
</html> 
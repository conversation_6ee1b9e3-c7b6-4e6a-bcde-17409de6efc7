<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Google登录测试</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            width: 100%;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        h1 {
            color: #1a73e8;
            margin-bottom: 20px;
            text-align: center;
        }
        .login-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
        }
        .login-btn {
            background-color: #1a73e8;
            color: white;
            border: none;
            padding: 12px 20px;
            font-size: 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px 0;
            display: inline-block;
            text-decoration: none;
            text-align: center;
        }
        .login-btn:hover {
            background-color: #0d47a1;
        }
        .code-example {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            overflow-x: auto;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Google登录测试页面</h1>
        
        <div class="login-section">
            <h2>方式一：直接链接登录</h2>
            <p>点击下方按钮，将直接跳转到Google登录页面:</p>
            <a href="/api/oauth/google/login" class="login-btn">使用Google账号登录</a>
        </div>
        
        <div class="login-section">
            <h2>方式二：前端集成登录按钮</h2>
            <p>这是一个模拟的前端按钮，实际应用中应按照Google Identity Services的要求进行集成:</p>
            
            <div id="googleLoginContainer"></div>
            
            <div class="code-example">
                <p>// Google Identity Services 集成代码示例:</p>
                <p>&lt;script src="https://accounts.google.com/gsi/client"&gt;&lt;/script&gt;</p>
                <p>&lt;div id="g_id_onload"</p>
                <p>&nbsp;&nbsp;data-client_id="<span id="clientIdPlaceholder">************-ablfe26cs337l64t1hvursfb5q47bmcl.apps.googleusercontent.com</span>"</p>
                <p>&nbsp;&nbsp;data-callback="handleCredentialResponse"&gt;</p>
                <p>&lt;/div&gt;</p>
                <p>&lt;div class="g_id_signin" data-type="standard"&gt;&lt;/div&gt;</p>
            </div>
        </div>
        
        <div class="login-section">
            <h2>API测试</h2>
            <p>获取登录URL：<a href="/api/oauth/google/auth-url" target="_blank">点击获取URL</a></p>
            <p>登录结果测试：登录后Token会显示在下方</p>
            <div id="tokenResult" style="word-break: break-all;"></div>
        </div>
    </div>

    <script>
        // 从URL获取token参数
        function getTokenFromUrl() {
            const urlParams = new URLSearchParams(window.location.search);
            const token = urlParams.get('token');
            return token;
        }
        
        // 页面加载时检查token
        window.onload = function() {
            const token = getTokenFromUrl();
            if (token) {
                document.getElementById('tokenResult').innerHTML = 
                    `<p>登录成功，访问令牌：</p><p>${token}</p>
                     <p>您现在可以使用此令牌访问API。</p>`;
            }
        };
    </script>
</body>
</html> 
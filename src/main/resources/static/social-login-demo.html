<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>社交平台登录示例</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
        }
        .container {
            max-width: 1000px;
            width: 100%;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            margin-bottom: 20px;
            text-align: center;
        }
        .login-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
        }
        .social-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            justify-content: center;
            margin: 20px 0;
        }
        .social-btn {
            color: white;
            border: none;
            padding: 12px 20px;
            font-size: 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px 0;
            min-width: 200px;
            display: flex;
            align-items: center;
            justify-content: center;
            text-decoration: none;
            transition: all 0.2s ease;
        }
        .social-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .social-btn i {
            margin-right: 10px;
            font-size: 20px;
        }
        .google-btn {
            background-color: #DB4437;
        }
        .facebook-btn {
            background-color: #4267B2;
        }
        .twitter-btn {
            background-color: #1DA1F2;
        }
        .linkedin-btn {
            background-color: #0077B5;
        }
        .github-btn {
            background-color: #333333;
        }
        .apple-btn {
            background-color: #000000;
        }
        .microsoft-btn {
            background-color: #00A4EF;
        }
        .amazon-btn {
            background-color: #FF9900;
        }
        .social-types {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .social-type-item {
            margin-bottom: 8px;
            padding: 5px;
            border-bottom: 1px solid #eee;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        th, td {
            padding: 10px;
            border: 1px solid #ddd;
            text-align: left;
        }
        th {
            background-color: #f5f5f5;
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
</head>
<body>
    <div class="container">
        <h1>社交平台登录示例</h1>
        
        <div class="login-section">
            <h2>支持的社交平台</h2>
            <p>本系统支持以下社交平台的账号登录和绑定：</p>
            
            <table>
                <tr>
                    <th>编码</th>
                    <th>平台名称</th>
                    <th>图标</th>
                </tr>
                <tr>
                    <td>1</td>
                    <td>Google</td>
                    <td><i class="fab fa-google"></i></td>
                </tr>
                <tr>
                    <td>2</td>
                    <td>Facebook</td>
                    <td><i class="fab fa-facebook"></i></td>
                </tr>
                <tr>
                    <td>3</td>
                    <td>Twitter</td>
                    <td><i class="fab fa-twitter"></i></td>
                </tr>
                <tr>
                    <td>4</td>
                    <td>LinkedIn</td>
                    <td><i class="fab fa-linkedin"></i></td>
                </tr>
                <tr>
                    <td>5</td>
                    <td>GitHub</td>
                    <td><i class="fab fa-github"></i></td>
                </tr>
                <tr>
                    <td>6</td>
                    <td>Apple</td>
                    <td><i class="fab fa-apple"></i></td>
                </tr>
                <tr>
                    <td>7</td>
                    <td>Microsoft</td>
                    <td><i class="fab fa-microsoft"></i></td>
                </tr>
                <tr>
                    <td>8</td>
                    <td>Amazon</td>
                    <td><i class="fab fa-amazon"></i></td>
                </tr>
            </table>
        </div>
        
        <div class="login-section">
            <h2>登录演示</h2>
            <p>点击下方按钮，选择您想要使用的社交平台进行登录：</p>
            
            <div class="social-buttons">
                <a href="/api/oauth/google/login" class="social-btn google-btn">
                    <i class="fab fa-google"></i> 使用Google登录
                </a>
                <a href="#" class="social-btn facebook-btn">
                    <i class="fab fa-facebook"></i> 使用Facebook登录
                </a>
                <a href="#" class="social-btn twitter-btn">
                    <i class="fab fa-twitter"></i> 使用Twitter登录
                </a>
                <a href="#" class="social-btn linkedin-btn">
                    <i class="fab fa-linkedin"></i> 使用LinkedIn登录
                </a>
                <a href="#" class="social-btn github-btn">
                    <i class="fab fa-github"></i> 使用GitHub登录
                </a>
                <a href="#" class="social-btn apple-btn">
                    <i class="fab fa-apple"></i> 使用Apple登录
                </a>
                <a href="#" class="social-btn microsoft-btn">
                    <i class="fab fa-microsoft"></i> 使用Microsoft登录
                </a>
                <a href="#" class="social-btn amazon-btn">
                    <i class="fab fa-amazon"></i> 使用Amazon登录
                </a>
            </div>
            <p class="note">注：目前仅实现了Google登录功能，其他平台登录功能正在开发中。</p>
        </div>
        
        <div class="login-section">
            <h2>API使用说明</h2>
            <p>社交账号绑定接口：POST /api/v1/social/bind</p>
            <p>社交账号解绑接口：POST /api/v1/social/unbind</p>
            <p>查询绑定的社交账号：GET /api/v1/social/accounts</p>
            <p>Google登录回调：GET /api/oauth/callback/google</p>
        </div>
    </div>
</body>
</html> 
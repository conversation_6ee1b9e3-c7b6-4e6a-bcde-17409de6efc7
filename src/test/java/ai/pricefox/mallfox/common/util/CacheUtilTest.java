package ai.pricefox.mallfox.common.util;

import ai.pricefox.mallfox.common.constant.RedisKeyConstants;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.test.context.ActiveProfiles;

import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 缓存工具类测试
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
public class CacheUtilTest {

    @Autowired
    private CacheUtil cacheUtil;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Test
    public void testUserTokenCache() {
        String token = "test_token_123";
        Long userId = 12345L;

        // 设置缓存
        cacheUtil.setUserToken(token, userId);

        // 获取缓存
        Long cachedUserId = cacheUtil.getUserToken(token);
        assertEquals(userId, cachedUserId);

        // 检查过期时间
        String key = RedisKeyConstants.USER_TOKEN + token;
        long expire = redisTemplate.getExpire(key, TimeUnit.SECONDS);
        assertTrue(expire > 0 && expire <= RedisKeyConstants.USER_TOKEN_TIMEOUT);

        // 删除缓存
        cacheUtil.deleteUserToken(token);
        assertNull(cacheUtil.getUserToken(token));

        log.info("用户令牌缓存测试通过");
    }

    @Test
    public void testProductDetailCache() {
        String skuId = "TEST_SKU_001";
        String sourcePlatform = "Amazon";
        String productDetail = "Test Product Detail";

        // 设置缓存
        cacheUtil.setProductDetail(skuId, sourcePlatform, productDetail);

        // 获取缓存
        Object cachedDetail = cacheUtil.getProductDetail(skuId, sourcePlatform);
        assertEquals(productDetail, cachedDetail);

        // 检查过期时间
        String key = RedisKeyConstants.PRODUCT_DETAIL + skuId + "_" + sourcePlatform;
        long expire = redisTemplate.getExpire(key, TimeUnit.SECONDS);
        assertTrue(expire > 0 && expire <= RedisKeyConstants.PRODUCT_DETAIL_TIMEOUT);

        // 删除缓存
        cacheUtil.deleteProductDetail(skuId, sourcePlatform);
        assertNull(cacheUtil.getProductDetail(skuId, sourcePlatform));

        log.info("商品详情缓存测试通过");
    }

    @Test
    public void testFieldSourcesCache() {
        String tableName = "product_data_offers";
        Long recordId = 123L;
        String fieldSources = "Test Field Sources";

        // 设置缓存
        cacheUtil.setFieldSources(tableName, recordId, fieldSources);

        // 获取缓存
        Object cachedSources = cacheUtil.getFieldSources(tableName, recordId);
        assertEquals(fieldSources, cachedSources);

        // 检查过期时间
        String key = RedisKeyConstants.FIELD_SOURCES + tableName + ":" + recordId;
        long expire = redisTemplate.getExpire(key, TimeUnit.SECONDS);
        assertTrue(expire > 0 && expire <= RedisKeyConstants.FIELD_SOURCES_TIMEOUT);

        // 删除缓存
        cacheUtil.deleteFieldSources(tableName, recordId);
        assertNull(cacheUtil.getFieldSources(tableName, recordId));

        log.info("字段来源缓存测试通过");
    }

    @Test
    public void testEmailCodeCache() {
        String email = "<EMAIL>";
        String code = "123456";

        // 设置缓存
        cacheUtil.setEmailCode(email, code);

        // 获取缓存
        String cachedCode = cacheUtil.getEmailCode(email);
        assertEquals(code, cachedCode);

        // 检查过期时间
        String key = RedisKeyConstants.EMAIL_CODE + email;
        long expire = redisTemplate.getExpire(key, TimeUnit.SECONDS);
        assertTrue(expire > 0 && expire <= RedisKeyConstants.VERIFY_CODE_TIMEOUT);

        // 删除缓存
        cacheUtil.deleteEmailCode(email);
        assertNull(cacheUtil.getEmailCode(email));

        log.info("邮箱验证码缓存测试通过");
    }

    @Test
    public void testSendFrequencyCache() {
        String identifier = "<EMAIL>";

        // 设置发送频率限制
        cacheUtil.setSendFrequency(identifier);

        // 检查发送频率限制
        assertTrue(cacheUtil.checkSendFrequency(identifier));

        // 等待过期（在实际测试中可能需要模拟）
        // 这里只是验证方法调用正常
        log.info("发送频率限制缓存测试通过");
    }

    @Test
    public void testEbayOAuthTokenCache() {
        String token = "ebay_test_token";
        long expireSeconds = 3600L;

        // 设置缓存
        cacheUtil.setEbayOAuthToken(token, expireSeconds);

        // 获取缓存
        String cachedToken = cacheUtil.getEbayOAuthToken();
        assertEquals(token, cachedToken);

        // 检查过期时间
        long expire = redisTemplate.getExpire(RedisKeyConstants.EBAY_OAUTH_TOKEN, TimeUnit.SECONDS);
        assertTrue(expire > 0 && expire <= expireSeconds);

        log.info("eBay OAuth令牌缓存测试通过");
    }

    @Test
    public void testEbaySyncCache() {
        // 测试待处理模型缓存
        String models = "test_models";
        cacheUtil.setEbayPendingModels(models);
        Object cachedModels = cacheUtil.getEbayPendingModels();
        assertEquals(models, cachedModels);

        // 测试已同步商品缓存
        String itemId = "test_item_123";
        cacheUtil.addEbaySyncedItem(itemId);
        assertTrue(cacheUtil.isEbayItemSynced(itemId));

        // 测试同步状态缓存
        String status = "syncing";
        cacheUtil.setEbaySyncStatus(status);
        Object cachedStatus = cacheUtil.getEbaySyncStatus();
        assertEquals(status, cachedStatus);

        log.info("eBay同步缓存测试通过");
    }

    @Test
    public void testGenericCacheOperations() {
        String key = "test:generic:key";
        String value = "test_value";
        long timeout = 60L;

        // 设置缓存
        cacheUtil.setCache(key, value, timeout, TimeUnit.SECONDS);

        // 获取缓存
        Object cachedValue = cacheUtil.getCache(key);
        assertEquals(value, cachedValue);

        // 检查键是否存在
        assertTrue(cacheUtil.hasKey(key));

        // 获取过期时间
        long expire = cacheUtil.getExpire(key);
        assertTrue(expire > 0 && expire <= timeout);

        // 删除缓存
        cacheUtil.deleteCache(key);
        assertFalse(cacheUtil.hasKey(key));

        log.info("通用缓存操作测试通过");
    }

    @Test
    public void testProductCalibrationCache() {
        Long offerId = 123L;
        Long simplifyId = 456L;
        String tags = "test_tags";

        // 测试 Offers 缓存
        cacheUtil.setProductCalibrationOffers(offerId, tags);
        Object cachedOfferTags = cacheUtil.getProductCalibrationOffers(offerId);
        assertEquals(tags, cachedOfferTags);

        // 测试 Simplify 缓存
        cacheUtil.setProductCalibrationSimplify(simplifyId, tags);
        Object cachedSimplifyTags = cacheUtil.getProductCalibrationSimplify(simplifyId);
        assertEquals(tags, cachedSimplifyTags);

        log.info("商品标记缓存测试通过");
    }

    @Test
    public void testCacheConstants() {
        // 验证缓存常量的合理性
        assertTrue(RedisKeyConstants.DEFAULT_TIMEOUT > 0);
        assertTrue(RedisKeyConstants.DEFAULT_LONG_TIMEOUT > RedisKeyConstants.DEFAULT_TIMEOUT);
        
        assertTrue(RedisKeyConstants.USER_TOKEN_TIMEOUT > 0);
        assertTrue(RedisKeyConstants.USER_REFRESH_TOKEN_TIMEOUT > RedisKeyConstants.USER_TOKEN_TIMEOUT);
        
        assertTrue(RedisKeyConstants.PRODUCT_DETAIL_TIMEOUT > 0);
        assertTrue(RedisKeyConstants.FIELD_SOURCES_TIMEOUT > 0);
        
        assertTrue(RedisKeyConstants.VERIFY_CODE_TIMEOUT > 0);
        assertTrue(RedisKeyConstants.SEND_FREQUENCY_TIMEOUT > 0);
        assertTrue(RedisKeyConstants.VERIFY_CODE_TIMEOUT > RedisKeyConstants.SEND_FREQUENCY_TIMEOUT);

        // 验证缓存键格式
        assertTrue(RedisKeyConstants.USER_TOKEN.endsWith(":"));
        assertTrue(RedisKeyConstants.PRODUCT_DETAIL.endsWith(":"));
        assertTrue(RedisKeyConstants.FIELD_SOURCES.endsWith(":"));
        assertTrue(RedisKeyConstants.EMAIL_CODE.endsWith(":"));

        log.info("缓存常量验证测试通过");
    }
}

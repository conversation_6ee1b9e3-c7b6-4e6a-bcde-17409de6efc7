package ai.pricefox.mallfox.common.util;

import ai.pricefox.mallfox.mapper.standard.StandardAttributeMapper;
import ai.pricefox.mallfox.mapper.standard.StandardAttributeValueMapper;
import ai.pricefox.mallfox.mapper.standard.StandardCategoryMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * IdGenerator属性编码生成测试
 * 验证generateAttributeCode方法生成ATTR + 8位数字格式的编码
 */
@ExtendWith(MockitoExtension.class)
class IdGeneratorAttributeCodeTest {

    @Mock
    private StandardCategoryMapper standardCategoryMapper;
    
    @Mock
    private StandardAttributeMapper standardAttributeMapper;
    
    @Mock
    private StandardAttributeValueMapper standardAttributeValueMapper;

    private IdGenerator idGenerator;

    @BeforeEach
    void setUp() {
        idGenerator = new IdGenerator(standardCategoryMapper, standardAttributeMapper, standardAttributeValueMapper);
    }

    @Test
    void shouldGenerateAttributeCodeWithEightDigits() {
        // 模拟数据库中没有现有编码
        when(standardAttributeMapper.selectMaxAttributeCode()).thenReturn(null);

        String attributeCode = idGenerator.generateAttributeCode();

        // 验证格式：ATTR + 8位数字
        assertNotNull(attributeCode);
        assertTrue(attributeCode.startsWith("ATTR"), "属性编码应该以ATTR开头");
        assertEquals(12, attributeCode.length(), "属性编码总长度应该是12位（ATTR + 8位数字）");
        assertEquals("ATTR00000001", attributeCode, "第一个属性编码应该是ATTR00000001");
        
        verify(standardAttributeMapper).selectMaxAttributeCode();
    }

    @Test
    void shouldGenerateNextAttributeCodeBasedOnExisting() {
        // 模拟数据库中已有编码
        when(standardAttributeMapper.selectMaxAttributeCode()).thenReturn("ATTR00000005");

        String attributeCode = idGenerator.generateAttributeCode();

        // 验证格式和递增逻辑
        assertNotNull(attributeCode);
        assertTrue(attributeCode.startsWith("ATTR"), "属性编码应该以ATTR开头");
        assertEquals(12, attributeCode.length(), "属性编码总长度应该是12位");
        assertEquals("ATTR00000006", attributeCode, "下一个属性编码应该是ATTR00000006");
        
        verify(standardAttributeMapper).selectMaxAttributeCode();
    }

    @Test
    void shouldGenerateAttributeCodeWithLargeNumber() {
        // 模拟数据库中有较大的编码
        when(standardAttributeMapper.selectMaxAttributeCode()).thenReturn("ATTR00012345");

        String attributeCode = idGenerator.generateAttributeCode();

        // 验证大数字的处理
        assertNotNull(attributeCode);
        assertTrue(attributeCode.startsWith("ATTR"), "属性编码应该以ATTR开头");
        assertEquals(12, attributeCode.length(), "属性编码总长度应该是12位");
        assertEquals("ATTR00012346", attributeCode, "下一个属性编码应该是ATTR00012346");
        
        verify(standardAttributeMapper).selectMaxAttributeCode();
    }

    @Test
    void shouldHandleMaximumEightDigitNumber() {
        // 测试8位数字的最大值
        when(standardAttributeMapper.selectMaxAttributeCode()).thenReturn("ATTR99999999");

        String attributeCode = idGenerator.generateAttributeCode();

        // 验证最大值处理（会溢出到9位，但格式化仍然有效）
        assertNotNull(attributeCode);
        assertTrue(attributeCode.startsWith("ATTR"), "属性编码应该以ATTR开头");
        // 注意：当数字超过8位时，String.format会保持实际位数
        assertTrue(attributeCode.length() >= 12, "属性编码长度应该至少是12位");
        
        verify(standardAttributeMapper).selectMaxAttributeCode();
    }

    @Test
    void shouldHandleInvalidExistingCode() {
        // 模拟数据库中有无效格式的编码
        when(standardAttributeMapper.selectMaxAttributeCode()).thenReturn("ATTR_INVALID");

        String attributeCode = idGenerator.generateAttributeCode();

        // 验证错误处理：应该重置为1
        assertNotNull(attributeCode);
        assertTrue(attributeCode.startsWith("ATTR"), "属性编码应该以ATTR开头");
        assertEquals(12, attributeCode.length(), "属性编码总长度应该是12位");
        assertEquals("ATTR00000001", attributeCode, "无效编码时应该重置为ATTR00000001");
        
        verify(standardAttributeMapper).selectMaxAttributeCode();
    }

    @Test
    void shouldHandleEmptyExistingCode() {
        // 模拟数据库返回空字符串
        when(standardAttributeMapper.selectMaxAttributeCode()).thenReturn("");

        String attributeCode = idGenerator.generateAttributeCode();

        // 验证空值处理
        assertNotNull(attributeCode);
        assertTrue(attributeCode.startsWith("ATTR"), "属性编码应该以ATTR开头");
        assertEquals(12, attributeCode.length(), "属性编码总长度应该是12位");
        assertEquals("ATTR00000001", attributeCode, "空编码时应该从ATTR00000001开始");
        
        verify(standardAttributeMapper).selectMaxAttributeCode();
    }

    @Test
    void shouldHandleNonAttrPrefixCode() {
        // 模拟数据库中有不是ATTR开头的编码
        when(standardAttributeMapper.selectMaxAttributeCode()).thenReturn("OTHER00000005");

        String attributeCode = idGenerator.generateAttributeCode();

        // 验证非ATTR前缀的处理：应该重置为1
        assertNotNull(attributeCode);
        assertTrue(attributeCode.startsWith("ATTR"), "属性编码应该以ATTR开头");
        assertEquals(12, attributeCode.length(), "属性编码总长度应该是12位");
        assertEquals("ATTR00000001", attributeCode, "非ATTR前缀时应该重置为ATTR00000001");
        
        verify(standardAttributeMapper).selectMaxAttributeCode();
    }

    @Test
    void shouldValidateEightDigitFormat() {
        // 验证8位数字格式的各种情况
        when(standardAttributeMapper.selectMaxAttributeCode()).thenReturn(null);

        String attributeCode = idGenerator.generateAttributeCode();
        
        // 提取数字部分
        String numberPart = attributeCode.substring(4);
        
        // 验证数字部分
        assertEquals(8, numberPart.length(), "数字部分应该是8位");
        assertEquals("00000001", numberPart, "第一个编码的数字部分应该是00000001");
        
        // 验证数字部分可以解析为整数
        assertDoesNotThrow(() -> {
            int number = Integer.parseInt(numberPart);
            assertEquals(1, number, "数字部分应该可以解析为1");
        });
    }
}

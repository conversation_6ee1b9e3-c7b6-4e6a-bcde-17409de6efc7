package ai.pricefox.mallfox.config;

import ai.pricefox.mallfox.domain.admin.AdminUser;
import ai.pricefox.mallfox.domain.auth.User;
import ai.pricefox.mallfox.service.admin.AdminTokenService;
import ai.pricefox.mallfox.service.auth.TokenService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.util.StringUtils;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * SaTokenConfigure安全性测试
 * 验证token为空时的安全处理逻辑
 */
@ExtendWith(MockitoExtension.class)
class SaTokenConfigureSecurityTest {

    @Mock
    private RequestLoggingInterceptor requestLoggingInterceptor;

    @Mock
    private CustomSaTokenDao customSaTokenDao;

    @Mock
    private TokenService tokenService;

    @Mock
    private AdminTokenService adminTokenService;

    private SaTokenConfigure saTokenConfigure;

    @BeforeEach
    void setUp() {
        saTokenConfigure = new SaTokenConfigure(
            requestLoggingInterceptor,
            customSaTokenDao,
            tokenService,
            adminTokenService
        );
    }

    @Test
    void shouldRejectRequestWithEmptyToken() {
        // 测试token为空的情况
        String token = null;
        String requestPath = "/admin/v1/users";

        // 验证：token为空时应该被拒绝
        assertFalse(StringUtils.hasText(token), "空token应该被识别为无效");

        // 验证逻辑：如果token为空，应该进入拒绝分支
        boolean shouldReject = !StringUtils.hasText(token);
        assertTrue(shouldReject, "空token应该被拒绝访问");
    }

    @Test
    void shouldRejectRequestWithBlankToken() {
        // 测试token为空白字符串的情况
        String token = "   ";
        String requestPath = "/admin/v1/users";

        // 验证：空白token应该被拒绝
        assertFalse(StringUtils.hasText(token), "空白token应该被识别为无效");

        // 验证逻辑：如果token为空白，应该进入拒绝分支
        boolean shouldReject = !StringUtils.hasText(token);
        assertTrue(shouldReject, "空白token应该被拒绝访问");
    }

    @Test
    void shouldRejectRequestWithInvalidToken() {
        // 模拟无效token的情况
        String invalidToken = "invalid-token-123";
        String requestPath = "/admin/v1/users";

        // 模拟tokenService返回null（token无效）
        when(adminTokenService.getUserByToken(invalidToken)).thenReturn(null);

        // 验证：token有值但无效时，应该调用验证服务
        assertTrue(StringUtils.hasText(invalidToken), "token不为空");

        AdminUser adminUser = adminTokenService.getUserByToken(invalidToken);
        assertNull(adminUser, "无效token应该返回null用户");

        verify(adminTokenService).getUserByToken(invalidToken);
    }

    @Test
    void shouldAllowRequestWithValidAdminToken() {
        // 模拟有效的管理员token
        String validToken = "valid-admin-token-123";
        String requestPath = "/admin/v1/users";
        
        AdminUser mockAdminUser = new AdminUser();
        mockAdminUser.setId(1L);
        mockAdminUser.setUsername("admin");
        
        when(adminTokenService.getUserByToken(validToken)).thenReturn(mockAdminUser);
        
        // 验证：不应该抛出异常
        assertDoesNotThrow(() -> {
            AdminUser adminUser = adminTokenService.getUserByToken(validToken);
            assertNotNull(adminUser);
            assertEquals(1L, adminUser.getId());
        });
        
        verify(adminTokenService).getUserByToken(validToken);
    }

    @Test
    void shouldAllowRequestWithValidUserToken() {
        // 模拟有效的用户token
        String validToken = "valid-user-token-123";
        String requestPath = "/v1/profile";
        
        User mockUser = new User();
        mockUser.setId(1L);
        mockUser.setUsername("user");
        
        when(tokenService.getUserByToken(validToken)).thenReturn(mockUser);
        
        // 验证：不应该抛出异常
        assertDoesNotThrow(() -> {
            User user = tokenService.getUserByToken(validToken);
            assertNotNull(user);
            assertEquals(1L, user.getId());
        });
        
        verify(tokenService).getUserByToken(validToken);
    }

    @Test
    void shouldHandleTokenExtractionFromAuthorizationHeader() {
        MockHttpServletRequest request = new MockHttpServletRequest();
        
        // 测试Bearer token格式
        request.addHeader("Authorization", "Bearer valid-token-123");
        String authHeader = request.getHeader("Authorization");
        
        assertNotNull(authHeader);
        assertTrue(authHeader.startsWith("Bearer "));
        
        String extractedToken = authHeader.substring(7);
        assertEquals("valid-token-123", extractedToken);
    }

    @Test
    void shouldHandleTokenExtractionFromParameter() {
        MockHttpServletRequest request = new MockHttpServletRequest();
        
        // 测试从参数中获取token
        request.setParameter("token", "param-token-123");
        String paramToken = request.getParameter("token");
        
        assertEquals("param-token-123", paramToken);
    }
}

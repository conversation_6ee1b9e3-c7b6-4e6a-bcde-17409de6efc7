package ai.pricefox.mallfox.config.handler;

import ai.pricefox.mallfox.domain.tracking.ProductFieldSourceTracking;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * CustomJacksonTypeHandler测试类
 * 验证LocalDateTime序列化/反序列化功能
 */
@SpringBootTest
@ActiveProfiles("test")
public class CustomJacksonTypeHandlerTest {

    private CustomJacksonTypeHandler typeHandler;
    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        // 创建支持JSR310的ObjectMapper
        objectMapper = new ObjectMapper();
        objectMapper.findAndRegisterModules(); // 自动注册JSR310模块
        
        // 创建TypeHandler并注入ObjectMapper
        typeHandler = new CustomJacksonTypeHandler();
        typeHandler.setObjectMapper(objectMapper);
    }

    @Test
    void testLocalDateTimeSerializationInFieldSourceInfo() throws Exception {
        // 创建包含LocalDateTime的FieldSourceInfo
        ProductFieldSourceTracking.FieldSourceInfo sourceInfo = new ProductFieldSourceTracking.FieldSourceInfo();
        sourceInfo.setDataSource(2);
        sourceInfo.setSourcePlatform("amazon");
        sourceInfo.setLastUpdate(LocalDateTime.of(2025, 1, 14, 10, 30, 0));
        sourceInfo.setOldValue("old_value");
        sourceInfo.setNewValue("new_value");

        // 创建fieldSources Map
        Map<String, ProductFieldSourceTracking.FieldSourceInfo> fieldSources = new HashMap<>();
        fieldSources.put("price", sourceInfo);

        // 测试序列化
        String json = objectMapper.writeValueAsString(fieldSources);
        assertNotNull(json);
        assertTrue(json.contains("2025-01-14 10:30:00")); // 验证LocalDateTime被正确序列化
        assertTrue(json.contains("amazon"));
        assertTrue(json.contains("old_value"));
        assertTrue(json.contains("new_value"));

        System.out.println("序列化结果: " + json);

        // 测试反序列化
        @SuppressWarnings("unchecked")
        Map<String, Object> deserializedMap = objectMapper.readValue(json, Map.class);
        assertNotNull(deserializedMap);
        assertTrue(deserializedMap.containsKey("price"));

        @SuppressWarnings("unchecked")
        Map<String, Object> priceInfo = (Map<String, Object>) deserializedMap.get("price");
        assertEquals(2, priceInfo.get("dataSource"));
        assertEquals("amazon", priceInfo.get("sourcePlatform"));
        assertEquals("old_value", priceInfo.get("oldValue"));
        assertEquals("new_value", priceInfo.get("newValue"));
        assertNotNull(priceInfo.get("lastUpdate")); // LocalDateTime应该被序列化为字符串

        System.out.println("反序列化成功，LocalDateTime字段: " + priceInfo.get("lastUpdate"));
    }

    @Test
    void testEmptyAndNullValues() throws Exception {
        // 测试空值处理
        String emptyJson = "";
        String nullJson = null;
        
        // 这些应该不会抛出异常
        assertDoesNotThrow(() -> {
            String result1 = objectMapper.writeValueAsString(null);
            assertEquals("null", result1);
            
            Object result2 = objectMapper.readValue("null", Object.class);
            assertNull(result2);
        });
    }

    @Test
    void testComplexFieldSourcesMap() throws Exception {
        // 创建复杂的fieldSources结构
        Map<String, ProductFieldSourceTracking.FieldSourceInfo> fieldSources = new HashMap<>();
        
        // 添加多个字段
        for (int i = 1; i <= 3; i++) {
            ProductFieldSourceTracking.FieldSourceInfo info = new ProductFieldSourceTracking.FieldSourceInfo();
            info.setDataSource(i % 2 + 1); // 1或2
            info.setSourcePlatform("platform" + i);
            info.setLastUpdate(LocalDateTime.now().minusHours(i));
            info.setOldValue("old" + i);
            info.setNewValue("new" + i);
            
            fieldSources.put("field" + i, info);
        }

        // 测试序列化
        String json = objectMapper.writeValueAsString(fieldSources);
        assertNotNull(json);
        
        // 验证包含所有字段
        for (int i = 1; i <= 3; i++) {
            assertTrue(json.contains("field" + i));
            assertTrue(json.contains("platform" + i));
            assertTrue(json.contains("old" + i));
            assertTrue(json.contains("new" + i));
        }

        System.out.println("复杂结构序列化成功，长度: " + json.length());

        // 测试反序列化
        @SuppressWarnings("unchecked")
        Map<String, Object> deserializedMap = objectMapper.readValue(json, Map.class);
        assertEquals(3, deserializedMap.size());
        
        for (int i = 1; i <= 3; i++) {
            assertTrue(deserializedMap.containsKey("field" + i));
        }

        System.out.println("复杂结构反序列化成功");
    }
}

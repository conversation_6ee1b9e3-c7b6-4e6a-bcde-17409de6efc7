package ai.pricefox.mallfox.controller;

import ai.pricefox.mallfox.model.param.ProductSearchRequest;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * <AUTHOR>
 * @desc 商品搜索控制器测试
 * @since 2025/7/8
 */
@SpringBootTest
//@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@ActiveProfiles("test")
public class ProductSearchControllerTest {

    @Autowired
    private WebApplicationContext webApplicationContext;

    @Autowired
    private ObjectMapper objectMapper;

    private MockMvc mockMvc;

    @Test
    public void testBasicSearch() throws Exception {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();

        ProductSearchRequest request = new ProductSearchRequest();
        request.setKeyword("iPhone");
        request.setPageNo(1);
        request.setPageSize(10);
        request.setSortBy("relevance");

        String requestJson = objectMapper.writeValueAsString(request);

        mockMvc.perform(post("/v1/products/search")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestJson))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").exists())
                .andExpect(jsonPath("$.data.products").isArray())
                .andExpect(jsonPath("$.data.total").exists())
                .andExpect(jsonPath("$.data.availableFilters").exists());

        System.out.println("基础搜索测试通过");
    }

    @Test
    public void testEmptySearch() throws Exception {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();

        ProductSearchRequest request = new ProductSearchRequest();
        request.setPageNo(1);
        request.setPageSize(10);

        String requestJson = objectMapper.writeValueAsString(request);

        mockMvc.perform(post("/v1/products/search")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestJson))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").exists());

        System.out.println("空搜索测试通过");
    }

    @Test
    public void testPriceRangeSearch() throws Exception {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();

        ProductSearchRequest request = new ProductSearchRequest();
        request.setKeyword("iPhone");
        request.setMinPrice(500.0);
        request.setMaxPrice(1200.0);
        request.setPageNo(1);
        request.setPageSize(10);

        String requestJson = objectMapper.writeValueAsString(request);

        mockMvc.perform(post("/v1/products/search")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestJson))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.products").isArray());

        System.out.println("价格区间搜索测试通过");
    }
}

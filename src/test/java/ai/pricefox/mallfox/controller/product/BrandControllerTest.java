package ai.pricefox.mallfox.controller.product;

import ai.pricefox.mallfox.vo.base.CommonResult;
import ai.pricefox.mallfox.vo.base.PageResult;
import ai.pricefox.mallfox.vo.brand.*;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.boot.test.web.server.LocalServerPort;
import org.springframework.http.*;
import org.springframework.test.context.ActiveProfiles;

import java.util.ArrayList;
import java.util.List;

/**
 * BrandController 接口测试类
 * 
 * <AUTHOR>
 * @date 2025-07-04
 */
@Slf4j
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles("dev")
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
public class BrandControllerTest {

    @LocalServerPort
    private int port;

    @Autowired
    private TestRestTemplate restTemplate;

    @Autowired
    private ObjectMapper objectMapper;

    private String baseUrl;
    private final List<Long> createdBrandIds = new ArrayList<>();

    @BeforeEach
    void setUp() {
        baseUrl = "http://localhost:" + port + "/api/admin/v1/brands";
        log.info("测试基础URL: {}", baseUrl);
    }

    @AfterAll
    void tearDown() {
        log.info("开始清理测试数据，共 {} 个品牌需要删除", createdBrandIds.size());
        for (Long brandId : createdBrandIds) {
            try {
                String deleteUrl = baseUrl + "/" + brandId;
                ResponseEntity<String> response = restTemplate.exchange(
                    deleteUrl, HttpMethod.DELETE, null, String.class);
                log.info("删除品牌 ID: {}, 响应状态: {}", brandId, response.getStatusCode());
            } catch (Exception e) {
                log.warn("删除品牌 ID: {} 失败: {}", brandId, e.getMessage());
            }
        }
        createdBrandIds.clear();
        log.info("测试数据清理完成");
    }

    // ==================== 正常流程测试 ====================

    @Test
    @Order(1)
    @DisplayName("1. 创建品牌 - 正常情况")
    void testCreateBrand_Success() {
        log.info("=== 开始测试：创建品牌 - 正常情况 ===");
        
        BrandInfoCreateReqVO reqVO = new BrandInfoCreateReqVO();
        reqVO.setName("Apple");
        reqVO.setLogoUrl("https://example.com/apple-logo.png");
        reqVO.setWebsite("https://www.apple.com");
        reqVO.setSortOrder(1);
        reqVO.setIsActive(true);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<BrandInfoCreateReqVO> request = new HttpEntity<>(reqVO, headers);

        ResponseEntity<String> response = restTemplate.postForEntity(baseUrl, request, String.class);
        
        log.info("响应状态码: {}", response.getStatusCode());
        log.info("响应内容: {}", response.getBody());

        Assertions.assertEquals(HttpStatus.OK, response.getStatusCode());
        
        try {
            CommonResult<BrandInfoRespVO> result = objectMapper.readValue(
                response.getBody(), new TypeReference<CommonResult<BrandInfoRespVO>>() {});
            
            Assertions.assertEquals(0, result.getCode());
            Assertions.assertNotNull(result.getData());
            Assertions.assertEquals("Apple", result.getData().getName());
            Assertions.assertEquals("https://example.com/apple-logo.png", result.getData().getLogoUrl());
            Assertions.assertEquals("https://www.apple.com", result.getData().getWebsite());
            Assertions.assertEquals(1, result.getData().getSortOrder());
            Assertions.assertTrue(result.getData().getIsActive());
            Assertions.assertNotNull(result.getData().getId());
            Assertions.assertNotNull(result.getData().getCreateTime());
            Assertions.assertNotNull(result.getData().getUpdateTime());
            
            // 记录创建的品牌ID用于后续测试和清理
            createdBrandIds.add(result.getData().getId());
            log.info("品牌创建成功，ID: {}", result.getData().getId());
            
        } catch (Exception e) {
            log.error("解析响应失败", e);
            Assertions.fail("解析响应失败: " + e.getMessage());
        }
    }

    @Test
    @Order(2)
    @DisplayName("2. 创建品牌 - 参数验证失败（名称为空）")
    void testCreateBrand_ValidationFailed() {
        log.info("=== 开始测试：创建品牌 - 参数验证失败 ===");
        
        BrandInfoCreateReqVO reqVO = new BrandInfoCreateReqVO();
        reqVO.setName(""); // 空名称，应该验证失败
        reqVO.setLogoUrl("https://example.com/logo.png");
        reqVO.setWebsite("https://www.example.com");

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<BrandInfoCreateReqVO> request = new HttpEntity<>(reqVO, headers);

        ResponseEntity<String> response = restTemplate.postForEntity(baseUrl, request, String.class);
        
        log.info("响应状态码: {}", response.getStatusCode());
        log.info("响应内容: {}", response.getBody());

        // 验证失败应该返回400或者业务错误码
        Assertions.assertTrue(response.getStatusCode() == HttpStatus.BAD_REQUEST || 
                            response.getStatusCode() == HttpStatus.OK);
    }

    @Test
    @Order(3)
    @DisplayName("3. 创建品牌 - 重复名称")
    void testCreateBrand_DuplicateName() {
        log.info("=== 开始测试：创建品牌 - 重复名称 ===");
        
        // 先创建一个品牌
        BrandInfoCreateReqVO reqVO1 = new BrandInfoCreateReqVO();
        reqVO1.setName("Samsung");
        reqVO1.setLogoUrl("https://example.com/samsung-logo.png");
        reqVO1.setWebsite("https://www.samsung.com");
        reqVO1.setSortOrder(2);
        reqVO1.setIsActive(true);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<BrandInfoCreateReqVO> request1 = new HttpEntity<>(reqVO1, headers);

        ResponseEntity<String> response1 = restTemplate.postForEntity(baseUrl, request1, String.class);
        log.info("第一次创建响应: {}", response1.getStatusCode());
        
        if (response1.getStatusCode() == HttpStatus.OK) {
            try {
                CommonResult<BrandInfoRespVO> result1 = objectMapper.readValue(
                    response1.getBody(), new TypeReference<CommonResult<BrandInfoRespVO>>() {});
                if (result1.getCode() == 0) {
                    createdBrandIds.add(result1.getData().getId());
                }
            } catch (Exception e) {
                log.warn("解析第一次创建响应失败", e);
            }
        }

        // 再次创建相同名称的品牌
        BrandInfoCreateReqVO reqVO2 = new BrandInfoCreateReqVO();
        reqVO2.setName("Samsung"); // 重复名称
        reqVO2.setLogoUrl("https://example.com/samsung-logo2.png");
        reqVO2.setWebsite("https://www.samsung.cn");

        HttpEntity<BrandInfoCreateReqVO> request2 = new HttpEntity<>(reqVO2, headers);
        ResponseEntity<String> response2 = restTemplate.postForEntity(baseUrl, request2, String.class);
        
        log.info("第二次创建响应状态码: {}", response2.getStatusCode());
        log.info("第二次创建响应内容: {}", response2.getBody());

        // 应该返回错误，表示名称已存在
        Assertions.assertEquals(HttpStatus.OK, response2.getStatusCode());
        
        try {
            CommonResult<?> result = objectMapper.readValue(
                response2.getBody(), new TypeReference<CommonResult<?>>() {});
            Assertions.assertNotEquals(0, result.getCode()); // 应该是错误码
            log.info("重复名称验证成功，错误码: {}, 错误信息: {}", result.getCode(), result.getMsg());
        } catch (Exception e) {
            log.error("解析重复名称响应失败", e);
        }
    }

    @Test
    @Order(4)
    @DisplayName("4. 根据ID获取品牌详情 - 正常情况")
    void testGetBrandById_Success() {
        log.info("=== 开始测试：根据ID获取品牌详情 - 正常情况 ===");
        
        // 先创建一个品牌
        BrandInfoCreateReqVO reqVO = new BrandInfoCreateReqVO();
        reqVO.setName("Huawei");
        reqVO.setLogoUrl("https://example.com/huawei-logo.png");
        reqVO.setWebsite("https://www.huawei.com");
        reqVO.setSortOrder(3);
        reqVO.setIsActive(true);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<BrandInfoCreateReqVO> createRequest = new HttpEntity<>(reqVO, headers);

        ResponseEntity<String> createResponse = restTemplate.postForEntity(baseUrl, createRequest, String.class);
        
        Long brandId = null;
        try {
            CommonResult<BrandInfoRespVO> createResult = objectMapper.readValue(
                createResponse.getBody(), new TypeReference<CommonResult<BrandInfoRespVO>>() {});
            brandId = createResult.getData().getId();
            createdBrandIds.add(brandId);
        } catch (Exception e) {
            Assertions.fail("创建品牌失败: " + e.getMessage());
        }

        // 根据ID获取品牌详情
        String getUrl = baseUrl + "/" + brandId;
        ResponseEntity<String> getResponse = restTemplate.getForEntity(getUrl, String.class);
        
        log.info("获取品牌详情响应状态码: {}", getResponse.getStatusCode());
        log.info("获取品牌详情响应内容: {}", getResponse.getBody());

        Assertions.assertEquals(HttpStatus.OK, getResponse.getStatusCode());
        
        try {
            CommonResult<BrandInfoRespVO> result = objectMapper.readValue(
                getResponse.getBody(), new TypeReference<CommonResult<BrandInfoRespVO>>() {});
            
            Assertions.assertEquals(0, result.getCode());
            Assertions.assertNotNull(result.getData());
            Assertions.assertEquals(brandId, result.getData().getId());
            Assertions.assertEquals("Huawei", result.getData().getName());
            log.info("获取品牌详情成功");
            
        } catch (Exception e) {
            log.error("解析获取品牌详情响应失败", e);
            Assertions.fail("解析响应失败: " + e.getMessage());
        }
    }

    @Test
    @Order(5)
    @DisplayName("5. 根据ID获取品牌详情 - 品牌不存在")
    void testGetBrandById_NotFound() {
        log.info("=== 开始测试：根据ID获取品牌详情 - 品牌不存在 ===");

        Long nonExistentId = 999999L;
        String getUrl = baseUrl + "/" + nonExistentId;
        ResponseEntity<String> response = restTemplate.getForEntity(getUrl, String.class);

        log.info("获取不存在品牌响应状态码: {}", response.getStatusCode());
        log.info("获取不存在品牌响应内容: {}", response.getBody());

        Assertions.assertEquals(HttpStatus.OK, response.getStatusCode());

        try {
            CommonResult<?> result = objectMapper.readValue(
                response.getBody(), new TypeReference<CommonResult<?>>() {});
            Assertions.assertNotEquals(0, result.getCode()); // 应该是错误码
            log.info("品牌不存在验证成功，错误码: {}, 错误信息: {}", result.getCode(), result.getMsg());
        } catch (Exception e) {
            log.error("解析品牌不存在响应失败", e);
        }
    }

    @Test
    @Order(6)
    @DisplayName("6. 更新品牌 - 正常情况")
    void testUpdateBrand_Success() {
        log.info("=== 开始测试：更新品牌 - 正常情况 ===");

        // 先创建一个品牌
        BrandInfoCreateReqVO createReqVO = new BrandInfoCreateReqVO();
        createReqVO.setName("Xiaomi");
        createReqVO.setLogoUrl("https://example.com/xiaomi-logo.png");
        createReqVO.setWebsite("https://www.mi.com");
        createReqVO.setSortOrder(4);
        createReqVO.setIsActive(true);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<BrandInfoCreateReqVO> createRequest = new HttpEntity<>(createReqVO, headers);

        ResponseEntity<String> createResponse = restTemplate.postForEntity(baseUrl, createRequest, String.class);

        Long brandId = null;
        try {
            CommonResult<BrandInfoRespVO> createResult = objectMapper.readValue(
                createResponse.getBody(), new TypeReference<CommonResult<BrandInfoRespVO>>() {});
            brandId = createResult.getData().getId();
            createdBrandIds.add(brandId);
        } catch (Exception e) {
            Assertions.fail("创建品牌失败: " + e.getMessage());
        }

        // 更新品牌信息
        BrandInfoUpdateReqVO updateReqVO = new BrandInfoUpdateReqVO();
        updateReqVO.setId(brandId);
        updateReqVO.setName("Xiaomi Updated");
        updateReqVO.setLogoUrl("https://example.com/xiaomi-logo-updated.png");
        updateReqVO.setWebsite("https://www.xiaomi.com");
        updateReqVO.setSortOrder(5);
        updateReqVO.setIsActive(false);

        HttpEntity<BrandInfoUpdateReqVO> updateRequest = new HttpEntity<>(updateReqVO, headers);
        String updateUrl = baseUrl + "/" + brandId;

        ResponseEntity<String> updateResponse = restTemplate.exchange(
            updateUrl, HttpMethod.PUT, updateRequest, String.class);

        log.info("更新品牌响应状态码: {}", updateResponse.getStatusCode());
        log.info("更新品牌响应内容: {}", updateResponse.getBody());

        Assertions.assertEquals(HttpStatus.OK, updateResponse.getStatusCode());

        try {
            CommonResult<BrandInfoRespVO> result = objectMapper.readValue(
                updateResponse.getBody(), new TypeReference<CommonResult<BrandInfoRespVO>>() {});

            Assertions.assertEquals(0, result.getCode());
            Assertions.assertNotNull(result.getData());
            Assertions.assertEquals(brandId, result.getData().getId());
            Assertions.assertEquals("Xiaomi Updated", result.getData().getName());
            Assertions.assertEquals("https://example.com/xiaomi-logo-updated.png", result.getData().getLogoUrl());
            Assertions.assertEquals("https://www.xiaomi.com", result.getData().getWebsite());
            Assertions.assertEquals(5, result.getData().getSortOrder());
            Assertions.assertFalse(result.getData().getIsActive());
            log.info("品牌更新成功");

        } catch (Exception e) {
            log.error("解析更新品牌响应失败", e);
            Assertions.fail("解析响应失败: " + e.getMessage());
        }
    }

    @Test
    @Order(7)
    @DisplayName("7. 更新品牌 - 品牌不存在")
    void testUpdateBrand_NotFound() {
        log.info("=== 开始测试：更新品牌 - 品牌不存在 ===");

        Long nonExistentId = 999999L;
        BrandInfoUpdateReqVO updateReqVO = new BrandInfoUpdateReqVO();
        updateReqVO.setId(nonExistentId);
        updateReqVO.setName("Non Existent Brand");
        updateReqVO.setLogoUrl("https://example.com/logo.png");
        updateReqVO.setWebsite("https://www.example.com");

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<BrandInfoUpdateReqVO> request = new HttpEntity<>(updateReqVO, headers);

        String updateUrl = baseUrl + "/" + nonExistentId;
        ResponseEntity<String> response = restTemplate.exchange(
            updateUrl, HttpMethod.PUT, request, String.class);

        log.info("更新不存在品牌响应状态码: {}", response.getStatusCode());
        log.info("更新不存在品牌响应内容: {}", response.getBody());

        Assertions.assertEquals(HttpStatus.OK, response.getStatusCode());

        try {
            CommonResult<?> result = objectMapper.readValue(
                response.getBody(), new TypeReference<CommonResult<?>>() {});
            Assertions.assertNotEquals(0, result.getCode()); // 应该是错误码
            log.info("更新不存在品牌验证成功，错误码: {}, 错误信息: {}", result.getCode(), result.getMsg());
        } catch (Exception e) {
            log.error("解析更新不存在品牌响应失败", e);
        }
    }

    @Test
    @Order(8)
    @DisplayName("8. 分页查询品牌 - 正常情况")
    void testGetBrandPage_Success() {
        log.info("=== 开始测试：分页查询品牌 - 正常情况 ===");

        // 先创建几个品牌用于分页测试
        for (int i = 1; i <= 3; i++) {
            BrandInfoCreateReqVO reqVO = new BrandInfoCreateReqVO();
            reqVO.setName("TestBrand" + i);
            reqVO.setLogoUrl("https://example.com/test" + i + "-logo.png");
            reqVO.setWebsite("https://www.test" + i + ".com");
            reqVO.setSortOrder(i);
            reqVO.setIsActive(true);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<BrandInfoCreateReqVO> request = new HttpEntity<>(reqVO, headers);

            try {
                ResponseEntity<String> response = restTemplate.postForEntity(baseUrl, request, String.class);
                if (response.getStatusCode() == HttpStatus.OK) {
                    CommonResult<BrandInfoRespVO> result = objectMapper.readValue(
                        response.getBody(), new TypeReference<CommonResult<BrandInfoRespVO>>() {});
                    if (result.getCode() == 0) {
                        createdBrandIds.add(result.getData().getId());
                    }
                }
            } catch (Exception e) {
                log.warn("创建测试品牌 {} 失败: {}", i, e.getMessage());
            }
        }

        // 分页查询
        String pageUrl = baseUrl + "?pageNo=1&pageSize=2";
        ResponseEntity<String> response = restTemplate.getForEntity(pageUrl, String.class);

        log.info("分页查询响应状态码: {}", response.getStatusCode());
        log.info("分页查询响应内容: {}", response.getBody());

        Assertions.assertEquals(HttpStatus.OK, response.getStatusCode());

        try {
            CommonResult<PageResult<BrandInfoRespVO>> result = objectMapper.readValue(
                response.getBody(), new TypeReference<CommonResult<PageResult<BrandInfoRespVO>>>() {});

            Assertions.assertEquals(0, result.getCode());
            Assertions.assertNotNull(result.getData());
            Assertions.assertNotNull(result.getData().getList());
            Assertions.assertTrue(result.getData().getTotal() >= 0);
            log.info("分页查询成功，总数: {}, 当前页数据量: {}",
                result.getData().getTotal(), result.getData().getList().size());

        } catch (Exception e) {
            log.error("解析分页查询响应失败", e);
            Assertions.fail("解析响应失败: " + e.getMessage());
        }
    }

    @Test
    @Order(9)
    @DisplayName("9. 分页查询品牌 - 带筛选条件")
    void testGetBrandPage_WithFilter() {
        log.info("=== 开始测试：分页查询品牌 - 带筛选条件 ===");

        // 创建一个特定名称的品牌用于筛选测试
        BrandInfoCreateReqVO reqVO = new BrandInfoCreateReqVO();
        reqVO.setName("FilterTestBrand");
        reqVO.setLogoUrl("https://example.com/filter-logo.png");
        reqVO.setWebsite("https://www.filter.com");
        reqVO.setSortOrder(10);
        reqVO.setIsActive(true);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<BrandInfoCreateReqVO> request = new HttpEntity<>(reqVO, headers);

        try {
            ResponseEntity<String> createResponse = restTemplate.postForEntity(baseUrl, request, String.class);
            if (createResponse.getStatusCode() == HttpStatus.OK) {
                CommonResult<BrandInfoRespVO> createResult = objectMapper.readValue(
                    createResponse.getBody(), new TypeReference<CommonResult<BrandInfoRespVO>>() {});
                if (createResult.getCode() == 0) {
                    createdBrandIds.add(createResult.getData().getId());
                }
            }
        } catch (Exception e) {
            log.warn("创建筛选测试品牌失败: {}", e.getMessage());
        }

        // 带筛选条件的分页查询
        String filterUrl = baseUrl + "?pageNo=1&pageSize=10&name=FilterTest&isActive=true";
        ResponseEntity<String> response = restTemplate.getForEntity(filterUrl, String.class);

        log.info("带筛选条件分页查询响应状态码: {}", response.getStatusCode());
        log.info("带筛选条件分页查询响应内容: {}", response.getBody());

        Assertions.assertEquals(HttpStatus.OK, response.getStatusCode());

        try {
            CommonResult<PageResult<BrandInfoRespVO>> result = objectMapper.readValue(
                response.getBody(), new TypeReference<CommonResult<PageResult<BrandInfoRespVO>>>() {});

            Assertions.assertEquals(0, result.getCode());
            Assertions.assertNotNull(result.getData());
            log.info("带筛选条件分页查询成功，总数: {}", result.getData().getTotal());

        } catch (Exception e) {
            log.error("解析带筛选条件分页查询响应失败", e);
            Assertions.fail("解析响应失败: " + e.getMessage());
        }
    }

    @Test
    @Order(10)
    @DisplayName("10. 获取所有品牌列表")
    void testGetAllBrands() {
        log.info("=== 开始测试：获取所有品牌列表 ===");

        String listUrl = baseUrl + "/list";
        ResponseEntity<String> response = restTemplate.getForEntity(listUrl, String.class);

        log.info("获取所有品牌列表响应状态码: {}", response.getStatusCode());
        log.info("获取所有品牌列表响应内容: {}", response.getBody());

        Assertions.assertEquals(HttpStatus.OK, response.getStatusCode());

        try {
            CommonResult<List<BrandInfoRespVO>> result = objectMapper.readValue(
                response.getBody(), new TypeReference<CommonResult<List<BrandInfoRespVO>>>() {});

            Assertions.assertEquals(0, result.getCode());
            Assertions.assertNotNull(result.getData());
            log.info("获取所有品牌列表成功，总数: {}", result.getData().size());

        } catch (Exception e) {
            log.error("解析获取所有品牌列表响应失败", e);
            Assertions.fail("解析响应失败: " + e.getMessage());
        }
    }

    @Test
    @Order(11)
    @DisplayName("11. 删除品牌 - 正常情况")
    void testDeleteBrand_Success() {
        log.info("=== 开始测试：删除品牌 - 正常情况 ===");

        // 先创建一个品牌用于删除测试
        BrandInfoCreateReqVO reqVO = new BrandInfoCreateReqVO();
        reqVO.setName("DeleteTestBrand");
        reqVO.setLogoUrl("https://example.com/delete-logo.png");
        reqVO.setWebsite("https://www.delete.com");
        reqVO.setSortOrder(99);
        reqVO.setIsActive(true);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<BrandInfoCreateReqVO> createRequest = new HttpEntity<>(reqVO, headers);

        ResponseEntity<String> createResponse = restTemplate.postForEntity(baseUrl, createRequest, String.class);

        Long brandId = null;
        try {
            CommonResult<BrandInfoRespVO> createResult = objectMapper.readValue(
                createResponse.getBody(), new TypeReference<CommonResult<BrandInfoRespVO>>() {});
            brandId = createResult.getData().getId();
            // 不添加到 createdBrandIds，因为我们要在测试中删除它
        } catch (Exception e) {
            Assertions.fail("创建用于删除的品牌失败: " + e.getMessage());
        }

        // 删除品牌
        String deleteUrl = baseUrl + "/" + brandId;
        ResponseEntity<String> deleteResponse = restTemplate.exchange(
            deleteUrl, HttpMethod.DELETE, null, String.class);

        log.info("删除品牌响应状态码: {}", deleteResponse.getStatusCode());
        log.info("删除品牌响应内容: {}", deleteResponse.getBody());

        Assertions.assertEquals(HttpStatus.OK, deleteResponse.getStatusCode());

        try {
            CommonResult<Boolean> result = objectMapper.readValue(
                deleteResponse.getBody(), new TypeReference<CommonResult<Boolean>>() {});

            Assertions.assertEquals(0, result.getCode());
            Assertions.assertNotNull(result.getData());
            Assertions.assertTrue(result.getData());
            log.info("品牌删除成功");

            // 验证品牌确实被删除了
            ResponseEntity<String> getResponse = restTemplate.getForEntity(deleteUrl.replace("/admin/v1/brands/", "/admin/v1/brands/"), String.class);
            CommonResult<?> getResult = objectMapper.readValue(
                getResponse.getBody(), new TypeReference<CommonResult<?>>() {});
            Assertions.assertNotEquals(0, getResult.getCode()); // 应该返回错误码
            log.info("验证品牌已被删除成功");

        } catch (Exception e) {
            log.error("解析删除品牌响应失败", e);
            Assertions.fail("解析响应失败: " + e.getMessage());
        }
    }

    @Test
    @Order(12)
    @DisplayName("12. 删除品牌 - 品牌不存在")
    void testDeleteBrand_NotFound() {
        log.info("=== 开始测试：删除品牌 - 品牌不存在 ===");

        Long nonExistentId = 999999L;
        String deleteUrl = baseUrl + "/" + nonExistentId;
        ResponseEntity<String> response = restTemplate.exchange(
            deleteUrl, HttpMethod.DELETE, null, String.class);

        log.info("删除不存在品牌响应状态码: {}", response.getStatusCode());
        log.info("删除不存在品牌响应内容: {}", response.getBody());

        Assertions.assertEquals(HttpStatus.OK, response.getStatusCode());

        try {
            CommonResult<?> result = objectMapper.readValue(
                response.getBody(), new TypeReference<CommonResult<?>>() {});
            Assertions.assertNotEquals(0, result.getCode()); // 应该是错误码
            log.info("删除不存在品牌验证成功，错误码: {}, 错误信息: {}", result.getCode(), result.getMsg());
        } catch (Exception e) {
            log.error("解析删除不存在品牌响应失败", e);
        }
    }

    // ==================== 测试结果统计 ====================

    @Test
    @Order(13)
    @DisplayName("13. 测试结果统计")
    void testSummary() {
        log.info("=== 测试结果统计 ===");
        log.info("本次测试共创建了 {} 个品牌用于测试", createdBrandIds.size());
        log.info("所有测试用例执行完成，准备清理测试数据");
    }
}

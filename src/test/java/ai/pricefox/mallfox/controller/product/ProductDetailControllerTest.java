package ai.pricefox.mallfox.controller.product;

import ai.pricefox.mallfox.service.product.ProductDetailService;
import ai.pricefox.mallfox.vo.product.ProductDetailRespVO;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 商品详情接口测试
 */
@SpringBootTest
@AutoConfigureMockMvc
@ActiveProfiles("test")
public class ProductDetailControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ProductDetailService productDetailService;

    @Autowired
    private ObjectMapper objectMapper;

    /**
     * 测试商品详情接口 - 正常情况
     */
    @Test
    public void testGetProductDetail_Success() throws Exception {
        // 使用测试数据
        String skuId = "TEST_SKU_001";
        String sourcePlatform = "Amazon";

        mockMvc.perform(get("/api/v1/products/detail")
                .param("skuId", skuId)
                .param("sourcePlatform", sourcePlatform))
                .andExpect(status().isOk())
                .andExpect(content().contentType("application/json"))
                .andExpect(jsonPath("$.code").value(200));
    }

    /**
     * 测试商品详情接口 - 参数缺失
     */
    @Test
    public void testGetProductDetail_MissingParams() throws Exception {
        mockMvc.perform(get("/api/v1/products/detail")
                .param("skuId", "TEST_SKU_001"))
                .andExpect(status().isBadRequest());
    }

    /**
     * 测试商品详情接口 - 商品不存在
     */
    @Test
    public void testGetProductDetail_NotFound() throws Exception {
        String skuId = "NON_EXISTENT_SKU";
        String sourcePlatform = "Amazon";

        mockMvc.perform(get("/api/v1/products/detail")
                .param("skuId", skuId)
                .param("sourcePlatform", sourcePlatform))
                .andExpect(status().isOk())
                .andExpect(content().contentType("application/json"))
                .andExpect(jsonPath("$.code").value(500))
                .andExpect(jsonPath("$.msg").value("商品详情不存在"));
    }

}

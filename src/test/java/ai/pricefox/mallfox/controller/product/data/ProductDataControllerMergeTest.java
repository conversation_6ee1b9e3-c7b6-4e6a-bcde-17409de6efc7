package ai.pricefox.mallfox.controller.product.data;

import ai.pricefox.mallfox.controller.admin.product.ProductDataController;
import ai.pricefox.mallfox.model.vo.product.data.ProductModelMergeReqVO;
import ai.pricefox.mallfox.model.vo.product.data.ProductModelMergeRespVO;
import ai.pricefox.mallfox.service.product.ProductDataSimplifyService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 商品数据控制器合并接口测试类
 *
 * <AUTHOR>
 * @since 2025-01-09
 */
@WebMvcTest(ProductDataController.class)
class ProductDataControllerMergeTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @MockBean
    private ProductDataSimplifyService productDataSimplifyService;

    @Test
    void testMergeProductModel_Success() throws Exception {
        // 准备测试数据
        ProductModelMergeReqVO reqVO = new ProductModelMergeReqVO();
        reqVO.setPageNo(1);
        reqVO.setPageSize(100);
        reqVO.setSourcePlatform("amazon");
        reqVO.setPreviewMode(false);

        ProductModelMergeRespVO respVO = new ProductModelMergeRespVO();
        respVO.setTotalModelGroups(5);
        respVO.setSuccessCount(15);
        respVO.setSkippedCount(3);
        respVO.setFailedCount(0);
        respVO.setHasMore(false);
        respVO.setMessage("合并完成，共处理5个型号组，成功合并15条记录，跳过3条，失败0条");

        // Mock 服务层方法
        when(productDataSimplifyService.mergeProductModel(any(ProductModelMergeReqVO.class)))
                .thenReturn(respVO);

        // 执行测试
        mockMvc.perform(post("/product/data/mergeModel")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(reqVO)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.data.totalModelGroups").value(5))
                .andExpect(jsonPath("$.data.successCount").value(15))
                .andExpect(jsonPath("$.data.skippedCount").value(3))
                .andExpect(jsonPath("$.data.failedCount").value(0))
                .andExpect(jsonPath("$.data.hasMore").value(false))
                .andExpect(jsonPath("$.data.mergeDetails[0].model").value("iPhone 15 Pro"))
                .andExpect(jsonPath("$.data.mergeDetails[0].basePlatform").value("amazon"))
                .andExpect(jsonPath("$.data.mergeDetails[0].mergedCount").value(3))
                .andExpect(jsonPath("$.data.mergeDetails[0].mergedSpuIds[0]").value("SPU0000000001"))
                .andExpect(jsonPath("$.data.mergeDetails[0].mergedSkuIds[0]").value("SKU0000000001"))
                .andExpect(jsonPath("$.data.mergeDetails[0].oldSpuIds[0]").value("SPU0000000002"))
                .andExpect(jsonPath("$.data.mergeDetails[0].oldSkuIds[0]").value("SKU0000000002"));
    }

    @Test
    void testMergeProductModel_PreviewMode() throws Exception {
        // 准备测试数据 - 预览模式
        ProductModelMergeReqVO reqVO = new ProductModelMergeReqVO();
        reqVO.setPageNo(1);
        reqVO.setPageSize(50);
        reqVO.setPreviewMode(true);
        reqVO.setTargetModel("Samsung Galaxy S24");

        ProductModelMergeRespVO respVO = new ProductModelMergeRespVO();
        respVO.setTotalModelGroups(1);
        respVO.setSuccessCount(2);
        respVO.setSkippedCount(0);
        respVO.setFailedCount(0);
        respVO.setHasMore(false);
        respVO.setMessage("预览完成，共处理1个型号组，成功合并2条记录，跳过0条，失败0条");

        // Mock 服务层方法
        when(productDataSimplifyService.mergeProductModel(any(ProductModelMergeReqVO.class)))
                .thenReturn(respVO);

        // 执行测试
        mockMvc.perform(post("/product/data/mergeModel")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(reqVO)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.data.totalModelGroups").value(1))
                .andExpect(jsonPath("$.data.successCount").value(2));
    }

    @Test
    void testMergeProductModel_ValidationError() throws Exception {
        // 准备无效的测试数据
        ProductModelMergeReqVO reqVO = new ProductModelMergeReqVO();
        reqVO.setPageNo(0); // 无效的页码
        reqVO.setPageSize(2000); // 超过最大限制

        // 执行测试，期望验证失败
        mockMvc.perform(post("/product/data/mergeModel")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(reqVO)))
                .andExpect(status().isBadRequest());
    }
}

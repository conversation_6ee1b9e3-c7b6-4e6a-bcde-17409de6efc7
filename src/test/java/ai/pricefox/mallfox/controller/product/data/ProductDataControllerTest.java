package ai.pricefox.mallfox.controller.product.data;

import ai.pricefox.mallfox.controller.admin.product.ProductDataController;
import ai.pricefox.mallfox.model.vo.product.data.ProductModelProcessReqVO;
import ai.pricefox.mallfox.model.vo.product.data.ProductModelProcessRespVO;
import ai.pricefox.mallfox.service.product.ProductDataSimplifyService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 商品数据控制器测试类
 *
 * <AUTHOR>
 * @since 2025-01-08
 */
@WebMvcTest(ProductDataController.class)
class ProductDataControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @MockBean
    private ProductDataSimplifyService productDataSimplifyService;

    @Test
    void testProcessProductModel_Success() throws Exception {
        // 准备测试数据
        ProductModelProcessReqVO reqVO = new ProductModelProcessReqVO();
        reqVO.setPageNo(1);
        reqVO.setPageSize(100);
        reqVO.setSourcePlatform("amazon");
        reqVO.setOnlyWithBrand(true);
        reqVO.setPreviewMode(false);

        ProductModelProcessRespVO respVO = new ProductModelProcessRespVO();
        respVO.setTotalProcessed(10);
        respVO.setSuccessCount(8);
        respVO.setSkippedCount(1);
        respVO.setFailedCount(1);
        respVO.setHasMore(false);
        respVO.setMessage("处理完成，共处理10条记录，成功8条，跳过1条，失败1条");

        // Mock 服务层方法
        when(productDataSimplifyService.processProductModel(any(ProductModelProcessReqVO.class)))
                .thenReturn(respVO);

        // 执行测试
        mockMvc.perform(post("/product/data/processModel")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(reqVO)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.data.totalProcessed").value(10))
                .andExpect(jsonPath("$.data.successCount").value(8))
                .andExpect(jsonPath("$.data.skippedCount").value(1))
                .andExpect(jsonPath("$.data.failedCount").value(1))
                .andExpect(jsonPath("$.data.hasMore").value(false));
    }

    @Test
    void testProcessProductModel_PreviewMode() throws Exception {
        // 准备测试数据 - 预览模式
        ProductModelProcessReqVO reqVO = new ProductModelProcessReqVO();
        reqVO.setPageNo(1);
        reqVO.setPageSize(50);
        reqVO.setPreviewMode(true);

        ProductModelProcessRespVO respVO = new ProductModelProcessRespVO();
        respVO.setTotalProcessed(5);
        respVO.setSuccessCount(4);
        respVO.setSkippedCount(1);
        respVO.setFailedCount(0);
        respVO.setHasMore(true);
        respVO.setMessage("预览完成，共预览5条记录，成功4条，跳过1条，失败0条");

        // Mock 服务层方法
        when(productDataSimplifyService.processProductModel(any(ProductModelProcessReqVO.class)))
                .thenReturn(respVO);

        // 执行测试
        mockMvc.perform(post("/product/data/processModel")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(reqVO)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.data.totalProcessed").value(5))
                .andExpect(jsonPath("$.data.hasMore").value(true));
    }

    @Test
    void testProcessProductModel_ValidationError() throws Exception {
        // 准备无效的测试数据
        ProductModelProcessReqVO reqVO = new ProductModelProcessReqVO();
        reqVO.setPageNo(0); // 无效的页码
        reqVO.setPageSize(2000); // 超过最大限制

        // 执行测试，期望验证失败
        mockMvc.perform(post("/product/data/processModel")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(reqVO)))
                .andExpect(status().isBadRequest());
    }
}

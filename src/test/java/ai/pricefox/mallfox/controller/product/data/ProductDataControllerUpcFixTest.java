package ai.pricefox.mallfox.controller.product.data;

import ai.pricefox.mallfox.controller.admin.product.ProductDataController;
import ai.pricefox.mallfox.model.vo.product.data.UpcCodeFixReqVO;
import ai.pricefox.mallfox.model.vo.product.data.UpcCodeFixRespVO;
import ai.pricefox.mallfox.service.product.ProductDataSimplifyService;
import ai.pricefox.mallfox.service.product.UnifiedProductDataService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Arrays;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * UPC码修复接口测试
 * 
 * <AUTHOR>
 * @since 2025-01-14
 */
@WebMvcTest(ProductDataController.class)
class ProductDataControllerUpcFixTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private ProductDataSimplifyService productDataSimplifyService;

    @MockBean
    private UnifiedProductDataService unifiedProductDataService;

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    void testFixUpcCodeDoesNotApply_Success() throws Exception {
        // 准备测试数据
        UpcCodeFixReqVO reqVO = new UpcCodeFixReqVO();
        reqVO.setPageNo(1);
        reqVO.setPageSize(100);
        reqVO.setPreviewMode(false);
        reqVO.setSourcePlatform("amazon");
        reqVO.setForceUpdate(false);

        UpcCodeFixRespVO respVO = new UpcCodeFixRespVO();
        respVO.setTotalProcessed(50);
        respVO.setSuccessCount(45);
        respVO.setSkippedCount(3);
        respVO.setFailedCount(2);
        respVO.setNewSpuCount(10);
        respVO.setReusedSpuCount(35);
        respVO.setHasMore(false);
        respVO.setMessage("修复完成，共处理50条记录，成功45条，跳过3条，失败2条");
        respVO.setDetails(Arrays.asList(
                "SKU PK12345 更新成功，新spuid: PP87654321，影响3条offers记录",
                "SKU PK23456 更新成功，新spuid: PP87654322，影响2条offers记录"
        ));

        // Mock 服务层方法
        when(productDataSimplifyService.fixUpcCodeDoesNotApplySpuIds(any(UpcCodeFixReqVO.class)))
                .thenReturn(respVO);

        // 执行测试
        mockMvc.perform(post("/product/data/fixUpcCodeDoesNotApply")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(reqVO)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.data.totalProcessed").value(50))
                .andExpect(jsonPath("$.data.successCount").value(45))
                .andExpect(jsonPath("$.data.skippedCount").value(3))
                .andExpect(jsonPath("$.data.failedCount").value(2))
                .andExpect(jsonPath("$.data.newSpuCount").value(10))
                .andExpect(jsonPath("$.data.reusedSpuCount").value(35))
                .andExpect(jsonPath("$.data.hasMore").value(false));
    }

    @Test
    void testFixUpcCodeDoesNotApply_PreviewMode() throws Exception {
        // 准备测试数据 - 预览模式
        UpcCodeFixReqVO reqVO = new UpcCodeFixReqVO();
        reqVO.setPageNo(1);
        reqVO.setPageSize(50);
        reqVO.setPreviewMode(true);

        UpcCodeFixRespVO respVO = new UpcCodeFixRespVO();
        respVO.setTotalProcessed(25);
        respVO.setSuccessCount(25);
        respVO.setSkippedCount(0);
        respVO.setFailedCount(0);
        respVO.setNewSpuCount(8);
        respVO.setReusedSpuCount(17);
        respVO.setHasMore(true);
        respVO.setMessage("预览完成，共分析25条记录，将生成8个新SPU，重用17个现有SPU");

        // Mock 服务层方法
        when(productDataSimplifyService.fixUpcCodeDoesNotApplySpuIds(any(UpcCodeFixReqVO.class)))
                .thenReturn(respVO);

        // 执行测试
        mockMvc.perform(post("/product/data/fixUpcCodeDoesNotApply")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(reqVO)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.data.totalProcessed").value(25))
                .andExpect(jsonPath("$.data.hasMore").value(true));
    }

    @Test
    void testFixUpcCodeDoesNotApply_ValidationError() throws Exception {
        // 准备无效的测试数据
        UpcCodeFixReqVO reqVO = new UpcCodeFixReqVO();
        reqVO.setPageNo(0); // 无效的页码
        reqVO.setPageSize(2000); // 超过最大限制

        // 执行测试，期望验证失败
        mockMvc.perform(post("/product/data/fixUpcCodeDoesNotApply")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(reqVO)))
                .andExpect(status().isBadRequest());
    }

    @Test
    void testFixUpcCodeDoesNotApply_EmptyResult() throws Exception {
        // 准备测试数据 - 没有需要修复的记录
        UpcCodeFixReqVO reqVO = new UpcCodeFixReqVO();
        reqVO.setPageNo(1);
        reqVO.setPageSize(100);

        UpcCodeFixRespVO respVO = new UpcCodeFixRespVO();
        respVO.setTotalProcessed(0);
        respVO.setSuccessCount(0);
        respVO.setSkippedCount(0);
        respVO.setFailedCount(0);
        respVO.setNewSpuCount(0);
        respVO.setReusedSpuCount(0);
        respVO.setHasMore(false);
        respVO.setMessage("没有找到需要修复的记录");

        // Mock 服务层方法
        when(productDataSimplifyService.fixUpcCodeDoesNotApplySpuIds(any(UpcCodeFixReqVO.class)))
                .thenReturn(respVO);

        // 执行测试
        mockMvc.perform(post("/product/data/fixUpcCodeDoesNotApply")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(reqVO)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.data.totalProcessed").value(0))
                .andExpect(jsonPath("$.data.message").value("没有找到需要修复的记录"));
    }
}

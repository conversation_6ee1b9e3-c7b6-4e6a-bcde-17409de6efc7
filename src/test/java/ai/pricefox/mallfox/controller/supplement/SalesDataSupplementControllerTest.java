package ai.pricefox.mallfox.controller.supplement;

import ai.pricefox.mallfox.controller.admin.supplement.SalesDataSupplementController;
import ai.pricefox.mallfox.model.dto.SalesDataManualSupplementRequest;
import ai.pricefox.mallfox.model.dto.SalesDataSupplementResponse;
import ai.pricefox.mallfox.service.supplement.SalesDataSupplementService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.time.LocalDateTime;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 销量数据补充控制器测试
 * 
 * <AUTHOR>
 * @since 2025-01-20
 */
@WebMvcTest(SalesDataSupplementController.class)
class SalesDataSupplementControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private SalesDataSupplementService salesDataSupplementService;

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    void testManualExecute_Success() throws Exception {
        // 准备测试数据
        SalesDataManualSupplementRequest request = new SalesDataManualSupplementRequest();
        request.setSourcePlatform("amazon");
        request.setStartTime(LocalDateTime.of(2025, 1, 1, 0, 0, 0));
        request.setEndTime(LocalDateTime.of(2025, 1, 10, 23, 59, 59));

        SalesDataSupplementResponse response = SalesDataSupplementResponse.success(
            100, 80, 5, 15, 5000L, "手动补充完成"
        );

        // Mock 服务方法
        when(salesDataSupplementService.executeManualSupplementTask(any(SalesDataManualSupplementRequest.class)))
            .thenReturn(response);

        // 执行测试
        mockMvc.perform(post("/admin/v1/sales-supplement/manual-execute")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.totalProcessed").value(100))
                .andExpect(jsonPath("$.data.successCount").value(80))
                .andExpect(jsonPath("$.data.failureCount").value(5))
                .andExpect(jsonPath("$.data.skippedCount").value(15));
    }

    @Test
    void testManualExecute_ValidationError() throws Exception {
        // 准备无效的测试数据（缺少必填字段）
        SalesDataManualSupplementRequest request = new SalesDataManualSupplementRequest();
        // 不设置必填字段

        // 执行测试
        mockMvc.perform(post("/admin/v1/sales-supplement/manual-execute")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest());
    }

    @Test
    void testGetStatistics_Success() throws Exception {
        String month = "2025-01";
        String statistics = "月份 2025-01 补充记录数: 50";

        when(salesDataSupplementService.getSupplementStatistics(month))
            .thenReturn(statistics);

        mockMvc.perform(get("/admin/v1/sales-supplement/statistics/{month}", month))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").value(statistics));
    }

    @Test
    void testTriggerMonthlyTask_Success() throws Exception {
        String currentMonth = "2025-01";
        String statistics = "月份 2025-01 补充记录数: 30";

        when(salesDataSupplementService.getCurrentMonth()).thenReturn(currentMonth);
        when(salesDataSupplementService.getSupplementStatistics(currentMonth)).thenReturn(statistics);

        mockMvc.perform(post("/admin/v1/sales-supplement/trigger-monthly"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").value("月度补充任务执行完成 - " + statistics));
    }
}

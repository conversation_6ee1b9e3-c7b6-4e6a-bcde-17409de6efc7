package ai.pricefox.mallfox.controller.tracking;

import ai.pricefox.mallfox.service.tracking.FieldTrackingService;
import ai.pricefox.mallfox.vo.tracking.CacheClearRequest;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * FieldTrackingController缓存清除功能测试
 */
@SpringBootTest
@ActiveProfiles("test")
@Slf4j
public class FieldTrackingControllerCacheTest {

    @Autowired
    private WebApplicationContext webApplicationContext;

    @Autowired
    private FieldTrackingService fieldTrackingService;

    @Autowired
    private ObjectMapper objectMapper;

    private MockMvc mockMvc;

    @Test
    void testClearAllCache() throws Exception {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();

        // 准备请求数据
        CacheClearRequest request = new CacheClearRequest();
        request.setClearType("all");

        // 执行请求
        mockMvc.perform(post("/api/v1/field-tracking/clear-cache")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.clearType").value("all"))
                .andExpect(jsonPath("$.data.success").value(true))
                .andExpect(jsonPath("$.data.message").value("已清除所有字段来源缓存"));

        log.info("清除所有缓存测试通过");
    }

    @Test
    void testClearCacheByTable() throws Exception {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();

        // 准备请求数据
        CacheClearRequest request = new CacheClearRequest();
        request.setClearType("table");
        request.setTableName("product_data_offers");

        // 执行请求
        mockMvc.perform(post("/api/v1/field-tracking/clear-cache")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.clearType").value("table"))
                .andExpect(jsonPath("$.data.success").value(true));

        log.info("按表清除缓存测试通过");
    }

    @Test
    void testClearCacheByRecord() throws Exception {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();

        // 准备请求数据
        CacheClearRequest request = new CacheClearRequest();
        request.setClearType("record");
        request.setTableName("product_data_offers");
        request.setRecordId(123L);

        // 执行请求
        mockMvc.perform(post("/api/v1/field-tracking/clear-cache")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.clearType").value("record"))
                .andExpect(jsonPath("$.data.success").value(true))
                .andExpect(jsonPath("$.data.clearedCount").value(1));

        log.info("按记录清除缓存测试通过");
    }

    @Test
    void testClearCacheByPattern() throws Exception {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();

        // 准备请求数据
        CacheClearRequest request = new CacheClearRequest();
        request.setClearType("pattern");
        request.setPattern("product_data_*");

        // 执行请求
        mockMvc.perform(post("/api/v1/field-tracking/clear-cache")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.clearType").value("pattern"))
                .andExpect(jsonPath("$.data.success").value(true));

        log.info("按模式清除缓存测试通过");
    }

    @Test
    void testClearCacheValidation() throws Exception {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();

        // 测试table类型缺少tableName参数
        CacheClearRequest request1 = new CacheClearRequest();
        request1.setClearType("table");

        mockMvc.perform(post("/api/v1/field-tracking/clear-cache")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request1)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(400));

        // 测试record类型缺少参数
        CacheClearRequest request2 = new CacheClearRequest();
        request2.setClearType("record");
        request2.setTableName("product_data_offers");
        // 缺少recordId

        mockMvc.perform(post("/api/v1/field-tracking/clear-cache")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request2)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(400));

        // 测试pattern类型缺少pattern参数
        CacheClearRequest request3 = new CacheClearRequest();
        request3.setClearType("pattern");

        mockMvc.perform(post("/api/v1/field-tracking/clear-cache")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request3)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(400));

        log.info("缓存清除参数验证测试通过");
    }

    @Test
    void testGetCacheStats() throws Exception {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();

        // 执行请求
        mockMvc.perform(get("/api/v1/field-tracking/cache-stats"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").exists())
                .andExpect(jsonPath("$.data.totalCacheCount").exists());

        log.info("获取缓存统计信息测试通过");
    }

    @Test
    void testFieldTrackingServiceCacheMethods() {
        // 测试服务层的缓存方法
        try {
            // 测试清除所有缓存
            fieldTrackingService.clearAllFieldSourceCache();
            log.info("clearAllFieldSourceCache 方法调用成功");

            // 测试按表清除缓存
            long tableCount = fieldTrackingService.clearFieldSourceCacheByTable("product_data_offers");
            log.info("clearFieldSourceCacheByTable 方法调用成功，清除数量: {}", tableCount);

            // 测试按记录清除缓存
            fieldTrackingService.clearFieldSourceCache("product_data_offers", 123L);
            log.info("clearFieldSourceCache 方法调用成功");

            // 测试按模式清除缓存
            long patternCount = fieldTrackingService.clearFieldSourceCacheByPattern("test_*");
            log.info("clearFieldSourceCacheByPattern 方法调用成功，清除数量: {}", patternCount);

            // 测试获取缓存统计
            Map<String, Object> stats = fieldTrackingService.getFieldSourceCacheStats();
            assertNotNull(stats);
            assertTrue(stats.containsKey("totalCacheCount"));
            log.info("getFieldSourceCacheStats 方法调用成功，统计信息: {}", stats);

        } catch (Exception e) {
            log.error("字段追踪服务缓存方法测试失败", e);
            fail("字段追踪服务缓存方法测试失败: " + e.getMessage());
        }
    }

    @Test
    void testInvalidClearType() throws Exception {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();

        // 测试无效的清除类型
        CacheClearRequest request = new CacheClearRequest();
        request.setClearType("invalid_type");

        mockMvc.perform(post("/api/v1/field-tracking/clear-cache")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.msg").value("不支持的清除类型: invalid_type"));

        log.info("无效清除类型测试通过");
    }
}

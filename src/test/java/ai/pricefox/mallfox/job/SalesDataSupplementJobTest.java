package ai.pricefox.mallfox.job;

import ai.pricefox.mallfox.service.supplement.SalesDataSupplementService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import static org.mockito.Mockito.*;

/**
 * 销量数据补充定时任务测试
 * 
 * <AUTHOR>
 * @since 2025-01-14
 */
@ExtendWith(MockitoExtension.class)
public class SalesDataSupplementJobTest {

    @Mock
    private SalesDataSupplementService salesDataSupplementService;

    @InjectMocks
    private SalesDataSupplementJob salesDataSupplementJob;

    @BeforeEach
    void setUp() {
        // 设置配置参数
        ReflectionTestUtils.setField(salesDataSupplementJob, "enabled", true);
    }

    @Test
    void testSupplementSalesData_WhenEnabled() {
        // 准备测试数据
        String currentMonth = "2025-01";
        String statistics = "月份 2025-01 补充记录数: 5";
        
        // Mock 服务方法
        when(salesDataSupplementService.getCurrentMonth()).thenReturn(currentMonth);
        when(salesDataSupplementService.getSupplementStatistics(currentMonth)).thenReturn(statistics);
        doNothing().when(salesDataSupplementService).executeMonthlySupplementTask(currentMonth);
        
        // 执行测试
        salesDataSupplementJob.supplementSalesData();
        
        // 验证调用
        verify(salesDataSupplementService, times(1)).getCurrentMonth();
        verify(salesDataSupplementService, times(1)).executeMonthlySupplementTask(currentMonth);
        verify(salesDataSupplementService, times(1)).getSupplementStatistics(currentMonth);
    }

    @Test
    void testSupplementSalesData_WhenDisabled() {
        // 设置为禁用状态
        ReflectionTestUtils.setField(salesDataSupplementJob, "enabled", false);
        
        // 执行测试
        salesDataSupplementJob.supplementSalesData();
        
        // 验证不会调用服务方法
        verify(salesDataSupplementService, never()).getCurrentMonth();
        verify(salesDataSupplementService, never()).executeMonthlySupplementTask(anyString());
        verify(salesDataSupplementService, never()).getSupplementStatistics(anyString());
    }

    @Test
    void testSupplementSalesData_WhenExceptionOccurs() {
        // 准备测试数据
        String currentMonth = "2025-01";
        
        // Mock 服务方法抛出异常
        when(salesDataSupplementService.getCurrentMonth()).thenReturn(currentMonth);
        doThrow(new RuntimeException("数据库连接异常")).when(salesDataSupplementService)
                .executeMonthlySupplementTask(currentMonth);
        
        // 执行测试（不应该抛出异常）
        salesDataSupplementJob.supplementSalesData();
        
        // 验证调用了getCurrentMonth但执行失败
        verify(salesDataSupplementService, times(1)).getCurrentMonth();
        verify(salesDataSupplementService, times(1)).executeMonthlySupplementTask(currentMonth);
        // 由于异常，不会调用统计方法
        verify(salesDataSupplementService, never()).getSupplementStatistics(anyString());
    }

    @Test
    void testReportSupplementStatistics_WhenEnabled() {
        // 准备测试数据
        String currentMonth = "2025-01";
        String statistics = "月份 2025-01 补充记录数: 3";
        
        // Mock 服务方法
        when(salesDataSupplementService.getCurrentMonth()).thenReturn(currentMonth);
        when(salesDataSupplementService.getSupplementStatistics(currentMonth)).thenReturn(statistics);
        
        // 执行测试
        salesDataSupplementJob.reportSupplementStatistics();
        
        // 验证调用
        verify(salesDataSupplementService, times(1)).getCurrentMonth();
        verify(salesDataSupplementService, times(1)).getSupplementStatistics(currentMonth);
    }

    @Test
    void testReportSupplementStatistics_WhenDisabled() {
        // 设置为禁用状态
        ReflectionTestUtils.setField(salesDataSupplementJob, "enabled", false);
        
        // 执行测试
        salesDataSupplementJob.reportSupplementStatistics();
        
        // 验证不会调用服务方法
        verify(salesDataSupplementService, never()).getCurrentMonth();
        verify(salesDataSupplementService, never()).getSupplementStatistics(anyString());
    }

    @Test
    void testReportSupplementStatistics_WhenExceptionOccurs() {
        // 准备测试数据
        String currentMonth = "2025-01";
        
        // Mock 服务方法抛出异常
        when(salesDataSupplementService.getCurrentMonth()).thenReturn(currentMonth);
        when(salesDataSupplementService.getSupplementStatistics(currentMonth))
                .thenThrow(new RuntimeException("查询异常"));
        
        // 执行测试（不应该抛出异常）
        salesDataSupplementJob.reportSupplementStatistics();
        
        // 验证调用了相关方法
        verify(salesDataSupplementService, times(1)).getCurrentMonth();
        verify(salesDataSupplementService, times(1)).getSupplementStatistics(currentMonth);
    }
}
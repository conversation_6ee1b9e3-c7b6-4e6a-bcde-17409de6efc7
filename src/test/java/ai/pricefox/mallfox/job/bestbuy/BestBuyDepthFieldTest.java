package ai.pricefox.mallfox.job.bestbuy;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 测试BestBuy API depth字段的反序列化问题修复
 */
class BestBuyDepthFieldTest {

    @Test
    void shouldDeserializeDepthFieldWithInches() throws Exception {
        // 模拟原始错误中的JSON数据
        String json = """
                {
                  "sku": "6505186",
                  "name": "Test Product",
                  "depth": "0.32 inches"
                }
                """;

        ObjectMapper objectMapper = new ObjectMapper();
        BestBuyProductDetailResponse response = objectMapper.readValue(json, BestBuyProductDetailResponse.class);

        assertEquals("6505186", response.getSku());
        assertEquals("Test Product", response.getName());
        assertEquals(new BigDecimal("0.32"), response.getDepth());
    }

    @Test
    void shouldHandleVariousDepthFormats() throws Exception {
        // 测试各种可能的depth格式
        String[] testCases = {
            "\"0.32 inches\"",
            "\"1.5 cm\"", 
            "\"2.75\"",
            "\"10 mm\"",
            "\"0.5 feet\""
        };
        
        BigDecimal[] expectedValues = {
            new BigDecimal("0.32"),
            new BigDecimal("1.5"),
            new BigDecimal("2.75"),
            new BigDecimal("10"),
            new BigDecimal("0.5")
        };

        ObjectMapper objectMapper = new ObjectMapper();
        
        for (int i = 0; i < testCases.length; i++) {
            String json = String.format("""
                {
                  "sku": "test%d",
                  "depth": %s
                }
                """, i, testCases[i]);
            
            BestBuyProductDetailResponse response = objectMapper.readValue(json, BestBuyProductDetailResponse.class);
            assertEquals(expectedValues[i], response.getDepth(), 
                "Failed for test case: " + testCases[i]);
        }
    }

    @Test
    void shouldHandleNullDepth() throws Exception {
        String json = """
                {
                  "sku": "6505186",
                  "name": "Test Product",
                  "depth": null
                }
                """;

        ObjectMapper objectMapper = new ObjectMapper();
        BestBuyProductDetailResponse response = objectMapper.readValue(json, BestBuyProductDetailResponse.class);

        assertEquals("6505186", response.getSku());
        assertEquals("Test Product", response.getName());
        assertNull(response.getDepth());
    }
}

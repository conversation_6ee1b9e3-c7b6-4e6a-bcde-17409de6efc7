package ai.pricefox.mallfox.job.bestbuy;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;

import static org.junit.jupiter.api.Assertions.*;

class BestBuyProductDetailResponseTest {

    @Test
    void shouldDeserializeProductWithDimensionUnits() throws Exception {
        String json = """
                {
                  "sku": "6505186",
                  "name": "Apple - iPhone 15 128GB - Pink",
                  "width": "2.82 inches",
                  "height": "5.81 inches", 
                  "depth": "0.31 inches",
                  "weight": "6.02 ounces",
                  "shippingWeight": "1.5 lbs"
                }
                """;

        ObjectMapper objectMapper = new ObjectMapper();
        BestBuyProductDetailResponse response = objectMapper.readValue(json, BestBuyProductDetailResponse.class);

        assertEquals("6505186", response.getSku());
        assertEquals("Apple - iPhone 15 128GB - Pink", response.getName());
        assertEquals(new BigDecimal("2.82"), response.getWidth());
        assertEquals(new BigDecimal("5.81"), response.getHeight());
        assertEquals(new BigDecimal("0.31"), response.getDepth());
        assertEquals(new BigDecimal("6.02"), response.getWeight());
        assertEquals(new BigDecimal("1.5"), response.getShippingWeight());
    }

    @Test
    void shouldHandleNullDimensions() throws Exception {
        String json = """
                {
                  "sku": "6505186",
                  "name": "Apple - iPhone 15 128GB - Pink",
                  "width": null,
                  "height": null,
                  "depth": null,
                  "weight": null,
                  "shippingWeight": null
                }
                """;

        ObjectMapper objectMapper = new ObjectMapper();
        BestBuyProductDetailResponse response = objectMapper.readValue(json, BestBuyProductDetailResponse.class);

        assertEquals("6505186", response.getSku());
        assertEquals("Apple - iPhone 15 128GB - Pink", response.getName());
        assertNull(response.getWidth());
        assertNull(response.getHeight());
        assertNull(response.getDepth());
        assertNull(response.getWeight());
        assertNull(response.getShippingWeight());
    }

    @Test
    void shouldHandleNumericDimensions() throws Exception {
        String json = """
                {
                  "sku": "6505186",
                  "name": "Apple - iPhone 15 128GB - Pink",
                  "width": "2.82",
                  "height": "5.81", 
                  "depth": "0.31",
                  "weight": "6.02",
                  "shippingWeight": "1.5"
                }
                """;

        ObjectMapper objectMapper = new ObjectMapper();
        BestBuyProductDetailResponse response = objectMapper.readValue(json, BestBuyProductDetailResponse.class);

        assertEquals("6505186", response.getSku());
        assertEquals("Apple - iPhone 15 128GB - Pink", response.getName());
        assertEquals(new BigDecimal("2.82"), response.getWidth());
        assertEquals(new BigDecimal("5.81"), response.getHeight());
        assertEquals(new BigDecimal("0.31"), response.getDepth());
        assertEquals(new BigDecimal("6.02"), response.getWeight());
        assertEquals(new BigDecimal("1.5"), response.getShippingWeight());
    }
}
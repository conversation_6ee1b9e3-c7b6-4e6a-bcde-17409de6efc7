package ai.pricefox.mallfox.job.bestbuy;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;

import static org.junit.jupiter.api.Assertions.*;

class DimensionDeserializerTest {

    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        objectMapper = new ObjectMapper();
        SimpleModule module = new SimpleModule();
        module.addDeserializer(BigDecimal.class, new DimensionDeserializer());
        objectMapper.registerModule(module);
    }

    @Test
    void shouldDeserializeStringWithUnits() throws Exception {
        String json = "\"0.61 inches\"";
        BigDecimal result = objectMapper.readValue(json, BigDecimal.class);
        assertEquals(new BigDecimal("0.61"), result);
    }

    @Test
    void shouldDeserializeStringWithDifferentUnits() throws Exception {
        String json = "\"5.5 cm\"";
        BigDecimal result = objectMapper.readValue(json, BigDecimal.class);
        assertEquals(new BigDecimal("5.5"), result);
    }

    @Test
    void shouldDeserializePlainNumber() throws Exception {
        String json = "\"123.45\"";
        BigDecimal result = objectMapper.readValue(json, BigDecimal.class);
        assertEquals(new BigDecimal("123.45"), result);
    }

    @Test
    void shouldDeserializeIntegerWithUnits() throws Exception {
        String json = "\"10 lbs\"";
        BigDecimal result = objectMapper.readValue(json, BigDecimal.class);
        assertEquals(new BigDecimal("10"), result);
    }

    @Test
    void shouldReturnNullForNullValue() throws Exception {
        String json = "null";
        BigDecimal result = objectMapper.readValue(json, BigDecimal.class);
        assertNull(result);
    }

    @Test
    void shouldReturnNullForEmptyString() throws Exception {
        String json = "\"\"";
        BigDecimal result = objectMapper.readValue(json, BigDecimal.class);
        assertNull(result);
    }

    @Test
    void shouldReturnNullForInvalidString() throws Exception {
        String json = "\"invalid\"";
        BigDecimal result = objectMapper.readValue(json, BigDecimal.class);
        assertNull(result);
    }

    @Test
    void shouldHandleExtraWhitespace() throws Exception {
        String json = "\"  12.34 inches  \"";
        BigDecimal result = objectMapper.readValue(json, BigDecimal.class);
        assertEquals(new BigDecimal("12.34"), result);
    }
}
package ai.pricefox.mallfox.service.integration;

import ai.pricefox.mallfox.service.integration.impl.BestBuyDataServiceImpl;
import org.junit.jupiter.api.Test;

import java.io.*;
import java.time.LocalDate;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 测试BestBuySearchProgress的序列化功能
 */
class BestBuySearchProgressSerializationTest {

    @Test
    void shouldSerializeAndDeserializeBestBuySearchProgress() throws Exception {
        // 创建测试对象
        BestBuyDataServiceImpl.BestBuySearchProgress progress = new BestBuyDataServiceImpl.BestBuySearchProgress();
        progress.setOffset(100);
        progress.setRequestCount(50);
        progress.setLastRequestDate(LocalDate.of(2025, 8, 4));

        // 序列化
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        ObjectOutputStream oos = new ObjectOutputStream(baos);
        oos.writeObject(progress);
        oos.close();

        // 反序列化
        ByteArrayInputStream bais = new ByteArrayInputStream(baos.toByteArray());
        ObjectInputStream ois = new ObjectInputStream(bais);
        BestBuyDataServiceImpl.BestBuySearchProgress deserializedProgress = 
            (BestBuyDataServiceImpl.BestBuySearchProgress) ois.readObject();
        ois.close();

        // 验证数据
        assertEquals(100, deserializedProgress.getOffset());
        assertEquals(50, deserializedProgress.getRequestCount());
        assertEquals(LocalDate.of(2025, 8, 4), deserializedProgress.getLastRequestDate());
    }

    @Test
    void shouldImplementSerializable() {
        BestBuyDataServiceImpl.BestBuySearchProgress progress = new BestBuyDataServiceImpl.BestBuySearchProgress();
        assertTrue(progress instanceof Serializable, "BestBuySearchProgress should implement Serializable");
    }

    @Test
    void shouldHaveSerialVersionUID() throws Exception {
        // 验证类有serialVersionUID字段
        Class<?> clazz = BestBuyDataServiceImpl.BestBuySearchProgress.class;
        java.lang.reflect.Field serialVersionUIDField = clazz.getDeclaredField("serialVersionUID");
        
        assertNotNull(serialVersionUIDField);
        assertEquals(long.class, serialVersionUIDField.getType());
        assertEquals(java.lang.reflect.Modifier.STATIC | java.lang.reflect.Modifier.FINAL, 
                    serialVersionUIDField.getModifiers() & (java.lang.reflect.Modifier.STATIC | java.lang.reflect.Modifier.FINAL));
    }
}

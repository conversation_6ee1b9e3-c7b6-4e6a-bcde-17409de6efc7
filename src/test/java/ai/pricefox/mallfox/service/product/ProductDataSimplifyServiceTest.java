package ai.pricefox.mallfox.service.product;

import ai.pricefox.mallfox.service.product.impl.ProductDataSimplifyServiceImpl;
import org.junit.jupiter.api.Test;
import org.springframework.test.util.ReflectionTestUtils;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 商品数据简化服务测试类
 * 主要测试型号处理逻辑
 *
 * <AUTHOR>
 * @since 2025-01-08
 */
class ProductDataSimplifyServiceTest {

    private final ProductDataSimplifyServiceImpl service = new ProductDataSimplifyServiceImpl();

    @Test
    void testProcessModelString_RemoveBrand() {
        // 测试去除品牌名称（不再进行首字母大写格式化）
        String result = invokeProcessModelString("Apple iPhone 15 Pro", "Apple");
        assertEquals("iPhone 15 Pro", result);
    }

    @Test
    void testProcessModelString_CaseInsensitive() {
        // 测试不区分大小写
        String result = invokeProcessModelString("SAMSUNG Galaxy S24", "samsung");
        assertEquals("Galaxy S24", result);
    }

    @Test
    void testProcessModelString_WithSpaces() {
        // 测试包含空格的品牌（5不是5G，不会被去除）
        String result = invokeProcessModelString("Sony PlayStation 5", "Sony");
        assertEquals("PlayStation 5", result);
    }

    @Test
    void testProcessModelString_BrandNotFound() {
        // 测试品牌不存在于型号中
        String result = invokeProcessModelString("iPhone 15 Pro", "Samsung");
        assertNull(result);
    }

    @Test
    void testProcessModelString_EmptyInput() {
        // 测试空输入
        String result = invokeProcessModelString("", "Apple");
        assertNull(result);
        
        result = invokeProcessModelString("iPhone 15", "");
        assertNull(result);
    }

    @Test
    void testFormatModelString_NetworkTypeRemoval() {
        // 测试网络类型去除（不再进行大小写格式化）
        String result = invokeFormatModelString("galaxy s24 ultra 5g");
        assertEquals("galaxy s24 ultra", result);
    }

    @Test
    void testFormatModelString_PreserveOriginalCase() {
        // 测试保持原始大小写
        String result = invokeFormatModelString("iPHONE 15 PRO mAx");
        assertEquals("iPHONE 15 PRO mAx", result);
    }



    @Test
    void testCompleteWorkflow() {
        // 测试完整的处理流程（保持原始大小写）
        String result = invokeProcessModelString("APPLE iPhone_15-Pro+Max", "Apple");
        assertEquals("iPhone_15-Pro+Max", result);
    }

    @Test
    void testComplexBrandRemoval() {
        // 测试复杂的品牌去除场景
        String result = invokeProcessModelString("Samsung Galaxy Samsung Note", "Samsung");
        // 应该去除第一个Samsung，保留Galaxy Note
        assertEquals("Galaxy Note", result);
    }

    @Test
    void testMultiWordBrand() {
        // 测试多单词品牌（5不是5G，不会被去除）
        String result = invokeProcessModelString("Sony PlayStation 5 Pro Console", "Sony PlayStation");
        assertEquals("5 Pro Console", result);
    }

    @Test
    void testBrandWithSpaces() {
        // 测试品牌名包含空格的情况
        String result = invokeProcessModelString("Apple iPhone 15 Pro Max", "Apple");
        assertEquals("iPhone 15 Pro Max", result);
    }

    @Test
    void testBrandAtEnd() {
        // 测试品牌在末尾的情况
        String result = invokeProcessModelString("iPhone 15 Pro Apple", "Apple");
        assertEquals("iPhone 15 Pro", result);
    }

    @Test
    void testBrandInMiddle() {
        // 测试品牌在中间的情况
        String result = invokeProcessModelString("Pro Apple iPhone 15", "Apple");
        assertEquals("Pro iPhone 15", result);
    }

    @Test
    void testCaseInsensitiveBrandRemoval() {
        // 测试不区分大小写的品牌去除
        String result = invokeProcessModelString("APPLE iphone 15 pro", "apple");
        assertEquals("iphone 15 pro", result);
    }

    @Test
    void testPartialBrandMatch() {
        // 测试部分匹配不应该被去除
        String result = invokeProcessModelString("Applewood iPhone Case", "Apple");
        // Applewood 不应该被匹配为 Apple
        assertEquals("Applewood iPhone Case", result);
    }

    @Test
    void testMultipleSpaces() {
        // 测试多个空格的情况
        String result = invokeProcessModelString("Apple   iPhone    15   Pro", "Apple");
        assertEquals("iPhone 15 Pro", result);
    }

    @Test
    void testBrandWithDifferentSpacing() {
        // 测试品牌和型号中空格不一致的情况（5不是5G，不会被去除）
        String result = invokeProcessModelString("SonyPlayStation 5", "Sony PlayStation");
        // 保持原始大小写和格式
        assertEquals("SonyPlayStation 5", result);
    }

    @Test
    void testOnlyBrandName() {
        // 测试型号只有品牌名的情况
        String result = invokeProcessModelString("Apple", "Apple");
        assertNull(result); // 去除品牌后为空，应该返回null
    }

    @Test
    void testBrandLongerThanModel() {
        // 测试品牌名比型号长的情况
        String result = invokeProcessModelString("iPhone", "Apple iPhone Pro");
        assertNull(result); // 品牌不存在于型号中
    }

    @Test
    void testSpecialCharactersInBrand() {
        // 测试品牌名包含特殊字符的情况
        String result = invokeProcessModelString("AT&T iPhone 15", "AT&T");
        assertEquals("iPhone 15", result);
    }

    @Test
    void testPlusSignPreservation() {
        // 测试+号应该被保留（不再进行大小写格式化）
        String result = invokeFormatModelString("iPhone 15 Pro+Max");
        assertEquals("iPhone 15 Pro+Max", result);
    }

    @Test
    void testRemove5G_LowerCase() {
        // 测试去除5g（小写）
        String result = invokeFormatModelString("Samsung Galaxy S24 5g");
        assertEquals("Samsung Galaxy S24", result);
    }

    @Test
    void testRemove5G_UpperCase() {
        // 测试去除5G（大写）
        String result = invokeFormatModelString("iPhone 15 Pro 5G");
        assertEquals("iPhone 15 Pro", result);
    }

    @Test
    void testRemove5G_MixedCase() {
        // 测试去除5G（混合大小写）
        String result = invokeFormatModelString("OnePlus 12 5g Pro");
        assertEquals("OnePlus 12 Pro", result);
    }

    @Test
    void testRemove5G_Multiple() {
        // 测试去除多个5G
        String result = invokeFormatModelString("Phone 5G Ultra 5g");
        assertEquals("Phone Ultra", result);
    }

    @Test
    void testRemove5G_NotPartOfWord() {
        // 测试不应该去除作为单词一部分的5G
        String result = invokeFormatModelString("Model A5G123");
        assertEquals("Model A5G123", result); // A5G123作为一个整体，不应该被处理
    }

    @Test
    void testRemove3G_LowerCase() {
        // 测试去除3g（小写）
        String result = invokeFormatModelString("Nokia 3310 3g");
        assertEquals("Nokia 3310", result);
    }

    @Test
    void testRemove3G_UpperCase() {
        // 测试去除3G（大写）
        String result = invokeFormatModelString("iPhone 3G Classic");
        assertEquals("iPhone Classic", result);
    }

    @Test
    void testRemove4G_LowerCase() {
        // 测试去除4g（小写）
        String result = invokeFormatModelString("Samsung Galaxy S10 4g");
        assertEquals("Samsung Galaxy S10", result);
    }

    @Test
    void testRemove4G_UpperCase() {
        // 测试去除4G（大写）
        String result = invokeFormatModelString("OnePlus 8 4G Pro");
        assertEquals("OnePlus 8 Pro", result);
    }

    @Test
    void testRemoveAllNetworkTypes() {
        // 测试同时去除3G、4G、5G
        String result = invokeFormatModelString("Phone 3G 4G 5G Ultra");
        assertEquals("Phone Ultra", result);
    }

    @Test
    void testNetworkTypes_NotPartOfWord() {
        // 测试不应该去除作为单词一部分的网络类型
        String result = invokeFormatModelString("Model A3G4G5G123");
        assertEquals("Model A3G4G5G123", result); // 作为一个整体，不应该被处理
    }

    // 辅助方法：通过反射调用私有方法
    private String invokeProcessModelString(String originalModel, String brand) {
        return (String) ReflectionTestUtils.invokeMethod(service, "processModelString", originalModel, brand);
    }

    private String invokeFormatModelString(String model) {
        return (String) ReflectionTestUtils.invokeMethod(service, "formatModelString", model);
    }


}

package ai.pricefox.mallfox.service.product;

import ai.pricefox.mallfox.model.param.ProductDataSearchRequest;
import ai.pricefox.mallfox.model.response.ProductDataViewResponse;
import ai.pricefox.mallfox.model.response.SkuMasterViewDTO;
import ai.pricefox.mallfox.model.response.SpuGroupViewResponse;
import ai.pricefox.mallfox.service.product.impl.ProductViewGroupedService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * ProductViewGroupedService字段来源信息修复测试
 * 验证表名和记录ID匹配问题的修复效果
 */
@SpringBootTest
@ActiveProfiles("test")
@Slf4j
public class ProductViewGroupedServiceFieldSourcesFixTest {

    @Autowired
    private ProductViewGroupedService productViewGroupedService;

    @Test
    void testSkuListFieldSourcesIdMapping() {
        // 测试二级接口：验证SKU列表的字段来源ID映射是否正确
        ProductDataSearchRequest request = new ProductDataSearchRequest();
        request.setId(1L); // 假设存在ID为1的记录
        request.setPageNo(1);
        request.setPageSize(5);

        try {
            IPage<SpuGroupViewResponse> result = productViewGroupedService.getSkuListBySpuId(request);
            
            if (result != null && !result.getRecords().isEmpty()) {
                log.info("二级接口测试：获取到 {} 个SKU记录", result.getRecords().size());
                
                for (SpuGroupViewResponse sku : result.getRecords()) {
                    log.info("SKU {} - simplifyId: {}, offerId: {}",
                        sku.getSkuId(), sku.getSimplifyId(), sku.getOfferId());

                    // 验证ID字段不为null
                    assertNotNull(sku.getSimplifyId(), "simplifyId不应为null");
                    assertNotNull(sku.getOfferId(), "offerId不应为null");

                    // 验证ID字段不相等（它们应该来自不同的表）
                    assertNotEquals(sku.getSimplifyId(), sku.getOfferId(),
                        "simplifyId和offerId应该不相等，因为它们来自不同的表");

                    // 验证字段来源信息
                    Map<String, Integer> fieldSources = sku.getFieldSources();
                    assertNotNull(fieldSources, "fieldSources不应为null");

                    log.info("SKU {} 的字段来源信息: {} 个字段",
                        sku.getSkuId(), fieldSources.size());

                    // 如果有字段来源信息，验证数据格式
                    for (Map.Entry<String, Integer> entry : fieldSources.entrySet()) {
                        String fieldName = entry.getKey();
                        Integer dataSource = entry.getValue();

                        assertNotNull(fieldName, "字段名不应为null");
                        assertNotNull(dataSource, "数据来源不应为null");
                        assertTrue(dataSource == 1 || dataSource == 2,
                            "数据来源应为1(爬虫)或2(API)");

                        log.debug("字段: {}, 数据来源: {}", fieldName, dataSource);
                    }
                }
            } else {
                log.info("二级接口测试：未获取到SKU记录，可能是测试数据不存在");
            }
        } catch (Exception e) {
            log.warn("二级接口测试失败，可能是测试数据不存在: {}", e.getMessage());
        }
    }

    @Test
    void testPlatformOffersFieldSourcesIdMapping() {
        // 测试三级接口：验证平台商品详情的字段来源ID映射是否正确
        Long offerId = 1L; // 假设存在ID为1的offer记录

        try {
            List<ProductDataViewResponse> offers = productViewGroupedService.getAllPlatformOffersBySkuId(offerId);
            
            if (offers != null && !offers.isEmpty()) {
                log.info("三级接口测试：获取到 {} 个平台商品详情记录", offers.size());
                
                for (ProductDataViewResponse offer : offers) {
                    log.info("平台商品详情 {} - simplifyId: {}, offerId: {}", 
                        offer.getSkuId(), offer.getSimplifyId(), offer.getOfferId());
                    
                    // 验证ID字段不为null
                    assertNotNull(offer.getSimplifyId(), "simplifyId不应为null");
                    assertNotNull(offer.getOfferId(), "offerId不应为null");
                    
                    // 验证ID字段不相等（它们应该来自不同的表）
                    assertNotEquals(offer.getSimplifyId(), offer.getOfferId(), 
                        "simplifyId和offerId应该不相等，因为它们来自不同的表");
                    
                    // 验证字段来源信息
                    Map<String, Integer> fieldSources = offer.getFieldSources();
                    assertNotNull(fieldSources, "fieldSources不应为null");
                    
                    log.info("平台商品详情 {} 的字段来源信息: {} 个字段", 
                        offer.getSkuId(), fieldSources.size());
                    
                    // 如果有字段来源信息，验证数据格式
                    for (Map.Entry<String, Integer> entry : fieldSources.entrySet()) {
                        String fieldName = entry.getKey();
                        Integer dataSource = entry.getValue();
                        
                        assertNotNull(fieldName, "字段名不应为null");
                        assertNotNull(dataSource, "数据来源不应为null");
                        assertTrue(dataSource == 1 || dataSource == 2, 
                            "数据来源应为1(爬虫)或2(API)");
                        
                        log.debug("字段: {}, 数据来源: {}", fieldName, dataSource);
                    }
                }
            } else {
                log.info("三级接口测试：未获取到平台商品详情记录，可能是测试数据不存在");
            }
        } catch (Exception e) {
            log.warn("三级接口测试失败，可能是测试数据不存在: {}", e.getMessage());
        }
    }

    @Test
    void testFieldSourcesConsistency() {
        // 测试字段来源信息的一致性
        log.info("测试字段来源信息一致性");
        
        try {
            // 测试一级接口（SPU列表）
            ProductDataSearchRequest request = new ProductDataSearchRequest();
            request.setPageNo(1);
            request.setPageSize(3);
            
            IPage<SpuGroupViewResponse> spuResult = productViewGroupedService.getSpuGroupedView(request);
            assertNotNull(spuResult, "SPU列表结果不应为null");
            
            log.info("一级接口测试通过：获取到 {} 个SPU记录", spuResult.getRecords().size());
            
            // 验证SPU记录的字段来源信息结构
            for (SpuGroupViewResponse spu : spuResult.getRecords()) {
                assertNotNull(spu.getFieldSources(), "SPU的fieldSources字段不应为null");
                
                log.info("SPU {} - id: {}, offerId: {}, 字段来源数量: {}", 
                    spu.getSpuId(), spu.getId(), spu.getOfferId(), spu.getFieldSources().size());
                
                // 验证ID字段的合理性
                if (spu.getId() != null && spu.getOfferId() != null) {
                    // 在SPU列表中，id和offerId可能相等（都来自offers表），这是正常的
                    log.debug("SPU {} 的ID映射正常", spu.getSpuId());
                }
            }
            
        } catch (Exception e) {
            log.warn("字段来源信息一致性测试失败: {}", e.getMessage());
        }
    }

    @Test
    void testIdMappingLogic() {
        // 专门测试ID映射逻辑的正确性
        log.info("测试ID映射逻辑的正确性");
        
        // 这个测试主要验证：
        // 1. simplifyId 用于查询 product_data_simplify 表的字段来源
        // 2. offerId 用于查询 product_data_offers 表的字段来源
        // 3. 两个ID应该来自不同的表，因此通常不相等
        
        ProductDataSearchRequest request = new ProductDataSearchRequest();
        request.setPageNo(1);
        request.setPageSize(2);
        
        try {
            // 测试二级接口
            request.setId(1L);
            IPage<SpuGroupViewResponse> skuResult = productViewGroupedService.getSkuListBySpuId(request);
            
            if (skuResult != null && !skuResult.getRecords().isEmpty()) {
                for (SpuGroupViewResponse sku : skuResult.getRecords()) {
                    log.info("验证SKU ID映射 - SKU: {}, simplifyId: {}, offerId: {}",
                        sku.getSkuId(), sku.getSimplifyId(), sku.getOfferId());

                    // 记录ID映射信息用于调试
                    if (sku.getSimplifyId() != null && sku.getOfferId() != null) {
                        if (sku.getSimplifyId().equals(sku.getOfferId())) {
                            log.warn("警告：SKU {} 的 simplifyId 和 offerId 相等，这可能表示数据问题",
                                sku.getSkuId());
                        } else {
                            log.info("正常：SKU {} 的 simplifyId 和 offerId 不相等，ID映射正确",
                                sku.getSkuId());
                        }
                    }
                }
            }
            
            // 测试三级接口
            List<ProductDataViewResponse> offers = productViewGroupedService.getAllPlatformOffersBySkuId(1L);
            
            if (offers != null && !offers.isEmpty()) {
                for (ProductDataViewResponse offer : offers) {
                    log.info("验证平台商品详情ID映射 - SKU: {}, simplifyId: {}, offerId: {}", 
                        offer.getSkuId(), offer.getSimplifyId(), offer.getOfferId());
                    
                    // 记录ID映射信息用于调试
                    if (offer.getSimplifyId() != null && offer.getOfferId() != null) {
                        if (offer.getSimplifyId().equals(offer.getOfferId())) {
                            log.warn("警告：平台商品详情 {} 的 simplifyId 和 offerId 相等，这可能表示数据问题", 
                                offer.getSkuId());
                        } else {
                            log.info("正常：平台商品详情 {} 的 simplifyId 和 offerId 不相等，ID映射正确", 
                                offer.getSkuId());
                        }
                    }
                }
            }
            
        } catch (Exception e) {
            log.warn("ID映射逻辑测试失败: {}", e.getMessage());
        }
    }
}

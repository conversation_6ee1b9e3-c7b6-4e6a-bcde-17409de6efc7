package ai.pricefox.mallfox.service.product;

import ai.pricefox.mallfox.model.param.ProductDataSearchRequest;
import ai.pricefox.mallfox.model.response.ProductDataViewResponse;
import ai.pricefox.mallfox.model.response.SpuGroupViewResponse;
import ai.pricefox.mallfox.service.product.impl.ProductViewGroupedService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * ProductViewGroupedService字段来源信息测试
 * 验证二级和三级接口的字段来源信息附加功能
 */
@SpringBootTest
@ActiveProfiles("test")
@Slf4j
public class ProductViewGroupedServiceFieldSourcesTest {

    @Autowired
    private ProductViewGroupedService productViewGroupedService;

    @Test
    void testSkuListFieldSourcesAttachment() {
        // 测试二级接口：根据SPU ID获取SKU列表
        ProductDataSearchRequest request = new ProductDataSearchRequest();
        request.setId(1L); // 假设存在ID为1的记录
        request.setPageNo(1);
        request.setPageSize(10);

        try {
            IPage<SpuGroupViewResponse> result = productViewGroupedService.getSkuListBySpuId(request);
            
            if (result != null && !result.getRecords().isEmpty()) {
                log.info("二级接口测试：获取到 {} 个SKU记录", result.getRecords().size());
                
                for (SpuGroupViewResponse sku : result.getRecords()) {
                    Map<String, Integer> fieldSources = sku.getFieldSources();
                    
                    // 验证fieldSources字段不为null
                    assertNotNull(fieldSources, "SKU的fieldSources字段不应为null");
                    
                    log.info("SKU {} 的字段来源信息: {} 个字段", 
                        sku.getSkuId(), fieldSources.size());
                    
                    // 如果有字段来源信息，验证数据格式
                    if (!fieldSources.isEmpty()) {
                        for (Map.Entry<String, Integer> entry : fieldSources.entrySet()) {
                            String fieldName = entry.getKey();
                            Integer dataSource = entry.getValue();
                            
                            assertNotNull(fieldName, "字段名不应为null");
                            assertNotNull(dataSource, "数据来源不应为null");
                            assertTrue(dataSource == 1 || dataSource == 2, 
                                "数据来源应为1(爬虫)或2(API)");
                            
                            log.debug("字段: {}, 数据来源: {}", fieldName, dataSource);
                        }
                    }
                }
            } else {
                log.info("二级接口测试：未获取到SKU记录，可能是测试数据不存在");
            }
        } catch (Exception e) {
            log.warn("二级接口测试失败，可能是测试数据不存在: {}", e.getMessage());
            // 不抛出异常，因为测试环境可能没有相应的数据
        }
    }

    @Test
    void testPlatformOffersFieldSourcesAttachment() {
        // 测试三级接口：获取所有平台商品SKU详情列表
        Long offerId = 1L; // 假设存在ID为1的offer记录

        try {
            List<ProductDataViewResponse> offers = productViewGroupedService.getAllPlatformOffersBySkuId(offerId);
            
            if (offers != null && !offers.isEmpty()) {
                log.info("三级接口测试：获取到 {} 个平台商品详情记录", offers.size());
                
                for (ProductDataViewResponse offer : offers) {
                    Map<String, Integer> fieldSources = offer.getFieldSources();
                    
                    // 验证fieldSources字段不为null
                    assertNotNull(fieldSources, "平台商品详情的fieldSources字段不应为null");
                    
                    log.info("平台商品详情 {} 的字段来源信息: {} 个字段", 
                        offer.getSkuId(), fieldSources.size());
                    
                    // 如果有字段来源信息，验证数据格式
                    if (!fieldSources.isEmpty()) {
                        for (Map.Entry<String, Integer> entry : fieldSources.entrySet()) {
                            String fieldName = entry.getKey();
                            Integer dataSource = entry.getValue();
                            
                            assertNotNull(fieldName, "字段名不应为null");
                            assertNotNull(dataSource, "数据来源不应为null");
                            assertTrue(dataSource == 1 || dataSource == 2, 
                                "数据来源应为1(爬虫)或2(API)");
                            
                            log.debug("字段: {}, 数据来源: {}", fieldName, dataSource);
                        }
                    }
                }
            } else {
                log.info("三级接口测试：未获取到平台商品详情记录，可能是测试数据不存在");
            }
        } catch (Exception e) {
            log.warn("三级接口测试失败，可能是测试数据不存在: {}", e.getMessage());
            // 不抛出异常，因为测试环境可能没有相应的数据
        }
    }

    @Test
    void testFieldSourcesStructure() {
        // 测试字段来源信息的基本结构
        log.info("测试字段来源信息结构完整性");
        
        // 这个测试主要验证方法调用不会抛出异常
        ProductDataSearchRequest request = new ProductDataSearchRequest();
        request.setPageNo(1);
        request.setPageSize(5);
        
        try {
            // 测试一级接口（SPU列表）
            IPage<SpuGroupViewResponse> spuResult = productViewGroupedService.getSpuGroupedView(request);
            assertNotNull(spuResult, "SPU列表结果不应为null");
            log.info("一级接口测试通过：获取到 {} 个SPU记录", spuResult.getRecords().size());
            
            // 验证SPU记录的fieldSources字段
            for (SpuGroupViewResponse spu : spuResult.getRecords()) {
                assertNotNull(spu.getFieldSources(), "SPU的fieldSources字段不应为null");
            }
            
        } catch (Exception e) {
            log.warn("字段来源信息结构测试失败: {}", e.getMessage());
        }
    }
}

package ai.pricefox.mallfox.service.supplement;

import ai.pricefox.mallfox.domain.product.ProductDataOffers;
import ai.pricefox.mallfox.domain.supplement.SalesDataSupplementRecord;
import ai.pricefox.mallfox.mapper.product.ProductDataOffersMapper;
import ai.pricefox.mallfox.mapper.product.ProductDataReviewsMapper;
import ai.pricefox.mallfox.mapper.product.ProductDataSimplifyMapper;
import ai.pricefox.mallfox.mapper.supplement.SalesDataSupplementRecordMapper;
import ai.pricefox.mallfox.model.dto.SalesDataManualSupplementRequest;
import ai.pricefox.mallfox.model.dto.SalesDataSupplementResponse;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * 销量数据补充服务测试
 * 
 * <AUTHOR>
 * @since 2025-01-14
 */
@ExtendWith(MockitoExtension.class)
public class SalesDataSupplementServiceTest {

    @Mock
    private ProductDataOffersMapper productDataOffersMapper;

    @Mock
    private ProductDataSimplifyMapper productDataSimplifyMapper;

    @Mock
    private ProductDataReviewsMapper productDataReviewsMapper;

    @Mock
    private SalesDataSupplementRecordMapper supplementRecordMapper;

    @InjectMocks
    private SalesDataSupplementService salesDataSupplementService;

    @BeforeEach
    void setUp() {
        // 设置配置参数
        ReflectionTestUtils.setField(salesDataSupplementService, "reviewToSalesRatio", new BigDecimal("0.03"));
        ReflectionTestUtils.setField(salesDataSupplementService, "batchSize", 1000);
        ReflectionTestUtils.setField(salesDataSupplementService, "enabled", true);
    }

    @Test
    void testExecuteMonthlySupplementTask_Success() {
        // 准备测试数据
        String currentMonth = "2025-01";
        
        // Mock 历史记录查询
        List<Long> historyOfferIds = Arrays.asList(1L, 2L);
        when(supplementRecordMapper.selectAllDistinctOfferIds()).thenReturn(historyOfferIds);
        
        // Mock 空销量offers查询
        ProductDataOffers offer1 = createMockOffer(3L, "SKU001", "SPU001", null);
        ProductDataOffers offer2 = createMockOffer(4L, "SKU002", "SPU002", "");
        List<ProductDataOffers> emptyOffers = Arrays.asList(offer1, offer2);
        when(productDataOffersMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(emptyOffers);
        
        // Mock offer查询和review_number查询
        when(productDataOffersMapper.selectById(anyLong())).thenReturn(offer1);
        when(productDataSimplifyMapper.selectReviewNumberBySkuAndSpu(anyString(), anyString())).thenReturn(30);
        
        // Mock 更新操作
        when(productDataOffersMapper.update(any(), any())).thenReturn(1);
        
        // 执行测试
        salesDataSupplementService.executeMonthlySupplementTask(currentMonth);
        
        // 验证调用
        verify(supplementRecordMapper, times(1)).selectAllDistinctOfferIds();
        verify(productDataOffersMapper, times(1)).selectList(any(LambdaQueryWrapper.class));
        verify(productDataOffersMapper, atLeast(2)).update(any(), any());
        verify(supplementRecordMapper, atLeast(2)).insert(any(SalesDataSupplementRecord.class));
    }

    @Test
    void testCalculateSalesFromReviews() {
        // 使用反射调用private方法进行测试
        Integer reviewNumber = 30;
        String result = (String) ReflectionTestUtils.invokeMethod(
            salesDataSupplementService, "calculateSalesFromReviews", reviewNumber);
        
        // 验证计算结果: 30 / 0.03 = 1000
        assertEquals("1000", result);
    }

    @Test
    void testCalculateSalesFromReviews_ZeroReviews() {
        Integer reviewNumber = 0;
        String result = (String) ReflectionTestUtils.invokeMethod(
            salesDataSupplementService, "calculateSalesFromReviews", reviewNumber);
        
        assertEquals("0", result);
    }

    @Test
    void testCalculateSalesFromReviews_NullReviews() {
        Integer reviewNumber = null;
        String result = (String) ReflectionTestUtils.invokeMethod(
            salesDataSupplementService, "calculateSalesFromReviews", reviewNumber);
        
        assertEquals("0", result);
    }

    @Test
    void testUpdateRecordedOffers_NoHistoryRecords() {
        // Mock 无历史记录
        when(supplementRecordMapper.selectAllDistinctOfferIds()).thenReturn(Collections.emptyList());
        
        // 使用反射调用private方法
        ReflectionTestUtils.invokeMethod(salesDataSupplementService, "updateRecordedOffers", "2025-01");
        
        // 验证只查询了历史记录，没有进行更新操作
        verify(supplementRecordMapper, times(1)).selectAllDistinctOfferIds();
        verify(productDataOffersMapper, never()).selectById(anyLong());
    }

    @Test
    void testSupplementNewEmptyOffers_NoEmptyOffers() {
        // Mock 无空销量offers
        when(productDataOffersMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());
        
        // 使用反射调用private方法
        ReflectionTestUtils.invokeMethod(salesDataSupplementService, "supplementNewEmptyOffers", "2025-01");
        
        // 验证只查询了空offers，没有进行补充操作
        verify(productDataOffersMapper, times(1)).selectList(any(LambdaQueryWrapper.class));
        verify(productDataSimplifyMapper, never()).selectReviewNumberBySkuAndSpu(anyString(), anyString());
    }

    @Test
    void testSupplementOfferSalesData_WithValidReviewNumber() {
        // 准备测试数据
        ProductDataOffers offer = createMockOffer(1L, "SKU001", "SPU001", null);
        String currentMonth = "2025-01";
        
        // Mock review_number查询
        when(productDataSimplifyMapper.selectReviewNumberBySkuAndSpu("SKU001", "SPU001")).thenReturn(60);
        
        // Mock 更新操作
        when(productDataOffersMapper.update(any(), any())).thenReturn(1);
        
        // 使用反射调用private方法
        ReflectionTestUtils.invokeMethod(salesDataSupplementService, "supplementOfferSalesData", offer, currentMonth);
        
        // 验证调用
        verify(productDataSimplifyMapper, times(1)).selectReviewNumberBySkuAndSpu("SKU001", "SPU001");
        verify(productDataOffersMapper, times(1)).update(any(), any());
        verify(supplementRecordMapper, times(1)).selectOne(any(LambdaQueryWrapper.class));
    }

    @Test
    void testSupplementOfferSalesData_WithInvalidReviewNumber() {
        // 准备测试数据
        ProductDataOffers offer = createMockOffer(1L, "SKU001", "SPU001", null);
        String currentMonth = "2025-01";
        
        // Mock review_number查询返回0
        when(productDataSimplifyMapper.selectReviewNumberBySkuAndSpu("SKU001", "SPU001")).thenReturn(0);
        
        // 使用反射调用private方法
        ReflectionTestUtils.invokeMethod(salesDataSupplementService, "supplementOfferSalesData", offer, currentMonth);
        
        // 验证不会进行更新操作
        verify(productDataSimplifyMapper, times(1)).selectReviewNumberBySkuAndSpu("SKU001", "SPU001");
        verify(productDataOffersMapper, never()).update(any(), any());
        verify(supplementRecordMapper, never()).insert(any());
    }

    @Test
    void testGetCurrentMonth() {
        String currentMonth = salesDataSupplementService.getCurrentMonth();
        
        // 验证返回格式正确（YYYY-MM）
        assert currentMonth.matches("\\d{4}-\\d{2}");
    }

    @Test
    void testGetSupplementStatistics() {
        String month = "2025-01";
        List<SalesDataSupplementRecord> records = Arrays.asList(
            new SalesDataSupplementRecord(),
            new SalesDataSupplementRecord()
        );
        
        when(supplementRecordMapper.selectRecordsByMonth(month)).thenReturn(records);
        
        String statistics = salesDataSupplementService.getSupplementStatistics(month);
        
        assertEquals("月份 2025-01 补充记录数: 2", statistics);
        verify(supplementRecordMapper, times(1)).selectRecordsByMonth(month);
    }

    @Test
    void testExecuteManualSupplementTask_Success() {
        // 准备测试数据
        SalesDataManualSupplementRequest request = new SalesDataManualSupplementRequest();
        request.setSourcePlatform("amazon");
        request.setStartTime(LocalDateTime.of(2025, 1, 1, 0, 0, 0));
        request.setEndTime(LocalDateTime.of(2025, 1, 10, 23, 59, 59));

        ProductDataOffers offer1 = createMockOffer(1L, "SKU001", "SPU001", null);
        ProductDataOffers offer2 = createMockOffer(2L, "SKU002", "SPU002", "");
        List<ProductDataOffers> emptyOffers = Arrays.asList(offer1, offer2);

        // Mock 查询空销量offers
        when(productDataOffersMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(emptyOffers);

        // Mock 评论数量查询
        when(productDataReviewsMapper.countReviewsBySkuSpuPlatformAndTimeRange(
            eq("SKU001"), eq("SPU001"), eq("amazon"), any(LocalDateTime.class), any(LocalDateTime.class)))
            .thenReturn(30);
        when(productDataReviewsMapper.countReviewsBySkuSpuPlatformAndTimeRange(
            eq("SKU002"), eq("SPU002"), eq("amazon"), any(LocalDateTime.class), any(LocalDateTime.class)))
            .thenReturn(60);

        // Mock 更新操作
        when(productDataOffersMapper.update(any(), any())).thenReturn(1);

        // 执行测试
        SalesDataSupplementResponse response = salesDataSupplementService.executeManualSupplementTask(request);

        // 验证结果
        assertNotNull(response);
        assertEquals(2, response.getTotalProcessed());
        assertEquals(2, response.getSuccessCount());
        assertEquals(0, response.getFailureCount());
        assertEquals(0, response.getSkippedCount());

        // 验证调用
        verify(productDataOffersMapper, times(1)).selectList(any(LambdaQueryWrapper.class));
        verify(productDataReviewsMapper, times(2)).countReviewsBySkuSpuPlatformAndTimeRange(
            anyString(), anyString(), anyString(), any(LocalDateTime.class), any(LocalDateTime.class));
        verify(productDataOffersMapper, times(2)).update(any(), any());
        verify(supplementRecordMapper, times(2)).insert(any(SalesDataSupplementRecord.class));
    }

    @Test
    void testExecuteManualSupplementTask_NoEmptyOffers() {
        // 准备测试数据
        SalesDataManualSupplementRequest request = new SalesDataManualSupplementRequest();
        request.setSourcePlatform("amazon");
        request.setStartTime(LocalDateTime.of(2025, 1, 1, 0, 0, 0));
        request.setEndTime(LocalDateTime.of(2025, 1, 10, 23, 59, 59));

        // Mock 查询空销量offers返回空列表
        when(productDataOffersMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // 执行测试
        SalesDataSupplementResponse response = salesDataSupplementService.executeManualSupplementTask(request);

        // 验证结果
        assertNotNull(response);
        assertEquals(0, response.getTotalProcessed());
        assertEquals(0, response.getSuccessCount());
        assertEquals(0, response.getFailureCount());
        assertEquals(0, response.getSkippedCount());
        assertEquals("没有需要补充的offers", response.getMessage());
    }

    @Test
    void testExecuteManualSupplementTask_WithSkippedOffers() {
        // 准备测试数据
        SalesDataManualSupplementRequest request = new SalesDataManualSupplementRequest();
        request.setSourcePlatform("amazon");
        request.setStartTime(LocalDateTime.of(2025, 1, 1, 0, 0, 0));
        request.setEndTime(LocalDateTime.of(2025, 1, 10, 23, 59, 59));

        ProductDataOffers offer1 = createMockOffer(1L, "SKU001", "SPU001", null);
        List<ProductDataOffers> emptyOffers = Arrays.asList(offer1);

        // Mock 查询空销量offers
        when(productDataOffersMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(emptyOffers);

        // Mock 评论数量查询返回0（应该被跳过）
        when(productDataReviewsMapper.countReviewsBySkuSpuPlatformAndTimeRange(
            eq("SKU001"), eq("SPU001"), eq("amazon"), any(LocalDateTime.class), any(LocalDateTime.class)))
            .thenReturn(0);

        // 执行测试
        SalesDataSupplementResponse response = salesDataSupplementService.executeManualSupplementTask(request);

        // 验证结果
        assertNotNull(response);
        assertEquals(1, response.getTotalProcessed());
        assertEquals(0, response.getSuccessCount());
        assertEquals(0, response.getFailureCount());
        assertEquals(1, response.getSkippedCount());

        // 验证不会进行更新操作
        verify(productDataOffersMapper, never()).update(any(), any());
        verify(supplementRecordMapper, never()).insert(any());
    }

    /**
     * 创建Mock ProductDataOffers对象
     */
    private ProductDataOffers createMockOffer(Long id, String skuId, String spuId, String salesLast30Days) {
        ProductDataOffers offer = new ProductDataOffers();
        offer.setId(id);
        offer.setSkuId(skuId);
        offer.setSpuId(spuId);
        offer.setSalesLast30Days(salesLast30Days);
        return offer;
    }
}
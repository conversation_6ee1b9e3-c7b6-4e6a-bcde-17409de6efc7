package ai.pricefox.mallfox.service.system;

import ai.pricefox.mallfox.domain.product.ProductTableConfig;
import ai.pricefox.mallfox.mapper.system.ProductTableConfigMapper;
import ai.pricefox.mallfox.model.param.ProductTableConfigRequest;
import ai.pricefox.mallfox.service.system.impl.ProductTableConfigServiceImpl;
import ai.pricefox.mallfox.vo.base.CommonResult;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * ProductTableConfig权重更新测试 - 新增功能测试
 * 测试新增配置和更新权重的功能
 */
@ExtendWith(MockitoExtension.class)
class ProductTableConfigUpdateWeightTest {

    @Mock
    private ProductTableConfigMapper productTableConfigMapper;

    private ProductTableConfigServiceImpl productTableConfigService;

    @BeforeEach
    void setUp() {
        productTableConfigService = new ProductTableConfigServiceImpl();
        // 使用反射设置私有字段
        try {
            java.lang.reflect.Field mapperField = ProductTableConfigServiceImpl.class.getDeclaredField("productTableConfigMapper");
            mapperField.setAccessible(true);
            mapperField.set(productTableConfigService, productTableConfigMapper);
        } catch (Exception e) {
            throw new RuntimeException("Failed to inject mock mapper", e);
        }
    }

    @Test
    void shouldInsertNewConfigWhenIdIsNull() {
        // 模拟新增配置的情况
        when(productTableConfigMapper.selectById(1)).thenReturn("aaaaa");
        when(productTableConfigMapper.selectById(2)).thenReturn("bbbbb");
        when(productTableConfigMapper.insert(any(ProductTableConfig.class))).thenAnswer(invocation -> {
            ProductTableConfig config = invocation.getArgument(0);
            config.setId(100); // 模拟数据库自动生成的ID
            return 1;
        });
        when(productTableConfigMapper.updateWeightById(anyInt(), anyString())).thenReturn(1);

        ProductTableConfigRequest.UpdateWeightRequest request =
            ProductTableConfigRequest.UpdateWeightRequest.builder()
                .tag("test_table")
                .id(null) // id为空，执行新增
                .field("new_field")
                .type(1)
                .info("{\"visible\": true}")
                .leftId(1)
                .rightId(2)
                .build();

        CommonResult<Boolean> result = productTableConfigService.updateWeight(request);

        assertTrue(result.isSuccess());

        // 验证调用了insert方法（先插入记录）
        verify(productTableConfigMapper).insert(argThat(config ->
            "test_table".equals(config.getTag()) &&
            "new_field".equals(config.getField()) &&
            config.getType() == 1 &&
            "{\"visible\": true}".equals(config.getInfo()) &&
            "V".equals(config.getWeight()) && // 默认权重
            config.getCreateTime() != null
        ));

        // 验证调用了selectById方法获取权重（用于计算新权重）
        verify(productTableConfigMapper).selectById(1);
        verify(productTableConfigMapper).selectById(2);

        // 验证调用了updateWeightById方法（更新权重）
        verify(productTableConfigMapper).updateWeightById(eq(100), anyString());
    }

    @Test
    void shouldUpdateWeightWhenIdIsNotNull() {
        // 模拟更新权重的情况
        when(productTableConfigMapper.selectById(1)).thenReturn("aaaaa");
        when(productTableConfigMapper.selectById(2)).thenReturn("bbbbb");
        when(productTableConfigMapper.updateWeightById(anyInt(), anyString())).thenReturn(1);

        ProductTableConfigRequest.UpdateWeightRequest request = 
            ProductTableConfigRequest.UpdateWeightRequest.builder()
                .tag("test_table")
                .id(3) // id不为空，执行更新
                .field("existing_field") // 这些字段在更新时会被忽略
                .type(1)
                .info("{\"visible\": false}")
                .leftId(1)
                .rightId(2)
                .build();

        CommonResult<Boolean> result = productTableConfigService.updateWeight(request);

        assertTrue(result.isSuccess());
        
        // 验证调用了selectById方法获取权重
        verify(productTableConfigMapper).selectById(1);
        verify(productTableConfigMapper).selectById(2);
        
        // 验证调用了updateWeightById方法而不是insert方法
        verify(productTableConfigMapper).updateWeightById(eq(3), anyString());
        
        // 验证没有调用insert方法
        verify(productTableConfigMapper, never()).insert(any(ProductTableConfig.class));
    }

    @Test
    void shouldInsertAtEndWhenBothIdsAreNull() {
        // 模拟在末尾插入的情况
        when(productTableConfigMapper.selectMaxWeightByTag("test_table")).thenReturn("zzzzz");
        when(productTableConfigMapper.insert(any(ProductTableConfig.class))).thenAnswer(invocation -> {
            ProductTableConfig config = invocation.getArgument(0);
            config.setId(200); // 模拟数据库自动生成的ID
            return 1;
        });
        when(productTableConfigMapper.updateWeightById(anyInt(), anyString())).thenReturn(1);

        ProductTableConfigRequest.UpdateWeightRequest request =
            ProductTableConfigRequest.UpdateWeightRequest.builder()
                .tag("test_table")
                .id(null)
                .field("end_field")
                .type(2)
                .info("{\"config\": \"value\"}")
                .leftId(null)
                .rightId(null)
                .build();

        CommonResult<Boolean> result = productTableConfigService.updateWeight(request);

        assertTrue(result.isSuccess());

        // 验证调用了insert方法（先插入记录）
        verify(productTableConfigMapper).insert(argThat(config ->
            "test_table".equals(config.getTag()) &&
            "end_field".equals(config.getField()) &&
            config.getType() == 2 &&
            "V".equals(config.getWeight()) // 默认权重
        ));

        // 验证调用了selectMaxWeightByTag方法（用于计算新权重）
        verify(productTableConfigMapper).selectMaxWeightByTag("test_table");

        // 验证调用了updateWeightById方法（更新权重）
        verify(productTableConfigMapper).updateWeightById(eq(200), anyString());
    }

    @Test
    void shouldInsertWithDefaultWeightWhenNoExistingRecords() {
        // 模拟没有现有记录时的插入
        when(productTableConfigMapper.selectMaxWeightByTag("new_table")).thenReturn(null);
        when(productTableConfigMapper.insert(any(ProductTableConfig.class))).thenAnswer(invocation -> {
            ProductTableConfig config = invocation.getArgument(0);
            config.setId(300); // 模拟数据库自动生成的ID
            return 1;
        });
        when(productTableConfigMapper.updateWeightById(anyInt(), anyString())).thenReturn(1);

        ProductTableConfigRequest.UpdateWeightRequest request =
            ProductTableConfigRequest.UpdateWeightRequest.builder()
                .tag("new_table")
                .id(null)
                .field("first_field")
                .type(1)
                .leftId(null)
                .rightId(null)
                .build();

        CommonResult<Boolean> result = productTableConfigService.updateWeight(request);

        assertTrue(result.isSuccess());

        // 验证调用了insert方法（先插入记录）
        verify(productTableConfigMapper).insert(argThat(config ->
            "new_table".equals(config.getTag()) &&
            "first_field".equals(config.getField()) &&
            "V".equals(config.getWeight()) // 默认权重
        ));

        // 验证调用了selectMaxWeightByTag方法（用于计算新权重）
        verify(productTableConfigMapper).selectMaxWeightByTag("new_table");

        // 验证调用了updateWeightById方法（更新权重）
        verify(productTableConfigMapper).updateWeightById(eq(300), eq("V")); // 由于没有现有记录，最终权重还是V
    }

    @Test
    void shouldInsertAfterSpecificElement() {
        // 模拟在指定元素之后插入
        when(productTableConfigMapper.selectById(5)).thenReturn("mmmmm");
        when(productTableConfigMapper.insert(any(ProductTableConfig.class))).thenAnswer(invocation -> {
            ProductTableConfig config = invocation.getArgument(0);
            config.setId(400); // 模拟数据库自动生成的ID
            return 1;
        });
        when(productTableConfigMapper.updateWeightById(anyInt(), anyString())).thenReturn(1);

        ProductTableConfigRequest.UpdateWeightRequest request =
            ProductTableConfigRequest.UpdateWeightRequest.builder()
                .tag("test_table")
                .id(null)
                .field("after_field")
                .type(1)
                .leftId(5)
                .rightId(null)
                .build();

        CommonResult<Boolean> result = productTableConfigService.updateWeight(request);

        assertTrue(result.isSuccess());

        // 验证调用了insert方法（先插入记录）
        verify(productTableConfigMapper).insert(argThat(config ->
            "test_table".equals(config.getTag()) &&
            "after_field".equals(config.getField()) &&
            "V".equals(config.getWeight()) // 默认权重
        ));

        // 验证调用了selectById方法（用于计算新权重）
        verify(productTableConfigMapper).selectById(5);

        // 验证调用了updateWeightById方法（更新权重）
        verify(productTableConfigMapper).updateWeightById(eq(400), anyString());
    }

    @Test
    void shouldInsertBeforeSpecificElement() {
        // 模拟在指定元素之前插入
        when(productTableConfigMapper.selectById(3)).thenReturn("ccccc");
        when(productTableConfigMapper.insert(any(ProductTableConfig.class))).thenAnswer(invocation -> {
            ProductTableConfig config = invocation.getArgument(0);
            config.setId(500); // 模拟数据库自动生成的ID
            return 1;
        });
        when(productTableConfigMapper.updateWeightById(anyInt(), anyString())).thenReturn(1);

        ProductTableConfigRequest.UpdateWeightRequest request =
            ProductTableConfigRequest.UpdateWeightRequest.builder()
                .tag("test_table")
                .id(null)
                .field("before_field")
                .type(1)
                .leftId(null)
                .rightId(3)
                .build();

        CommonResult<Boolean> result = productTableConfigService.updateWeight(request);

        assertTrue(result.isSuccess());

        // 验证调用了insert方法（先插入记录）
        verify(productTableConfigMapper).insert(argThat(config ->
            "test_table".equals(config.getTag()) &&
            "before_field".equals(config.getField()) &&
            "V".equals(config.getWeight()) // 默认权重
        ));

        // 验证调用了selectById方法（用于计算新权重）
        verify(productTableConfigMapper).selectById(3);

        // 验证调用了updateWeightById方法（更新权重）
        verify(productTableConfigMapper).updateWeightById(eq(500), anyString());
    }

    @Test
    void shouldValidateRequiredFieldsForInsert() {
        // 测试新增时必需字段的验证
        ProductTableConfigRequest.UpdateWeightRequest request = 
            ProductTableConfigRequest.UpdateWeightRequest.builder()
                .tag("test_table")
                .id(null)
                .field("test_field")
                .type(null) // type为空，应该抛出异常
                .leftId(1)
                .rightId(2)
                .build();

        // 验证：应该抛出异常
        assertThrows(Exception.class, () -> {
            productTableConfigService.updateWeight(request);
        });
    }
}

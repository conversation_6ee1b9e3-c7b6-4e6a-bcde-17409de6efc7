package ai.pricefox.mallfox.service.system;

import ai.pricefox.mallfox.mapper.system.ProductTableConfigMapper;
import ai.pricefox.mallfox.model.param.ProductTableConfigRequest;
import ai.pricefox.mallfox.service.system.impl.ProductTableConfigServiceImpl;
import ai.pricefox.mallfox.utils.LexoRankUtils;
import ai.pricefox.mallfox.vo.base.CommonResult;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * ProductTableConfig权重更新测试
 * 重点测试相同权重值的处理逻辑
 */
@ExtendWith(MockitoExtension.class)
class ProductTableConfigWeightTest {

    @Mock
    private ProductTableConfigMapper productTableConfigMapper;

    private ProductTableConfigServiceImpl productTableConfigService;

    @BeforeEach
    void setUp() {
        productTableConfigService = new ProductTableConfigServiceImpl();
        // 使用反射设置私有字段
        try {
            java.lang.reflect.Field mapperField = ProductTableConfigServiceImpl.class.getDeclaredField("productTableConfigMapper");
            mapperField.setAccessible(true);
            mapperField.set(productTableConfigService, productTableConfigMapper);
        } catch (Exception e) {
            throw new RuntimeException("Failed to inject mock mapper", e);
        }
    }

    @Test
    void shouldHandleSameWeightValues() {
        // 模拟两个相同的权重值
        String sameWeight = "aaaaV";
        
        when(productTableConfigMapper.selectById(1)).thenReturn(sameWeight);
        when(productTableConfigMapper.selectById(2)).thenReturn(sameWeight);
        when(productTableConfigMapper.updateWeightById(anyInt(), anyString())).thenReturn(1);

        ProductTableConfigRequest.UpdateWeightRequest request = 
            ProductTableConfigRequest.UpdateWeightRequest.builder()
                .tag("test_table")
                .id(3)
                .leftId(1)
                .rightId(2)
                .build();

        CommonResult<Boolean> result = productTableConfigService.updateWeight(request);

        assertTrue(result.isSuccess());
        
        // 验证调用了selectById方法
        verify(productTableConfigMapper).selectById(1);
        verify(productTableConfigMapper).selectById(2);
        
        // 验证调用了updateWeightById方法，并且新权重不等于原权重
        verify(productTableConfigMapper).updateWeightById(eq(3), argThat(newWeight -> 
            !sameWeight.equals(newWeight) && newWeight != null
        ));
    }

    @Test
    void shouldHandleDifferentWeightValues() {
        // 模拟两个不同的权重值
        String leftWeight = "aaaaa";
        String rightWeight = "bbbbb";
        
        when(productTableConfigMapper.selectById(1)).thenReturn(leftWeight);
        when(productTableConfigMapper.selectById(2)).thenReturn(rightWeight);
        when(productTableConfigMapper.updateWeightById(anyInt(), anyString())).thenReturn(1);

        ProductTableConfigRequest.UpdateWeightRequest request = 
            ProductTableConfigRequest.UpdateWeightRequest.builder()
                .tag("test_table")
                .id(3)
                .leftId(1)
                .rightId(2)
                .build();

        CommonResult<Boolean> result = productTableConfigService.updateWeight(request);

        assertTrue(result.isSuccess());
        
        // 验证调用了selectById方法
        verify(productTableConfigMapper).selectById(1);
        verify(productTableConfigMapper).selectById(2);
        
        // 验证调用了updateWeightById方法
        verify(productTableConfigMapper).updateWeightById(eq(3), anyString());
    }

    @Test
    void testLexoRankUtilsBetweenWithSameValues() {
        // 直接测试LexoRankUtils.between方法处理相同值的情况
        String sameWeight = "aaaaV";
        String result = LexoRankUtils.between(sameWeight, sameWeight);
        
        // 验证：当两个值相同时，between方法返回相同的值
        assertEquals(sameWeight, result, "LexoRankUtils.between对相同值应该返回相同值");
    }

    @Test
    void testLexoRankUtilsAfterStrategy() {
        // 测试使用after策略处理相同权重的情况
        String sameWeight = "aaaaV";
        String afterWeight = LexoRankUtils.after(sameWeight);
        
        // 验证：after方法应该生成一个不同的权重
        assertNotEquals(sameWeight, afterWeight, "LexoRankUtils.after应该生成不同的权重");
        assertTrue(afterWeight.compareTo(sameWeight) > 0, "after生成的权重应该大于原权重");
    }

    @Test
    void testLexoRankUtilsBeforeStrategy() {
        // 测试使用before策略
        String weight = "bbbbb";
        String beforeWeight = LexoRankUtils.before(weight);
        
        // 验证：before方法应该生成一个更小的权重
        assertNotEquals(weight, beforeWeight, "LexoRankUtils.before应该生成不同的权重");
        assertTrue(beforeWeight.compareTo(weight) < 0, "before生成的权重应该小于原权重");
    }

    @Test
    void shouldHandleAfterScenario() {
        // 测试场景2：在某个元素之后插入
        String leftWeight = "ccccc";
        
        when(productTableConfigMapper.selectById(1)).thenReturn(leftWeight);
        when(productTableConfigMapper.updateWeightById(anyInt(), anyString())).thenReturn(1);

        ProductTableConfigRequest.UpdateWeightRequest request = 
            ProductTableConfigRequest.UpdateWeightRequest.builder()
                .tag("test_table")
                .id(3)
                .leftId(1)
                .rightId(null)
                .build();

        CommonResult<Boolean> result = productTableConfigService.updateWeight(request);

        assertTrue(result.isSuccess());
        
        verify(productTableConfigMapper).selectById(1);
        verify(productTableConfigMapper).updateWeightById(eq(3), anyString());
    }

    @Test
    void shouldHandleBeforeScenario() {
        // 测试场景3：在某个元素之前插入
        String rightWeight = "ddddd";
        
        when(productTableConfigMapper.selectById(2)).thenReturn(rightWeight);
        when(productTableConfigMapper.updateWeightById(anyInt(), anyString())).thenReturn(1);

        ProductTableConfigRequest.UpdateWeightRequest request = 
            ProductTableConfigRequest.UpdateWeightRequest.builder()
                .tag("test_table")
                .id(3)
                .leftId(null)
                .rightId(2)
                .build();

        CommonResult<Boolean> result = productTableConfigService.updateWeight(request);

        assertTrue(result.isSuccess());
        
        verify(productTableConfigMapper).selectById(2);
        verify(productTableConfigMapper).updateWeightById(eq(3), anyString());
    }

    @Test
    void shouldGenerateUniqueWeightsForMultipleSameWeights() {
        // 测试多个相同权重的情况
        String[] sameWeights = {"aaaaV", "aaaaV", "aaaaV"};
        String[] expectedDifferentWeights = new String[3];
        
        // 使用after策略生成不同的权重
        expectedDifferentWeights[0] = LexoRankUtils.after(sameWeights[0]);
        expectedDifferentWeights[1] = LexoRankUtils.after(expectedDifferentWeights[0]);
        expectedDifferentWeights[2] = LexoRankUtils.after(expectedDifferentWeights[1]);
        
        // 验证生成的权重都不相同
        assertNotEquals(expectedDifferentWeights[0], sameWeights[0]);
        assertNotEquals(expectedDifferentWeights[1], expectedDifferentWeights[0]);
        assertNotEquals(expectedDifferentWeights[2], expectedDifferentWeights[1]);
        
        // 验证权重的顺序性
        assertTrue(expectedDifferentWeights[0].compareTo(sameWeights[0]) > 0);
        assertTrue(expectedDifferentWeights[1].compareTo(expectedDifferentWeights[0]) > 0);
        assertTrue(expectedDifferentWeights[2].compareTo(expectedDifferentWeights[1]) > 0);
    }
}

package ai.pricefox.mallfox.service.system;

import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 权重生成算法测试
 */
class WeightGenerationTest {

    @Test
    void testWeightGeneration() {
        // 测试权重生成算法
        List<String> weights = generateSequentialWeights(5, 5, "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz");
        
        System.out.println("生成的5位权重：");
        for (String weight : weights) {
            System.out.println(weight);
        }
        
        // 验证权重格式
        assertEquals(5, weights.size());
        for (String weight : weights) {
            assertEquals(5, weight.length(), "权重应该是5位");
        }
        
        // 验证权重是有序的
        for (int i = 1; i < weights.size(); i++) {
            assertTrue(weights.get(i-1).compareTo(weights.get(i)) < 0, 
                "权重应该是递增的: " + weights.get(i-1) + " < " + weights.get(i));
        }
    }

    @Test
    void testPositionToWeight() {
        String charSet = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
        
        // 测试位置转换
        String weight1 = convertPositionToWeight(1000, 4, charSet);
        String weight2 = convertPositionToWeight(2000, 4, charSet);
        String weight3 = convertPositionToWeight(3000, 4, charSet);
        
        System.out.println("位置转权重测试：");
        System.out.println("1000 -> " + weight1);
        System.out.println("2000 -> " + weight2);
        System.out.println("3000 -> " + weight3);
        
        // 验证长度
        assertEquals(4, weight1.length());
        assertEquals(4, weight2.length());
        assertEquals(4, weight3.length());
        
        // 验证顺序
        assertTrue(weight1.compareTo(weight2) < 0);
        assertTrue(weight2.compareTo(weight3) < 0);
    }

    @Test
    void testCompatibilityWithExistingWeights() {
        // 测试与现有权重格式的兼容性
        String existingWeight = "aaaaV";

        // 生成新的权重应该与现有权重兼容
        List<String> newWeights = generateSequentialWeights(5, 5, "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz");

        System.out.println("现有权重: " + existingWeight);
        System.out.println("顺序生成的权重:");
        for (String weight : newWeights) {
            System.out.println(weight);
        }

        // 验证格式一致性
        for (String weight : newWeights) {
            assertEquals(existingWeight.length(), weight.length(), "新权重长度应该与现有权重一致");
        }

        // 验证顺序递增
        for (int i = 1; i < newWeights.size(); i++) {
            assertTrue(newWeights.get(i-1).compareTo(newWeights.get(i)) < 0,
                "权重应该是递增的: " + newWeights.get(i-1) + " < " + newWeights.get(i));
        }
    }

    @Test
    void testSequentialWeightGeneration() {
        // 测试顺序权重生成
        List<String> weights = generateSequentialWeights(10, 4, "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz");

        System.out.println("顺序生成的4位权重（10个）:");
        for (String weight : weights) {
            System.out.println(weight);
        }

        // 验证数量
        assertEquals(10, weights.size());

        // 验证长度
        for (String weight : weights) {
            assertEquals(4, weight.length());
        }

        // 验证顺序
        for (int i = 1; i < weights.size(); i++) {
            assertTrue(weights.get(i-1).compareTo(weights.get(i)) < 0);
        }

        // 验证起始权重
        assertEquals("0000", weights.get(0));
    }

    @Test
    void testNextWeightGeneration() {
        String charSet = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";

        // 测试简单递增
        String next1 = getNextWeight("0000", charSet);
        assertEquals("0001", next1);

        String next2 = getNextWeight("0001", charSet);
        assertEquals("0002", next2);

        // 测试进位
        String next3 = getNextWeight("000z", charSet);
        assertEquals("0010", next3);

        String next4 = getNextWeight("00zz", charSet);
        assertEquals("0100", next4);

        // 测试全进位（增加长度）
        String next5 = getNextWeight("zzzz", charSet);
        assertEquals("0zzzz", next5);

        System.out.println("权重递增测试:");
        System.out.println("0000 -> " + next1);
        System.out.println("0001 -> " + next2);
        System.out.println("000z -> " + next3);
        System.out.println("00zz -> " + next4);
        System.out.println("zzzz -> " + next5);
    }

    /**
     * 生成顺序递增的权重
     */
    private List<String> generateSequentialWeights(int count, int length, String charSet) {
        List<String> weights = new ArrayList<>();

        // 生成起始权重
        String startWeight = String.valueOf(charSet.charAt(0)).repeat(length);

        String currentWeight = startWeight;
        for (int i = 0; i < count; i++) {
            weights.add(currentWeight);
            currentWeight = getNextWeight(currentWeight, charSet);
        }

        return weights;
    }

    /**
     * 获取下一个权重
     */
    private String getNextWeight(String currentWeight, String charSet) {
        char[] chars = currentWeight.toCharArray();
        int length = chars.length;

        // 从右到左进行进位
        for (int i = length - 1; i >= 0; i--) {
            char currentChar = chars[i];
            int currentIndex = charSet.indexOf(currentChar);

            if (currentIndex < charSet.length() - 1) {
                // 当前位可以递增
                chars[i] = charSet.charAt(currentIndex + 1);
                return new String(chars);
            } else {
                // 当前位已经是最大值，需要进位
                chars[i] = charSet.charAt(0); // 重置为第一个字符
            }
        }

        // 所有位都已经是最大值，需要增加长度
        return charSet.charAt(0) + currentWeight;
    }

    /**
     * 将位置转换为权重字符串
     */
    private String convertPositionToWeight(long position, int length, String charSet) {
        StringBuilder weight = new StringBuilder();
        long remaining = position;
        int base = charSet.length();
        
        for (int i = 0; i < length; i++) {
            int charIndex = (int) (remaining % base);
            weight.insert(0, charSet.charAt(charIndex));
            remaining /= base;
        }
        
        return weight.toString();
    }
}

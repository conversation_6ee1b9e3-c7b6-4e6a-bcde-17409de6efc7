package ai.pricefox.mallfox.service.tracking;

import ai.pricefox.mallfox.config.FieldTrackingConfig;
import ai.pricefox.mallfox.domain.product.ProductDataOffers;
import ai.pricefox.mallfox.domain.tracking.ProductFieldSourceTracking;
import ai.pricefox.mallfox.mapper.tracking.FieldTrackingMapper;
import ai.pricefox.mallfox.model.dto.FieldChangeEvent;
import ai.pricefox.mallfox.model.dto.FieldTrackingContext;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.*;

/**
 * 字段追踪服务测试
 * 
 * <AUTHOR>
 * @since 2025-01-14
 */
@ExtendWith(MockitoExtension.class)
public class FieldTrackingServiceTest {

    @Mock
    private FieldTrackingMapper fieldTrackingMapper;

    @Mock
    private FieldTrackingConfig config;

    @InjectMocks
    private FieldTrackingService fieldTrackingService;

    @BeforeEach
    void setUp() {
        when(config.getEnabled()).thenReturn(true);
        when(config.getAsyncEnabled()).thenReturn(false); // 同步模式便于测试
    }

    @Test
    void testRecordFieldUpdate() {
        // 准备测试数据
        FieldChangeEvent event = new FieldChangeEvent(
            "product_data_offers", 1L, "price", 2, "100.00", "120.00"
        );

        // 执行测试
        fieldTrackingService.recordFieldUpdate(event);

        // 验证结果
        verify(fieldTrackingMapper, times(1)).insertOrUpdate(any(ProductFieldSourceTracking.class));
    }

    @Test
    void testBatchRecordFieldChanges() {
        // 准备测试数据
        List<FieldChangeEvent> events = Arrays.asList(
            new FieldChangeEvent("product_data_offers", 1L, "price", 2, "100.00", "120.00"),
            new FieldChangeEvent("product_data_offers", 1L, "title", 2, "Old Title", "New Title")
        );

        // 执行测试
        fieldTrackingService.batchRecordFieldChanges(events);

        // 验证结果
        verify(fieldTrackingMapper, times(1)).batchInsertOrUpdate(anyList());
    }

    @Test
    void testTrackableEntityBasicFunctionality() {
        // 准备测试数据
        ProductDataOffers offer = new ProductDataOffers();
        offer.setId(1L);
        offer.setPrice(java.math.BigDecimal.valueOf(100.00));
        offer.setTitle("Original Title");

        FieldTrackingContext context = FieldTrackingContext.forOffers(1L, 2);
        
        // 创建可追踪实体
        TrackableEntity<ProductDataOffers> trackable = new TrackableEntity<>(
            offer, context, fieldTrackingService, config
        );

        // 修改字段值
        offer.setPrice(java.math.BigDecimal.valueOf(120.00));
        offer.setTitle("New Title");

        // 检测变更
        trackable.detectAndRecordChanges();

        // 验证变更被记录
        verify(fieldTrackingMapper, times(1)).batchInsertOrUpdate(anyList());
    }

    @Test
    void testRecordNewRecordFields() {
        // 准备测试数据
        ProductDataOffers offer = new ProductDataOffers();
        offer.setId(1L);
        offer.setPrice(java.math.BigDecimal.valueOf(100.00));
        offer.setTitle("Test Product");
        offer.setSourcePlatform("amazon");

        // 执行测试
        fieldTrackingService.recordNewRecordFields(offer, "product_data_offers", 1L, 2, "amazon");

        // 验证结果 - 应该记录所有非空字段
        verify(fieldTrackingMapper, times(1)).batchInsertOrUpdate(anyList());
    }

    @Test
    void testConfigDisabled() {
        // 配置禁用追踪
        when(config.getEnabled()).thenReturn(false);

        FieldChangeEvent event = new FieldChangeEvent(
            "product_data_offers", 1L, "price", 2, "100.00", "120.00"
        );

        // 执行测试
        fieldTrackingService.recordFieldUpdate(event);

        // 验证不会记录
        verify(fieldTrackingMapper, never()).insertOrUpdate(any());
    }
}

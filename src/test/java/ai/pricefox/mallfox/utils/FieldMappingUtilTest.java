package ai.pricefox.mallfox.utils;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 * @desc 字段映射工具类测试
 * @since 2025/6/29
 */
public class FieldMappingUtilTest {

    @Test
    public void testConvertToDbField() {
        // 测试已映射的字段
        assertEquals("s.spu_id", FieldMappingUtil.convertToDbField("spuId"));
        assertEquals("s.model", FieldMappingUtil.convertToDbField("model"));
        assertEquals("o.brand", FieldMappingUtil.convertToDbField("brand"));
        assertEquals("o.price", FieldMappingUtil.convertToDbField("price"));
        assertEquals("s.create_time", FieldMappingUtil.convertToDbField("createTime"));
        
        // 测试自动转换
        assertEquals("s.test_field", FieldMappingUtil.convertToDbField("testField"));
        assertEquals("s.another_test", FieldMappingUtil.convertToDbField("anotherTest"));
        
        // 测试空值和null
        assertEquals("s.spu_id", FieldMappingUtil.convertToDbField(""));
        assertEquals("s.spu_id", FieldMappingUtil.convertToDbField(null));
    }

    @Test
    public void testValidateSortOrder() {
        // 测试有效的排序方向
        assertEquals("ASC", FieldMappingUtil.validateSortOrder("asc"));
        assertEquals("ASC", FieldMappingUtil.validateSortOrder("ASC"));
        assertEquals("DESC", FieldMappingUtil.validateSortOrder("desc"));
        assertEquals("DESC", FieldMappingUtil.validateSortOrder("DESC"));
        
        // 测试无效的排序方向，应该返回默认值ASC
        assertEquals("ASC", FieldMappingUtil.validateSortOrder("invalid"));
        assertEquals("ASC", FieldMappingUtil.validateSortOrder(""));
        assertEquals("ASC", FieldMappingUtil.validateSortOrder(null));
    }

    @Test
    public void testGetSupportedSortFields() {
        var supportedFields = FieldMappingUtil.getSupportedSortFields();

        // 验证包含关键字段
        assertTrue(supportedFields.containsKey("spuId"));
        assertTrue(supportedFields.containsKey("brand"));
        assertTrue(supportedFields.containsKey("price"));
        assertTrue(supportedFields.containsKey("createTime"));

        // 验证映射正确
        assertEquals("s.spu_id", supportedFields.get("spuId"));
        assertEquals("o.brand", supportedFields.get("brand"));
        assertEquals("o.price", supportedFields.get("price"));
    }

    @Test
    public void testConvertToDbFieldForGroupBy() {
        // 测试GROUP BY字段（spuId不需要聚合函数）
        assertEquals("s.spu_id", FieldMappingUtil.convertToDbFieldForGroupBy("spuId"));

        // 测试非GROUP BY字段（需要MIN()聚合函数）
        assertEquals("MIN(s.model)", FieldMappingUtil.convertToDbFieldForGroupBy("model"));
        assertEquals("MIN(o.brand)", FieldMappingUtil.convertToDbFieldForGroupBy("brand"));
        assertEquals("MIN(o.price)", FieldMappingUtil.convertToDbFieldForGroupBy("price"));
        assertEquals("MIN(s.create_time)", FieldMappingUtil.convertToDbFieldForGroupBy("createTime"));

        // 测试空值
        assertEquals("s.spu_id", FieldMappingUtil.convertToDbFieldForGroupBy(""));
        assertEquals("s.spu_id", FieldMappingUtil.convertToDbFieldForGroupBy(null));
    }
}

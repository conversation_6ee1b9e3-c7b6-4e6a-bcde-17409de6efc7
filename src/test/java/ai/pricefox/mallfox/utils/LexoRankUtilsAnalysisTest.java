package ai.pricefox.mallfox.utils;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * LexoRankUtils详细分析测试
 * 分析为什么 after("aaaaV") 返回 "m"
 */
class LexoRankUtilsAnalysisTest {

    @Test
    void analyzeAfterMethodWithAaaaV() {
        String input = "aaaaV";
        String result = LexoRankUtils.after(input);
        
        System.out.println("=== LexoRankUtils.after(\"" + input + "\") 分析 ===");
        System.out.println("输入: " + input);
        System.out.println("输出: " + result);
        System.out.println("输入长度: " + input.length());
        System.out.println("输出长度: " + result.length());
        
        // 分析字符集
        String charSet = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
        char maxChar = 'z';
        
        System.out.println("字符集: " + charSet);
        System.out.println("最大字符: " + maxChar);
        
        // 分析maxRank
        String maxRank = String.valueOf(maxChar).repeat(input.length());
        System.out.println("maxRank (长度" + input.length() + "的最大值): " + maxRank);
        System.out.println("input.equals(maxRank): " + maxRank.equals(input));
        
        // 由于 "aaaaV" != "zzzzz"，所以会调用 between("aaaaV", "zzzzz")
        String betweenResult = LexoRankUtils.between(input, maxRank);
        System.out.println("between(\"" + input + "\", \"" + maxRank + "\"): " + betweenResult);
        
        assertEquals(result, betweenResult);
    }

    @Test
    void analyzeBetweenMethodWithAaaaVAndZzzzz() {
        String prev = "aaaaV";
        String next = "zzzzz";
        String result = LexoRankUtils.between(prev, next);
        
        System.out.println("=== LexoRankUtils.between(\"" + prev + "\", \"" + next + "\") 分析 ===");
        System.out.println("prev: " + prev);
        System.out.println("next: " + next);
        System.out.println("result: " + result);
        
        // 分析字符差异
        System.out.println("字符比较:");
        for (int i = 0; i < Math.min(prev.length(), next.length()); i++) {
            char prevChar = prev.charAt(i);
            char nextChar = next.charAt(i);
            System.out.println("位置" + i + ": '" + prevChar + "' vs '" + nextChar + "' (相同: " + (prevChar == nextChar) + ")");
            if (prevChar != nextChar) {
                System.out.println("第一个不同位置: " + i);
                break;
            }
        }
        
        // 分析字符集中的位置
        String charSet = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
        char prevChar = prev.charAt(0); // 'a'
        char nextChar = next.charAt(0); // 'z'
        
        int prevIndex = charSet.indexOf(prevChar);
        int nextIndex = charSet.indexOf(nextChar);
        int midIndex = (prevIndex + nextIndex) / 2;
        char midChar = charSet.charAt(midIndex);
        
        System.out.println("字符集分析:");
        System.out.println("'" + prevChar + "' 在字符集中的位置: " + prevIndex);
        System.out.println("'" + nextChar + "' 在字符集中的位置: " + nextIndex);
        System.out.println("中间位置: (" + prevIndex + " + " + nextIndex + ") / 2 = " + midIndex);
        System.out.println("中间字符: '" + midChar + "'");
        
        assertEquals('m', midChar);
        assertEquals("m", result);
    }

    @Test
    void analyzeCharacterSet() {
        String charSet = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
        
        System.out.println("=== 字符集分析 ===");
        System.out.println("完整字符集: " + charSet);
        System.out.println("字符集长度: " + charSet.length());
        
        // 分析关键字符的位置
        char[] keyChars = {'0', '9', 'A', 'Z', 'a', 'z', 'V', 'm'};
        for (char c : keyChars) {
            int index = charSet.indexOf(c);
            System.out.println("字符 '" + c + "' 的位置: " + index);
        }
        
        // 验证 'a' 和 'z' 的中间值
        int aIndex = charSet.indexOf('a');
        int zIndex = charSet.indexOf('z');
        int midIndex = (aIndex + zIndex) / 2;
        char midChar = charSet.charAt(midIndex);
        
        System.out.println("'a' 位置: " + aIndex + ", 'z' 位置: " + zIndex);
        System.out.println("中间位置: " + midIndex + ", 中间字符: '" + midChar + "'");
    }

    @Test
    void testVariousAfterCases() {
        String[] testCases = {
            "a", "aaaaV", "zzzzz", "V", "m", "0", "9", "Z"
        };
        
        System.out.println("=== 各种情况的 after() 测试 ===");
        for (String testCase : testCases) {
            String result = LexoRankUtils.after(testCase);
            System.out.println("after(\"" + testCase + "\") = \"" + result + "\"");
        }
    }

    @Test
    void testBetweenWithSameFirstChar() {
        // 测试第一个字符相同的情况
        String result1 = LexoRankUtils.between("a", "az");
        String result2 = LexoRankUtils.between("aa", "az");
        String result3 = LexoRankUtils.between("aaa", "aaz");
        
        System.out.println("=== 第一个字符相同的 between() 测试 ===");
        System.out.println("between(\"a\", \"az\") = \"" + result1 + "\"");
        System.out.println("between(\"aa\", \"az\") = \"" + result2 + "\"");
        System.out.println("between(\"aaa\", \"aaz\") = \"" + result3 + "\"");
    }
}

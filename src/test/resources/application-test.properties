# 测试环境配置
spring.profiles.active=test

# 数据库配置 - 使用内存数据库H2进行测试
spring.datasource.driver-class-name=org.h2.Driver
spring.datasource.url=jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
spring.datasource.username=sa
spring.datasource.password=

# H2数据库控制台
spring.h2.console.enabled=true

# MyBatis-Plus配置
mybatis-plus.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl
mybatis-plus.global-config.db-config.logic-delete-field=deleted
mybatis-plus.global-config.db-config.logic-delete-value=1
mybatis-plus.global-config.db-config.logic-not-delete-value=0

# 初始化数据库表结构
spring.sql.init.mode=always
spring.sql.init.schema-locations=classpath:schema-test.sql

# 日志配置
logging.level.ai.pricefox.mallfox=DEBUG
logging.level.org.springframework.web=DEBUG

# Sa-Token配置
sa-token.token-name=Authorization
sa-token.timeout=2592000
sa-token.activity-timeout=-1
sa-token.is-concurrent=true
sa-token.is-share=true
sa-token.token-style=uuid
sa-token.is-log=false

-- 创建分类信息表
CREATE TABLE IF NOT EXISTS category_info (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '分类ID',
    parent_id BIGINT NOT NULL DEFAULT 0 COMMENT '父分类ID (0代表顶级分类)',
    name VARCHAR(100) NOT NULL COMMENT '分类名称 (如: 手机, 智能手机)',
    level INT NOT NULL COMMENT '分类层级',
    icon_url VARCHAR(500) COMMENT '分类图标',
    sort_order INT DEFAULT 0 COMMENT '排序',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否激活',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
);

-- 创建管理员用户表（用于测试认证）
CREATE TABLE IF NOT EXISTS admin_user (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    email VARCHAR(100),
    phone VARCHAR(20),
    status TINYINT DEFAULT 1,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 插入测试管理员用户
INSERT INTO admin_user (id, username, password, email, phone) VALUES 
(1, 'admin', 'admin123', '<EMAIL>', '13800138000')
ON DUPLICATE KEY UPDATE username = username;

package ai.pricefox.mallfox.model.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 属性值映射响应
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
@Data
@Schema(description = "属性值映射响应")
public class AttributeValueMappingResponse {

    @Schema(description = "属性值ID", example = "1")
    private Long valueId;

    @Schema(description = "属性值编码", example = "VALUE001")
    private String valueCode;

    @Schema(description = "属性值名称", example = "Apple")
    private String valueName;

    @Schema(description = "属性值CN", example = "苹果")
    private String valueNameCn;

    @Schema(description = "映射属性值名称列表", example = "['Apple Inc.', 'APPLE', '苹果公司']")
    private List<String> mappingValueNames;

    @Schema(description = "映射属性值名称（显示用，逗号分隔）", example = "Apple Inc.、APPLE、苹果公司")
    private String mappingValueNamesDisplay;

    @Schema(description = "创建时间")
    private LocalDateTime createDate;

    @Schema(description = "更新时间")
    private LocalDateTime updateDate;
}